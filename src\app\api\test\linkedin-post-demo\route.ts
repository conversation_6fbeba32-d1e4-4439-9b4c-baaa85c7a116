import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Create a live LinkedIn test post using Demo User account
 * POST /api/test/linkedin-post-demo
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Creating LinkedIn test post using Demo User account...');

    const body = await request.json();
    const { 
      accountName = 'burak ozan',
      dryRun = false 
    } = body;

    const testContent = '🧪 Testing eWasl Social Scheduler LinkedIn integration! 🚀\n\nThis is a live test of our LinkedIn posting functionality.\n\n#eWasl #LinkedIn #SocialMediaManagement #Test';

    const results = {
      timestamp: new Date().toISOString(),
      accountName,
      content: testContent,
      dryRun,
      steps: {} as any
    };

    // Step 1: Get LinkedIn account data using service role client
    try {
      console.log('Step 1: Retrieving LinkedIn account data...');
      const supabase = createServiceRoleClient();
      const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
      
      const { data: account, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'LINKEDIN')
        .eq('account_name', accountName)
        .single();

      if (error || !account) {
        throw new Error(`LinkedIn account "${accountName}" not found`);
      }

      results.steps.accountRetrieval = {
        success: true,
        account: {
          id: account.id,
          account_name: account.account_name,
          account_id: account.account_id,
          expires_at: account.expires_at,
          hasAccessToken: !!account.access_token,
          tokenLength: account.access_token?.length || 0
        }
      };

      console.log(`✅ Found LinkedIn account: ${account.account_name} (${account.account_id})`);

      // Step 2: Test LinkedIn API connectivity
      try {
        console.log('Step 2: Testing LinkedIn API connectivity...');
        
        const userInfoResponse = await fetch(`https://api.linkedin.com/v2/userinfo`, {
          headers: {
            'Authorization': `Bearer ${account.access_token}`,
            'Accept': 'application/json',
          },
        });

        if (!userInfoResponse.ok) {
          const errorText = await userInfoResponse.text();
          throw new Error(`LinkedIn API connectivity failed: ${userInfoResponse.status} - ${errorText}`);
        }

        const userData = await userInfoResponse.json();
        results.steps.apiConnectivity = {
          success: true,
          userInfo: {
            name: userData.name,
            email: userData.email,
            sub: userData.sub
          }
        };

        console.log(`✅ LinkedIn API connectivity confirmed for: ${userData.name}`);

        if (dryRun) {
          results.steps.posting = {
            success: true,
            dryRun: true,
            message: 'Dry run completed - LinkedIn API connectivity confirmed, ready for posting'
          };

          return NextResponse.json({
            success: true,
            message: '✅ LinkedIn posting dry run completed successfully',
            results,
            recommendation: 'LinkedIn API is accessible and ready for live posting!'
          });
        }

        // Step 3: Create LinkedIn post
        console.log('Step 3: Creating LinkedIn post...');
        
        const postData = {
          author: `urn:li:person:${account.account_id}`,
          lifecycleState: 'PUBLISHED',
          specificContent: {
            'com.linkedin.ugc.ShareContent': {
              shareCommentary: {
                text: testContent
              },
              shareMediaCategory: 'NONE'
            }
          },
          visibility: {
            'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
          }
        };

        console.log('Posting to LinkedIn with data:', {
          author: postData.author,
          contentLength: testContent.length,
          mediaCategory: 'NONE'
        });

        const postResponse = await fetch('https://api.linkedin.com/v2/ugcPosts', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${account.access_token}`,
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0'
          },
          body: JSON.stringify(postData)
        });

        console.log('LinkedIn API response status:', postResponse.status);

        if (!postResponse.ok) {
          const errorText = await postResponse.text();
          console.error('LinkedIn API error response:', errorText);
          throw new Error(`LinkedIn posting failed: ${postResponse.status} - ${errorText}`);
        }

        const postResult = await postResponse.json();
        console.log('LinkedIn post result:', postResult);
        
        results.steps.posting = {
          success: true,
          postId: postResult.id,
          linkedinPostUrl: `https://www.linkedin.com/feed/update/${postResult.id}/`,
          apiResponse: {
            id: postResult.id,
            status: 'PUBLISHED'
          }
        };

        console.log(`🎉 LinkedIn post created successfully! Post ID: ${postResult.id}`);

        // Step 4: Log the successful post
        try {
          await supabase
            .from('activities')
            .insert({
              user_id: demoUserId,
              action: 'LINKEDIN_TEST_POST_SUCCESS',
              details: `LinkedIn test post created successfully. Post ID: ${postResult.id}`,
              created_at: new Date().toISOString(),
            });
        } catch (logError) {
          console.warn('Failed to log activity:', logError);
        }

        return NextResponse.json({
          success: true,
          message: '🎉 LinkedIn test post created successfully!',
          results,
          postDetails: {
            postId: postResult.id,
            postUrl: `https://www.linkedin.com/feed/update/${postResult.id}/`,
            account: account.account_name,
            content: testContent
          },
          instructions: {
            verification: 'Check the LinkedIn profile to verify the post appears correctly',
            profileUrl: 'https://www.linkedin.com/in/burak-ozan-profile/', // Adjust as needed
            cleanup: 'You can delete the test post from LinkedIn if desired'
          }
        });

      } catch (apiError: any) {
        results.steps.apiConnectivity = {
          success: false,
          error: apiError.message
        };
        throw apiError;
      }

    } catch (accountError: any) {
      results.steps.accountRetrieval = {
        success: false,
        error: accountError.message
      };
      throw accountError;
    }

  } catch (error: any) {
    console.error('❌ LinkedIn posting test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      results,
      details: 'LinkedIn posting test failed'
    }, { status: 500 });
  }
}

/**
 * Get test information
 * GET /api/test/linkedin-post-demo
 */
export async function GET() {
  try {
    // Get available LinkedIn accounts
    const supabase = createServiceRoleClient();
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    const { data: linkedinAccounts, error } = await supabase
      .from('social_accounts')
      .select('account_name, account_id, expires_at')
      .eq('user_id', demoUserId)
      .eq('platform', 'LINKEDIN');

    return NextResponse.json({
      message: '🧪 LinkedIn Demo Posting Test Endpoint',
      description: 'Create a live test post on LinkedIn using Demo User account',
      usage: {
        method: 'POST',
        body: {
          accountName: 'burak ozan (default)',
          dryRun: 'true/false (default: false)'
        }
      },
      availableAccounts: linkedinAccounts || [],
      testContent: '🧪 Testing eWasl Social Scheduler LinkedIn integration! 🚀\n\nThis is a live test of our LinkedIn posting functionality.\n\n#eWasl #LinkedIn #SocialMediaManagement #Test',
      safety: {
        dryRun: 'Set dryRun: true to test connectivity without creating actual posts',
        cleanup: 'Test posts can be manually deleted from LinkedIn after verification'
      }
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to get test information',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
