import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET - Fetch detailed analytics
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching detailed analytics...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('Fetching detailed analytics for user:', user.id);

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'month'; // week, month, year
    const metric = searchParams.get('metric') || 'all'; // views, engagement, posts

    // Calculate date range based on period
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default: // month
        startDate.setMonth(startDate.getMonth() - 1);
    }

    // 1. Get posts performance data
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select(`
        id,
        content,
        status,
        created_at,
        published_at
      `)
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: false });

    if (postsError) {
      console.error('Error fetching posts performance:', postsError);
    }

    // 2. Generate engagement metrics (mock data for now)
    const generateEngagementData = (days: number) => {
      const data = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        data.push({
          date: date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' }),
          views: Math.floor(Math.random() * 500) + 100,
          likes: Math.floor(Math.random() * 100) + 20,
          comments: Math.floor(Math.random() * 50) + 5,
          shares: Math.floor(Math.random() * 30) + 2,
          engagement: Math.floor(Math.random() * 20) + 60 // 60-80%
        });
      }
      return data;
    };

    const daysCount = period === 'week' ? 7 : period === 'year' ? 12 : 30;
    const engagementData = generateEngagementData(daysCount);

    // 3. Calculate top performing posts
    const topPosts = posts ? posts.slice(0, 5).map(post => ({
      id: post.id,
      content: post.content.substring(0, 50) + '...',
      status: post.status,
      created_at: post.created_at,
      views: Math.floor(Math.random() * 1000) + 100,
      engagement: Math.floor(Math.random() * 30) + 50,
      likes: Math.floor(Math.random() * 100) + 10,
      comments: Math.floor(Math.random() * 50) + 2,
      shares: Math.floor(Math.random() * 20) + 1
    })) : [];

    // 4. Platform performance (mock data)
    const platformPerformance = [
      { platform: 'TWITTER', posts: Math.floor(Math.random() * 20) + 5, engagement: Math.floor(Math.random() * 20) + 70 },
      { platform: 'FACEBOOK', posts: Math.floor(Math.random() * 15) + 3, engagement: Math.floor(Math.random() * 15) + 60 },
      { platform: 'INSTAGRAM', posts: Math.floor(Math.random() * 10) + 2, engagement: Math.floor(Math.random() * 25) + 65 },
      { platform: 'LINKEDIN', posts: Math.floor(Math.random() * 8) + 1, engagement: Math.floor(Math.random() * 10) + 55 }
    ];

    // 5. Audience insights (mock data)
    const audienceInsights = {
      demographics: {
        age: [
          { range: '18-24', percentage: 25 },
          { range: '25-34', percentage: 35 },
          { range: '35-44', percentage: 25 },
          { range: '45+', percentage: 15 }
        ],
        gender: [
          { type: 'ذكر', percentage: 55 },
          { type: 'أنثى', percentage: 45 }
        ],
        location: [
          { country: 'السعودية', percentage: 40 },
          { country: 'الإمارات', percentage: 25 },
          { country: 'مصر', percentage: 20 },
          { country: 'أخرى', percentage: 15 }
        ]
      },
      activeHours: [
        { hour: '6:00', activity: 20 },
        { hour: '9:00', activity: 45 },
        { hour: '12:00', activity: 80 },
        { hour: '15:00', activity: 65 },
        { hour: '18:00', activity: 90 },
        { hour: '21:00', activity: 75 },
        { hour: '24:00', activity: 30 }
      ]
    };

    // 6. Growth metrics
    const currentPeriodPosts = posts ? posts.length : 0;
    const previousPeriodPosts = Math.floor(currentPeriodPosts * 0.85); // Mock previous period
    const growthRate = previousPeriodPosts > 0 ? 
      Math.round(((currentPeriodPosts - previousPeriodPosts) / previousPeriodPosts) * 100) : 0;

    console.log(`Detailed analytics fetched successfully for user ${user.id}`);

    return NextResponse.json({
      success: true,
      data: {
        period,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        overview: {
          totalPosts: currentPeriodPosts,
          totalViews: engagementData.reduce((sum, day) => sum + day.views, 0),
          totalEngagement: engagementData.reduce((sum, day) => sum + day.likes + day.comments + day.shares, 0),
          averageEngagementRate: Math.round(engagementData.reduce((sum, day) => sum + day.engagement, 0) / engagementData.length),
          growthRate: `${growthRate > 0 ? '+' : ''}${growthRate}%`
        },
        engagementData,
        topPosts,
        platformPerformance,
        audienceInsights,
        trends: {
          bestPerformingDay: engagementData.reduce((best, day) => 
            day.views > best.views ? day : best, engagementData[0]),
          mostEngagedContent: topPosts[0] || null,
          peakEngagementTime: '18:00'
        }
      }
    });

  } catch (error) {
    console.error('Detailed analytics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
