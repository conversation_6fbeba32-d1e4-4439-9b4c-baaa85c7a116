# 🚀 **SOCIAL MEDIA INTEGRATIONS - IMPLEMENTATION STARTER GUIDE**

## **🎯 IMMEDIATE ACTION ITEMS**

### **CRITICAL: Instagram API Migration (3 Days)**

The Instagram Basic Display API is deprecated as of April 2024. This is the highest priority fix.

#### **Step 1: Update Instagram OAuth Configuration**

```typescript
// src/lib/social/oauth-config.ts - UPDATE IMMEDIATELY
export const INSTAGRAM_CONFIG = {
  // OLD (DEPRECATED)
  // authUrl: 'https://api.instagram.com/oauth/authorize',
  // tokenUrl: 'https://api.instagram.com/oauth/access_token',
  
  // NEW (Instagram Graph API via Facebook)
  authUrl: 'https://www.facebook.com/v19.0/dialog/oauth',
  tokenUrl: 'https://graph.facebook.com/v19.0/oauth/access_token',
  scope: [
    'instagram_graph_user_profile',
    'instagram_graph_user_media',
    'pages_show_list',
    'pages_read_engagement'
  ],
  apiVersion: 'v19.0'
};
```

#### **Step 2: Create Instagram Graph API Service**

```typescript
// src/lib/social/services/instagram-graph-service.ts - CREATE NEW
export class InstagramGraphService {
  private readonly baseUrl = 'https://graph.facebook.com/v19.0';

  async getBusinessAccounts(accessToken: string): Promise<InstagramAccount[]> {
    // Get Facebook pages first
    const pagesResponse = await fetch(
      `${this.baseUrl}/me/accounts?access_token=${accessToken}`
    );
    const pages = await pagesResponse.json();

    // Get Instagram business accounts for each page
    const instagramAccounts = [];
    for (const page of pages.data) {
      const igResponse = await fetch(
        `${this.baseUrl}/${page.id}?fields=instagram_business_account&access_token=${accessToken}`
      );
      const igData = await igResponse.json();
      
      if (igData.instagram_business_account) {
        instagramAccounts.push({
          id: igData.instagram_business_account.id,
          pageId: page.id,
          pageName: page.name,
          accessToken: page.access_token
        });
      }
    }

    return instagramAccounts;
  }

  async publishPost(accountId: string, accessToken: string, content: PostContent): Promise<PublishResult> {
    // Step 1: Create media container
    const containerResponse = await fetch(
      `${this.baseUrl}/${accountId}/media`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          image_url: content.mediaUrl,
          caption: content.content,
          access_token: accessToken
        })
      }
    );

    const container = await containerResponse.json();

    // Step 2: Publish the container
    const publishResponse = await fetch(
      `${this.baseUrl}/${accountId}/media_publish`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          creation_id: container.id,
          access_token: accessToken
        })
      }
    );

    const result = await publishResponse.json();
    return {
      success: true,
      postId: result.id,
      url: `https://www.instagram.com/p/${result.id}/`
    };
  }
}
```

### **HIGH PRIORITY: Twitter OAuth 2.0 Migration (5 Days)**

#### **Step 3: Implement Twitter OAuth 2.0 with PKCE**

```typescript
// src/lib/social/services/twitter-oauth-service.ts - CREATE NEW
export class TwitterOAuthService {
  private readonly baseUrl = 'https://api.twitter.com/2';
  
  generatePKCE(): { codeVerifier: string; codeChallenge: string } {
    const codeVerifier = this.generateRandomString(128);
    const codeChallenge = this.base64URLEncode(
      crypto.createHash('sha256').update(codeVerifier).digest()
    );
    
    return { codeVerifier, codeChallenge };
  }

  getAuthorizationUrl(clientId: string, redirectUri: string, state: string): string {
    const { codeChallenge } = this.generatePKCE();
    
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: 'tweet.read tweet.write users.read media.upload offline.access',
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256'
    });

    return `https://twitter.com/i/oauth2/authorize?${params.toString()}`;
  }

  async exchangeCodeForToken(
    code: string, 
    codeVerifier: string, 
    clientId: string, 
    redirectUri: string
  ): Promise<TokenResponse> {
    const response = await fetch('https://api.twitter.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${clientId}:${process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier
      })
    });

    return await response.json();
  }

  async refreshToken(refreshToken: string, clientId: string): Promise<TokenResponse> {
    const response = await fetch('https://api.twitter.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${clientId}:${process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken
      })
    });

    return await response.json();
  }
}
```

### **IMMEDIATE: Replace Mock Publishers (7 Days)**

#### **Step 4: Implement Real LinkedIn Publisher**

```typescript
// src/lib/social/publishers/linkedin-publisher-v2.ts - REPLACE EXISTING
export class LinkedInPublisherV2 {
  private readonly apiUrl = 'https://api.linkedin.com/v2';

  async publishPost(account: SocialAccount, content: PostContent): Promise<PublishResult> {
    try {
      // Validate token first
      await this.validateToken(account.access_token);

      let shareData: any = {
        author: `urn:li:person:${account.account_id}`,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content.content
            },
            shareMediaCategory: content.mediaUrl ? 'IMAGE' : 'NONE'
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
        }
      };

      // Handle media upload if present
      if (content.mediaUrl) {
        const mediaUrn = await this.uploadMedia(account, content.mediaUrl);
        shareData.specificContent['com.linkedin.ugc.ShareContent'].media = [{
          status: 'READY',
          description: { text: content.content },
          media: mediaUrn,
          title: { text: 'Shared via eWasl' }
        }];
      }

      const response = await fetch(`${this.apiUrl}/ugcPosts`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
          'Content-Type': 'application/json',
          'X-Restli-Protocol-Version': '2.0.0'
        },
        body: JSON.stringify(shareData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`LinkedIn API error: ${error.message}`);
      }

      const result = await response.json();
      const postId = result.id.split(':').pop();
      
      return {
        success: true,
        postId,
        url: `https://www.linkedin.com/feed/update/${result.id}`,
        platformResponse: result
      };

    } catch (error) {
      console.error('LinkedIn publishing failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  private async validateToken(accessToken: string): Promise<void> {
    const response = await fetch(`${this.apiUrl}/people/(id:me)`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!response.ok) {
      throw new Error('LinkedIn token is invalid or expired');
    }
  }

  private async uploadMedia(account: SocialAccount, mediaUrl: string): Promise<string> {
    // Step 1: Register upload
    const registerResponse = await fetch(`${this.apiUrl}/assets?action=registerUpload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${account.access_token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        registerUploadRequest: {
          recipes: ['urn:li:digitalmedia:uploading:MediaUploadHttpRequest'],
          owner: `urn:li:person:${account.account_id}`,
          serviceRelationships: [{
            relationshipType: 'OWNER',
            identifier: 'urn:li:userGeneratedContent'
          }]
        }
      })
    });

    const registerData = await registerResponse.json();
    const uploadUrl = registerData.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'].uploadUrl;
    const asset = registerData.value.asset;

    // Step 2: Download and upload media
    const mediaResponse = await fetch(mediaUrl);
    const mediaBuffer = await mediaResponse.arrayBuffer();

    await fetch(uploadUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${account.access_token}`
      },
      body: mediaBuffer
    });

    return asset;
  }
}
```

## **📋 IMPLEMENTATION CHECKLIST**

### **Week 1: Critical Fixes**
- [ ] ✅ Update Instagram OAuth to Graph API
- [ ] ✅ Test Instagram business account detection
- [ ] ✅ Migrate Twitter to OAuth 2.0 with PKCE
- [ ] ✅ Update Twitter API endpoints to v2
- [ ] ✅ Test token refresh mechanisms

### **Week 2: Real Publishing**
- [ ] ✅ Replace LinkedIn mock publisher with real API calls
- [ ] ✅ Implement Facebook Graph API publishing
- [ ] ✅ Complete Instagram Graph API publishing
- [ ] ✅ Implement Twitter API v2 posting
- [ ] ✅ Add comprehensive error handling

### **Week 3: Media Upload**
- [ ] ✅ Build media validation service
- [ ] ✅ Implement platform-specific media optimization
- [ ] ✅ Add upload progress tracking
- [ ] ✅ Create media storage and CDN integration
- [ ] ✅ Test with various media formats

### **Week 4: Testing & Integration**
- [ ] ✅ End-to-end testing for all platforms
- [ ] ✅ Integration with existing scheduler
- [ ] ✅ Performance optimization
- [ ] ✅ Security audit
- [ ] ✅ Documentation updates

## **🚨 BREAKING CHANGES TO HANDLE**

### **Environment Variables Updates**
```bash
# Add these new environment variables
INSTAGRAM_GRAPH_API_VERSION=v19.0
TWITTER_API_VERSION=v2
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret

# Update existing variables
FACEBOOK_API_VERSION=v19.0
LINKEDIN_API_VERSION=v2
```

### **Database Schema Updates**
```sql
-- Add new columns for enhanced token management
ALTER TABLE social_accounts 
ADD COLUMN api_version TEXT DEFAULT 'v1',
ADD COLUMN business_account_id TEXT,
ADD COLUMN page_access_token TEXT,
ADD COLUMN token_scopes TEXT[],
ADD COLUMN last_token_refresh TIMESTAMPTZ;

-- Create index for faster lookups
CREATE INDEX idx_social_accounts_business_id ON social_accounts(business_account_id);
```

## **🔧 QUICK START COMMANDS**

```bash
# 1. Update dependencies
npm install @types/crypto-js crypto-js

# 2. Create new service directories
mkdir -p src/lib/social/services
mkdir -p src/lib/social/publishers/v2

# 3. Run database migrations
npm run db:migrate

# 4. Test OAuth flows
npm run test:oauth

# 5. Deploy to staging
npm run deploy:staging
```

## **📞 SUPPORT & RESOURCES**

- **LinkedIn API Documentation**: https://docs.microsoft.com/en-us/linkedin/
- **Facebook Graph API**: https://developers.facebook.com/docs/graph-api/
- **Instagram Graph API**: https://developers.facebook.com/docs/instagram-api/
- **Twitter API v2**: https://developer.twitter.com/en/docs/twitter-api

---

**🎯 Focus on the critical Instagram migration first, then Twitter OAuth 2.0, then real publishing implementation. This order ensures maximum stability and user impact.**
