import { NextRequest, NextResponse } from 'next/server';

// Simple in-memory rate limiting (for production, use Redis)
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string; // Custom error message
}

export const rateLimitConfigs = {
  // General API endpoints
  default: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
    message: 'Too many requests, please try again later.'
  },

  // Authentication endpoints (stricter)
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 login attempts per 15 minutes
    message: 'Too many authentication attempts, please try again later.'
  },

  // Payment endpoints (very strict)
  payment: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10, // 10 payment requests per hour
    message: 'Too many payment requests, please try again later.'
  },

  // Social media operations
  social: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 20, // 20 social operations per 5 minutes
    message: 'Too many social media operations, please try again later.'
  },

  // Post publishing (strict)
  publish: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 posts per minute
    message: 'Too many posts published, please wait before publishing again.'
  }
} as const;

export function createRateLimit(config: RateLimitConfig) {
  return function rateLimit(request: NextRequest): NextResponse | null {
    // Get client identifier (IP address)
    const clientId = getClientId(request);

    // Clean up expired entries
    cleanupExpiredEntries();

    const now = Date.now();
    const entry = rateLimitStore.get(clientId);

    if (!entry || now > entry.resetTime) {
      // First request or window expired, create new entry
      rateLimitStore.set(clientId, {
        count: 1,
        resetTime: now + config.windowMs
      });
      return null; // Allow request
    }

    if (entry.count >= config.maxRequests) {
      // Rate limit exceeded
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);

      return NextResponse.json(
        {
          error: config.message || 'Rate limit exceeded',
          retryAfter: retryAfter
        },
        {
          status: 429,
          headers: {
            'Retry-After': retryAfter.toString(),
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': entry.resetTime.toString()
          }
        }
      );
    }

    // Increment counter
    entry.count++;
    rateLimitStore.set(clientId, entry);

    return null; // Allow request
  };
}

function getClientId(request: NextRequest): string {
  // Try to get real IP from headers (for production behind proxy)
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');

  // Use the first available IP
  const ip = forwardedFor?.split(',')[0]?.trim() ||
            realIp ||
            cfConnectingIp ||
            'unknown';

  return ip;
}

function cleanupExpiredEntries(): void {
  const now = Date.now();

  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Middleware wrapper for API routes
export function withRateLimit(
  handler: (request: NextRequest) => Promise<NextResponse>,
  configKey: keyof typeof rateLimitConfigs = 'default'
) {
  const rateLimit = createRateLimit(rateLimitConfigs[configKey]);

  return async function(request: NextRequest): Promise<NextResponse> {
    // Check rate limit
    const rateLimitResponse = rateLimit(request);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    // Continue with original handler
    return await handler(request);
  };
}

// Helper to add rate limit headers to successful responses
export function addRateLimitHeaders(
  response: NextResponse,
  request: NextRequest,
  configKey: keyof typeof rateLimitConfigs = 'default'
): NextResponse {
  const config = rateLimitConfigs[configKey];
  const clientId = getClientId(request);
  const entry = rateLimitStore.get(clientId);

  if (entry) {
    const remaining = Math.max(0, config.maxRequests - entry.count);

    response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', remaining.toString());
    response.headers.set('X-RateLimit-Reset', entry.resetTime.toString());
  }

  return response;
}
