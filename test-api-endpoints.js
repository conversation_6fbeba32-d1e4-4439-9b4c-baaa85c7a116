const axios = require('axios');

const BASE_URL = 'https://app.ewasl.com';

const apiTests = [
  {
    name: 'Health Check',
    method: 'GET',
    endpoint: '/api/health',
    expectedStatus: 200,
    expectedResponse: { status: 'healthy' }
  },
  {
    name: 'NextAuth Providers',
    method: 'GET',
    endpoint: '/api/auth/providers',
    expectedStatus: 200,
    expectedResponse: { credentials: {} }
  },
  {
    name: 'NextAuth Session (Unauthenticated)',
    method: 'GET',
    endpoint: '/api/auth/session',
    expectedStatus: 200,
    expectedResponse: {}
  },
  {
    name: 'NextAuth CSRF Token',
    method: 'GET',
    endpoint: '/api/auth/csrf',
    expectedStatus: 200,
    expectedResponse: { csrfToken: 'string' }
  },
  {
    name: 'Posts API (Should require auth)',
    method: 'GET',
    endpoint: '/api/posts',
    expectedStatus: [401, 403, 200], // Allow multiple valid responses
    expectedResponse: null // Don't check response content
  }
];

async function runApiTests() {
  console.log('🧪 Starting API Endpoint Tests\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const test of apiTests) {
    try {
      console.log(`Testing: ${test.name}`);
      
      const response = await axios({
        method: test.method,
        url: `${BASE_URL}${test.endpoint}`,
        timeout: 10000,
        validateStatus: () => true // Don't throw on non-2xx status
      });
      
      const expectedStatuses = Array.isArray(test.expectedStatus) 
        ? test.expectedStatus 
        : [test.expectedStatus];
      
      if (expectedStatuses.includes(response.status)) {
        console.log(`✅ ${test.name}: Status ${response.status} ✓`);
        console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
        passed++;
      } else {
        console.log(`❌ ${test.name}: Expected ${test.expectedStatus}, got ${response.status}`);
        console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
        failed++;
      }
      
    } catch (error) {
      console.log(`❌ ${test.name}: Error - ${error.message}`);
      failed++;
    }
    
    console.log(''); // Empty line for readability
  }
  
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
  return { passed, failed };
}

// Run tests if called directly
if (require.main === module) {
  runApiTests();
}

module.exports = { runApiTests, apiTests };
