/**
 * AI-Powered Content Analysis Engine
 * Analyzes image and video content to determine optimal compression settings
 * Uses intelligent algorithms for quality detection and format selection
 */

import sharp from 'sharp';

export interface ContentAnalysisResult {
  contentType: 'photo' | 'graphics' | 'text' | 'mixed' | 'animation';
  complexity: 'low' | 'medium' | 'high';
  hasTransparency: boolean;
  isAnimated: boolean;
  colorProfile: 'grayscale' | 'limited' | 'full';
  textDensity: 'none' | 'low' | 'medium' | 'high';
  noiseLevel: 'low' | 'medium' | 'high';
  edgeComplexity: 'simple' | 'moderate' | 'complex';
  recommendedFormat: 'jpeg' | 'png' | 'webp' | 'avif';
  recommendedQuality: number;
  compressionStrategy: 'aggressive' | 'balanced' | 'conservative';
  confidence: number; // 0-1
}

export interface VideoContentAnalysis {
  motionComplexity: 'low' | 'medium' | 'high';
  sceneChanges: 'few' | 'moderate' | 'frequent';
  colorVariation: 'limited' | 'moderate' | 'high';
  hasText: boolean;
  hasSubtitles: boolean;
  audioComplexity: 'simple' | 'moderate' | 'complex';
  recommendedBitrate: number;
  recommendedCodec: 'h264' | 'h265' | 'vp9' | 'av1';
  compressionProfile: 'fast' | 'balanced' | 'quality';
  confidence: number;
}

export interface OptimizationRecommendation {
  format: string;
  quality: number;
  width?: number;
  height?: number;
  progressive?: boolean;
  lossless?: boolean;
  effort?: number;
  expectedSizeReduction: number; // percentage
  qualityScore: number; // 0-100
  reasoning: string[];
}

export class ContentAnalyzer {
  /**
   * Analyze image content to determine optimal compression settings
   */
  async analyzeImage(buffer: Buffer): Promise<ContentAnalysisResult> {
    try {
      console.log('Analyzing image content for optimal compression...');

      const image = sharp(buffer);
      const metadata = await image.metadata();
      const stats = await image.stats();

      // Extract image data for analysis
      const { data, info } = await image
        .raw()
        .toBuffer({ resolveWithObject: true });

      // Perform content analysis
      const contentType = this.detectContentType(data, info, stats);
      const complexity = this.analyzeComplexity(data, info);
      const colorProfile = this.analyzeColorProfile(stats);
      const textDensity = this.detectTextDensity(data, info);
      const noiseLevel = this.analyzeNoiseLevel(data, info);
      const edgeComplexity = this.analyzeEdgeComplexity(data, info);

      // Generate recommendations
      const recommendation = this.generateImageRecommendation({
        contentType,
        complexity,
        hasTransparency: metadata.hasAlpha || false,
        isAnimated: (metadata.pages || 1) > 1,
        colorProfile,
        textDensity,
        noiseLevel,
        edgeComplexity,
        metadata
      });

      const result: ContentAnalysisResult = {
        contentType,
        complexity,
        hasTransparency: metadata.hasAlpha || false,
        isAnimated: (metadata.pages || 1) > 1,
        colorProfile,
        textDensity,
        noiseLevel,
        edgeComplexity,
        recommendedFormat: recommendation.format as any,
        recommendedQuality: recommendation.quality,
        compressionStrategy: this.determineCompressionStrategy(complexity, contentType),
        confidence: this.calculateConfidence(contentType, complexity, colorProfile)
      };

      console.log('Image content analysis completed:', {
        contentType: result.contentType,
        complexity: result.complexity,
        recommendedFormat: result.recommendedFormat,
        recommendedQuality: result.recommendedQuality,
        confidence: Math.round(result.confidence * 100) + '%'
      });

      return result;

    } catch (error) {
      console.error('Image content analysis error:', error);
      // Return fallback analysis
      return this.getFallbackImageAnalysis();
    }
  }

  /**
   * Analyze video content for optimal encoding settings
   */
  async analyzeVideo(buffer: Buffer): Promise<VideoContentAnalysis> {
    try {
      console.log('Analyzing video content for optimal encoding...');

      // For video analysis, we would typically use FFmpeg to extract frames
      // and analyze motion vectors, scene changes, etc.
      // For now, we'll implement basic analysis based on file characteristics

      const fileSize = buffer.length;
      const estimatedBitrate = this.estimateBitrate(fileSize);
      
      // Simulate content analysis (in production, this would analyze actual video frames)
      const motionComplexity = this.estimateMotionComplexity(fileSize);
      const sceneChanges = this.estimateSceneChanges(fileSize);
      const colorVariation = this.estimateColorVariation(fileSize);

      const recommendation = this.generateVideoRecommendation({
        motionComplexity,
        sceneChanges,
        colorVariation,
        fileSize
      });

      const result: VideoContentAnalysis = {
        motionComplexity,
        sceneChanges,
        colorVariation,
        hasText: false, // Would be detected through OCR in production
        hasSubtitles: false, // Would be detected through subtitle track analysis
        audioComplexity: 'moderate', // Would be analyzed through audio analysis
        recommendedBitrate: recommendation.bitrate,
        recommendedCodec: recommendation.codec,
        compressionProfile: recommendation.profile,
        confidence: 0.7 // Lower confidence for simulated analysis
      };

      console.log('Video content analysis completed:', {
        motionComplexity: result.motionComplexity,
        recommendedBitrate: result.recommendedBitrate,
        recommendedCodec: result.recommendedCodec,
        confidence: Math.round(result.confidence * 100) + '%'
      });

      return result;

    } catch (error) {
      console.error('Video content analysis error:', error);
      return this.getFallbackVideoAnalysis();
    }
  }

  /**
   * Generate comprehensive optimization recommendation
   */
  async generateOptimizationRecommendation(
    buffer: Buffer,
    isVideo: boolean = false,
    targetPlatform?: string
  ): Promise<OptimizationRecommendation> {
    try {
      if (isVideo) {
        const analysis = await this.analyzeVideo(buffer);
        return this.createVideoOptimizationRecommendation(analysis, targetPlatform);
      } else {
        const analysis = await this.analyzeImage(buffer);
        return this.createImageOptimizationRecommendation(analysis, targetPlatform);
      }
    } catch (error) {
      console.error('Optimization recommendation error:', error);
      return this.getFallbackRecommendation(isVideo);
    }
  }

  /**
   * Detect content type (photo, graphics, text, etc.)
   */
  private detectContentType(
    data: Buffer, 
    info: sharp.OutputInfo, 
    stats: sharp.Stats
  ): 'photo' | 'graphics' | 'text' | 'mixed' | 'animation' {
    const { channels } = info;
    const channelStats = stats.channels;

    // Analyze color distribution
    const colorVariance = channelStats.reduce((sum, channel) => sum + channel.stdev, 0) / channels;
    const colorRange = channelStats.reduce((sum, channel) => sum + (channel.max - channel.min), 0) / channels;

    // High variance and range typically indicate photos
    if (colorVariance > 50 && colorRange > 200) {
      return 'photo';
    }

    // Low variance with high range might indicate graphics
    if (colorVariance < 30 && colorRange > 150) {
      return 'graphics';
    }

    // Very low variance typically indicates text or simple graphics
    if (colorVariance < 20) {
      return 'text';
    }

    return 'mixed';
  }

  /**
   * Analyze image complexity
   */
  private analyzeComplexity(data: Buffer, info: sharp.OutputInfo): 'low' | 'medium' | 'high' {
    const { width, height, channels } = info;
    const pixelCount = width * height;

    // Simple heuristic based on image size and data variance
    if (pixelCount < 100000) { // < 100K pixels
      return 'low';
    } else if (pixelCount < 1000000) { // < 1M pixels
      return 'medium';
    } else {
      return 'high';
    }
  }

  /**
   * Analyze color profile
   */
  private analyzeColorProfile(stats: sharp.Stats): 'grayscale' | 'limited' | 'full' {
    const channelStats = stats.channels;
    
    if (channelStats.length === 1) {
      return 'grayscale';
    }

    // Check if colors are similar across channels (indicating limited palette)
    const colorSimilarity = this.calculateColorSimilarity(channelStats);
    
    if (colorSimilarity > 0.8) {
      return 'limited';
    }

    return 'full';
  }

  /**
   * Calculate color similarity across channels
   */
  private calculateColorSimilarity(channelStats: sharp.ChannelStats[]): number {
    if (channelStats.length < 2) return 1;

    const means = channelStats.map(ch => ch.mean);
    const maxMean = Math.max(...means);
    const minMean = Math.min(...means);
    
    return 1 - (maxMean - minMean) / 255;
  }

  /**
   * Detect text density (simplified)
   */
  private detectTextDensity(data: Buffer, info: sharp.OutputInfo): 'none' | 'low' | 'medium' | 'high' {
    // In production, this would use OCR or edge detection algorithms
    // For now, we'll use a simple heuristic based on data patterns
    const { width, height } = info;
    const sampleSize = Math.min(1000, data.length);
    const sample = data.slice(0, sampleSize);
    
    // Count sharp transitions (potential text edges)
    let transitions = 0;
    for (let i = 1; i < sample.length; i++) {
      if (Math.abs(sample[i] - sample[i - 1]) > 50) {
        transitions++;
      }
    }
    
    const transitionRatio = transitions / sampleSize;
    
    if (transitionRatio > 0.3) return 'high';
    if (transitionRatio > 0.15) return 'medium';
    if (transitionRatio > 0.05) return 'low';
    return 'none';
  }

  /**
   * Analyze noise level
   */
  private analyzeNoiseLevel(data: Buffer, info: sharp.OutputInfo): 'low' | 'medium' | 'high' {
    // Simplified noise detection based on local variance
    const sampleSize = Math.min(1000, data.length);
    const sample = data.slice(0, sampleSize);
    
    let variance = 0;
    const mean = sample.reduce((sum, val) => sum + val, 0) / sample.length;
    
    for (let i = 0; i < sample.length; i++) {
      variance += Math.pow(sample[i] - mean, 2);
    }
    variance /= sample.length;
    
    if (variance > 2000) return 'high';
    if (variance > 1000) return 'medium';
    return 'low';
  }

  /**
   * Analyze edge complexity
   */
  private analyzeEdgeComplexity(data: Buffer, info: sharp.OutputInfo): 'simple' | 'moderate' | 'complex' {
    // Simplified edge detection
    const sampleSize = Math.min(1000, data.length);
    const sample = data.slice(0, sampleSize);
    
    let edgeCount = 0;
    for (let i = 2; i < sample.length - 2; i++) {
      const gradient = Math.abs(sample[i + 1] - sample[i - 1]);
      if (gradient > 30) edgeCount++;
    }
    
    const edgeRatio = edgeCount / sampleSize;
    
    if (edgeRatio > 0.2) return 'complex';
    if (edgeRatio > 0.1) return 'moderate';
    return 'simple';
  }

  /**
   * Generate image optimization recommendation
   */
  private generateImageRecommendation(analysis: any): { format: string; quality: number } {
    const { contentType, hasTransparency, complexity, colorProfile } = analysis;

    // Format selection logic
    let format = 'jpeg';
    let quality = 85;

    if (hasTransparency) {
      format = 'webp'; // WebP handles transparency well
      quality = 80;
    } else if (contentType === 'graphics' || contentType === 'text') {
      format = 'png'; // PNG is better for graphics and text
      quality = 90;
    } else if (contentType === 'photo') {
      format = 'webp'; // WebP provides better compression for photos
      quality = 85;
    }

    // Adjust quality based on complexity
    if (complexity === 'high') {
      quality = Math.min(quality + 5, 95);
    } else if (complexity === 'low') {
      quality = Math.max(quality - 10, 70);
    }

    // Adjust for color profile
    if (colorProfile === 'limited') {
      quality = Math.max(quality - 5, 70);
    }

    return { format, quality };
  }

  /**
   * Determine compression strategy
   */
  private determineCompressionStrategy(
    complexity: string, 
    contentType: string
  ): 'aggressive' | 'balanced' | 'conservative' {
    if (contentType === 'text' || complexity === 'low') {
      return 'aggressive';
    } else if (contentType === 'photo' && complexity === 'high') {
      return 'conservative';
    }
    return 'balanced';
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(
    contentType: string, 
    complexity: string, 
    colorProfile: string
  ): number {
    let confidence = 0.8; // Base confidence

    // Adjust based on content type certainty
    if (contentType === 'photo' || contentType === 'text') {
      confidence += 0.1;
    }

    // Adjust based on complexity analysis
    if (complexity === 'low' || complexity === 'high') {
      confidence += 0.05;
    }

    return Math.min(confidence, 1.0);
  }

  // Video analysis helper methods
  private estimateBitrate(fileSize: number): number {
    // Rough estimation based on file size
    return Math.max(1000, Math.min(8000, fileSize / 1000));
  }

  private estimateMotionComplexity(fileSize: number): 'low' | 'medium' | 'high' {
    if (fileSize < 5 * 1024 * 1024) return 'low';
    if (fileSize < 50 * 1024 * 1024) return 'medium';
    return 'high';
  }

  private estimateSceneChanges(fileSize: number): 'few' | 'moderate' | 'frequent' {
    if (fileSize < 10 * 1024 * 1024) return 'few';
    if (fileSize < 100 * 1024 * 1024) return 'moderate';
    return 'frequent';
  }

  private estimateColorVariation(fileSize: number): 'limited' | 'moderate' | 'high' {
    if (fileSize < 20 * 1024 * 1024) return 'limited';
    if (fileSize < 100 * 1024 * 1024) return 'moderate';
    return 'high';
  }

  private generateVideoRecommendation(analysis: any): { bitrate: number; codec: string; profile: string } {
    const { motionComplexity, sceneChanges, fileSize } = analysis;

    let bitrate = 3000; // Default 3Mbps
    let codec = 'h264';
    let profile = 'balanced';

    // Adjust bitrate based on complexity
    if (motionComplexity === 'high' || sceneChanges === 'frequent') {
      bitrate = 5000;
      profile = 'quality';
    } else if (motionComplexity === 'low' && sceneChanges === 'few') {
      bitrate = 1500;
      profile = 'fast';
    }

    // Choose codec based on requirements
    if (fileSize > 100 * 1024 * 1024) {
      codec = 'h265'; // Better compression for large files
    }

    return { bitrate, codec, profile };
  }

  // Fallback methods
  private getFallbackImageAnalysis(): ContentAnalysisResult {
    return {
      contentType: 'mixed',
      complexity: 'medium',
      hasTransparency: false,
      isAnimated: false,
      colorProfile: 'full',
      textDensity: 'low',
      noiseLevel: 'medium',
      edgeComplexity: 'moderate',
      recommendedFormat: 'webp',
      recommendedQuality: 80,
      compressionStrategy: 'balanced',
      confidence: 0.5
    };
  }

  private getFallbackVideoAnalysis(): VideoContentAnalysis {
    return {
      motionComplexity: 'medium',
      sceneChanges: 'moderate',
      colorVariation: 'moderate',
      hasText: false,
      hasSubtitles: false,
      audioComplexity: 'moderate',
      recommendedBitrate: 3000,
      recommendedCodec: 'h264',
      compressionProfile: 'balanced',
      confidence: 0.5
    };
  }

  private createImageOptimizationRecommendation(
    analysis: ContentAnalysisResult, 
    targetPlatform?: string
  ): OptimizationRecommendation {
    return {
      format: analysis.recommendedFormat,
      quality: analysis.recommendedQuality,
      progressive: analysis.contentType === 'photo',
      lossless: analysis.contentType === 'text',
      effort: analysis.complexity === 'high' ? 6 : 4,
      expectedSizeReduction: this.estimateSizeReduction(analysis),
      qualityScore: this.calculateQualityScore(analysis),
      reasoning: this.generateReasoningForImage(analysis)
    };
  }

  private createVideoOptimizationRecommendation(
    analysis: VideoContentAnalysis, 
    targetPlatform?: string
  ): OptimizationRecommendation {
    return {
      format: 'mp4',
      quality: 80,
      expectedSizeReduction: 30,
      qualityScore: 85,
      reasoning: ['Video optimized for streaming', 'Balanced quality and file size']
    };
  }

  private getFallbackRecommendation(isVideo: boolean): OptimizationRecommendation {
    return {
      format: isVideo ? 'mp4' : 'webp',
      quality: 80,
      expectedSizeReduction: 25,
      qualityScore: 80,
      reasoning: ['Fallback optimization applied']
    };
  }

  private estimateSizeReduction(analysis: ContentAnalysisResult): number {
    let reduction = 30; // Base 30% reduction

    if (analysis.compressionStrategy === 'aggressive') {
      reduction += 20;
    } else if (analysis.compressionStrategy === 'conservative') {
      reduction -= 10;
    }

    if (analysis.recommendedFormat === 'webp') {
      reduction += 15;
    }

    return Math.min(reduction, 70); // Cap at 70%
  }

  private calculateQualityScore(analysis: ContentAnalysisResult): number {
    let score = analysis.recommendedQuality;

    if (analysis.confidence > 0.8) {
      score += 5;
    }

    return Math.min(score, 100);
  }

  private generateReasoningForImage(analysis: ContentAnalysisResult): string[] {
    const reasons: string[] = [];

    reasons.push(`Content type: ${analysis.contentType}`);
    reasons.push(`Complexity: ${analysis.complexity}`);
    reasons.push(`Recommended format: ${analysis.recommendedFormat}`);
    reasons.push(`Compression strategy: ${analysis.compressionStrategy}`);

    if (analysis.hasTransparency) {
      reasons.push('Transparency detected - using format that supports alpha channel');
    }

    if (analysis.textDensity !== 'none') {
      reasons.push('Text content detected - optimizing for readability');
    }

    return reasons;
  }
}

export default ContentAnalyzer;
