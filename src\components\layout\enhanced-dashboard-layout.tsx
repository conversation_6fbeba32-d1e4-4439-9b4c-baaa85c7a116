"use client";

import React, { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import EnhancedSidebar from "./enhanced-sidebar";
import EnhancedHeader from "./enhanced-header";

interface EnhancedDashboardLayoutProps {
  children: React.ReactNode;
}

const EnhancedDashboardLayout = ({ children }: EnhancedDashboardLayoutProps) => {
  const pathname = usePathname();
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Page titles mapping
  const pageTitle = {
    '/dashboard': language === 'ar' ? 'لوحة التحكم' : 'Dashboard',
    '/posts': language === 'ar' ? 'إدارة المنشورات' : 'Posts Management',
    '/posts/new': language === 'ar' ? 'إنشاء منشور جديد' : 'Create New Post',
    '/schedule': language === 'ar' ? 'جدولة المنشورات' : 'Schedule',
    '/social': language === 'ar' ? 'الحسابات الاجتماعية' : 'Social Accounts',
    '/analytics': language === 'ar' ? 'التحليلات' : 'Analytics',
    '/dashboard/billing': language === 'ar' ? 'الفوترة' : 'Billing',
    '/templates': language === 'ar' ? 'مكتبة القوالب' : 'Templates Library',
    '/settings': language === 'ar' ? 'الإعدادات' : 'Settings',
    '/api-testing': language === 'ar' ? 'اختبار APIs' : 'API Testing'
  };

  const getCurrentPageTitle = () => {
    return pageTitle[pathname as keyof typeof pageTitle] || (language === 'ar' ? 'لوحة التحكم' : 'Dashboard');
  };

  const handleLanguageChange = (lang: 'ar' | 'en') => {
    setLanguage(lang);
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;
  };

  // Set initial direction and language
  useEffect(() => {
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
  }, [language]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 w-full" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="flex w-full min-h-screen">
        {/* Desktop Sidebar - Now on the RIGHT side for RTL */}
        <div className="hidden lg:block order-last">
          <EnhancedSidebar 
            isCollapsed={sidebarCollapsed}
            onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
            language={language}
            onLanguageChange={handleLanguageChange}
          />
        </div>

        {/* Main content area - Now takes remaining space on the LEFT for RTL */}
        <div className={cn(
          "flex-1 flex flex-col overflow-hidden",
          language === 'ar' && !sidebarCollapsed ? "lg:mr-80" : "",
          language === 'ar' && sidebarCollapsed ? "lg:mr-20" : "",
          language === 'en' && !sidebarCollapsed ? "lg:ml-80" : "",
          language === 'en' && sidebarCollapsed ? "lg:ml-20" : ""
        )}>
          <EnhancedHeader 
            language={language} 
            title={getCurrentPageTitle()} 
            onMobileMenuToggle={() => setMobileMenuOpen(!mobileMenuOpen)}
            onLanguageChange={handleLanguageChange}
          />
          
          <main className="flex-1 overflow-y-auto p-3 sm:p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>

        {/* Mobile Sidebar Overlay - Now slides from RIGHT for RTL */}
        {mobileMenuOpen && (
          <div className="fixed inset-0 z-50 lg:hidden">
            <div 
              className="absolute inset-0 bg-black/50" 
              onClick={() => setMobileMenuOpen(false)}
            />
            <div className={cn(
              "absolute top-0 h-full w-80 bg-white shadow-xl",
              language === 'ar' ? 'right-0' : 'left-0'
            )}>
              <EnhancedSidebar 
                isCollapsed={false}
                onToggle={() => setMobileMenuOpen(false)}
                language={language}
                onLanguageChange={handleLanguageChange}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedDashboardLayout;
