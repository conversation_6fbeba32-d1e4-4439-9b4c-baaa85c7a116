import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Clean up corrupted Facebook accounts
 * GET /api/test/facebook-cleanup - List corrupted Facebook accounts
 * POST /api/test/facebook-cleanup - Remove corrupted Facebook accounts
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧹 Facebook cleanup - Listing corrupted accounts...');

    const results = {
      timestamp: new Date().toISOString(),
      corruptedAccounts: [] as any[],
      summary: {} as any
    };

    // Get Facebook accounts from database
    const supabase = createServiceRoleClient();
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    const { data: facebookAccounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .eq('platform', 'FACEBOOK');

    if (error) {
      throw new Error(`Failed to fetch Facebook accounts: ${error.message}`);
    }

    // Identify corrupted accounts (we know these are corrupted from our testing)
    const knownCorruptedAccountIds = [
      'test-fb-**********.347804', // "Test Facebook Account"
      'ewasl_page' // "@eWasl Page"
    ];

    results.corruptedAccounts = facebookAccounts?.filter(acc => 
      knownCorruptedAccountIds.includes(acc.account_id)
    ).map(acc => ({
      id: acc.id,
      account_name: acc.account_name,
      account_id: acc.account_id,
      created_at: acc.created_at,
      user_id: acc.user_id,
      hasAccessToken: !!acc.access_token,
      tokenLength: acc.access_token?.length || 0,
      corruptionReason: 'Invalid OAuth access token - Cannot parse access token'
    })) || [];

    results.summary = {
      totalFacebookAccounts: facebookAccounts?.length || 0,
      corruptedAccounts: results.corruptedAccounts.length,
      cleanAccounts: (facebookAccounts?.length || 0) - results.corruptedAccounts.length,
      cleanupRequired: results.corruptedAccounts.length > 0
    };

    return NextResponse.json({
      success: true,
      message: '🧹 Facebook cleanup - Corrupted accounts identified',
      results,
      instructions: {
        cleanup: 'Use POST method to remove corrupted Facebook accounts',
        warning: 'This will permanently delete the corrupted accounts from the database'
      }
    });

  } catch (error) {
    console.error('❌ Facebook cleanup listing error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Facebook cleanup listing failed'
    }, { status: 500 });
  }
}

/**
 * Remove corrupted Facebook accounts
 * POST /api/test/facebook-cleanup
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧹 Facebook cleanup - Removing corrupted accounts...');

    const body = await request.json();
    const { confirm = false, dryRun = false } = body;

    if (!confirm && !dryRun) {
      return NextResponse.json({
        error: 'Cleanup requires confirmation. Set confirm: true or dryRun: true'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      dryRun,
      confirm,
      cleanup: {} as any
    };

    // Get corrupted Facebook accounts
    const supabase = createServiceRoleClient();
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    const { data: facebookAccounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .eq('platform', 'FACEBOOK');

    if (error) {
      throw new Error(`Failed to fetch Facebook accounts: ${error.message}`);
    }

    // Identify corrupted accounts
    const knownCorruptedAccountIds = [
      'test-fb-**********.347804', // "Test Facebook Account"
      'ewasl_page' // "@eWasl Page"
    ];

    const corruptedAccounts = facebookAccounts?.filter(acc => 
      knownCorruptedAccountIds.includes(acc.account_id)
    ) || [];

    results.cleanup = {
      accountsToRemove: corruptedAccounts.map(acc => ({
        id: acc.id,
        account_name: acc.account_name,
        account_id: acc.account_id,
        created_at: acc.created_at
      })),
      totalToRemove: corruptedAccounts.length
    };

    if (dryRun) {
      results.cleanup.dryRunResult = 'Accounts identified for removal but not deleted';
      
      return NextResponse.json({
        success: true,
        message: '🧹 Facebook cleanup dry run completed',
        results,
        recommendation: 'Set confirm: true to actually remove the corrupted accounts'
      });
    }

    // Actual cleanup
    if (confirm && corruptedAccounts.length > 0) {
      console.log(`Removing ${corruptedAccounts.length} corrupted Facebook accounts...`);
      
      const removalResults = [];
      
      for (const account of corruptedAccounts) {
        try {
          console.log(`Removing account: ${account.account_name} (${account.id})`);
          
          // Remove from database
          const { error: deleteError } = await supabase
            .from('social_accounts')
            .delete()
            .eq('id', account.id);

          if (deleteError) {
            throw new Error(`Failed to delete account ${account.id}: ${deleteError.message}`);
          }

          removalResults.push({
            accountId: account.id,
            accountName: account.account_name,
            success: true,
            message: 'Account removed successfully'
          });

          console.log(`✅ Removed account: ${account.account_name}`);

        } catch (removeError: any) {
          removalResults.push({
            accountId: account.id,
            accountName: account.account_name,
            success: false,
            error: removeError.message
          });
          console.error(`❌ Failed to remove account ${account.account_name}:`, removeError);
        }
      }

      results.cleanup.removalResults = removalResults;
      results.cleanup.successfulRemovals = removalResults.filter(r => r.success).length;
      results.cleanup.failedRemovals = removalResults.filter(r => !r.success).length;

      // Log cleanup activity
      await supabase
        .from('activities')
        .insert({
          user_id: demoUserId,
          action: 'FACEBOOK_ACCOUNTS_CLEANUP',
          metadata: {
            removedAccounts: removalResults.filter(r => r.success).length,
            failedRemovals: removalResults.filter(r => !r.success).length,
            timestamp: new Date().toISOString(),
          },
        });

      return NextResponse.json({
        success: true,
        message: `🎉 Facebook cleanup completed! Removed ${results.cleanup.successfulRemovals} corrupted accounts`,
        results,
        nextSteps: {
          phase3: 'Access Facebook Developer Console for new tokens',
          phase4: 'Re-establish Facebook connections with fresh OAuth flow',
          phase5: 'Verify complete Facebook integration'
        }
      });

    } else {
      return NextResponse.json({
        success: true,
        message: 'No corrupted Facebook accounts found to remove',
        results
      });
    }

  } catch (error: any) {
    console.error('❌ Facebook cleanup error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      results,
      details: 'Facebook cleanup failed'
    }, { status: 500 });
  }
}
