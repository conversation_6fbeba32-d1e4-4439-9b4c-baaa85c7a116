"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  FileText, 
  Calendar, 
  Users, 
  BarChart3, 
  Settings,
  ChevronRight,
  LogOut,
  Globe,
  X,
  BookTemplate,
  CreditCard,
  TestTube
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface EnhancedSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  language: 'ar' | 'en';
  onLanguageChange: (lang: 'ar' | 'en') => void;
  className?: string;
}

const EnhancedSidebar = ({ 
  isCollapsed, 
  onToggle, 
  language, 
  onLanguageChange,
  className 
}: EnhancedSidebarProps) => {
  const pathname = usePathname();
  const router = useRouter();

  const handleLogout = async () => {
    const supabase = createClient();
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      toast.error(language === 'ar' ? 'فشل في تسجيل الخروج' : 'Failed to logout');
      return;
    }
    
    toast.success(language === 'ar' ? 'تم تسجيل الخروج بنجاح' : 'Logged out successfully');
    router.push('/auth/signin');
  };

  const menuItems = [
    {
      id: 'dashboard',
      label: language === 'ar' ? 'لوحة التحكم' : 'Dashboard',
      icon: LayoutDashboard,
      path: '/dashboard'
    },
    {
      id: 'posts',
      label: language === 'ar' ? 'إدارة المنشورات' : 'Posts Management',
      icon: FileText,
      path: '/posts'
    },
    {
      id: 'schedule',
      label: language === 'ar' ? 'جدولة المنشورات' : 'Schedule',
      icon: Calendar,
      path: '/schedule'
    },
    {
      id: 'accounts',
      label: language === 'ar' ? 'الحسابات الاجتماعية' : 'Social Accounts',
      icon: Users,
      path: '/social'
    },
    {
      id: 'analytics',
      label: language === 'ar' ? 'التحليلات' : 'Analytics',
      icon: BarChart3,
      path: '/analytics'
    },
    {
      id: 'billing',
      label: language === 'ar' ? 'الفوترة' : 'Billing',
      icon: CreditCard,
      path: '/dashboard/billing'
    },
    {
      id: 'templates',
      label: language === 'ar' ? 'مكتبة القوالب' : 'Templates',
      icon: BookTemplate,
      path: '/templates'
    },
    {
      id: 'settings',
      label: language === 'ar' ? 'الإعدادات' : 'Settings',
      icon: Settings,
      path: '/settings'
    },
    {
      id: 'api-testing',
      label: language === 'ar' ? '🧪 اختبار APIs' : '🧪 API Testing',
      icon: TestTube,
      path: '/api-testing'
    }
  ];

  return (
    <div className={cn(
      "bg-white/95 backdrop-blur-sm h-screen flex flex-col transition-all duration-300 fixed top-0 z-40 border-gray-100",
      language === 'ar' ? "border-l right-0 shadow-2xl shadow-indigo-500/10" : "border-r left-0 shadow-2xl shadow-indigo-500/10",
      isCollapsed ? "w-20" : "w-80",
      className
    )}>
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className={cn(
          "flex items-center gap-4",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="h-12 w-12 rounded-xl hover:bg-indigo-50 hover:text-indigo-600 transition-all duration-200 group"
          >
            {/* Show X icon on mobile, chevron on desktop */}
            <span className="lg:hidden">
              <X className="h-5 w-5" />
            </span>
            <span className="hidden lg:block">
              <ChevronRight className={cn(
                "h-5 w-5 transition-transform duration-300 group-hover:scale-110",
                language === 'ar' ? (
                  isCollapsed ? "rotate-180" : "rotate-0"
                ) : (
                  isCollapsed ? "rotate-0" : "rotate-180"
                )
              )} />
            </span>
          </Button>
          
          {!isCollapsed && (
            <div className={cn(
              "flex items-center gap-3 flex-1",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-xl">eW</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-emerald-400 rounded-full border-2 border-white"></div>
              </div>
              <div className={cn(
                "flex-1",
                language === 'ar' ? "text-right" : "text-left"
              )}>
                <h1 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  {language === 'ar' ? 'إي وصل' : 'eWasl'}
                </h1>
                <p className="text-sm text-gray-500 font-medium">
                  {language === 'ar' ? 'إدارة ذكية' : 'Smart Management'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 py-6 overflow-y-auto">
        <ul className="space-y-2 px-4">
          {menuItems.map((item) => {
            const isActive = pathname === item.path || 
              (item.path !== '/dashboard' && pathname.startsWith(item.path));
            const Icon = item.icon;
            
            return (
              <li key={item.id}>
                <Link
                  href={item.path}
                  onClick={() => {
                    // Close mobile menu on item click
                    if (window.innerWidth < 1024) {
                      onToggle();
                    }
                  }}
                  className={cn(
                    "relative flex items-center gap-4 px-4 py-3.5 rounded-2xl transition-all duration-200 group",
                    isActive 
                      ? "bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25 transform scale-105" 
                      : "text-gray-600 hover:bg-indigo-50 hover:text-indigo-600",
                    isCollapsed ? "justify-center" : (
                      language === 'ar' ? "flex-row-reverse text-right" : "text-left"
                    )
                  )}
                >
                  <Icon className={cn(
                    "h-6 w-6 shrink-0 transition-all duration-200",
                    isActive ? "text-white drop-shadow-sm" : "text-gray-500 group-hover:text-indigo-600 group-hover:scale-110"
                  )} />
                  {!isCollapsed && (
                    <span className={cn(
                      "font-medium text-base flex-1 transition-all duration-200",
                      language === 'ar' ? "text-right" : "text-left"
                    )}>
                      {item.label}
                    </span>
                  )}
                  
                  {isActive && !isCollapsed && (
                    <div className={cn(
                      "w-2 h-8 bg-white/30 rounded-full",
                      language === 'ar' ? "mr-2" : "ml-2"
                    )} />
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-100 space-y-3 bg-gray-50/50">
        {!isCollapsed && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onLanguageChange(language === 'ar' ? 'en' : 'ar')}
            className={cn(
              "w-full btn-pro-secondary text-sm font-medium",
              language === 'ar' ? "flex-row-reverse" : ""
            )}
          >
            <Globe className="h-4 w-4" />
            <span>
              {language === 'ar' ? 'English' : 'العربية'}
            </span>
          </Button>
        )}
        <Button
          variant="ghost"
          size={isCollapsed ? "icon" : "sm"}
          onClick={handleLogout}
          className={cn(
            "text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 font-medium",
            isCollapsed ? "w-12 h-12" : (
              language === 'ar' ? "w-full flex-row-reverse" : "w-full"
            )
          )}
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && (
            <span className={cn(
              "text-sm",
              language === 'ar' ? "text-right" : "text-left"
            )}>
              {language === 'ar' ? 'تسجيل الخروج' : 'Logout'}
            </span>
          )}
        </Button>
      </div>
    </div>
  );
};

export default EnhancedSidebar;
