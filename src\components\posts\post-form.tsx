"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { Calendar as CalendarIcon, Loader2, <PERSON><PERSON><PERSON> } from "lucide-react";
import { format } from "date-fns";
import { arSA } from "date-fns/locale";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AICaptionGenerator } from "@/components/posts/ai-caption-generator";
import { AdvancedEditor } from "@/components/content/advanced-editor";
import { MediaUpload } from "@/components/media/media-upload";
import { MediaLibrary } from "@/components/media/media-library";
import { toast } from "sonner";

const formSchema = z.object({
  content: z.string().min(1, {
    message: "المحتوى مطلوب",
  }).max(280, {
    message: "المحتوى يجب أن يكون أقل من 280 حرف",
  }),
  mediaUrl: z.string().url({
    message: "يرجى إدخال رابط صالح",
  }).optional().or(z.literal("")),
  status: z.enum(["DRAFT", "SCHEDULED", "PUBLISHED"]),
  scheduledAt: z.date().optional(),
  platforms: z.array(z.string()).min(1, {
    message: "يرجى اختيار منصة واحدة على الأقل",
  }),
});

interface PostFormProps {
  initialData?: any;
}

export function PostForm({ initialData }: PostFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    initialData?.scheduledAt ? new Date(initialData.scheduledAt) : undefined
  );
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);
  const [uploadedMedia, setUploadedMedia] = useState<any>(null);
  const [useAdvancedEditor, setUseAdvancedEditor] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: initialData?.content || "",
      mediaUrl: initialData?.mediaUrl || "",
      status: initialData?.status || "DRAFT",
      scheduledAt: initialData?.scheduledAt ? new Date(initialData.scheduledAt) : undefined,
      platforms: initialData?.platforms || [],
    },
  });

  const status = form.watch("status");

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Submitting post:', values);

      // Prepare API payload
      const payload = {
        content: values.content,
        media_url: values.mediaUrl || '',
        status: values.status,
        scheduled_at: values.scheduledAt ? values.scheduledAt.toISOString() : undefined,
        social_account_ids: values.platforms, // For now, using platforms as account IDs
      };

      // Make API call
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to save post');
      }

      console.log('Post saved successfully:', data);

      // If status is PUBLISHED, trigger immediate publishing
      if (values.status === 'PUBLISHED' && data.post?.id) {
        try {
          console.log('Publishing post immediately...');
          const publishResponse = await fetch('/api/posts/publish', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              postId: data.post.id,
              platforms: values.platforms,
              publishNow: true
            }),
          });

          const publishData = await publishResponse.json();

          if (publishResponse.ok) {
            toast.success(`تم نشر المنشور بنجاح على ${publishData.summary.successful} من ${publishData.summary.total} منصات!`);
            if (publishData.summary.failed > 0) {
              toast.warning(`فشل النشر على ${publishData.summary.failed} منصات`);
            }
          } else {
            console.error('Publishing failed:', publishData);
            toast.error('تم حفظ المنشور ولكن فشل النشر. يمكنك المحاولة مرة أخرى من صفحة المنشورات.');
          }
        } catch (publishError) {
          console.error('Publishing error:', publishError);
          toast.error('تم حفظ المنشور ولكن فشل النشر. يمكنك المحاولة مرة أخرى من صفحة المنشورات.');
        }
      } else {
        // Show success message based on status
        if (values.status === 'SCHEDULED') {
          toast.success('تم جدولة المنشور بنجاح!');
        } else {
          toast.success('تم حفظ المنشور كمسودة!');
        }
      }

      // Redirect to posts page
      router.push("/posts");
    } catch (error: any) {
      console.error('Post submission error:', error);
      setError(error.message || "حدث خطأ أثناء حفظ المنشور");
      toast.error(error.message || "حدث خطأ أثناء حفظ المنشور");
    } finally {
      setIsLoading(false);
    }
  }

  const platforms = [
    { id: "twitter", label: "تويتر" },
    { id: "facebook", label: "فيسبوك" },
    { id: "instagram", label: "انستغرام" },
    { id: "linkedin", label: "لينكد إن" },
  ];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8" dir="rtl">
        <Card>
          <CardContent className="pt-6">
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="content">المحتوى</TabsTrigger>
                <TabsTrigger value="advanced">محرر متقدم</TabsTrigger>
                <TabsTrigger value="ai">الذكاء الاصطناعي</TabsTrigger>
                <TabsTrigger value="settings">الإعدادات</TabsTrigger>
              </TabsList>
              <TabsContent value="content" className="space-y-6 py-4">
                <div className="grid gap-6 lg:grid-cols-2">
                  {/* Content Editor */}
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="content"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-lg font-semibold">المحتوى</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Textarea
                                placeholder="اكتب محتوى المنشور هنا... 🚀"
                                className="min-h-40 resize-none text-lg leading-relaxed border-2 focus:border-blue-500 transition-colors"
                                {...field}
                              />
                              <div className="absolute bottom-3 left-3 text-sm text-gray-500">
                                {field.value.length}/280
                              </div>
                            </div>
                          </FormControl>
                          <div className="flex items-center justify-between">
                            <FormDescription className="text-sm">
                              اكتب محتوى جذاب ومؤثر لجمهورك
                            </FormDescription>
                            <div className={`text-sm font-medium ${
                              field.value.length > 250 ? 'text-red-500' :
                              field.value.length > 200 ? 'text-yellow-500' : 'text-green-500'
                            }`}>
                              {280 - field.value.length} حرف متبقي
                            </div>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="mediaUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-lg font-semibold">الوسائط</FormLabel>
                          <FormControl>
                            <div className="space-y-3">
                              {/* Media Upload Component */}
                              <MediaUpload
                                onUploadComplete={(media) => {
                                  setUploadedMedia(media);
                                  field.onChange(media.publicUrl);
                                  toast.success('تم رفع الملف بنجاح!');
                                }}
                                onUploadStart={() => {
                                  toast.info('جاري رفع الملف...');
                                }}
                              />

                              {/* URL Input as fallback */}
                              <div className="relative">
                                <Input
                                  placeholder="أو أدخل رابط الصورة أو الفيديو"
                                  className="border-2 focus:border-blue-500 transition-colors"
                                  {...field}
                                />
                              </div>

                              {/* Media Library Button */}
                              <div className="flex gap-2">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  className="h-10"
                                  onClick={() => setShowMediaLibrary(true)}
                                >
                                  📁 مكتبة الوسائط
                                </Button>
                                {uploadedMedia && (
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    className="h-10 text-red-500"
                                    onClick={() => {
                                      setUploadedMedia(null);
                                      field.onChange('');
                                    }}
                                  >
                                    🗑️ إزالة
                                  </Button>
                                )}
                              </div>

                              {/* Media Preview */}
                              {(field.value || uploadedMedia) && (
                                <div className="border rounded-lg p-3 bg-gray-50">
                                  <div className="flex items-center gap-3">
                                    <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                      {uploadedMedia?.fileType?.startsWith('image') ? '🖼️' :
                                       uploadedMedia?.fileType?.startsWith('video') ? '🎥' : '📎'}
                                    </div>
                                    <div className="flex-1">
                                      <p className="text-sm font-medium">
                                        {uploadedMedia?.fileName || 'ملف وسائط'}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {field.value ? 'تم الرفع بنجاح' : 'رابط خارجي'}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </FormControl>
                          <FormDescription>
                            ارفع صورة أو فيديو من جهازك أو أدخل رابط خارجي
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Live Preview */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">معاينة المنشور</h3>
                    <div className="space-y-4">
                      {/* Twitter Preview */}
                      <div className="border-2 border-gray-200 rounded-xl p-4 bg-white">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center text-white text-sm font-bold">
                            𝕏
                          </div>
                          <div>
                            <div className="font-semibold text-sm">@ewasl_app</div>
                            <div className="text-xs text-gray-500">تويتر</div>
                          </div>
                        </div>
                        <div className="text-sm leading-relaxed mb-3">
                          {form.watch("content") || "معاينة المحتوى ستظهر هنا..."}
                        </div>
                        {form.watch("mediaUrl") && (
                          <div className="bg-gray-100 rounded-lg p-4 text-center text-sm text-gray-500">
                            🖼️ صورة/فيديو
                          </div>
                        )}
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-3 pt-3 border-t">
                          <span>💬 0</span>
                          <span>🔄 0</span>
                          <span>❤️ 0</span>
                        </div>
                      </div>

                      {/* Instagram Preview */}
                      <div className="border-2 border-gray-200 rounded-xl p-4 bg-white">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white text-sm">
                            📷
                          </div>
                          <div>
                            <div className="font-semibold text-sm">@ewasl_app</div>
                            <div className="text-xs text-gray-500">انستغرام</div>
                          </div>
                        </div>
                        {form.watch("mediaUrl") && (
                          <div className="bg-gray-100 rounded-lg aspect-square mb-3 flex items-center justify-center text-gray-500">
                            🖼️ صورة/فيديو
                          </div>
                        )}
                        <div className="text-sm leading-relaxed">
                          {form.watch("content") || "معاينة المحتوى ستظهر هنا..."}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-3 pt-3 border-t">
                          <span>❤️ 0</span>
                          <span>💬 0</span>
                          <span>📤 0</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="advanced" className="py-4">
                <AdvancedEditor
                  initialContent={form.watch("content")}
                  platform="facebook"
                  onContentChange={(content) => {
                    form.setValue("content", content);
                  }}
                  onSave={(content, metadata) => {
                    form.setValue("content", content);
                    if (metadata.hashtags && metadata.hashtags.length > 0) {
                      // Add hashtags to content if not already present
                      const hashtagsText = metadata.hashtags.map((tag: string) => `#${tag}`).join(' ');
                      const currentContent = form.getValues("content");
                      if (!currentContent.includes(hashtagsText)) {
                        form.setValue("content", `${currentContent}\n\n${hashtagsText}`);
                      }
                    }
                    toast.success('تم حفظ المحتوى من المحرر المتقدم');
                  }}
                />
              </TabsContent>
              <TabsContent value="ai" className="py-4">
                <AICaptionGenerator
                  onSelectCaption={(caption) => {
                    form.setValue("content", caption);
                  }}
                />
              </TabsContent>
              <TabsContent value="settings" className="space-y-6 py-4">
                <div className="grid gap-6 lg:grid-cols-2">
                  {/* Publishing Settings */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">إعدادات النشر</h3>

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium">حالة المنشور</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="h-12 border-2 focus:border-blue-500">
                                  <SelectValue placeholder="اختر حالة المنشور" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="DRAFT">
                                  <div className="flex items-center gap-2">
                                    <span>📝</span>
                                    <div>
                                      <div className="font-medium">مسودة</div>
                                      <div className="text-xs text-gray-500">حفظ للتعديل لاحقاً</div>
                                    </div>
                                  </div>
                                </SelectItem>
                                <SelectItem value="SCHEDULED">
                                  <div className="flex items-center gap-2">
                                    <span>⏰</span>
                                    <div>
                                      <div className="font-medium">مجدول</div>
                                      <div className="text-xs text-gray-500">نشر في وقت محدد</div>
                                    </div>
                                  </div>
                                </SelectItem>
                                <SelectItem value="PUBLISHED">
                                  <div className="flex items-center gap-2">
                                    <span>🚀</span>
                                    <div>
                                      <div className="font-medium">نشر الآن</div>
                                      <div className="text-xs text-gray-500">نشر فوري</div>
                                    </div>
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {status === "SCHEDULED" && (
                        <div className="space-y-4 mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <FormField
                            control={form.control}
                            name="scheduledAt"
                            render={({ field }) => (
                              <FormItem className="flex flex-col">
                                <FormLabel className="text-base font-medium">موعد النشر</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant={"outline"}
                                        className={`w-full justify-start text-right h-12 border-2 ${
                                          !field.value && "text-muted-foreground"
                                        }`}
                                      >
                                        <CalendarIcon className="ml-2 h-4 w-4" />
                                        {field.value ? (
                                          format(field.value, "PPP", { locale: arSA })
                                        ) : (
                                          <span>اختر تاريخ ووقت النشر</span>
                                        )}
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={(date) => {
                                        setSelectedDate(date);
                                        field.onChange(date);
                                      }}
                                      initialFocus
                                      locale={arSA}
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormDescription className="text-sm">
                                  سيتم نشر المنشور تلقائياً في الوقت المحدد
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Quick Schedule Options */}
                          <div className="grid grid-cols-2 gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const tomorrow = new Date();
                                tomorrow.setDate(tomorrow.getDate() + 1);
                                tomorrow.setHours(9, 0, 0, 0);
                                form.setValue("scheduledAt", tomorrow);
                              }}
                            >
                              غداً 9 ص
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const nextWeek = new Date();
                                nextWeek.setDate(nextWeek.getDate() + 7);
                                nextWeek.setHours(12, 0, 0, 0);
                                form.setValue("scheduledAt", nextWeek);
                              }}
                            >
                              الأسبوع القادم
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Platform Selection */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">اختيار المنصات</h3>

                      <FormField
                        control={form.control}
                        name="platforms"
                        render={() => (
                          <FormItem>
                            <FormDescription className="text-sm mb-4">
                              اختر المنصات التي تريد النشر عليها
                            </FormDescription>
                            <div className="space-y-3">
                              {platforms.map((platform) => (
                                <FormField
                                  key={platform.id}
                                  control={form.control}
                                  name="platforms"
                                  render={({ field }) => {
                                    const isSelected = field.value?.includes(platform.id);
                                    return (
                                      <FormItem key={platform.id}>
                                        <FormControl>
                                          <div
                                            className={`flex items-center justify-between p-4 rounded-lg border-2 cursor-pointer transition-all ${
                                              isSelected
                                                ? 'border-blue-500 bg-blue-50'
                                                : 'border-gray-200 hover:border-gray-300'
                                            }`}
                                            onClick={() => {
                                              const checked = !isSelected;
                                              return checked
                                                ? field.onChange([...field.value, platform.id])
                                                : field.onChange(
                                                    field.value?.filter((value) => value !== platform.id)
                                                  );
                                            }}
                                          >
                                            <div className="flex items-center gap-3">
                                              <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white font-bold ${
                                                platform.id === 'twitter' ? 'bg-black' :
                                                platform.id === 'facebook' ? 'bg-blue-600' :
                                                platform.id === 'instagram' ? 'bg-pink-500' :
                                                'bg-blue-700'
                                              }`}>
                                                {platform.id === 'twitter' ? '𝕏' :
                                                 platform.id === 'facebook' ? 'f' :
                                                 platform.id === 'instagram' ? '📷' : 'in'}
                                              </div>
                                              <div>
                                                <div className="font-medium">{platform.label}</div>
                                                <div className="text-xs text-gray-500">
                                                  {platform.id === 'twitter' ? '@ewasl_app • 5.2K متابع' :
                                                   platform.id === 'instagram' ? '@ewasl_app • 7.3K متابع' :
                                                   'غير متصل'}
                                                </div>
                                              </div>
                                            </div>
                                            <Checkbox
                                              checked={isSelected}
                                              onChange={() => {}}
                                              className="pointer-events-none"
                                            />
                                          </div>
                                        </FormControl>
                                      </FormItem>
                                    );
                                  }}
                                />
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {error && <div className="text-red-500 text-sm text-center">{error}</div>}

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/posts")}
          >
            إلغاء
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
            {initialData ? "تحديث المنشور" : "إنشاء المنشور"}
          </Button>
        </div>
      </form>

      {/* Media Library Modal */}
      <MediaLibrary
        isOpen={showMediaLibrary}
        onClose={() => setShowMediaLibrary(false)}
        onSelectMedia={(media) => {
          setUploadedMedia(media);
          form.setValue('mediaUrl', media.publicUrl);
          toast.success('تم اختيار الملف من المكتبة!');
        }}
      />
    </Form>
  );
}
