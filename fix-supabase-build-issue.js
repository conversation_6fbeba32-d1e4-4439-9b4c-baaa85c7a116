// Script to fix Supabase client initialization issues that cause build failures
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Supabase Build Issues...\n');

// Critical API routes that are likely causing build failures
const criticalRoutes = [
  'src/app/api/system/health/route.ts',
  'src/app/api/scheduler/health/route.ts',
  'src/app/api/analytics/dashboard/route.ts',
  'src/app/api/analytics/overview/route.ts',
  'src/app/api/analytics/detailed/route.ts',
  'src/app/api/social/accounts/route.ts',
  'src/app/api/posts/route.ts',
  'src/app/api/media/route.ts'
];

let totalFixed = 0;

criticalRoutes.forEach(filePath => {
  console.log(`📁 Processing: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`  ❌ File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let fixCount = 0;
  
  // Pattern 1: Module-level Supabase client creation
  const modulePattern = /const supabase = createClient\(\s*process\.env\.NEXT_PUBLIC_SUPABASE_URL!,\s*process\.env\.SUPABASE_SERVICE_ROLE_KEY!,\s*\{[^}]*\}\s*\);/g;
  
  if (modulePattern.test(content)) {
    // Reset regex
    modulePattern.lastIndex = 0;
    
    // Replace module-level client with function
    content = content.replace(
      modulePattern,
      `function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}`
    );
    
    // Add client creation inside functions
    content = content.replace(
      /(export async function \w+\([^)]*\)\s*\{)/g,
      '$1\n  const supabase = getSupabaseClient();'
    );
    
    fixCount++;
    totalFixed++;
  }
  
  if (fixCount > 0) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ Fixed ${fixCount} Supabase initialization issues`);
  } else {
    console.log(`  ℹ️  No critical issues found`);
  }
});

console.log(`\n📊 SUMMARY:`);
console.log(`Total critical files processed: ${criticalRoutes.length}`);
console.log(`Total Supabase issues fixed: ${totalFixed}`);

if (totalFixed > 0) {
  console.log('\n🎉 Critical Supabase build issues fixed!');
  console.log('✅ Build should now complete successfully');
  console.log('✅ Environment variables will be loaded at runtime');
  console.log('✅ No more "supabaseKey is required" errors');
} else {
  console.log('\n✅ All critical routes already optimized');
}

// Additional check for any remaining module-level clients
console.log('\n🔍 Checking for remaining module-level clients...');

const allApiFiles = [];
function findApiFiles(dir) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    if (stat.isDirectory()) {
      findApiFiles(fullPath);
    } else if (file === 'route.ts') {
      allApiFiles.push(fullPath);
    }
  });
}

findApiFiles('src/app/api');

let remainingIssues = 0;
allApiFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  if (content.includes('const supabase = createClient(') && 
      content.includes('process.env.NEXT_PUBLIC_SUPABASE_URL') &&
      !content.includes('function getSupabaseClient()')) {
    remainingIssues++;
  }
});

console.log(`Remaining module-level Supabase clients: ${remainingIssues}`);

if (remainingIssues === 0) {
  console.log('🎉 All module-level Supabase clients have been fixed!');
} else {
  console.log(`⚠️  ${remainingIssues} files still need fixing (non-critical)`);
}
