'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CheckCircle,
  XCircle,
  Clock,
  Play,
  RefreshCw,
  AlertTriangle,
  Info,
  Zap,
  Settings,
  Eye,
  Send,
  TestTube,
  Activity,
  Shield,
  Link as LinkIcon
} from 'lucide-react';
import { toast } from 'sonner';

interface TestResult {
  platform: string;
  testType: 'connection' | 'auth' | 'publish' | 'permissions';
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  details?: any;
  timestamp: string;
  duration?: number;
}

interface PlatformConfig {
  platform: string;
  name: string;
  icon: string;
  connected: boolean;
  credentials: {
    [key: string]: string;
  };
  permissions: string[];
  lastTested?: string;
  testResults: TestResult[];
}

interface EnhancedApiTestingProps {
  userId: string;
}

const EnhancedApiTesting: React.FC<EnhancedApiTestingProps> = ({
  userId
}) => {
  const [platforms, setPlatforms] = useState<PlatformConfig[]>([]);
  const [activePlatform, setActivePlatform] = useState('facebook');
  const [testContent, setTestContent] = useState('');
  const [running, setRunning] = useState(false);
  const [testProgress, setTestProgress] = useState(0);

  const text = {
    title: 'اختبار تكامل واجهات برمجة التطبيقات',
    description: 'اختبار شامل لاتصالات منصات التواصل الاجتماعي',
    connection: 'اختبار الاتصال',
    authentication: 'اختبار المصادقة',
    publishing: 'اختبار النشر',
    permissionsTest: 'اختبار الصلاحيات',
    runAllTests: 'تشغيل جميع الاختبارات',
    runTest: 'تشغيل الاختبار',
    testContent: 'محتوى الاختبار',
    testContentPlaceholder: 'أدخل محتوى لاختبار النشر...',
    connected: 'متصل',
    disconnected: 'غير متصل',
    success: 'نجح',
    error: 'فشل',
    pending: 'في الانتظار',
    running: 'قيد التشغيل',
    lastTested: 'آخر اختبار',
    duration: 'المدة',
    testResults: 'نتائج الاختبار',
    clearResults: 'مسح النتائج',
    exportResults: 'تصدير النتائج',
    credentials: 'بيانات الاعتماد',
    permissions: 'الصلاحيات',
    testHistory: 'تاريخ الاختبارات',
    overallHealth: 'الصحة العامة',
    platformStatus: 'حالة المنصة',
    apiEndpoints: 'نقاط النهاية',
    rateLimits: 'حدود المعدل',
    errorLogs: 'سجلات الأخطاء',
    runningTests: 'تشغيل الاختبارات...',
    allTestsCompleted: 'تم إنجاز جميع الاختبارات',
    testResultsCleared: 'تم مسح نتائج الاختبار',
    noTestHistory: 'لا يوجد تاريخ اختبارات متاح. قم بتشغيل بعض الاختبارات لرؤية النتائج هنا.',
    successRate: 'معدل النجاح',
    requiredPermissions: 'الصلاحيات المطلوبة',
    credentialInfo: 'معلومات بيانات الاعتماد تتم إدارتها من خلال تدفق اتصال OAuth. أعد ربط حسابك إذا كنت تواجه مشاكل في المصادقة.'
  };

  useEffect(() => {
    initializePlatforms();
  }, [userId]);

  const initializePlatforms = async () => {
    try {
      // Fetch connected accounts
      const response = await fetch(`/api/social/accounts?userId=${userId}`);
      const data = await response.json();
      
      const platformConfigs: PlatformConfig[] = [
        {
          platform: 'facebook',
          name: 'Facebook',
          icon: '📘',
          connected: false,
          credentials: {},
          permissions: ['pages_manage_posts', 'pages_read_engagement'],
          testResults: []
        },
        {
          platform: 'instagram',
          name: 'Instagram',
          icon: '📷',
          connected: false,
          credentials: {},
          permissions: ['instagram_basic', 'instagram_content_publish'],
          testResults: []
        },
        {
          platform: 'linkedin',
          name: 'LinkedIn',
          icon: '💼',
          connected: false,
          credentials: {},
          permissions: ['w_member_social', 'w_organization_social'],
          testResults: []
        },
        {
          platform: 'twitter',
          name: 'Twitter/X',
          icon: '🐦',
          connected: false,
          credentials: {},
          permissions: ['tweet.read', 'tweet.write', 'users.read'],
          testResults: []
        }
      ];

      // Update connection status based on actual accounts
      if (data.success && data.accounts) {
        data.accounts.forEach((account: any) => {
          const platform = platformConfigs.find(p => p.platform === account.platform.toLowerCase());
          if (platform) {
            platform.connected = account.is_active;
            platform.lastTested = account.updated_at;
          }
        });
      }

      setPlatforms(platformConfigs);
    } catch (error) {
      console.error('Error initializing platforms:', error);
      toast.error('Failed to load platform configurations');
    }
  };

  const runSingleTest = async (platform: string, testType: TestResult['testType']) => {
    const platformConfig = platforms.find(p => p.platform === platform);
    if (!platformConfig) return;

    const testResult: TestResult = {
      platform,
      testType,
      status: 'running',
      message: 'Test in progress...',
      timestamp: new Date().toISOString()
    };

    // Add test result to platform
    platformConfig.testResults.unshift(testResult);
    setPlatforms([...platforms]);

    try {
      const startTime = Date.now();
      
      // Call the appropriate test endpoint
      const response = await fetch('/api/test/social-integration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform,
          testType,
          userId,
          testContent: testType === 'publish' ? testContent : undefined
        })
      });

      const result = await response.json();
      const duration = Date.now() - startTime;

      // Update test result
      testResult.status = result.success ? 'success' : 'error';
      testResult.message = result.message || (result.success ? 'Test completed successfully' : 'Test failed');
      testResult.details = result.details;
      testResult.duration = duration;

      setPlatforms([...platforms]);

      if (result.success) {
        toast.success(`${platform} ${testType} test passed`);
      } else {
        toast.error(`${platform} ${testType} test failed: ${result.message}`);
      }

    } catch (error) {
      testResult.status = 'error';
      testResult.message = 'Test execution failed';
      testResult.duration = Date.now() - Date.parse(testResult.timestamp);
      setPlatforms([...platforms]);
      toast.error(`Test execution failed: ${error}`);
    }
  };

  const runAllTests = async () => {
    setRunning(true);
    setTestProgress(0);

    const connectedPlatforms = platforms.filter(p => p.connected);
    const totalTests = connectedPlatforms.length * 4; // 4 test types per platform
    let completedTests = 0;

    for (const platform of connectedPlatforms) {
      const testTypes: TestResult['testType'][] = ['connection', 'auth', 'permissions', 'publish'];
      
      for (const testType of testTypes) {
        await runSingleTest(platform.platform, testType);
        completedTests++;
        setTestProgress((completedTests / totalTests) * 100);
        
        // Add delay between tests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    setRunning(false);
    setTestProgress(100);
    toast.success('All tests completed');
  };

  const clearResults = () => {
    const updatedPlatforms = platforms.map(platform => ({
      ...platform,
      testResults: []
    }));
    setPlatforms(updatedPlatforms);
    toast.success('Test results cleared');
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running': return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'pending': return <Clock className="w-4 h-4 text-gray-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const currentPlatform = platforms.find(p => p.platform === activePlatform);

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{text.title}</h1>
          <p className="text-muted-foreground">{text.description}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            onClick={clearResults} 
            variant="outline"
            disabled={running}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            {text.clearResults}
          </Button>
          <Button 
            onClick={runAllTests} 
            disabled={running}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            <TestTube className="w-4 h-4 mr-2" />
            {text.runAllTests}
          </Button>
        </div>
      </div>

      {/* Progress Bar */}
      {running && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">{text.runningTests}</span>
              <span className="text-sm text-muted-foreground">{Math.round(testProgress)}%</span>
            </div>
            <Progress value={testProgress} className="w-full" />
          </CardContent>
        </Card>
      )}

      {/* Platform Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {platforms.map((platform) => {
          const recentTests = platform.testResults.slice(0, 4);
          const successRate = recentTests.length > 0 
            ? (recentTests.filter(t => t.status === 'success').length / recentTests.length) * 100 
            : 0;

          return (
            <Card 
              key={platform.platform}
              className={`cursor-pointer transition-all hover:shadow-md ${
                activePlatform === platform.platform ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setActivePlatform(platform.platform)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{platform.icon}</span>
                    <div>
                      <h3 className="font-semibold">{platform.name}</h3>
                      <Badge 
                        variant={platform.connected ? "default" : "secondary"}
                        className={platform.connected ? "bg-green-100 text-green-800" : ""}
                      >
                        {platform.connected ? text.connected : text.disconnected}
                      </Badge>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{text.successRate}</span>
                    <span className="font-medium">{Math.round(successRate)}%</span>
                  </div>
                  <Progress value={successRate} className="h-2" />
                  
                  {platform.lastTested && (
                    <p className="text-xs text-muted-foreground">
                      {text.lastTested}: {new Date(platform.lastTested).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Detailed Testing Interface */}
      {currentPlatform && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <span className="text-3xl">{currentPlatform.icon}</span>
                <div>
                  <CardTitle>اختبار {currentPlatform.name}</CardTitle>
                  <CardDescription>
                    اختبار شامل لتكامل {currentPlatform.name}
                  </CardDescription>
                </div>
              </div>
              <Badge 
                variant={currentPlatform.connected ? "default" : "secondary"}
                className={currentPlatform.connected ? "bg-green-100 text-green-800" : ""}
              >
                {currentPlatform.connected ? text.connected : text.disconnected}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="tests" className="space-y-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="tests">الاختبارات</TabsTrigger>
                <TabsTrigger value="credentials">{text.credentials}</TabsTrigger>
                <TabsTrigger value="permissions">{text.permissions}</TabsTrigger>
                <TabsTrigger value="history">{text.testHistory}</TabsTrigger>
              </TabsList>

              <TabsContent value="tests" className="space-y-4">
                {/* Test Content Input */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">{text.testContent}</label>
                  <Textarea
                    value={testContent}
                    onChange={(e) => setTestContent(e.target.value)}
                    placeholder={text.testContentPlaceholder}
                    className="min-h-20"
                  />
                </div>

                {/* Individual Tests */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { type: 'connection' as const, icon: LinkIcon, title: text.connection },
                    { type: 'auth' as const, icon: Shield, title: text.authentication },
                    { type: 'permissions' as const, icon: Settings, title: text.permissionsTest },
                    { type: 'publish' as const, icon: Send, title: text.publishing }
                  ].map(({ type, icon: Icon, title }) => {
                    const latestResult = currentPlatform.testResults.find(r => r.testType === type);
                    
                    return (
                      <Card key={type} className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <Icon className="w-5 h-5" />
                            <h4 className="font-medium">{title}</h4>
                          </div>
                          {latestResult && (
                            <Badge className={getStatusColor(latestResult.status)}>
                              {getStatusIcon(latestResult.status)}
                              <span className="ml-1">{latestResult.status}</span>
                            </Badge>
                          )}
                        </div>
                        
                        {latestResult && (
                          <div className="text-sm text-muted-foreground mb-3">
                            <p>{latestResult.message}</p>
                            {latestResult.duration && (
                              <p className="text-xs mt-1">
                                {text.duration}: {latestResult.duration}ms
                              </p>
                            )}
                          </div>
                        )}
                        
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => runSingleTest(currentPlatform.platform, type)}
                          disabled={running}
                          className="w-full"
                        >
                          <Play className="w-4 h-4 mr-2" />
                          {text.runTest}
                        </Button>
                      </Card>
                    );
                  })}
                </div>
              </TabsContent>

              <TabsContent value="credentials" className="space-y-4">
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    {text.credentialInfo}
                  </AlertDescription>
                </Alert>
              </TabsContent>

              <TabsContent value="permissions" className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium">{text.requiredPermissions}</h4>
                  <div className="space-y-2">
                    {currentPlatform.permissions.map((permission) => (
                      <div key={permission} className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm font-mono">{permission}</span>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="history" className="space-y-4">
                <div className="space-y-2">
                  {currentPlatform.testResults.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                      {text.noTestHistory}
                    </p>
                  ) : (
                    currentPlatform.testResults.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(result.status)}
                          <div>
                            <p className="font-medium capitalize">اختبار {result.testType}</p>
                            <p className="text-sm text-muted-foreground">{result.message}</p>
                          </div>
                        </div>
                        <div className="text-right text-sm text-muted-foreground">
                          <p>{new Date(result.timestamp).toLocaleString()}</p>
                          {result.duration && <p>{result.duration}ms</p>}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedApiTesting;
