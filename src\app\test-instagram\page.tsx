'use client';

import { useState } from 'react';

export default function TestInstagramPage() {
  const [authUrl, setAuthUrl] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [configStatus, setConfigStatus] = useState<any>(null);

  const checkConfiguration = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/social/oauth-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'instagram',
          action: 'check-credentials'
        }),
      });

      const data = await response.json();
      setConfigStatus(data);
      
      if (!data.success) {
        setError(data.message || 'Configuration check failed');
      }
    } catch (err) {
      setError('Failed to check configuration');
      console.error('Configuration check error:', err);
    } finally {
      setLoading(false);
    }
  };

  const generateAuthUrl = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/social/oauth-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'instagram',
          action: 'test-auth-url'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setAuthUrl(data.authUrl);
      } else {
        setError(data.message || 'Failed to generate authorization URL');
      }
    } catch (err) {
      setError('Failed to generate authorization URL');
      console.error('Auth URL generation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const startOAuth = () => {
    if (authUrl) {
      window.location.href = authUrl;
    } else {
      setError('Please generate authorization URL first');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="border-b border-gray-200 pb-4 mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Instagram Graph API Integration Test</h1>
            <p className="mt-2 text-gray-600">
              Test the Instagram Graph API OAuth flow (Business accounts only)
            </p>
          </div>

          {/* Important Notice */}
          <div className="mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <h3 className="font-medium text-yellow-800 mb-2">⚠️ Important: Business Account Required</h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <p><strong>Instagram Basic Display API was deprecated on December 4, 2024.</strong></p>
              <p><strong>Instagram Graph API requires business accounts only.</strong></p>
              <p>Personal Instagram accounts will not work with this integration.</p>
              <p>Your Instagram account must be connected to a Facebook Page and converted to a business account.</p>
            </div>
          </div>

          {/* Configuration Status */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Step 1: Check Configuration</h2>
            <button
              onClick={checkConfiguration}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Checking...' : 'Check Instagram Configuration'}
            </button>
            
            {configStatus && (
              <div className={`mt-4 p-4 rounded-md ${configStatus.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <h3 className={`font-medium ${configStatus.success ? 'text-green-800' : 'text-red-800'}`}>
                  Configuration Status: {configStatus.success ? 'CONFIGURED' : 'NOT CONFIGURED'}
                </h3>
                <div className="mt-2 text-sm">
                  <p><strong>Platform:</strong> {configStatus.platform}</p>
                  <p><strong>Enabled:</strong> {configStatus.enabled ? 'Yes' : 'No'}</p>
                  <p><strong>Client ID:</strong> {configStatus.hasClientId ? 'Present' : 'Missing'}</p>
                  <p><strong>Client Secret:</strong> {configStatus.hasClientSecret ? 'Present' : 'Missing'}</p>
                  <p><strong>Scopes:</strong> {configStatus.scopes?.join(', ') || 'None'}</p>
                  <p><strong>Message:</strong> {configStatus.message}</p>
                </div>
              </div>
            )}
          </div>

          {/* Authorization URL Generation */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Step 2: Generate Authorization URL</h2>
            <button
              onClick={generateAuthUrl}
              disabled={loading || !configStatus?.success}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Generating...' : 'Generate Authorization URL'}
            </button>
            
            {authUrl && (
              <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
                <h3 className="font-medium text-gray-900 mb-2">Generated Authorization URL:</h3>
                <div className="bg-white p-3 border rounded text-sm font-mono break-all">
                  {authUrl}
                </div>
                <p className="mt-2 text-sm text-gray-600">
                  This URL will redirect users to Facebook for Instagram business account authorization
                </p>
              </div>
            )}
          </div>

          {/* OAuth Flow */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Step 3: Start OAuth Flow</h2>
            <button
              onClick={startOAuth}
              disabled={!authUrl}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Start Instagram OAuth
            </button>
            <p className="mt-2 text-sm text-gray-600">
              This will redirect you to Facebook for Instagram business account authorization. After approval, you'll be redirected back to the callback URL.
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="font-medium text-red-800">Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          )}

          {/* Test Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="font-medium text-blue-800 mb-2">Test Information</h3>
            <div className="text-sm text-blue-700 space-y-1">
              <p><strong>Test User:</strong> Demo User (3ddaeb03-2d95-4fff-abad-2a2c7dd25037)</p>
              <p><strong>Callback URL:</strong> http://localhost:3001/api/instagram/callback</p>
              <p><strong>Success Redirect:</strong> http://localhost:3001/auth/success?platform=instagram</p>
              <p><strong>Error Redirect:</strong> http://localhost:3001/auth/error?platform=instagram</p>
              <p><strong>Required Scopes:</strong> instagram_basic, instagram_manage_insights, pages_show_list, business_management</p>
              <p><strong>Account Type:</strong> Business accounts only (personal accounts will be rejected)</p>
            </div>
          </div>

          {/* Business Account Requirements */}
          <div className="mt-6 bg-orange-50 border border-orange-200 rounded-md p-4">
            <h3 className="font-medium text-orange-800 mb-2">📋 Business Account Setup Requirements</h3>
            <div className="text-sm text-orange-700 space-y-1">
              <p>1. <strong>Connect Instagram to Facebook Page:</strong> Your Instagram account must be connected to a Facebook Page</p>
              <p>2. <strong>Convert to Business Account:</strong> Switch your Instagram account to a business account</p>
              <p>3. <strong>Verify Business:</strong> Complete Facebook business verification if required</p>
              <p>4. <strong>Grant Permissions:</strong> Approve the requested permissions during OAuth flow</p>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex space-x-4">
              <a
                href="/test-facebook"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                ← Test Facebook OAuth
              </a>
              <a
                href="/test-linkedin"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Test LinkedIn OAuth
              </a>
              <a
                href="/"
                className="text-gray-600 hover:text-gray-800 text-sm font-medium"
              >
                Back to Home
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
