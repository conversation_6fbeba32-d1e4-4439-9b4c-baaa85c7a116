import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Test Facebook account disconnection functionality
 * GET /api/test/facebook-disconnect - List Facebook accounts
 * POST /api/test/facebook-disconnect - Disconnect specific Facebook account
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook disconnect functionality...');

    const results = {
      timestamp: new Date().toISOString(),
      facebookAccounts: [] as any[],
      summary: {} as any
    };

    // Get Facebook accounts from database
    const supabase = createServiceRoleClient();
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    const { data: facebookAccounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .eq('platform', 'FACEBOOK');

    if (error) {
      throw new Error(`Failed to fetch Facebook accounts: ${error.message}`);
    }

    results.facebookAccounts = facebookAccounts?.map(acc => ({
      id: acc.id,
      account_name: acc.account_name,
      account_id: acc.account_id,
      created_at: acc.created_at,
      hasAccessToken: !!acc.access_token,
      tokenLength: acc.access_token?.length || 0
    })) || [];

    results.summary = {
      totalFacebookAccounts: results.facebookAccounts.length,
      accountsReadyForDisconnect: results.facebookAccounts.length,
      disconnectEndpoint: '/api/social/disconnect',
      testDisconnectEndpoint: '/api/test/facebook-disconnect'
    };

    return NextResponse.json({
      success: true,
      message: '🧪 Facebook disconnect test - Account listing',
      results,
      instructions: {
        disconnect: 'Use POST method with accountId to test disconnect functionality',
        example: 'POST /api/test/facebook-disconnect with body: {"accountId": "account-id-here"}'
      }
    });

  } catch (error) {
    console.error('❌ Facebook disconnect test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Facebook disconnect test failed'
    }, { status: 500 });
  }
}

/**
 * Test Facebook account disconnection
 * POST /api/test/facebook-disconnect
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook account disconnection...');

    const body = await request.json();
    const { accountId, dryRun = false } = body;

    if (!accountId) {
      return NextResponse.json({
        error: 'Missing accountId parameter'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      accountId,
      dryRun,
      steps: {} as any
    };

    // Step 1: Verify account exists
    try {
      console.log('Step 1: Verifying Facebook account exists...');
      const supabase = createServiceRoleClient();
      const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
      
      const { data: account, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('id', accountId)
        .eq('user_id', demoUserId)
        .eq('platform', 'FACEBOOK')
        .single();

      if (error || !account) {
        throw new Error(`Facebook account not found: ${error?.message || 'Account does not exist'}`);
      }

      results.steps.accountVerification = {
        success: true,
        account: {
          id: account.id,
          account_name: account.account_name,
          account_id: account.account_id,
          platform: account.platform,
          user_id: account.user_id
        }
      };

      console.log(`✅ Facebook account verified: ${account.account_name}`);

      if (dryRun) {
        results.steps.disconnection = {
          success: true,
          dryRun: true,
          message: 'Dry run completed - account would be disconnected'
        };

        return NextResponse.json({
          success: true,
          message: '✅ Facebook disconnect dry run completed successfully',
          results,
          recommendation: 'Account is ready for disconnection'
        });
      }

      // Step 2: Test actual disconnection
      console.log('Step 2: Testing actual Facebook account disconnection...');
      
      const disconnectResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/social/disconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId })
      });

      if (!disconnectResponse.ok) {
        const errorData = await disconnectResponse.json();
        throw new Error(`Disconnect API failed: ${disconnectResponse.status} - ${errorData.error || 'Unknown error'}`);
      }

      const disconnectResult = await disconnectResponse.json();
      
      results.steps.disconnection = {
        success: true,
        apiResponse: disconnectResult,
        message: 'Facebook account disconnected successfully'
      };

      // Step 3: Verify account was removed
      console.log('Step 3: Verifying Facebook account was removed...');
      
      const { data: verifyAccount, error: verifyError } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('id', accountId)
        .single();

      results.steps.verification = {
        success: !!verifyError && verifyError.code === 'PGRST116', // Account not found
        accountRemoved: !!verifyError && verifyError.code === 'PGRST116',
        verifyError: verifyError?.message
      };

      console.log(`✅ Facebook account disconnection completed`);

      return NextResponse.json({
        success: true,
        message: '🎉 Facebook account disconnected successfully!',
        results,
        summary: {
          accountName: results.steps.accountVerification.account.account_name,
          disconnected: results.steps.disconnection.success,
          removed: results.steps.verification.accountRemoved
        }
      });

    } catch (accountError: any) {
      results.steps.accountVerification = {
        success: false,
        error: accountError.message
      };
      throw accountError;
    }

  } catch (error: any) {
    console.error('❌ Facebook disconnect test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      results,
      details: 'Facebook disconnect test failed'
    }, { status: 500 });
  }
}
