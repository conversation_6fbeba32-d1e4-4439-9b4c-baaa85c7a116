import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { TeamDashboard } from '@/components/teams/team-dashboard';

// Mock fetch
global.fetch = jest.fn();

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
  usePathname: () => '/teams',
}));

describe('Team Dashboard Component Tests', () => {
  const mockOrganizations = [
    {
      id: 'org-123',
      name: 'Test Organization',
      slug: 'test-org',
      description: 'Test organization description',
      subscription_plan: 'free',
      member_count: 5,
      workspace_count: 3,
      user_role: 'owner',
    },
  ];

  const mockWorkspaces = [
    {
      id: 'workspace-123',
      name: 'Test Workspace',
      slug: 'test-workspace',
      description: 'Test workspace description',
      color: '#3b82f6',
      member_count: 3,
      post_count: 15,
      pending_approvals: 2,
      user_role: 'admin',
    },
  ];

  const mockMembers = [
    {
      id: 'member-123',
      user_id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      avatar: null,
      role: 'admin',
      status: 'active',
      added_at: '2024-01-01T00:00:00Z',
    },
  ];

  const mockApprovals = [
    {
      id: 'approval-123',
      post: {
        id: 'post-123',
        content: 'Test post content for approval',
        scheduled_at: '2024-01-02T10:00:00Z',
      },
      status: 'pending',
      current_step: 1,
      submitted_at: '2024-01-01T12:00:00Z',
      can_approve: true,
      current_step_info: {
        name: 'Content Review',
        required_approvers: 1,
      },
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default fetch responses
    (global.fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/teams/organizations')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            organizations: mockOrganizations,
          }),
        });
      }

      if (url.includes('/api/teams/workspaces')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            workspaces: mockWorkspaces,
          }),
        });
      }

      if (url.includes('/api/teams/members')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            members: mockMembers,
          }),
        });
      }

      if (url.includes('/api/teams/approvals')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            approvals: mockApprovals,
          }),
        });
      }

      return Promise.reject(new Error('Unknown URL'));
    });
  });

  describe('Initial Rendering', () => {
    test('should render loading state initially', () => {
      render(<TeamDashboard />);
      expect(screen.getByText('جاري تحميل لوحة الفريق...')).toBeInTheDocument();
    });

    test('should render dashboard after loading organizations', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        expect(screen.getByText('لوحة الفريق')).toBeInTheDocument();
        expect(screen.getByText('إدارة الفرق والتعاون في المحتوى')).toBeInTheDocument();
      }, { timeout: 5000 });
    });

    test('should render empty state when no organizations', async () => {
      (global.fetch as jest.Mock).mockImplementation((url: string) => {
        if (url.includes('/api/teams/organizations')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              organizations: [],
            }),
          });
        }
        return Promise.reject(new Error('Unknown URL'));
      });

      render(<TeamDashboard />);

      await waitFor(() => {
        expect(screen.getByText('لا توجد مؤسسات')).toBeInTheDocument();
        expect(screen.getByText('ابدأ بإنشاء مؤسسة للتعاون مع فريقك')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Organization Management', () => {
    test('should display organization information', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Test Organization')).toBeInTheDocument();
        expect(screen.getByText('Test organization description')).toBeInTheDocument();
        expect(screen.getByText('5')).toBeInTheDocument(); // member count
        expect(screen.getByText('3')).toBeInTheDocument(); // workspace count
        expect(screen.getByText('free')).toBeInTheDocument(); // subscription plan
      });
    });

    test('should allow organization selection', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const orgSelector = screen.getByRole('combobox');
        expect(orgSelector).toBeInTheDocument();
      });
    });
  });

  describe('Workspace Management', () => {
    test('should display workspace cards', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Test Workspace')).toBeInTheDocument();
        expect(screen.getByText('3 أعضاء')).toBeInTheDocument();
        expect(screen.getByText('15 منشورات')).toBeInTheDocument();
        expect(screen.getByText('2 موافقات معلقة')).toBeInTheDocument();
      });
    });

    test('should highlight selected workspace', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const workspaceCard = screen.getByText('Test Workspace').closest('div');
        expect(workspaceCard).toHaveClass('ring-2', 'ring-blue-500');
      });
    });
  });

  describe('Tab Navigation', () => {
    test('should render all tabs', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        expect(screen.getByText('نظرة عامة')).toBeInTheDocument();
        expect(screen.getByText('الأعضاء')).toBeInTheDocument();
        expect(screen.getByText('الموافقات')).toBeInTheDocument();
        expect(screen.getByText('الإعدادات')).toBeInTheDocument();
      });
    });

    test('should switch between tabs', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const membersTab = screen.getByText('الأعضاء');
        fireEvent.click(membersTab);

        expect(screen.getByText('أعضاء الفريق')).toBeInTheDocument();
        expect(screen.getByText('دعوة عضو')).toBeInTheDocument();
      });
    });
  });

  describe('Overview Tab', () => {
    test('should display quick stats', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        expect(screen.getByText('أعضاء الفريق')).toBeInTheDocument();
        expect(screen.getByText('موافقات معلقة')).toBeInTheDocument();
        expect(screen.getByText('منشورات هذا الشهر')).toBeInTheDocument();
      });
    });

    test('should display recent activity', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        expect(screen.getByText('النشاط الأخير')).toBeInTheDocument();
        expect(screen.getByText('طلب موافقة جديد')).toBeInTheDocument();
      });
    });
  });

  describe('Members Tab', () => {
    test('should display team members', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const membersTab = screen.getByText('الأعضاء');
        fireEvent.click(membersTab);
      });

      await waitFor(() => {
        expect(screen.getByText('Test User')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('admin')).toBeInTheDocument();
        expect(screen.getByText('نشط')).toBeInTheDocument();
      });
    });

    test('should show invite member button', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const membersTab = screen.getByText('الأعضاء');
        fireEvent.click(membersTab);
      });

      await waitFor(() => {
        expect(screen.getByText('دعوة عضو')).toBeInTheDocument();
      });
    });
  });

  describe('Approvals Tab', () => {
    test('should display pending approvals', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const approvalsTab = screen.getByText('الموافقات');
        fireEvent.click(approvalsTab);
      });

      await waitFor(() => {
        expect(screen.getByText('طلبات الموافقة')).toBeInTheDocument();
        expect(screen.getByText('طلب موافقة - الخطوة 1')).toBeInTheDocument();
        expect(screen.getByText('Content Review')).toBeInTheDocument();
        expect(screen.getByText('Test post content for approval')).toBeInTheDocument();
      });
    });

    test('should show approval action buttons for authorized users', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const approvalsTab = screen.getByText('الموافقات');
        fireEvent.click(approvalsTab);
      });

      await waitFor(() => {
        expect(screen.getByText('موافقة')).toBeInTheDocument();
        expect(screen.getByText('طلب تعديل')).toBeInTheDocument();
        expect(screen.getByText('رفض')).toBeInTheDocument();
      });
    });

    test('should handle approval decisions', async () => {
      (global.fetch as jest.Mock).mockImplementation((url: string, options?: any) => {
        if (url.includes('/api/teams/approvals') && options?.method === 'POST') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              message: 'Approval decision made successfully',
            }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, approvals: mockApprovals }),
        });
      });

      render(<TeamDashboard />);

      await waitFor(() => {
        const approvalsTab = screen.getByText('الموافقات');
        fireEvent.click(approvalsTab);
      });

      await waitFor(() => {
        const approveButton = screen.getByText('موافقة');
        fireEvent.click(approveButton);
      });

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          '/api/teams/approvals',
          expect.objectContaining({
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: expect.stringContaining('"decision":"approve"'),
          })
        );
      });
    });
  });

  describe('Settings Tab', () => {
    test('should display workspace settings', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const settingsTab = screen.getByText('الإعدادات');
        fireEvent.click(settingsTab);
      });

      await waitFor(() => {
        expect(screen.getByText('إعدادات مساحة العمل')).toBeInTheDocument();
        expect(screen.getByText('إدارة إعدادات وصلاحيات مساحة العمل')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test Workspace')).toBeInTheDocument();
      });
    });

    test('should show save changes button', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const settingsTab = screen.getByText('الإعدادات');
        fireEvent.click(settingsTab);
      });

      await waitFor(() => {
        expect(screen.getByText('حفظ التغييرات')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      (global.fetch as jest.Mock).mockImplementation(() => {
        return Promise.reject(new Error('API Error'));
      });

      const { toast } = require('sonner');

      render(<TeamDashboard />);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('فشل في تحميل المؤسسات');
      });
    });

    test('should handle approval decision errors', async () => {
      (global.fetch as jest.Mock).mockImplementation((url: string, options?: any) => {
        if (url.includes('/api/teams/approvals') && options?.method === 'POST') {
          return Promise.resolve({
            ok: false,
            json: () => Promise.resolve({
              success: false,
              error: 'Insufficient permissions',
            }),
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, approvals: mockApprovals }),
        });
      });

      const { toast } = require('sonner');

      render(<TeamDashboard />);

      await waitFor(() => {
        const approvalsTab = screen.getByText('الموافقات');
        fireEvent.click(approvalsTab);
      });

      await waitFor(() => {
        const approveButton = screen.getByText('موافقة');
        fireEvent.click(approveButton);
      });

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Insufficient permissions');
      });
    });
  });

  describe('Responsive Design', () => {
    test('should adapt to mobile viewport', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<TeamDashboard />);

      await waitFor(() => {
        const dashboard = screen.getByText('لوحة الفريق').closest('div');
        expect(dashboard).toHaveAttribute('dir', 'rtl');
      });
    });
  });

  describe('Accessibility', () => {
    test('should have proper ARIA labels', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const orgSelector = screen.getByRole('combobox');
        expect(orgSelector).toHaveAttribute('aria-expanded');
      });
    });

    test('should support keyboard navigation', async () => {
      render(<TeamDashboard />);

      await waitFor(() => {
        const membersTab = screen.getByText('الأعضاء');
        membersTab.focus();
        fireEvent.keyDown(membersTab, { key: 'Enter' });

        expect(screen.getByText('أعضاء الفريق')).toBeInTheDocument();
      });
    });
  });
});
