"use client";

import { Suspense } from "react";
import { ResetPasswordForm } from "@/components/auth/reset-password-form";

export default function ResetPasswordPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-lg">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <span className="text-3xl font-bold text-white">eW</span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
            eWasl
          </h1>
          <p className="text-gray-600 text-lg">منصة إدارة وسائل التواصل الاجتماعي</p>
          <p className="text-sm text-gray-500 mt-2">إعادة تعيين كلمة المرور</p>
        </div>

        {/* Reset Password Form */}
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-10 border border-white/50">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">إعادة تعيين كلمة المرور</h2>
            <p className="text-gray-600 text-lg">أدخل كلمة المرور الجديدة</p>
          </div>

          <Suspense fallback={<div>جاري التحميل...</div>}>
            <ResetPasswordForm />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
