import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { RecurringManager } from '@/lib/scheduling/recurring-manager';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for creating recurring posts
const createRecurringSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  content: z.string().min(1, 'Content is required').max(2000, 'Content must be less than 2000 characters'),
  mediaUrl: z.string().url('Invalid media URL').optional(),
  platforms: z.array(z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'])).min(1, 'At least one platform is required'),
  pattern: z.object({
    type: z.enum(['daily', 'weekly', 'monthly', 'custom']),
    config: z.object({
      interval: z.number().min(1).max(365).optional(),
      time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)').optional(),
      days: z.array(z.enum(['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'])).optional(),
      dayOfMonth: z.number().min(1).max(31).optional(),
      dates: z.array(z.string().datetime()).optional(),
    }),
  }),
  startDate: z.string().datetime('Invalid start date'),
  endDate: z.string().datetime('Invalid end date').optional(),
  timezone: z.string().default('UTC'),
});

// POST /api/posts/recurring - Create recurring post
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Creating recurring post...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = createRecurringSchema.safeParse(body);

    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validation.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    // Additional validation for pattern-specific requirements
    if (data.pattern.type === 'daily' && !data.pattern.config.interval) {
      return NextResponse.json(
        { error: 'Daily pattern requires interval' },
        { status: 400 }
      );
    }

    if (data.pattern.type === 'weekly' && (!data.pattern.config.days || data.pattern.config.days.length === 0)) {
      return NextResponse.json(
        { error: 'Weekly pattern requires at least one day' },
        { status: 400 }
      );
    }

    if (data.pattern.type === 'monthly' && !data.pattern.config.dayOfMonth) {
      return NextResponse.json(
        { error: 'Monthly pattern requires day of month' },
        { status: 400 }
      );
    }

    if (data.pattern.type === 'custom' && (!data.pattern.config.dates || data.pattern.config.dates.length === 0)) {
      return NextResponse.json(
        { error: 'Custom pattern requires at least one date' },
        { status: 400 }
      );
    }

    // Validate date range
    const startDate = new Date(data.startDate);
    const endDate = data.endDate ? new Date(data.endDate) : undefined;

    if (startDate < new Date()) {
      return NextResponse.json(
        { error: 'Start date cannot be in the past' },
        { status: 400 }
      );
    }

    if (endDate && endDate <= startDate) {
      return NextResponse.json(
        { error: 'End date must be after start date' },
        { status: 400 }
      );
    }

    // Create recurring post using RecurringManager
    const recurringManager = new RecurringManager();
    const result = await recurringManager.createRecurringPost(user.id, {
      title: data.title,
      content: data.content,
      mediaUrl: data.mediaUrl,
      platforms: data.platforms,
      pattern: data.pattern,
      startDate,
      endDate,
      timezone: data.timezone,
    });

    console.log(`Created recurring post with ${result.previewCount} generated posts`);

    return NextResponse.json({
      success: true,
      data: {
        recurringPost: {
          id: result.recurringPost.id,
          title: result.recurringPost.title,
          content: result.recurringPost.content,
          platforms: result.recurringPost.platforms,
          patternType: result.recurringPost.pattern_type,
          patternConfig: result.recurringPost.pattern_config,
          startDate: result.recurringPost.start_date,
          endDate: result.recurringPost.end_date,
          timezone: result.recurringPost.timezone,
          isActive: result.recurringPost.is_active,
          postsGenerated: result.recurringPost.posts_generated,
          createdAt: result.recurringPost.created_at,
        },
        generatedPosts: result.generatedPosts,
        previewCount: result.previewCount,
      },
      message: `تم إنشاء المنشور المتكرر بنجاح! سيتم إنشاء ${result.previewCount} منشور`,
    });

  } catch (error: any) {
    console.error('Error creating recurring post:', error);
    return NextResponse.json(
      {
        error: 'Failed to create recurring post',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// GET /api/posts/recurring - List user's recurring posts
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching recurring posts...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status'); // 'active', 'inactive', 'all'

    // Get recurring posts using RecurringManager
    const recurringManager = new RecurringManager();
    const recurringPosts = await recurringManager.getUserRecurringPosts(user.id);

    // Filter by status if specified
    let filteredPosts = recurringPosts;
    if (status === 'active') {
      filteredPosts = recurringPosts.filter(post => post.isActive);
    } else if (status === 'inactive') {
      filteredPosts = recurringPosts.filter(post => !post.isActive);
    }

    // Apply pagination
    const paginatedPosts = filteredPosts.slice(offset, offset + limit);

    // Get statistics for each recurring post
    const postsWithStats = await Promise.all(
      paginatedPosts.map(async (post) => {
        // Get count of generated posts
        const { count: scheduledCount } = await supabase
          .from('posts')
          .select('*', { count: 'exact', head: true })
          .eq('parent_post_id', post.id)
          .eq('status', 'SCHEDULED');

        const { count: publishedCount } = await supabase
          .from('posts')
          .select('*', { count: 'exact', head: true })
          .eq('parent_post_id', post.id)
          .eq('status', 'PUBLISHED');

        return {
          ...post,
          statistics: {
            scheduledPosts: scheduledCount || 0,
            publishedPosts: publishedCount || 0,
            totalGenerated: post.postsGenerated,
          },
        };
      })
    );

    console.log(`Found ${filteredPosts.length} recurring posts for user`);

    return NextResponse.json({
      success: true,
      data: {
        recurringPosts: postsWithStats,
        pagination: {
          total: filteredPosts.length,
          limit,
          offset,
          hasMore: offset + limit < filteredPosts.length,
        },
        summary: {
          totalRecurringPosts: recurringPosts.length,
          activeRecurringPosts: recurringPosts.filter(p => p.isActive).length,
          inactiveRecurringPosts: recurringPosts.filter(p => !p.isActive).length,
        },
      },
    });

  } catch (error: any) {
    console.error('Error fetching recurring posts:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch recurring posts',
        details: error.message,
      },
      { status: 500 }
    );
  }
}


