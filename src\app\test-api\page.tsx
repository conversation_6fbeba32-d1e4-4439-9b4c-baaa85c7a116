'use client';

import { useState } from 'react';

export default function TestAPIPage() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const testAPI = async (endpoint: string, method: string = 'GET', body?: any) => {
    setLoading(true);
    try {
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      const data = await response.json();
      const result = {
        endpoint,
        method,
        status: response.status,
        success: response.ok,
        data,
        timestamp: new Date().toLocaleString('ar-SA'),
      };

      setResults(prev => [result, ...prev]);
      return result;
    } catch (error: any) {
      const result = {
        endpoint,
        method,
        status: 'ERROR',
        success: false,
        data: { error: error.message },
        timestamp: new Date().toLocaleString('ar-SA'),
      };
      setResults(prev => [result, ...prev]);
      return result;
    } finally {
      setLoading(false);
    }
  };

  const testConnectionAPI = () => {
    testAPI('/api/social-accounts/test-connection', 'POST', {
      platform: 'TWITTER',
      accessToken: 'test_token',
      accountId: 'test_account'
    });
  };

  const testPublishAPI = () => {
    testAPI('/api/posts/test-publish', 'POST', {
      platform: 'TWITTER',
      content: '[اختبار] مرحباً من منصة eWasl! 🚀',
      accessToken: 'test_token',
      isTest: true
    });
  };

  const testHealthAPI = () => {
    testAPI('/api/health');
  };

  const testSocialConnectAPI = () => {
    testAPI('/api/social/connect');
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      padding: '2rem',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          marginBottom: '2rem'
        }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            background: 'linear-gradient(to right, #2563eb, #9333ea)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: '0.5rem'
          }}>
            🧪 اختبار APIs - eWasl
          </h1>
          <p style={{ color: '#6b7280', fontSize: '1rem' }}>
            اختبار مباشر لجميع نقاط النهاية الخاصة بـ APIs
          </p>
        </div>

        {/* Test Buttons */}
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          marginBottom: '2rem'
        }}>
          <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>
            اختبارات API
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem'
          }}>
            <button
              onClick={testHealthAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: 'linear-gradient(to right, #10b981, #059669)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.5 : 1
              }}
            >
              🏥 اختبار Health API
            </button>

            <button
              onClick={testSocialConnectAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: 'linear-gradient(to right, #3b82f6, #1d4ed8)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.5 : 1
              }}
            >
              🔗 اختبار Social Connect API
            </button>

            <button
              onClick={testConnectionAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: 'linear-gradient(to right, #8b5cf6, #7c3aed)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.5 : 1
              }}
            >
              🧪 اختبار Connection API
            </button>

            <button
              onClick={testPublishAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: 'linear-gradient(to right, #f59e0b, #d97706)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.5 : 1
              }}
            >
              📤 اختبار Publish API
            </button>
          </div>

          {loading && (
            <div style={{
              marginTop: '1rem',
              padding: '1rem',
              background: '#eff6ff',
              borderRadius: '0.5rem',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>⏳</div>
              <p style={{ color: '#1d4ed8', fontWeight: '500' }}>جاري تنفيذ الاختبار...</p>
            </div>
          )}
        </div>

        {/* Results */}
        {results.length > 0 && (
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '1rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>
              📊 نتائج الاختبارات
            </h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {results.map((result, index) => (
                <div
                  key={index}
                  style={{
                    padding: '1rem',
                    border: `2px solid ${result.success ? '#10b981' : '#ef4444'}`,
                    borderRadius: '0.5rem',
                    background: result.success ? '#f0fdf4' : '#fef2f2'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '0.5rem'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <span style={{ fontSize: '1.2rem' }}>
                        {result.success ? '✅' : '❌'}
                      </span>
                      <strong>{result.method} {result.endpoint}</strong>
                    </div>
                    <div style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '0.25rem',
                      background: result.success ? '#10b981' : '#ef4444',
                      color: 'white',
                      fontSize: '0.875rem'
                    }}>
                      {result.status}
                    </div>
                  </div>
                  <div style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
                    {result.timestamp}
                  </div>
                  <pre style={{
                    background: '#f9fafb',
                    padding: '1rem',
                    borderRadius: '0.25rem',
                    fontSize: '0.875rem',
                    overflow: 'auto',
                    maxHeight: '200px'
                  }}>
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Clear Results */}
        {results.length > 0 && (
          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              onClick={() => setResults([])}
              style={{
                padding: '0.75rem 1.5rem',
                background: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              🗑️ مسح النتائج
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
