'use client';

import { useState } from 'react';
import SubscriptionPlans from '@/components/billing/SubscriptionPlans';
import BillingDashboard from '@/components/billing/BillingDashboard';

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'plans'>('dashboard');
  const [isRTL, setIsRTL] = useState(false);

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                {isRTL ? 'إدارة الفواتير' : 'Billing Management'}
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Language Toggle */}
              <button
                onClick={() => setIsRTL(!isRTL)}
                className="bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                {isRTL ? 'English' : 'العربية'}
              </button>
              
              {/* Home Link */}
              <a
                href="/"
                className="text-gray-600 hover:text-gray-900 text-sm font-medium"
              >
                {isRTL ? 'الرئيسية' : 'Home'}
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'dashboard'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {isRTL ? 'لوحة التحكم' : 'Dashboard'}
            </button>
            <button
              onClick={() => setActiveTab('plans')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'plans'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {isRTL ? 'خطط الاشتراك' : 'Subscription Plans'}
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && (
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                {isRTL ? 'نظرة عامة على الاشتراك' : 'Subscription Overview'}
              </h2>
              <p className="text-gray-600 mt-1">
                {isRTL 
                  ? 'إدارة اشتراكك ومراقبة الاستخدام وتاريخ الفواتير'
                  : 'Manage your subscription, monitor usage, and view billing history'
                }
              </p>
            </div>
            <BillingDashboard isRTL={isRTL} />
          </div>
        )}

        {activeTab === 'plans' && (
          <div>
            <div className="mb-6 text-center">
              <h2 className="text-3xl font-bold text-gray-900">
                {isRTL ? 'اختر الخطة المناسبة لك' : 'Choose the Right Plan for You'}
              </h2>
              <p className="text-xl text-gray-600 mt-2">
                {isRTL 
                  ? 'خطط مرنة تنمو مع احتياجاتك في إدارة وسائل التواصل الاجتماعي'
                  : 'Flexible plans that grow with your social media management needs'
                }
              </p>
            </div>
            <SubscriptionPlans currentPlan="free" isRTL={isRTL} />
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">
                {isRTL ? 'الدعم' : 'Support'}
              </h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  <a href="/help" className="hover:text-gray-900">
                    {isRTL ? 'مركز المساعدة' : 'Help Center'}
                  </a>
                </li>
                <li>
                  <a href="/contact" className="hover:text-gray-900">
                    {isRTL ? 'تواصل معنا' : 'Contact Us'}
                  </a>
                </li>
                <li>
                  <a href="/docs" className="hover:text-gray-900">
                    {isRTL ? 'الوثائق' : 'Documentation'}
                  </a>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">
                {isRTL ? 'الشركة' : 'Company'}
              </h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  <a href="/about" className="hover:text-gray-900">
                    {isRTL ? 'من نحن' : 'About Us'}
                  </a>
                </li>
                <li>
                  <a href="/privacy" className="hover:text-gray-900">
                    {isRTL ? 'سياسة الخصوصية' : 'Privacy Policy'}
                  </a>
                </li>
                <li>
                  <a href="/terms" className="hover:text-gray-900">
                    {isRTL ? 'شروط الخدمة' : 'Terms of Service'}
                  </a>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">
                {isRTL ? 'الميزات' : 'Features'}
              </h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  <a href="/features/scheduling" className="hover:text-gray-900">
                    {isRTL ? 'جدولة المنشورات' : 'Post Scheduling'}
                  </a>
                </li>
                <li>
                  <a href="/features/analytics" className="hover:text-gray-900">
                    {isRTL ? 'التحليلات' : 'Analytics'}
                  </a>
                </li>
                <li>
                  <a href="/features/ai" className="hover:text-gray-900">
                    {isRTL ? 'المحتوى الذكي' : 'AI Content'}
                  </a>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-200 mt-8 pt-8 text-center">
            <p className="text-sm text-gray-600">
              {isRTL 
                ? '© 2024 eWasl. جميع الحقوق محفوظة.'
                : '© 2024 eWasl. All rights reserved.'
              }
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
