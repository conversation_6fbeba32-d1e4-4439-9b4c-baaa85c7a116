// =====================================================
// eWasl Advanced Scheduling System - TypeScript Types
// =====================================================

export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  config: {
    // Daily pattern
    interval?: number;
    time?: string;
    
    // Weekly pattern
    days?: string[];
    
    // Monthly pattern
    dayOfMonth?: number;
    
    // Custom pattern
    dates?: string[];
  };
}

export interface RecurringPost {
  id: string;
  userId: string;
  title: string;
  content: string;
  mediaUrl?: string;
  platforms: string[];
  patternType: 'daily' | 'weekly' | 'monthly' | 'custom';
  patternConfig: RecurringPattern['config'];
  startDate: Date;
  endDate?: Date;
  timezone: string;
  isActive: boolean;
  postsGenerated: number;
  lastGeneratedAt?: Date;
  nextGenerationAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateRecurringPostData {
  title: string;
  content: string;
  mediaUrl?: string;
  platforms: string[];
  pattern: RecurringPattern;
  startDate: Date;
  endDate?: Date;
  timezone: string;
}

export interface BulkOperation {
  id: string;
  userId: string;
  operationType: 'import' | 'schedule' | 'update' | 'delete';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  sourceFileUrl?: string;
  operationConfig?: Record<string, any>;
  errorLog: any[];
  resultSummary?: Record<string, any>;
  processingLog: any[];
  startedAt?: Date;
  completedAt?: Date;
  estimatedCompletionAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface BulkScheduleItem {
  content: string;
  mediaUrl?: string;
  platforms: string[];
  scheduledAt: Date;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface BulkImportResult {
  operation: BulkOperation;
  validItems: BulkScheduleItem[];
  invalidItems: Array<{
    item: any;
    errors: string[];
  }>;
  summary: {
    totalItems: number;
    validItems: number;
    invalidItems: number;
    estimatedProcessingTime: number;
  };
}

export interface OptimalTime {
  id: string;
  userId: string;
  platform: string;
  hourOfDay: number;
  dayOfWeek: number;
  engagementScore: number;
  postCount: number;
  avgLikes: number;
  avgComments: number;
  avgShares: number;
  avgClicks: number;
  avgImpressions: number;
  lastAnalyzedAt: Date;
  sampleSize: number;
  confidenceLevel: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface OptimalTimeAnalysis {
  platform: string;
  recommendedTimes: Array<{
    time: string;
    score: number;
    reasoning: string;
    confidence: number;
  }>;
  audienceInsights: {
    timezone: string;
    activeHours: string[];
    peakDays: string[];
  };
  historicalPerformance: {
    bestPerformingTime: string;
    averageEngagement: number;
    sampleSize: number;
  };
}

export interface TimeRecommendation {
  platform: string;
  recommendedTime: Date;
  score: number;
  reasoning: string;
  alternatives: Array<{
    time: Date;
    score: number;
  }>;
}

export interface SchedulingConflict {
  conflictType: 'time_overlap' | 'platform_limit' | 'rate_limit';
  message: string;
  affectedPosts: string[];
  suggestedResolution: {
    action: 'reschedule' | 'merge' | 'delay';
    newTime?: Date;
    reason: string;
  };
}

export interface CalendarPost {
  id: string;
  content: string;
  platforms: string[];
  scheduledAt: Date;
  status: 'scheduled' | 'published' | 'failed' | 'cancelled';
  isRecurring: boolean;
  parentRecurringId?: string;
  optimalTimeScore?: number;
  conflictWarnings?: SchedulingConflict[];
}

export interface CalendarView {
  posts: CalendarPost[];
  conflicts: SchedulingConflict[];
  optimalSlots: Array<{
    time: Date;
    platforms: string[];
    score: number;
  }>;
  statistics: {
    totalPosts: number;
    scheduledPosts: number;
    recurringPosts: number;
    conflictCount: number;
  };
}

// CSV Import Types
export interface CSVImportTemplate {
  headers: string[];
  example: string[];
  validation: Record<string, {
    required: boolean;
    maxLength?: number;
    format?: string;
    validValues?: string[];
  }>;
}

export interface CSVValidationError {
  row: number;
  column: string;
  value: any;
  error: string;
}

export interface CSVImportResult {
  isValid: boolean;
  validRows: BulkScheduleItem[];
  invalidRows: Array<{
    row: number;
    data: any;
    errors: CSVValidationError[];
  }>;
  summary: {
    totalRows: number;
    validRows: number;
    invalidRows: number;
    warnings: string[];
  };
}

// Pattern Generation Types
export interface PatternGenerationOptions {
  maxPosts?: number;
  maxDays?: number;
  skipWeekends?: boolean;
  skipHolidays?: boolean;
  respectOptimalTimes?: boolean;
  avoidConflicts?: boolean;
}

export interface GeneratedPostSchedule {
  dates: Date[];
  conflicts: SchedulingConflict[];
  optimizations: Array<{
    originalTime: Date;
    optimizedTime: Date;
    reason: string;
    scoreImprovement: number;
  }>;
  statistics: {
    totalPosts: number;
    optimizedPosts: number;
    conflictsPrevented: number;
    averageOptimalScore: number;
  };
}

// API Response Types
export interface RecurringPostResponse {
  success: boolean;
  data?: {
    recurringPost: RecurringPost;
    generatedPosts: CalendarPost[];
    previewCount: number;
  };
  error?: string;
  details?: any;
}

export interface BulkOperationResponse {
  success: boolean;
  data?: BulkOperation;
  error?: string;
  details?: any;
}

export interface OptimalTimesResponse {
  success: boolean;
  data?: OptimalTimeAnalysis[];
  error?: string;
  details?: any;
}

// Validation Schemas (for Zod)
export const RECURRING_PATTERN_TYPES = ['daily', 'weekly', 'monthly', 'custom'] as const;
export const BULK_OPERATION_TYPES = ['import', 'schedule', 'update', 'delete'] as const;
export const BULK_OPERATION_STATUSES = ['pending', 'processing', 'completed', 'failed', 'cancelled'] as const;
export const SUPPORTED_PLATFORMS = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'] as const;

// Constants
export const CSV_TEMPLATE: CSVImportTemplate = {
  headers: ['content', 'platforms', 'scheduled_date', 'scheduled_time', 'media_url', 'tags'],
  example: [
    'Hello from eWasl! 🚀',
    'twitter,facebook',
    '2025-02-01',
    '09:00',
    'https://example.com/image.jpg',
    'marketing,social'
  ],
  validation: {
    content: { required: true, maxLength: 2000 },
    platforms: { required: true, validValues: ['twitter', 'facebook', 'instagram', 'linkedin'] },
    scheduled_date: { required: true, format: 'YYYY-MM-DD' },
    scheduled_time: { required: true, format: 'HH:MM' },
    media_url: { required: false, format: 'url' },
    tags: { required: false, format: 'comma-separated' }
  }
};

export const DEFAULT_PATTERN_CONFIGS = {
  daily: { interval: 1, time: '09:00' },
  weekly: { days: ['monday', 'wednesday', 'friday'], time: '14:00' },
  monthly: { dayOfMonth: 15, time: '10:00' },
  custom: { dates: [] }
};

export const TIMEZONE_OPTIONS = [
  { value: 'UTC', label: 'UTC' },
  { value: 'Asia/Riyadh', label: 'الرياض (GMT+3)' },
  { value: 'Asia/Dubai', label: 'دبي (GMT+4)' },
  { value: 'Africa/Cairo', label: 'القاهرة (GMT+2)' },
  { value: 'Europe/London', label: 'لندن (GMT+0)' },
  { value: 'America/New_York', label: 'نيويورك (GMT-5)' },
];

export const WEEKDAY_OPTIONS = [
  { value: 'sunday', label: 'الأحد' },
  { value: 'monday', label: 'الإثنين' },
  { value: 'tuesday', label: 'الثلاثاء' },
  { value: 'wednesday', label: 'الأربعاء' },
  { value: 'thursday', label: 'الخميس' },
  { value: 'friday', label: 'الجمعة' },
  { value: 'saturday', label: 'السبت' },
];
