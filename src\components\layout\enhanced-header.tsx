"use client";

import React, { useState } from 'react';
import { Search, User, LogOut, Menu, Globe, ChevronDown, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import NotificationDropdown from '@/components/ui/notification-dropdown';
import { useNotifications } from '@/hooks/useNotifications';
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface EnhancedHeaderProps {
  language: 'ar' | 'en';
  title: string;
  onMobileMenuToggle?: () => void;
  onLanguageChange?: (lang: 'ar' | 'en') => void;
  className?: string;
}

const EnhancedHeader = ({ 
  language, 
  title, 
  onMobileMenuToggle, 
  onLanguageChange,
  className 
}: EnhancedHeaderProps) => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();

  const handleLogout = async () => {
    const supabase = createClient();
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      toast.error(language === 'ar' ? 'فشل في تسجيل الخروج' : 'Failed to logout');
      return;
    }
    
    toast.success(language === 'ar' ? 'تم تسجيل الخروج بنجاح' : 'Logged out successfully');
    router.push('/auth/signin');
  };

  const handleLanguageChange = (newLang: 'ar' | 'en') => {
    if (onLanguageChange) {
      onLanguageChange(newLang);
    }
  };

  return (
    <header className={cn(
      "bg-white/95 backdrop-blur-sm border-b border-gray-200 px-3 sm:px-4 md:px-6 py-3 sm:py-4 shadow-sm",
      language === 'ar' ? "text-right" : "text-left",
      className
    )}>
      <div className={cn(
        "flex items-center justify-between gap-3",
        language === 'ar' ? "flex-row-reverse" : ""
      )}>
        {/* Left Section - Mobile Menu & Logo */}
        <div className={cn(
          "flex items-center gap-3",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          {/* Mobile menu button - only visible on mobile */}
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={onMobileMenuToggle}
            className="lg:hidden rounded-xl hover:bg-gray-100"
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          {/* Enhanced Logo */}
          <div className={cn(
            "flex items-center gap-3",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-2 shadow-lg">
              <span className="text-white font-bold text-lg">eW</span>
            </div>
            <div className={cn(
              "flex flex-col",
              language === 'ar' ? "text-right" : "text-left"
            )}>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                إي وصل
              </h1>
              <p className="text-sm text-gray-500 -mt-1">إدارة ذكية</p>
            </div>
          </div>
        </div>

        {/* Center Section - Global Search */}
        <div className="flex-1 max-w-md mx-4 hidden md:block">
          <div className="relative">
            <Search className={cn(
              "absolute top-2.5 h-4 w-4 text-gray-400",
              language === 'ar' ? "right-3" : "left-3"
            )} />
            <Input
              placeholder={language === 'ar' ? 'البحث...' : 'Search...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={cn(
                "w-full rounded-lg bg-gray-50 border-gray-200 focus:border-purple-300 focus:ring-purple-200 text-sm transition-all duration-200",
                language === 'ar' ? "pr-10 text-right" : "pl-10 text-left"
              )}
            />
          </div>
        </div>
        
        {/* Right Section - Actions */}
        <div className={cn(
          "flex items-center gap-3",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          {/* Language Toggle with Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm"
                className="rounded-lg border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200"
              >
                <Globe className="h-4 w-4" />
                <span className="hidden sm:inline font-medium">
                  {language === 'ar' ? 'العربية' : 'English'}
                </span>
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={language === 'ar' ? 'start' : 'end'} className="bg-white shadow-lg border border-gray-200">
              <DropdownMenuItem 
                onClick={() => handleLanguageChange('ar')}
                className="gap-2 hover:bg-gray-50"
              >
                <span>🇸🇦</span>
                <span>العربية</span>
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleLanguageChange('en')}
                className="gap-2 hover:bg-gray-50"
              >
                <span>🇺🇸</span>
                <span>English</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Notifications */}
          <NotificationDropdown
            notifications={notifications}
            unreadCount={unreadCount}
            onMarkAsRead={markAsRead}
            onMarkAllAsRead={markAllAsRead}
            language={language}
          />

          {/* Profile - Hidden on small mobile */}
          <Button 
            variant="ghost" 
            size="icon" 
            className="hidden sm:flex rounded-lg hover:bg-gray-100 h-9 w-9 sm:h-10 sm:w-10 transition-colors duration-200"
          >
            <User className="h-4 w-4 sm:h-5 sm:w-5" />
          </Button>

          {/* Logout */}
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={handleLogout} 
            title={language === 'ar' ? 'تسجيل الخروج' : 'Logout'}
            className="rounded-lg hover:bg-red-50 hover:text-red-600 transition-all duration-200 h-9 w-9 sm:h-10 sm:w-10"
          >
            <LogOut className="h-4 w-4 sm:h-5 sm:w-5" />
          </Button>
        </div>
      </div>
    </header>
  );
};

export default EnhancedHeader;
