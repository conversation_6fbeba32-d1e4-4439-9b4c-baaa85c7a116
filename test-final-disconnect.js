#!/usr/bin/env node

/**
 * 🔌 FINAL DISCONNECT TEST - Testing Twitter Account Removal
 */

const BASE_URL = 'https://app.ewasl.com';

async function testFinalDisconnect() {
  console.log('🔌 Final Disconnect Test - Testing Twitter Account Removal...\n');

  try {
    // Get current accounts
    console.log('📋 Getting current accounts...');
    const accountsResponse = await fetch(`${BASE_URL}/api/social/connect`);
    const accountsData = await accountsResponse.json();
    
    console.log('Current accounts:', accountsData.connectedAccounts?.map(acc => ({
      platform: acc.platform,
      name: acc.account_name,
      id: acc.id
    })));

    // Find Twitter account
    const twitterAccount = accountsData.connectedAccounts?.find(acc => acc.platform === 'TWITTER');
    
    if (!twitterAccount) {
      console.log('❌ No Twitter account found');
      return;
    }

    console.log(`\n🐦 Disconnecting Twitter account: ${twitterAccount.account_name}`);
    
    // Disconnect Twitter account
    const disconnectResponse = await fetch(`${BASE_URL}/api/social/disconnect`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ accountId: twitterAccount.id })
    });

    const disconnectData = await disconnectResponse.json();
    
    console.log('📡 Disconnect result:', {
      status: disconnectResponse.status,
      success: disconnectData.success,
      message: disconnectData.message
    });

    if (disconnectData.success) {
      console.log('✅ Twitter account disconnected successfully!');
      
      // Verify final state
      const finalResponse = await fetch(`${BASE_URL}/api/social/connect`);
      const finalData = await finalResponse.json();
      
      console.log('\n📊 Final state:', {
        connectedAccounts: finalData.connectedAccounts?.length || 0,
        platforms: finalData.connectedAccounts?.map(acc => acc.platform) || [],
        availablePlatforms: finalData.availablePlatforms?.length || 0
      });
      
      console.log('\n🎯 ALL DISCONNECT TESTS COMPLETED SUCCESSFULLY! 🎉');
    } else {
      console.log('❌ Twitter disconnect failed:', disconnectData.error);
    }

  } catch (error) {
    console.error('💥 Test error:', error.message);
  }
}

testFinalDisconnect();
