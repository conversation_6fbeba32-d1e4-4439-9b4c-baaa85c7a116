'use client';

import { useState } from 'react';

export default function DirectAPITestPage() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setResults(prev => [`[${timestamp}] ${message}`, ...prev]);
  };

  const testAPI = async (name: string, url: string, options?: RequestInit) => {
    setIsLoading(true);
    addResult(`🔄 Testing ${name}...`);
    
    try {
      const response = await fetch(url, options);
      const data = await response.json();
      
      if (response.ok) {
        addResult(`✅ ${name} SUCCESS: ${JSON.stringify(data)}`);
      } else {
        addResult(`❌ ${name} FAILED (${response.status}): ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addResult(`❌ ${name} ERROR: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const runAllTests = async () => {
    setResults([]);
    addResult('🚀 Starting comprehensive API testing...');
    
    // Test 1: Health API
    await testAPI('Health API', '/api/health');
    
    // Test 2: Social Connect API
    await testAPI('Social Connect API', '/api/social/connect');
    
    // Test 3: Test Connection API
    await testAPI('Test Connection API', '/api/social-accounts/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'TWITTER',
        accessToken: 'test_token_123',
        accountId: 'test_account'
      })
    });
    
    // Test 4: Test Publish API
    await testAPI('Test Publish API', '/api/posts/test-publish', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'TWITTER',
        content: '[TEST] Hello from eWasl Priority 2 Testing! 🚀',
        accessToken: 'test_token_123',
        isTest: true
      })
    });
    
    addResult('🎯 All API tests completed!');
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '2rem',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          marginBottom: '2rem',
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: '0.5rem'
          }}>
            🧪 Priority 2 API Testing
          </h1>
          <p style={{ color: '#666', fontSize: '1.1rem' }}>
            Complete Core Publishing - API Integration Verification
          </p>
        </div>

        {/* Test Controls */}
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          marginBottom: '2rem'
        }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#333' }}>
            🚀 API Test Suite
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            marginBottom: '1.5rem'
          }}>
            <button
              onClick={() => testAPI('Health API', '/api/health')}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #4CAF50, #45a049)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🏥 Health API
            </button>

            <button
              onClick={() => testAPI('Social Connect', '/api/social/connect')}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #2196F3, #1976D2)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🔗 Social Connect
            </button>

            <button
              onClick={() => testAPI('Test Connection', '/api/social-accounts/test-connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  platform: 'TWITTER',
                  accessToken: 'test_token_123',
                  accountId: 'test_account'
                })
              })}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #9C27B0, #7B1FA2)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🧪 Test Connection
            </button>

            <button
              onClick={() => testAPI('Test Publish', '/api/posts/test-publish', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  platform: 'TWITTER',
                  content: '[TEST] Hello from eWasl! 🚀',
                  accessToken: 'test_token_123',
                  isTest: true
                })
              })}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #FF9800, #F57C00)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              📤 Test Publish
            </button>
          </div>

          <button
            onClick={runAllTests}
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '1rem',
              background: isLoading ? '#ccc' : 'linear-gradient(45deg, #E91E63, #C2185B)',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              fontWeight: 'bold',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontSize: '1.1rem'
            }}
          >
            {isLoading ? '⏳ Running Tests...' : '🎯 Run All Tests'}
          </button>
        </div>

        {/* Results */}
        {results.length > 0 && (
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '1rem',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', color: '#333', margin: 0 }}>
                📊 Test Results
              </h2>
              <button
                onClick={() => setResults([])}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#f44336',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}
              >
                🗑️ Clear
              </button>
            </div>
            
            <div style={{
              maxHeight: '500px',
              overflowY: 'auto',
              background: '#f5f5f5',
              padding: '1rem',
              borderRadius: '0.5rem',
              fontFamily: 'monospace'
            }}>
              {results.map((result, index) => (
                <div
                  key={index}
                  style={{
                    padding: '0.5rem',
                    marginBottom: '0.5rem',
                    background: result.includes('✅') ? '#e8f5e8' : 
                               result.includes('❌') ? '#ffeaea' : 
                               result.includes('🔄') ? '#fff3cd' : '#fff',
                    borderRadius: '0.25rem',
                    fontSize: '0.9rem',
                    borderLeft: result.includes('✅') ? '4px solid #4CAF50' :
                               result.includes('❌') ? '4px solid #f44336' :
                               result.includes('🔄') ? '4px solid #ff9800' : '4px solid #ddd'
                  }}
                >
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status */}
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          background: 'rgba(255,255,255,0.9)',
          borderRadius: '0.5rem',
          textAlign: 'center'
        }}>
          <p style={{ margin: 0, color: '#666' }}>
            🎯 <strong>Priority 2 Status:</strong> API Integration Testing - Ready for Verification
          </p>
        </div>
      </div>
    </div>
  );
}
