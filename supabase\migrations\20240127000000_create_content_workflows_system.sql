-- Create advanced content workflows and approval system
-- This migration adds comprehensive content approval workflows with collaboration features

-- Create content workflow templates table
CREATE TABLE IF NOT EXISTS workflow_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  
  -- Template details
  name TEXT NOT NULL CHECK (length(name) >= 2 AND length(name) <= 100),
  description TEXT,
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  -- Workflow configuration
  steps JSONB DEFAULT '[]' NOT NULL, -- Array of approval steps with roles and conditions
  auto_approval_rules JSONB DEFAULT '{}', -- Conditions for automatic approval
  escalation_rules JSONB DEFAULT '{}', -- Rules for escalating overdue approvals
  
  -- Workflow settings
  settings JSONB DEFAULT '{
    "require_all_approvers": false,
    "allow_parallel_approval": true,
    "auto_approve_author": false,
    "deadline_hours": 24,
    "reminder_hours": [12, 2],
    "allow_comments": true,
    "allow_edits_during_approval": false
  }',
  
  -- Usage tracking
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id)
);

-- Create content approval requests table (enhanced from existing post_approvals)
DROP TABLE IF EXISTS post_approvals;
CREATE TABLE IF NOT EXISTS content_approval_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Content reference
  content_type TEXT NOT NULL CHECK (content_type IN ('post', 'campaign', 'template')),
  content_id UUID NOT NULL, -- References posts.id, campaigns.id, etc.
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  
  -- Workflow reference
  workflow_template_id UUID REFERENCES workflow_templates(id),
  workflow_steps JSONB NOT NULL DEFAULT '[]', -- Snapshot of workflow steps at request time
  
  -- Request details
  title TEXT NOT NULL,
  description TEXT,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  
  -- Status tracking
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'canceled', 'expired')),
  current_step INTEGER DEFAULT 1,
  current_approvers UUID[] DEFAULT '{}', -- Array of user IDs who need to approve current step
  
  -- Request metadata
  requested_by UUID NOT NULL REFERENCES auth.users(id),
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deadline_at TIMESTAMP WITH TIME ZONE,
  
  -- Completion tracking
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejected_by UUID REFERENCES auth.users(id),
  rejected_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  
  -- Additional data
  metadata JSONB DEFAULT '{}', -- Additional request-specific data
  attachments JSONB DEFAULT '[]', -- Array of attachment URLs/references
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create approval actions table for tracking individual approver actions
CREATE TABLE IF NOT EXISTS approval_actions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  approval_request_id UUID NOT NULL REFERENCES content_approval_requests(id) ON DELETE CASCADE,
  
  -- Action details
  step_number INTEGER NOT NULL,
  approver_id UUID NOT NULL REFERENCES auth.users(id),
  action TEXT NOT NULL CHECK (action IN ('approved', 'rejected', 'delegated', 'commented')),
  
  -- Action metadata
  comments TEXT,
  decision_reason TEXT,
  delegated_to UUID REFERENCES auth.users(id),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(approval_request_id, step_number, approver_id, action)
);

-- Create content revisions table for tracking changes during approval
CREATE TABLE IF NOT EXISTS content_revisions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Content reference
  content_type TEXT NOT NULL CHECK (content_type IN ('post', 'campaign', 'template')),
  content_id UUID NOT NULL,
  approval_request_id UUID REFERENCES content_approval_requests(id) ON DELETE CASCADE,
  
  -- Revision details
  revision_number INTEGER NOT NULL DEFAULT 1,
  title TEXT,
  content_data JSONB NOT NULL, -- Snapshot of content at this revision
  change_summary TEXT,
  
  -- Revision metadata
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(content_id, revision_number)
);

-- Create approval notifications table
CREATE TABLE IF NOT EXISTS approval_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Notification target
  user_id UUID NOT NULL REFERENCES auth.users(id),
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  
  -- Notification details
  type TEXT NOT NULL CHECK (type IN ('approval_request', 'approval_reminder', 'approval_decision', 'approval_escalation')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  
  -- Related content
  approval_request_id UUID REFERENCES content_approval_requests(id) ON DELETE CASCADE,
  action_url TEXT, -- URL to take action
  
  -- Status
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMP WITH TIME ZONE,
  
  -- Delivery
  delivery_method TEXT[] DEFAULT '{"in_app"}' CHECK (delivery_method <@ '{"in_app", "email", "slack"}'),
  delivered_at TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE
);

-- Create content collaboration comments table (enhanced from existing team_comments)
CREATE TABLE IF NOT EXISTS content_collaboration_comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Comment target
  target_type TEXT NOT NULL CHECK (target_type IN ('approval_request', 'content_revision', 'workflow_template')),
  target_id UUID NOT NULL,
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  
  -- Comment details
  content TEXT NOT NULL CHECK (length(content) >= 1 AND length(content) <= 2000),
  comment_type TEXT DEFAULT 'general' CHECK (comment_type IN ('general', 'suggestion', 'issue', 'approval_note')),
  
  -- Author details
  author_id UUID NOT NULL REFERENCES auth.users(id),
  author_role TEXT, -- Role at time of comment
  
  -- Comment metadata
  is_internal BOOLEAN DEFAULT false, -- Internal team comment vs client-visible
  is_resolved BOOLEAN DEFAULT false,
  resolved_by UUID REFERENCES auth.users(id),
  resolved_at TIMESTAMP WITH TIME ZONE,
  
  -- Threading
  parent_id UUID REFERENCES content_collaboration_comments(id),
  thread_root_id UUID, -- Root comment ID for threading
  
  -- Mentions and attachments
  mentions UUID[] DEFAULT '{}', -- Array of mentioned user IDs
  attachments JSONB DEFAULT '[]', -- Array of attachment references
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workflow_templates_workspace_id ON workflow_templates(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_is_active ON workflow_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_is_default ON workflow_templates(is_default);

CREATE INDEX IF NOT EXISTS idx_content_approval_requests_workspace_id ON content_approval_requests(workspace_id);
CREATE INDEX IF NOT EXISTS idx_content_approval_requests_content ON content_approval_requests(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_content_approval_requests_status ON content_approval_requests(status);
CREATE INDEX IF NOT EXISTS idx_content_approval_requests_requested_by ON content_approval_requests(requested_by);
CREATE INDEX IF NOT EXISTS idx_content_approval_requests_current_approvers ON content_approval_requests USING GIN(current_approvers);
CREATE INDEX IF NOT EXISTS idx_content_approval_requests_deadline ON content_approval_requests(deadline_at);

CREATE INDEX IF NOT EXISTS idx_approval_actions_request_id ON approval_actions(approval_request_id);
CREATE INDEX IF NOT EXISTS idx_approval_actions_approver_id ON approval_actions(approver_id);
CREATE INDEX IF NOT EXISTS idx_approval_actions_step_number ON approval_actions(step_number);

CREATE INDEX IF NOT EXISTS idx_content_revisions_content ON content_revisions(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_content_revisions_approval_request ON content_revisions(approval_request_id);
CREATE INDEX IF NOT EXISTS idx_content_revisions_created_by ON content_revisions(created_by);

CREATE INDEX IF NOT EXISTS idx_approval_notifications_user_id ON approval_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_approval_notifications_workspace_id ON approval_notifications(workspace_id);
CREATE INDEX IF NOT EXISTS idx_approval_notifications_type ON approval_notifications(type);
CREATE INDEX IF NOT EXISTS idx_approval_notifications_is_read ON approval_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_approval_notifications_approval_request ON approval_notifications(approval_request_id);

CREATE INDEX IF NOT EXISTS idx_content_collaboration_comments_target ON content_collaboration_comments(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_content_collaboration_comments_workspace_id ON content_collaboration_comments(workspace_id);
CREATE INDEX IF NOT EXISTS idx_content_collaboration_comments_author_id ON content_collaboration_comments(author_id);
CREATE INDEX IF NOT EXISTS idx_content_collaboration_comments_parent_id ON content_collaboration_comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_content_collaboration_comments_thread_root ON content_collaboration_comments(thread_root_id);

-- Add RLS (Row Level Security) policies
ALTER TABLE workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_approval_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_revisions ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_collaboration_comments ENABLE ROW LEVEL SECURITY;

-- Workflow templates policies
CREATE POLICY "Users can view workflow templates in their workspaces" ON workflow_templates
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Workspace managers can manage workflow templates" ON workflow_templates
  FOR ALL USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND role IN ('OWNER', 'ADMIN', 'MANAGER') AND status = 'active'
    )
  );

-- Content approval requests policies
CREATE POLICY "Users can view approval requests in their workspaces" ON content_approval_requests
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can create approval requests for their content" ON content_approval_requests
  FOR INSERT WITH CHECK (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    ) AND requested_by = auth.uid()
  );

CREATE POLICY "Approvers can update approval requests" ON content_approval_requests
  FOR UPDATE USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    ) AND (
      requested_by = auth.uid() OR 
      auth.uid() = ANY(current_approvers) OR
      workspace_id IN (
        SELECT workspace_id FROM workspace_members 
        WHERE user_id = auth.uid() AND role IN ('OWNER', 'ADMIN') AND status = 'active'
      )
    )
  );

-- Approval actions policies
CREATE POLICY "Users can view approval actions in their workspaces" ON approval_actions
  FOR SELECT USING (
    approval_request_id IN (
      SELECT id FROM content_approval_requests 
      WHERE workspace_id IN (
        SELECT workspace_id FROM workspace_members 
        WHERE user_id = auth.uid() AND status = 'active'
      )
    )
  );

CREATE POLICY "Approvers can create approval actions" ON approval_actions
  FOR INSERT WITH CHECK (
    approver_id = auth.uid() AND
    approval_request_id IN (
      SELECT id FROM content_approval_requests 
      WHERE workspace_id IN (
        SELECT workspace_id FROM workspace_members 
        WHERE user_id = auth.uid() AND status = 'active'
      )
    )
  );

-- Content revisions policies
CREATE POLICY "Users can view content revisions in their workspaces" ON content_revisions
  FOR SELECT USING (
    approval_request_id IN (
      SELECT id FROM content_approval_requests 
      WHERE workspace_id IN (
        SELECT workspace_id FROM workspace_members 
        WHERE user_id = auth.uid() AND status = 'active'
      )
    )
  );

-- Approval notifications policies
CREATE POLICY "Users can view their own notifications" ON approval_notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can create notifications for workspace members" ON approval_notifications
  FOR INSERT WITH CHECK (
    user_id IN (
      SELECT user_id FROM workspace_members 
      WHERE workspace_id = approval_notifications.workspace_id AND status = 'active'
    )
  );

-- Content collaboration comments policies
CREATE POLICY "Users can view comments in their workspaces" ON content_collaboration_comments
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can create comments in their workspaces" ON content_collaboration_comments
  FOR INSERT WITH CHECK (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    ) AND author_id = auth.uid()
  );

-- Create functions for workflow management
CREATE OR REPLACE FUNCTION get_user_pending_approvals(user_uuid UUID)
RETURNS TABLE(
  approval_id UUID,
  title TEXT,
  content_type TEXT,
  priority TEXT,
  requested_by_email TEXT,
  deadline_at TIMESTAMP WITH TIME ZONE,
  step_number INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    car.id,
    car.title,
    car.content_type,
    car.priority,
    u.email,
    car.deadline_at,
    car.current_step
  FROM content_approval_requests car
  JOIN auth.users u ON car.requested_by = u.id
  WHERE car.status = 'pending' 
    AND user_uuid = ANY(car.current_approvers)
    AND car.workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = user_uuid AND status = 'active'
    )
  ORDER BY car.deadline_at ASC NULLS LAST, car.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to advance approval workflow
CREATE OR REPLACE FUNCTION advance_approval_workflow(
  request_id UUID,
  approver_uuid UUID,
  action_type TEXT,
  comments_text TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  request_record content_approval_requests%ROWTYPE;
  workflow_steps JSONB;
  current_step_config JSONB;
  next_step_config JSONB;
  result JSONB;
BEGIN
  -- Get the approval request
  SELECT * INTO request_record FROM content_approval_requests WHERE id = request_id;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object('success', false, 'error', 'Approval request not found');
  END IF;
  
  -- Verify approver is authorized
  IF NOT (approver_uuid = ANY(request_record.current_approvers)) THEN
    RETURN jsonb_build_object('success', false, 'error', 'User not authorized to approve this step');
  END IF;
  
  -- Record the approval action
  INSERT INTO approval_actions (
    approval_request_id, step_number, approver_id, action, comments
  ) VALUES (
    request_id, request_record.current_step, approver_uuid, action_type, comments_text
  );
  
  workflow_steps := request_record.workflow_steps;
  current_step_config := workflow_steps->((request_record.current_step - 1)::text);
  
  IF action_type = 'rejected' THEN
    -- Mark as rejected
    UPDATE content_approval_requests 
    SET status = 'rejected', 
        rejected_by = approver_uuid, 
        rejected_at = NOW(),
        rejection_reason = comments_text,
        updated_at = NOW()
    WHERE id = request_id;
    
    result := jsonb_build_object('success', true, 'status', 'rejected');
  ELSE
    -- Check if we need to advance to next step
    IF request_record.current_step >= jsonb_array_length(workflow_steps) THEN
      -- Final approval
      UPDATE content_approval_requests 
      SET status = 'approved', 
          approved_by = approver_uuid, 
          approved_at = NOW(),
          updated_at = NOW()
      WHERE id = request_id;
      
      result := jsonb_build_object('success', true, 'status', 'approved');
    ELSE
      -- Advance to next step
      next_step_config := workflow_steps->(request_record.current_step::text);
      
      UPDATE content_approval_requests 
      SET current_step = current_step + 1,
          current_approvers = ARRAY(
            SELECT jsonb_array_elements_text(next_step_config->'approvers')
          )::UUID[],
          status = 'in_review',
          updated_at = NOW()
      WHERE id = request_id;
      
      result := jsonb_build_object('success', true, 'status', 'advanced', 'next_step', request_record.current_step + 1);
    END IF;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger functions for updated_at timestamps
CREATE TRIGGER trigger_workflow_templates_updated_at
  BEFORE UPDATE ON workflow_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_content_approval_requests_updated_at
  BEFORE UPDATE ON content_approval_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_content_collaboration_comments_updated_at
  BEFORE UPDATE ON content_collaboration_comments
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert default workflow templates
INSERT INTO workflow_templates (workspace_id, name, description, is_default, steps, created_by)
SELECT 
  w.id,
  'Simple Approval',
  'Basic one-step approval workflow',
  true,
  '[
    {
      "name": "Manager Review",
      "description": "Content review by manager",
      "approvers": [],
      "required_approvals": 1,
      "auto_approve_conditions": {}
    }
  ]'::jsonb,
  wm.user_id
FROM workspaces w
JOIN workspace_members wm ON w.id = wm.workspace_id 
WHERE wm.role = 'OWNER' AND wm.status = 'active'
ON CONFLICT DO NOTHING;

-- Grant necessary permissions
GRANT SELECT ON workflow_templates TO authenticated;
GRANT SELECT ON content_approval_requests TO authenticated;
GRANT SELECT ON approval_actions TO authenticated;
GRANT SELECT ON content_revisions TO authenticated;
GRANT SELECT ON approval_notifications TO authenticated;
GRANT SELECT ON content_collaboration_comments TO authenticated;

-- Add comments to tables
COMMENT ON TABLE workflow_templates IS 'Configurable approval workflow templates for content';
COMMENT ON TABLE content_approval_requests IS 'Content approval requests with workflow tracking';
COMMENT ON TABLE approval_actions IS 'Individual approver actions and decisions';
COMMENT ON TABLE content_revisions IS 'Content revision history during approval process';
COMMENT ON TABLE approval_notifications IS 'Approval-related notifications for users';
COMMENT ON TABLE content_collaboration_comments IS 'Collaboration comments on content and approvals';
