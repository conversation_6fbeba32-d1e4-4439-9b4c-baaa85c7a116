"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { useRedirectIfAuthenticated } from "@/hooks/useAuth";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";


const formSchema = z.object({
  email: z.string().email({
    message: "البريد الإلكتروني غير صالح",
  }),
  password: z.string().min(6, {
    message: "كلمة المرور يجب أن تكون 6 أحرف على الأقل",
  }),
});

export function SignInForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [callbackUrl, setCallbackUrl] = useState("/dashboard");

  // Redirect if already authenticated
  useRedirectIfAuthenticated();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Handle search params on client side to avoid hydration issues
  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const urlCallbackUrl = searchParams.get("callbackUrl");
      if (urlCallbackUrl) {
        setCallbackUrl(urlCallbackUrl);
      }
    }
  }, []);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Attempting to sign in with Supabase Auth...");
      const supabase = createClient();

      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });

      if (authError) {
        console.error('Supabase auth error:', authError);
        setError("بيانات الاعتماد غير صالحة");
        toast.error("بيانات الاعتماد غير صالحة");
        return;
      }

      if (authData.user && authData.session) {
        console.log("Login successful, redirecting to dashboard...");
        toast.success("تم تسجيل الدخول بنجاح!");
        router.push(callbackUrl);
      } else {
        console.log("Login failed - no user or session");
        setError("فشل في تسجيل الدخول");
        toast.error("فشل في تسجيل الدخول");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("حدث خطأ أثناء تسجيل الدخول");
      toast.error("حدث خطأ أثناء تسجيل الدخول");
    } finally {
      setIsLoading(false);
    }
  }

  // Function to handle Google OAuth login
  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Attempting Google OAuth sign-in...");
      const supabase = createClient();

      // Get the correct origin URL
      const origin = window.location.origin;
      const redirectUrl = `${origin}/auth/callback?redirect=${encodeURIComponent(callbackUrl)}`;

      console.log('Google OAuth redirect URL:', redirectUrl);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl
        }
      });

      if (error) {
        console.error('Google sign-in error:', error);
        toast.error("فشل في تسجيل الدخول بـ Google");
        setError("فشل في تسجيل الدخول بـ Google");
      }
      // Note: If successful, user will be redirected to Google OAuth flow
    } catch (error: any) {
      console.error("Google sign-in error:", error);
      toast.error("حدث خطأ أثناء تسجيل الدخول بـ Google");
      setError("حدث خطأ أثناء تسجيل الدخول بـ Google");
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="w-full" dir="rtl">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-700 font-medium">البريد الإلكتروني</FormLabel>
                <FormControl>
                  <Input
                    placeholder="أدخل بريدك الإلكتروني"
                    className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-700 font-medium">كلمة المرور</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="أدخل كلمة المرور"
                    className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm text-center">{error}</p>
            </div>
          )}
          <Button
            type="submit"
            className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                جاري تسجيل الدخول...
              </div>
            ) : (
              "تسجيل الدخول"
            )}
          </Button>
        </form>
      </Form>

      {/* Forgot Password Link */}
      <div className="text-center mt-4">
        <Link
          href="/auth/forgot-password"
          className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
        >
          نسيت كلمة المرور؟
        </Link>
      </div>

      {/* Divider */}
      <div className="my-6 flex items-center">
        <div className="flex-1 border-t border-gray-200"></div>
        <span className="px-4 text-sm text-gray-500">أو</span>
        <div className="flex-1 border-t border-gray-200"></div>
      </div>

      {/* Google OAuth Button */}
      <div className="mb-4">
        <Button
          variant="outline"
          className="w-full h-12 border-2 border-gray-200 hover:border-red-300 hover:bg-red-50 transition-all duration-200 flex items-center justify-center gap-3"
          onClick={handleGoogleSignIn}
          disabled={isLoading}
        >
          <svg className="w-5 h-5" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          تسجيل الدخول بـ Google
        </Button>
      </div>



      {/* Sign Up Link */}
      <div className="text-center mt-6">
        <p className="text-gray-600">
          ليس لديك حساب؟{" "}
          <Link href="/auth/signup" className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">
            إنشاء حساب جديد
          </Link>
        </p>
      </div>
    </div>
  );
}
