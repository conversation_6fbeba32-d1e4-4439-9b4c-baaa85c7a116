"use client";

import React from 'react';
import { MoreHorizontal, Calendar, Eye, Edit, Trash2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface Post {
  id: string;
  content: string;
  status: string;
  media_url?: string;
  scheduled_at?: string;
  published_at?: string;
  created_at: string;
  updated_at: string;
}

interface EnhancedPostCardProps {
  post: Post;
  language: 'ar' | 'en';
  onEdit?: (post: Post) => void;
  onDelete?: (post: Post) => void;
  onView?: (post: Post) => void;
}

const EnhancedPostCard = ({ 
  post, 
  language, 
  onEdit, 
  onDelete, 
  onView 
}: EnhancedPostCardProps) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "DRAFT":
        return {
          label: language === 'ar' ? 'مسودة' : 'Draft',
          className: 'status-neutral',
          color: 'bg-gray-100 text-gray-700'
        };
      case "SCHEDULED":
        return {
          label: language === 'ar' ? 'مجدول' : 'Scheduled',
          className: 'status-info',
          color: 'bg-blue-100 text-blue-700'
        };
      case "PUBLISHED":
        return {
          label: language === 'ar' ? 'منشور' : 'Published',
          className: 'status-success',
          color: 'bg-emerald-100 text-emerald-700'
        };
      case "FAILED":
        return {
          label: language === 'ar' ? 'فشل' : 'Failed',
          className: 'status-error',
          color: 'bg-red-100 text-red-700'
        };
      default:
        return {
          label: language === 'ar' ? 'غير معروف' : 'Unknown',
          className: 'status-neutral',
          color: 'bg-gray-100 text-gray-700'
        };
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return language === 'ar' ? 'غير محدد' : 'Not specified';
    try {
      return new Date(dateString).toLocaleDateString(
        language === 'ar' ? 'ar-SA' : 'en-US',
        {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }
      );
    } catch (error) {
      return language === 'ar' ? 'غير محدد' : 'Invalid date';
    }
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const statusConfig = getStatusConfig(post.status);

  const text = {
    ar: {
      createdAt: 'تم الإنشاء:',
      scheduledAt: 'مجدول في:',
      publishedAt: 'تم النشر في:',
      actions: {
        view: 'عرض',
        edit: 'تعديل',
        delete: 'حذف'
      }
    },
    en: {
      createdAt: 'Created:',
      scheduledAt: 'Scheduled:',
      publishedAt: 'Published:',
      actions: {
        view: 'View',
        edit: 'Edit',
        delete: 'Delete'
      }
    }
  };

  const currentText = text[language];

  return (
    <Card className="card-pro-hover animate-pro-slide-up">
      <CardContent className="card-pro-padding-md">
        <div className={cn(
          "flex items-start justify-between gap-4",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <div className={cn(
            "flex-1 min-w-0",
            language === 'ar' ? "text-right" : "text-left"
          )}>
            {/* Status Badge */}
            <div className={cn(
              "flex items-center gap-2 mb-3",
              language === 'ar' ? "flex-row-reverse justify-end" : "justify-start"
            )}>
              <span className={cn("status-pro", statusConfig.className)}>
                {statusConfig.label}
              </span>
              {post.media_url && (
                <span className="status-pro bg-purple-50 text-purple-700 border-purple-200">
                  📷 {language === 'ar' ? 'يحتوي على وسائط' : 'Has Media'}
                </span>
              )}
            </div>

            {/* Content */}
            <p className="text-body text-gray-900 mb-4 leading-relaxed">
              {truncateContent(post.content)}
            </p>

            {/* Metadata */}
            <div className={cn(
              "space-y-1 text-body-sm text-gray-500",
              language === 'ar' ? "text-right" : "text-left"
            )}>
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Calendar className="h-3 w-3" />
                <span>
                  {currentText.createdAt} {formatDate(post.created_at)}
                </span>
              </div>
              
              {post.scheduled_at && (
                <div className={cn(
                  "flex items-center gap-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Calendar className="h-3 w-3 text-blue-500" />
                  <span>
                    {currentText.scheduledAt} {formatDate(post.scheduled_at)}
                  </span>
                </div>
              )}
              
              {post.published_at && (
                <div className={cn(
                  "flex items-center gap-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Calendar className="h-3 w-3 text-emerald-500" />
                  <span>
                    {currentText.publishedAt} {formatDate(post.published_at)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-8 w-8 rounded-lg hover:bg-gray-100"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              align={language === 'ar' ? 'start' : 'end'} 
              className="w-40"
            >
              <DropdownMenuItem 
                onClick={() => onView?.(post)}
                className={cn(
                  "gap-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}
              >
                <Eye className="h-4 w-4" />
                {currentText.actions.view}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onEdit?.(post)}
                className={cn(
                  "gap-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}
              >
                <Edit className="h-4 w-4" />
                {currentText.actions.edit}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onDelete?.(post)}
                className={cn(
                  "gap-2 text-red-600 hover:text-red-700 hover:bg-red-50",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}
              >
                <Trash2 className="h-4 w-4" />
                {currentText.actions.delete}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedPostCard;
