/**
 * Comprehensive Test Suite for Advanced Content Creation System
 * Tests AI generation, templates, and advanced editor functionality
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { createClient } from '@/lib/supabase/client';
import { aiContentGenerator } from '@/lib/ai/content-generator';
import { contentTemplateManager } from '@/lib/templates/content-templates';

// Mock environment variables for testing
process.env.OPENAI_API_KEY = 'test-openai-key';
process.env.OPENROUTER_API_KEY = 'test-openrouter-key';
process.env.NEXT_PUBLIC_SITE_URL = 'http://localhost:3000';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Advanced Content Creation System', () => {
  let supabase: any;
  let testUserId: string;

  beforeAll(async () => {
    supabase = createClient();
    testUserId = 'test-user-123';
  });

  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  describe('AI Content Generator', () => {
    test('should generate Arabic content successfully', async () => {
      // Mock OpenRouter API response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: JSON.stringify({
                content: 'محتوى تجريبي باللغة العربية للمنصات الاجتماعية',
                hashtags: ['تجربة', 'محتوى', 'عربي'],
                emojis: ['📱', '✨'],
                suggestions: ['نسخة بديلة للمحتوى']
              })
            }
          }]
        })
      });

      const request = {
        prompt: 'اكتب منشور عن فوائد التكنولوجيا',
        platform: 'facebook' as const,
        tone: 'friendly' as const,
        language: 'ar' as const,
        includeHashtags: true,
        includeEmojis: true,
        targetAudience: 'الشباب المهتمين بالتكنولوجيا'
      };

      const result = await aiContentGenerator.generateContent(request);

      expect(result).toBeDefined();
      expect(result.content).toContain('محتوى');
      expect(result.hashtags).toContain('تجربة');
      expect(result.characterCount).toBeGreaterThan(0);
      expect(result.platformOptimized).toBe(true);
    });

    test('should generate English content successfully', async () => {
      // Mock OpenAI API response
      const mockCompletion = {
        choices: [{
          message: {
            content: JSON.stringify({
              content: 'Test English content for social media platforms',
              hashtags: ['test', 'content', 'english'],
              emojis: ['📱', '✨'],
              suggestions: ['Alternative content version']
            })
          }
        }]
      };

      // Mock OpenAI client
      const mockCreate = jest.fn().mockResolvedValue(mockCompletion);
      (aiContentGenerator as any).openai = {
        chat: {
          completions: {
            create: mockCreate
          }
        }
      };

      const request = {
        prompt: 'Write a post about technology benefits',
        platform: 'twitter' as const,
        tone: 'professional' as const,
        language: 'en' as const,
        includeHashtags: true,
        includeEmojis: true,
        targetAudience: 'Tech enthusiasts'
      };

      const result = await aiContentGenerator.generateContent(request);

      expect(result).toBeDefined();
      expect(result.content).toContain('content');
      expect(result.hashtags).toContain('test');
      expect(result.characterCount).toBeGreaterThan(0);
      expect(mockCreate).toHaveBeenCalledWith(
        expect.objectContaining({
          model: 'gpt-4',
          messages: expect.arrayContaining([
            expect.objectContaining({ role: 'system' }),
            expect.objectContaining({ role: 'user' })
          ])
        })
      );
    });

    test('should generate hashtag suggestions', async () => {
      // Mock API response for hashtag generation
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: JSON.stringify([
                { tag: 'تكنولوجيا', popularity: 'high', relevance: 0.9, category: 'tech' },
                { tag: 'ابتكار', popularity: 'medium', relevance: 0.8, category: 'innovation' },
                { tag: 'مستقبل', popularity: 'high', relevance: 0.7, category: 'future' }
              ])
            }
          }]
        })
      });

      const hashtags = await aiContentGenerator.generateHashtagSuggestions(
        'محتوى عن التكنولوجيا والابتكار',
        'facebook',
        'ar'
      );

      expect(hashtags).toHaveLength(3);
      expect(hashtags[0]).toHaveProperty('tag', 'تكنولوجيا');
      expect(hashtags[0]).toHaveProperty('popularity', 'high');
      expect(hashtags[0]).toHaveProperty('relevance', 0.9);
    });

    test('should optimize content for different platforms', async () => {
      // Mock API response for content optimization
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'محتوى محسن لمنصة تويتر مع حد الأحرف المناسب'
            }
          }]
        })
      });

      const originalContent = 'محتوى طويل مناسب لفيسبوك يحتوي على تفاصيل كثيرة ومعلومات شاملة';
      
      const optimizedContent = await aiContentGenerator.optimizeForPlatform(
        originalContent,
        'facebook',
        'twitter',
        'ar'
      );

      expect(optimizedContent).toBeDefined();
      expect(optimizedContent.length).toBeLessThan(originalContent.length);
      expect(optimizedContent).toContain('محتوى');
    });

    test('should handle API errors gracefully', async () => {
      // Mock API error
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'));

      const request = {
        prompt: 'Test prompt',
        platform: 'facebook' as const,
        tone: 'friendly' as const,
        language: 'ar' as const,
        includeHashtags: true,
        includeEmojis: true
      };

      await expect(aiContentGenerator.generateContent(request)).rejects.toThrow('Failed to generate content');
    });
  });

  describe('Content Template Manager', () => {
    test('should search templates with filters', () => {
      const filters = {
        category: 'promotional' as const,
        platform: 'facebook' as const,
        language: 'ar' as const,
        tone: 'friendly' as const
      };

      const templates = contentTemplateManager.searchTemplates(filters);

      expect(templates).toBeDefined();
      expect(Array.isArray(templates)).toBe(true);
      
      templates.forEach(template => {
        expect(template.category).toBe('promotional');
        expect(template.platform).toContain('facebook');
        expect(['ar', 'both']).toContain(template.language);
        expect(template.tone).toBe('friendly');
      });
    });

    test('should get template by ID', () => {
      const templateId = 'promo-product-launch-ar';
      const template = contentTemplateManager.getTemplate(templateId);

      expect(template).toBeDefined();
      expect(template?.id).toBe(templateId);
      expect(template?.name).toBe('إطلاق منتج جديد');
      expect(template?.category).toBe('promotional');
    });

    test('should get popular templates', () => {
      const popularTemplates = contentTemplateManager.getPopularTemplates(5);

      expect(popularTemplates).toBeDefined();
      expect(Array.isArray(popularTemplates)).toBe(true);
      expect(popularTemplates.length).toBeLessThanOrEqual(5);
      
      // Should be sorted by usage count
      for (let i = 1; i < popularTemplates.length; i++) {
        expect(popularTemplates[i-1].usageCount).toBeGreaterThanOrEqual(popularTemplates[i].usageCount);
      }
    });

    test('should process template with variables', () => {
      const template = contentTemplateManager.getTemplate('promo-product-launch-ar');
      expect(template).toBeDefined();

      const variables = {
        productName: 'هاتف ذكي جديد',
        productDescription: 'هاتف متطور بمواصفات عالية',
        features: '• كاميرا عالية الدقة\n• بطارية طويلة المدى\n• معالج سريع',
        price: '2999 ريال',
        launchDate: '2024-02-01',
        callToAction: 'احصل عليه الآن!'
      };

      const processedContent = contentTemplateManager.processTemplate(template!, variables);

      expect(processedContent).toContain('هاتف ذكي جديد');
      expect(processedContent).toContain('هاتف متطور بمواصفات عالية');
      expect(processedContent).toContain('2999 ريال');
      expect(processedContent).toContain('احصل عليه الآن!');
      expect(processedContent).not.toContain('{{productName}}');
    });

    test('should validate template variables', () => {
      const template = contentTemplateManager.getTemplate('promo-product-launch-ar');
      expect(template).toBeDefined();

      // Test with valid variables
      const validVariables = {
        productName: 'منتج تجريبي',
        productDescription: 'وصف المنتج',
        features: 'المميزات',
        launchDate: '2024-02-01',
        callToAction: 'اطلب الآن'
      };

      const validResult = contentTemplateManager.validateTemplateVariables(template!, validVariables);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      // Test with missing required variables
      const invalidVariables = {
        productName: 'منتج تجريبي'
        // Missing other required fields
      };

      const invalidResult = contentTemplateManager.validateTemplateVariables(template!, invalidVariables);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
    });

    test('should filter templates by category', () => {
      const educationalTemplates = contentTemplateManager.getTemplatesByCategory('educational');
      
      expect(educationalTemplates).toBeDefined();
      expect(Array.isArray(educationalTemplates)).toBe(true);
      
      educationalTemplates.forEach(template => {
        expect(template.category).toBe('educational');
        expect(template.isPublic).toBe(true);
      });
    });
  });

  describe('API Integration Tests', () => {
    test('should test content generation API endpoint', async () => {
      const mockResponse = {
        success: true,
        result: {
          content: 'Generated content',
          hashtags: ['test'],
          emojis: ['📱'],
          suggestions: ['Alternative'],
          characterCount: 17,
          platformOptimized: true
        }
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const response = await fetch('/api/content/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          prompt: 'Test prompt',
          platform: 'facebook',
          tone: 'friendly',
          language: 'ar'
        })
      });

      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.result.content).toBe('Generated content');
    });

    test('should test templates API endpoint', async () => {
      const mockResponse = {
        success: true,
        templates: [
          {
            id: 'test-template',
            name: 'Test Template',
            category: 'promotional',
            platform: ['facebook'],
            language: 'ar'
          }
        ],
        total: 1,
        hasMore: false
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const response = await fetch('/api/content/templates?action=search&limit=10');
      const result = await response.json();
      
      expect(result.success).toBe(true);
      expect(result.templates).toHaveLength(1);
      expect(result.templates[0].id).toBe('test-template');
    });
  });

  describe('Performance Tests', () => {
    test('should generate content within acceptable time', async () => {
      // Mock fast API response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: JSON.stringify({
                content: 'Fast generated content',
                hashtags: ['fast'],
                emojis: ['⚡'],
                suggestions: []
              })
            }
          }]
        })
      });

      const startTime = Date.now();
      
      const result = await aiContentGenerator.generateContent({
        prompt: 'Quick test',
        platform: 'twitter',
        tone: 'casual',
        language: 'en',
        includeHashtags: true,
        includeEmojis: true
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    test('should process templates efficiently', () => {
      const template = contentTemplateManager.getTemplate('promo-product-launch-ar');
      expect(template).toBeDefined();

      const variables = {
        productName: 'Test Product',
        productDescription: 'Test Description',
        features: 'Test Features',
        price: '100',
        launchDate: '2024-01-01',
        callToAction: 'Buy Now'
      };

      const startTime = Date.now();
      
      for (let i = 0; i < 100; i++) {
        contentTemplateManager.processTemplate(template!, variables);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(1000); // Should process 100 templates within 1 second
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle invalid template ID', () => {
      const template = contentTemplateManager.getTemplate('non-existent-template');
      expect(template).toBeUndefined();
    });

    test('should handle empty search results', () => {
      const templates = contentTemplateManager.searchTemplates({
        category: 'non-existent-category' as any,
        platform: 'non-existent-platform' as any
      });

      expect(templates).toBeDefined();
      expect(Array.isArray(templates)).toBe(true);
      expect(templates).toHaveLength(0);
    });

    test('should handle malformed AI responses', async () => {
      // Mock malformed API response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'Invalid JSON response'
            }
          }]
        })
      });

      const result = await aiContentGenerator.generateContent({
        prompt: 'Test prompt',
        platform: 'facebook',
        tone: 'friendly',
        language: 'ar',
        includeHashtags: true,
        includeEmojis: true
      });

      expect(result).toBeDefined();
      expect(result.content).toBe('Invalid JSON response');
      expect(Array.isArray(result.hashtags)).toBe(true);
      expect(Array.isArray(result.emojis)).toBe(true);
    });
  });
});
