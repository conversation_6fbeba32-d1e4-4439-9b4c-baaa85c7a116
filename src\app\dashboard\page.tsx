"use client";

import React, { useEffect, useState } from "react";
import { MessageSquare, Calendar, BarChart3, Users, TrendingUp } from "lucide-react";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { WelcomeSection } from "@/components/dashboard/welcome-section";
import { StatsCard } from "@/components/dashboard/stats-card";
import { QuickActions } from "@/components/dashboard/quick-actions";

interface DashboardData {
  overview: {
    totalPosts: number;
    scheduledPosts: number;
    connectedAccounts: number;
    totalEngagement: number;
    engagementRate: number;
  };
  postsByStatus: Array<{
    status: string;
    count: number;
    label: string;
  }>;
  postsByDay: Array<{
    date: string;
    posts: number;
  }>;
  recentPosts: Array<{
    id: string;
    content: string;
    status: string;
    created_at: string;
    scheduled_at?: string;
    published_at?: string;
  }>;
  recentActivities: Array<{
    id: string;
    action: string;
    details: string;
    created_at: string;
    post_id?: string;
  }>;
  trends: {
    postsGrowth: string;
    engagementGrowth: string;
    accountsGrowth: string;
  };
}

export default function DashboardPage() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const router = useRouter();

  useEffect(() => {
    checkAuthAndLoadData();
  }, []);

  const checkAuthAndLoadData = async () => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      router.push('/auth/signin');
      return;
    }

    setUser(user);
    await loadDashboardData();
  };

  const loadDashboardData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/analytics/dashboard');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load dashboard data');
      }

      setDashboardData(data.data);
    } catch (error: any) {
      console.error('Error loading dashboard data:', error);
      toast.error('فشل في تحميل بيانات لوحة التحكم');

      // Fallback to mock data
      setDashboardData({
        overview: {
          totalPosts: 0,
          scheduledPosts: 0,
          connectedAccounts: 0,
          totalEngagement: 1227,
          engagementRate: 0
        },
        postsByStatus: [],
        postsByDay: [],
        recentPosts: [],
        recentActivities: [],
        trends: {
          postsGrowth: '0%',
          engagementGrowth: '0%',
          accountsGrowth: '0%'
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getUserName = () => {
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name;
    }
    if (user?.email) {
      return user.email.split('@')[0];
    }
    return language === 'ar' ? 'المستخدم' : 'User';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="section-pro animate-pro-fade-in">
      {/* Welcome Section */}
      <WelcomeSection
        userName={getUserName()}
        language={language}
      />

      {/* Status Info */}
      <div className="card-pro card-pro-padding-md mb-8 bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200">
        <div className="flex items-center gap-3">
          <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
          <div>
            <p className="font-semibold text-emerald-800 text-body">
              🚀 eWasl v8.0 - {language === 'ar' ? 'تصميم حديث ومتطور' : 'Modern & Advanced Design'}
            </p>
            <p className="text-body-sm text-emerald-700 mt-1">
              <span className="font-medium">
                {language === 'ar' ? 'البيئة:' : 'Environment:'}
              </span> {language === 'ar' ? 'إنتاج' : 'Production'} |
              <span className="font-medium">
                {language === 'ar' ? ' المنشورات:' : ' Posts:'}
              </span> {dashboardData?.overview.totalPosts || 0} |
              <span className="font-medium">
                {language === 'ar' ? ' الحسابات:' : ' Accounts:'}
              </span> {dashboardData?.overview.connectedAccounts || 0}
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid-pro-4 mb-8">
        {[
          {
            title: language === 'ar' ? 'إجمالي المنشورات' : 'Total Posts',
            value: dashboardData?.overview.totalPosts.toString() || '0',
            icon: MessageSquare,
            description: language === 'ar' ? 'إجمالي المنشورات المنشورة والمجدولة' : 'Total published and scheduled posts',
            change: dashboardData?.trends.postsGrowth || '0%',
            changeType: 'positive' as const,
            iconColor: 'bg-gradient-to-r from-blue-500 to-blue-600'
          },
          {
            title: language === 'ar' ? 'منشورات مجدولة' : 'Scheduled Posts',
            value: dashboardData?.overview.scheduledPosts.toString() || '0',
            icon: Calendar,
            description: language === 'ar' ? 'المنشورات المجدولة للنشر لاحقاً' : 'Posts scheduled for later publishing',
            change: '+8%',
            changeType: 'positive' as const,
            iconColor: 'bg-gradient-to-r from-purple-500 to-purple-600'
          },
          {
            title: language === 'ar' ? 'حسابات متصلة' : 'Connected Accounts',
            value: dashboardData?.overview.connectedAccounts.toString() || '0',
            icon: Users,
            description: language === 'ar' ? 'عدد حسابات وسائل التواصل الاجتماعي المتصلة' : 'Number of connected social media accounts',
            change: dashboardData?.trends.accountsGrowth || '0%',
            changeType: 'positive' as const,
            iconColor: 'bg-gradient-to-r from-emerald-500 to-emerald-600'
          },
          {
            title: language === 'ar' ? 'إجمالي التفاعلات' : 'Total Engagement',
            value: dashboardData?.overview.totalEngagement.toLocaleString() || '1,227',
            icon: TrendingUp,
            description: language === 'ar' ? 'إجمالي التفاعلات على جميع المنشورات' : 'Total engagement across all posts',
            change: dashboardData?.trends.engagementGrowth || '0%',
            changeType: 'positive' as const,
            iconColor: 'bg-gradient-to-r from-amber-500 to-amber-600'
          }
        ].map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            description={stat.description}
            change={stat.change}
            changeType={stat.changeType}
            iconColor={stat.iconColor}
            language={language}
            className="animate-pro-slide-up"
            style={{ animationDelay: `${index * 0.1}s` }}
          />
        ))}
      </div>

      {/* Quick Actions */}
      <QuickActions language={language} />
    </div>
  );
}
