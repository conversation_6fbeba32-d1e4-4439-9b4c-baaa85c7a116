"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, <PERSON>rkles } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface AICaptionGeneratorProps {
  onSelectCaption: (caption: string) => void;
}

export function AICaptionGenerator({ onSelectCaption }: AICaptionGeneratorProps) {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCaptions, setGeneratedCaptions] = useState<string[]>([]);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "أدخل موضوعًا",
        description: "يرجى إدخال موضوع لإنشاء تعليقات",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedCaptions([]);

    try {
      console.log(`Generating captions for prompt: ${prompt}`);

      // Call our AI caption generation API
      const response = await fetch('/api/ai/caption', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          language: 'arabic',
          style: 'engaging',
          count: 3
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate captions');
      }

      if (data.captions && data.captions.length > 0) {
        setGeneratedCaptions(data.captions);
        toast({
          title: "تم إنشاء التعليقات بنجاح!",
          description: `تم إنشاء ${data.captions.length} تعليقات جديدة`,
        });
      } else {
        throw new Error('No captions generated');
      }
    } catch (error: any) {
      console.error('Caption generation error:', error);
      toast({
        title: "حدث خطأ",
        description: error.message || "لم نتمكن من إنشاء تعليقات. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });

      // Fallback to mock captions if API fails
      const fallbackCaptions = [
        `استمتع بتجربة إدارة وسائل التواصل الاجتماعي مع منصة eWasl! 🚀 #${prompt} #eWasl`,
        `اكتشف كيف يمكن لمنصة eWasl أن تساعدك في تنظيم محتواك على وسائل التواصل الاجتماعي بسهولة وفعالية. #${prompt}`,
        `حان الوقت لتحسين استراتيجية التواصل الاجتماعي الخاصة بك مع منصة eWasl المتطورة! 📱✨ #${prompt}`,
      ];
      setGeneratedCaptions(fallbackCaptions);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSelectCaption = (caption: string) => {
    onSelectCaption(caption);
    toast({
      title: "تم اختيار التعليق",
      description: "تم إضافة التعليق إلى المنشور",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>إنشاء تعليق بالذكاء الاصطناعي</CardTitle>
        <CardDescription>
          استخدم الذكاء الاصطناعي لإنشاء تعليقات جذابة باللغة العربية
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="prompt">موضوع المنشور</Label>
          <Input
            id="prompt"
            placeholder="أدخل موضوع المنشور (مثال: التسويق الرقمي، الصحة، التكنولوجيا)"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
          />
        </div>

        <Button
          onClick={handleGenerate}
          disabled={isGenerating || !prompt.trim()}
          className="w-full"
        >
          {isGenerating ? (
            <>
              <Loader2 className="ml-2 h-4 w-4 animate-spin" />
              جاري الإنشاء...
            </>
          ) : (
            <>
              <Sparkles className="ml-2 h-4 w-4" />
              إنشاء تعليقات
            </>
          )}
        </Button>

        {generatedCaptions.length > 0 && (
          <div className="space-y-4 mt-4">
            <h3 className="text-sm font-medium">التعليقات المقترحة:</h3>
            <div className="space-y-3">
              {generatedCaptions.map((caption, index) => (
                <div key={index} className="p-3 border rounded-md">
                  <p className="text-sm mb-2">{caption}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSelectCaption(caption)}
                  >
                    استخدام هذا التعليق
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
