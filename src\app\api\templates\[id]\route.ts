import { NextRequest, NextResponse } from 'next/server';
import { supabaseServiceRole as supabase } from '@/lib/supabase/service-role';

// GET /api/templates/[id] - Get single template
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const { data: template, error } = await supabase
      .from('content_templates')
      .select(`
        *,
        template_categories!inner(name, name_ar, icon),
        template_ratings(rating, review, created_at)
      `)
      .eq('id', resolvedParams.id)
      .eq('is_public', true)
      .single();

    if (error) {
      console.error('Error fetching template:', error);
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      template
    });

  } catch (error) {
    console.error('Template fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/templates/[id] - Update template
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const body = await request.json();
    const {
      name,
      description,
      category,
      platform_compatibility,
      language,
      content_body,
      variables,
      hashtags,
      tone,
      industry_tags,
      is_public,
      metadata
    } = body;

    const { data: template, error } = await supabase
      .from('content_templates')
      .update({
        name,
        description,
        category,
        platform_compatibility,
        language,
        content_body,
        variables,
        hashtags,
        tone,
        industry_tags,
        is_public,
        metadata
      })
      .eq('id', resolvedParams.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating template:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update template' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      template
    });

  } catch (error) {
    console.error('Template update error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/templates/[id] - Delete template
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const { error } = await supabase
      .from('content_templates')
      .delete()
      .eq('id', resolvedParams.id);

    if (error) {
      console.error('Error deleting template:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete template' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    });

  } catch (error) {
    console.error('Template deletion error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
