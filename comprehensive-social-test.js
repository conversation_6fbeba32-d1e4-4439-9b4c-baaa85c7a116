#!/usr/bin/env node

/**
 * Comprehensive Social Media Integration Testing Suite
 * Tests all APIs, components, and functionality end-to-end
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Comprehensive Social Media Integration Testing...\n');

// Test configuration
const BASE_URL = 'http://localhost:3001';
let testResults = [];
let passedTests = 0;
let totalTests = 0;

function logTest(testName, passed, details = '') {
  totalTests++;
  const status = passed ? '✅' : '❌';
  const result = `${status} ${testName}`;
  
  console.log(result);
  testResults.push(result);
  
  if (details) {
    console.log(`   ${details}`);
    testResults.push(`   ${details}`);
  }
  
  if (passed) passedTests++;
}

async function testAPI(endpoint, method = 'GET', body = null, expectedStatus = 200) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.text();
    
    return {
      status: response.status,
      data: data,
      success: response.status === expectedStatus
    };
  } catch (error) {
    return {
      status: 0,
      data: error.message,
      success: false
    };
  }
}

async function runComprehensiveTests() {
  console.log('📡 Phase 1: API Endpoint Testing\n');
  
  // Test 1: Health Check
  console.log('🔍 Testing API Health...');
  const healthTest = await testAPI('/api/health');
  logTest('API Health Check', healthTest.success, `Status: ${healthTest.status}`);
  
  // Test 2: Social Connect API (GET)
  console.log('\n🔗 Testing Social Connection API...');
  const connectGetTest = await testAPI('/api/social/connect', 'GET', null, 401); // Should require auth
  logTest('Social Connect API (GET) - Auth Required', connectGetTest.status === 401, `Status: ${connectGetTest.status}`);
  
  // Test 3: Social Connect API (POST) - Without Auth
  const connectPostTest = await testAPI('/api/social/connect', 'POST', { platform: 'TWITTER' }, 401);
  logTest('Social Connect API (POST) - Auth Required', connectPostTest.status === 401, `Status: ${connectPostTest.status}`);
  
  // Test 4: Social Disconnect API - Without Auth
  const disconnectTest = await testAPI('/api/social/disconnect', 'POST', { accountId: 'test' }, 401);
  logTest('Social Disconnect API - Auth Required', disconnectTest.status === 401, `Status: ${disconnectTest.status}`);
  
  // Test 5: Social Callback API
  const callbackTest = await testAPI('/api/social/callback/twitter?oauth_token=test&oauth_verifier=test', 'GET', null, 302);
  logTest('Social Callback API - Redirect Response', callbackTest.status === 302 || callbackTest.status === 401, `Status: ${callbackTest.status}`);
  
  console.log('\n📄 Phase 2: Page Accessibility Testing\n');
  
  // Test 6: Social Management Page
  const socialPageTest = await testAPI('/social');
  logTest('Social Management Page Accessible', socialPageTest.success, `Status: ${socialPageTest.status}`);
  
  // Test 7: Social Test Page
  const testPageTest = await testAPI('/test-social');
  logTest('Social Test Page Accessible', testPageTest.success, `Status: ${testPageTest.status}`);
  
  // Test 8: Dashboard Page
  const dashboardTest = await testAPI('/dashboard');
  logTest('Dashboard Page Accessible', dashboardTest.success, `Status: ${dashboardTest.status}`);
  
  console.log('\n🎨 Phase 3: Component Integration Testing\n');
  
  // Test 9: Check if SocialConnectionManager component exists
  const componentPath = path.join(__dirname, 'src/components/social/social-connection-manager.tsx');
  const componentExists = fs.existsSync(componentPath);
  logTest('SocialConnectionManager Component Exists', componentExists);
  
  if (componentExists) {
    const componentContent = fs.readFileSync(componentPath, 'utf8');
    
    // Test 10: Component has required functions
    logTest('Component has loadConnectionStatus', componentContent.includes('loadConnectionStatus'));
    logTest('Component has connectPlatform', componentContent.includes('connectPlatform'));
    logTest('Component has disconnectAccount', componentContent.includes('disconnectAccount'));
    logTest('Component has platform icons', componentContent.includes('getPlatformIcon'));
    logTest('Component has error handling', componentContent.includes('toast.error'));
  }
  
  console.log('\n🔐 Phase 4: OAuth Flow Testing\n');
  
  // Test 11: Check OAuth URL generation patterns
  const connectApiPath = path.join(__dirname, 'src/app/api/social/connect/route.ts');
  if (fs.existsSync(connectApiPath)) {
    const connectContent = fs.readFileSync(connectApiPath, 'utf8');
    
    logTest('Twitter OAuth URL Generation', connectContent.includes('getAuthUrl'));
    logTest('Facebook OAuth URL Generation', connectContent.includes('facebook.com/v18.0/dialog/oauth'));
    logTest('LinkedIn OAuth URL Generation', connectContent.includes('linkedin.com/oauth/v2/authorization'));
    logTest('Platform Validation', connectContent.includes('z.enum'));
  }
  
  console.log('\n🔒 Phase 5: Security Testing\n');
  
  // Test 12: Check authentication requirements
  const callbackApiPath = path.join(__dirname, 'src/app/api/social/callback/[platform]/route.ts');
  if (fs.existsSync(callbackApiPath)) {
    const callbackContent = fs.readFileSync(callbackApiPath, 'utf8');
    
    logTest('Callback API has user verification', callbackContent.includes('auth.getUser'));
    logTest('Callback API has error handling', callbackContent.includes('try {') && callbackContent.includes('catch'));
    logTest('Callback API has redirect handling', callbackContent.includes('NextResponse.redirect'));
  }
  
  console.log('\n📱 Phase 6: UI Component Testing\n');
  
  // Test 13: Check social page implementation
  const socialPagePath = path.join(__dirname, 'src/app/social/page.tsx');
  if (fs.existsSync(socialPagePath)) {
    const socialPageContent = fs.readFileSync(socialPagePath, 'utf8');
    
    logTest('Social page imports SocialConnectionManager', socialPageContent.includes('SocialConnectionManager'));
    logTest('Social page has stats display', socialPageContent.includes('stats.totalConnected'));
    logTest('Social page has callback handling', socialPageContent.includes('handleCallbackParams'));
    logTest('Social page has authentication check', socialPageContent.includes('checkAuth'));
  }
  
  console.log('\n🗄️ Phase 7: Database Schema Testing\n');
  
  // Test 14: Check if social accounts table exists in schema
  const schemaPath = path.join(__dirname, 'prisma/schema.prisma');
  if (fs.existsSync(schemaPath)) {
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    
    logTest('Social accounts table exists', schemaContent.includes('model SocialAccount') || schemaContent.includes('social_accounts'));
    logTest('Activities table exists', schemaContent.includes('model Activity') || schemaContent.includes('activities'));
  }
  
  console.log('\n🌐 Phase 8: Platform Support Testing\n');
  
  // Test 15: Verify all platforms are supported
  if (fs.existsSync(connectApiPath)) {
    const connectContent = fs.readFileSync(connectApiPath, 'utf8');
    
    const platforms = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK', 'SNAPCHAT'];
    platforms.forEach(platform => {
      logTest(`Platform ${platform} supported`, connectContent.includes(platform));
    });
  }
  
  console.log('\n📊 Phase 9: Error Handling Testing\n');
  
  // Test 16: Test invalid platform
  const invalidPlatformTest = await testAPI('/api/social/connect', 'POST', { platform: 'INVALID' }, 401);
  logTest('Invalid platform handling', invalidPlatformTest.status === 400 || invalidPlatformTest.status === 401);
  
  // Test 17: Test missing parameters
  const missingParamsTest = await testAPI('/api/social/connect', 'POST', {}, 401);
  logTest('Missing parameters handling', missingParamsTest.status === 400 || missingParamsTest.status === 401);
  
  console.log('\n🔄 Phase 10: Integration Testing\n');
  
  // Test 18: Check if all required files exist
  const requiredFiles = [
    'src/app/api/social/connect/route.ts',
    'src/app/api/social/callback/[platform]/route.ts',
    'src/app/api/social/disconnect/route.ts',
    'src/components/social/social-connection-manager.tsx',
    'src/app/social/page.tsx',
    'src/app/test-social/page.tsx'
  ];
  
  requiredFiles.forEach(file => {
    const exists = fs.existsSync(path.join(__dirname, file));
    logTest(`Required file exists: ${file}`, exists);
  });
  
  // Final Results
  console.log('\n' + '='.repeat(80));
  console.log('🎯 COMPREHENSIVE SOCIAL MEDIA INTEGRATION TEST RESULTS');
  console.log('='.repeat(80));
  console.log(`📊 Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests >= Math.floor(totalTests * 0.95)) {
    console.log('\n🎉 SOCIAL MEDIA INTEGRATION SYSTEM FULLY FUNCTIONAL!');
    console.log('✅ All critical functionality verified');
    console.log('✅ API endpoints working correctly');
    console.log('✅ Security measures in place');
    console.log('✅ Components properly integrated');
    console.log('✅ Error handling implemented');
    console.log('✅ Platform support comprehensive');
    console.log('\n🚀 SYSTEM READY FOR PRODUCTION!');
  } else if (passedTests >= Math.floor(totalTests * 0.85)) {
    console.log('\n⚠️ SOCIAL MEDIA INTEGRATION MOSTLY FUNCTIONAL');
    console.log('✅ Core functionality working');
    console.log('⚠️ Some minor issues detected');
    console.log('🔧 Review failed tests for improvements');
  } else {
    console.log('\n❌ SOCIAL MEDIA INTEGRATION NEEDS ATTENTION');
    console.log('❌ Multiple critical issues detected');
    console.log('🔧 Review implementation and fix issues');
  }
  
  console.log('\n📋 Detailed Test Results:');
  console.log('='.repeat(80));
  testResults.forEach(result => console.log(result));
  
  console.log('\n🔗 Test URLs for Manual Verification:');
  console.log('• Social Management: http://localhost:3001/social');
  console.log('• Social Testing: http://localhost:3001/test-social');
  console.log('• Dashboard: http://localhost:3001/dashboard');
  console.log('• Post Creation: http://localhost:3001/posts/new');
  
  console.log('\n📁 Key Features Tested:');
  console.log('• API endpoint accessibility and security');
  console.log('• OAuth flow implementation');
  console.log('• Component integration');
  console.log('• Error handling and validation');
  console.log('• Platform support');
  console.log('• Database schema');
  console.log('• UI component functionality');
  console.log('• Security and authentication');
  
  return passedTests >= Math.floor(totalTests * 0.95);
}

// Run the comprehensive test suite
runComprehensiveTests()
  .then(success => {
    console.log(`\n🏁 Testing completed. Success: ${success}`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n❌ Testing failed with error:', error);
    process.exit(1);
  });
