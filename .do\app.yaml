# DigitalOcean App Platform Specification for eWasl.com
# Enhanced Media Processing Pipeline Production Deployment

name: ewasl-social-scheduler
region: nyc3

services:
  - name: web
    source_dir: /
    github:
      repo: TahaOsa/eWasl.com
      branch: main
      deploy_on_push: true
    
    # Build Configuration
    build_command: npm run build
    run_command: npm start
    
    # Instance Configuration
    instance_count: 2
    instance_size_slug: apps-s-2vcpu-4gb
    
    # HTTP Configuration
    http_port: 3000
    
    # Health Check Configuration
    health_check:
      http_path: /api/system/health
      initial_delay_seconds: 30
      period_seconds: 30
      timeout_seconds: 10
      success_threshold: 1
      failure_threshold: 3
    
    # Environment Variables
    envs:
      # Application Environment
      - key: NODE_ENV
        value: production
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_APP_URL
        value: "${APP_URL}"
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_API_URL
        value: "${APP_URL}/api"
        scope: RUN_TIME
      
      # Supabase Configuration
      - key: NEXT_PUBLIC_SUPABASE_URL
        value: https://ajpcbugydftdyhlbddpl.supabase.co
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
        scope: RUN_TIME
      
      - key: SUPABASE_SERVICE_ROLE_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDk3MTk3NCwiZXhwIjoyMDUwNTQ3OTc0fQ.SERVICE_ROLE_KEY_HERE
        scope: RUN_TIME
      # DigitalOcean Configuration
      - key: DIGITALOCEAN_API_TOKEN
        value: ***********************************************************************
        scope: RUN_TIME
      
      - key: DIGITALOCEAN_SPACES_ENDPOINT
        value: https://nyc3.digitaloceanspaces.com
        scope: RUN_TIME
      
      - key: DIGITALOCEAN_SPACES_BUCKET
        value: ewasl-media-production
        scope: RUN_TIME
      
      - key: DIGITALOCEAN_SPACES_REGION
        value: nyc3
        scope: RUN_TIME
      
      # CDN Configuration
      - key: CDN_PROVIDER
        value: digitalocean
        scope: RUN_TIME
      
      - key: CDN_DOMAIN
        value: ewasl-media-production.nyc3.cdn.digitaloceanspaces.com
        scope: RUN_TIME
      
      - key: CDN_ENABLE_PURGING
        value: "true"
        scope: RUN_TIME
      
      # Stripe Configuration
      - key: STRIPE_PUBLISHABLE_KEY
        value: pk_live_51NlyLHEpEYvJL85MjYJKc8Zq0Ow8oKQZPC8zsLEmci1lh5V10usokfB7WDwfsdsHw4tKg7gQYkSM3MELtznTFFmU00VQPK2Taz
        scope: RUN_TIME
      
      - key: STRIPE_SECRET_KEY
        value: ***********************************************************************************************************
        scope: RUN_TIME
      
      # Social Media API Keys
      - key: LINKEDIN_CLIENT_ID
        value: 787coegnsdocvq
        scope: RUN_TIME
      
      - key: LINKEDIN_CLIENT_SECRET
        value: WPL_AP1.F6yN8tH55DHmqsqP.ep1SGQ==
        scope: RUN_TIME
      
      - key: LINKEDIN_REDIRECT_URI
        value: "${APP_URL}/api/linkedin/callback"
        scope: RUN_TIME
      
      - key: FACEBOOK_APP_ID
        value: "1366325774493759"
        scope: RUN_TIME
      
      - key: FACEBOOK_REDIRECT_URI
        value: "${APP_URL}/api/facebook/callback"
        scope: RUN_TIME      
      # Media Processing Configuration (Production Scale)
      - key: MEDIA_MAX_FILE_SIZE
        value: "209715200"
        scope: RUN_TIME
      
      - key: MEDIA_MAX_CONCURRENT_UPLOADS
        value: "50"
        scope: RUN_TIME
      
      - key: MEDIA_ENABLE_AI_OPTIMIZATION
        value: "true"
        scope: RUN_TIME
      
      - key: MEDIA_ENABLE_BATCH_PROCESSING
        value: "true"
        scope: RUN_TIME
      
      # Monitoring Configuration
      - key: MONITORING_ENABLE_METRICS
        value: "true"
        scope: RUN_TIME
      
      - key: MONITORING_ENABLE_ALERTS
        value: "true"
        scope: RUN_TIME
      
      - key: MONITORING_LOG_LEVEL
        value: warn
        scope: RUN_TIME
      
      # Performance Configuration
      - key: PERFORMANCE_ENABLE_CACHING
        value: "true"
        scope: RUN_TIME
      
      - key: PERFORMANCE_MAX_WORKERS
        value: "8"
        scope: RUN_TIME
      
      # Security Configuration
      - key: SECURITY_ENABLE_RATE_LIMIT
        value: "true"
        scope: RUN_TIME
      
      - key: SECURITY_RATE_LIMIT_MAX
        value: "500"
        scope: RUN_TIME
      
      # Feature Flags
      - key: FEATURE_AI_OPTIMIZATION
        value: "true"
        scope: RUN_TIME
      
      - key: FEATURE_RTL_SUPPORT
        value: "true"
        scope: RUN_TIME
      
      # Next.js Configuration
      - key: NEXTAUTH_URL
        value: "${APP_URL}"
        scope: RUN_TIME

# Domain Configuration
domains:
  - domain: app.ewasl.com
    type: PRIMARY
  - domain: ewasl.com
    type: ALIAS
  - domain: www.ewasl.com
    type: ALIAS

# Alerts Configuration
alerts:
  - rule: CPU_UTILIZATION
    disabled: false
    operator: GREATER_THAN
    value: 80
    window: FIVE_MINUTES