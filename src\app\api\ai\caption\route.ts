import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for caption generation
const captionSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required').max(500, 'Prompt too long'),
  language: z.enum(['arabic', 'english']).default('arabic'),
  style: z.enum(['professional', 'casual', 'engaging', 'formal']).default('engaging'),
  count: z.number().min(1).max(5).default(3),
});

// POST - Generate AI captions
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Generating AI captions...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log('Caption generation request:', { ...body, prompt: body.prompt?.substring(0, 50) + '...' });

    // Validate request body
    const validation = captionSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        { 
          error: 'Invalid input', 
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    const { prompt, language, style, count } = validation.data;

    console.log('Generating captions for user:', user.id);

    // Check if we have OpenRouter API key
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    
    let generatedCaptions: string[] = [];

    if (openRouterApiKey) {
      try {
        // Use OpenRouter with Qwen model for cost-effective Arabic generation
        const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${openRouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com',
            'X-Title': 'eWasl Social Media Manager',
          },
          body: JSON.stringify({
            model: 'qwen/qwen-4b:free', // Free model for cost efficiency
            messages: [
              {
                role: 'system',
                content: language === 'arabic' 
                  ? `أنت خبير في كتابة المحتوى العربي لوسائل التواصل الاجتماعي. اكتب تعليقات جذابة ومؤثرة باللغة العربية بأسلوب ${style === 'professional' ? 'مهني' : style === 'casual' ? 'غير رسمي' : style === 'engaging' ? 'جذاب' : 'رسمي'}. استخدم الهاشتاغات المناسبة والرموز التعبيرية عند الحاجة.`
                  : `You are an expert social media content writer. Write ${style} and engaging captions in English. Use appropriate hashtags and emojis when suitable.`
              },
              {
                role: 'user',
                content: language === 'arabic'
                  ? `اكتب ${count} تعليقات مختلفة لمنشور عن: ${prompt}. كل تعليق يجب أن يكون في سطر منفصل ومرقم.`
                  : `Write ${count} different captions for a post about: ${prompt}. Each caption should be on a separate line and numbered.`
              }
            ],
            max_tokens: 800,
            temperature: 0.8,
          }),
        });

        if (openRouterResponse.ok) {
          const openRouterData = await openRouterResponse.json();
          const content = openRouterData.choices?.[0]?.message?.content;
          
          if (content) {
            // Parse the numbered captions
            const captions = content
              .split('\n')
              .filter((line: string) => line.trim())
              .map((line: string) => line.replace(/^\d+\.\s*/, '').trim())
              .filter((caption: string) => caption.length > 10);
            
            if (captions.length > 0) {
              generatedCaptions = captions.slice(0, count);
              console.log('Generated captions via OpenRouter:', generatedCaptions.length);
            }
          }
        } else {
          console.log('OpenRouter API failed, falling back to mock captions');
        }
      } catch (openRouterError) {
        console.error('OpenRouter API error:', openRouterError);
      }
    }

    // Fallback to high-quality mock captions if OpenRouter fails or is not configured
    if (generatedCaptions.length === 0) {
      console.log('Using fallback mock captions');
      
      const mockCaptions = language === 'arabic' ? [
        `🚀 استكشف عالم ${prompt} مع منصة eWasl! نحن هنا لنساعدك في تحقيق أهدافك بأفضل الطرق الممكنة. #${prompt.replace(/\s+/g, '_')} #eWasl #نجاح`,
        `💡 هل تعلم أن ${prompt} يمكن أن يغير حياتك؟ اكتشف المزيد من النصائح والحلول المبتكرة معنا! ✨ #${prompt.replace(/\s+/g, '_')} #تطوير_الذات`,
        `🌟 دعنا نأخذك في رحلة مثيرة لاستكشاف ${prompt}! شاركنا تجربتك وآرائك في التعليقات 👇 #${prompt.replace(/\s+/g, '_')} #مجتمع_eWasl`,
        `📈 النجاح في ${prompt} يبدأ بخطوة واحدة! تابعنا لتحصل على أحدث النصائح والاستراتيجيات المجربة 🎯 #${prompt.replace(/\s+/g, '_')} #استراتيجية`,
        `🎉 احتفل معنا بالإنجازات الجديدة في مجال ${prompt}! كل يوم هو فرصة جديدة للتعلم والنمو 🌱 #${prompt.replace(/\s+/g, '_')} #إنجاز`
      ] : [
        `🚀 Discover the world of ${prompt} with eWasl! We're here to help you achieve your goals in the best possible way. #${prompt.replace(/\s+/g, '_')} #eWasl #Success`,
        `💡 Did you know that ${prompt} can change your life? Discover more innovative tips and solutions with us! ✨ #${prompt.replace(/\s+/g, '_')} #SelfDevelopment`,
        `🌟 Let us take you on an exciting journey to explore ${prompt}! Share your experience and opinions in the comments 👇 #${prompt.replace(/\s+/g, '_')} #Community`,
        `📈 Success in ${prompt} starts with one step! Follow us for the latest proven tips and strategies 🎯 #${prompt.replace(/\s+/g, '_')} #Strategy`,
        `🎉 Celebrate with us the new achievements in ${prompt}! Every day is a new opportunity to learn and grow 🌱 #${prompt.replace(/\s+/g, '_')} #Achievement`
      ];

      // Select random captions based on style
      const shuffled = mockCaptions.sort(() => 0.5 - Math.random());
      generatedCaptions = shuffled.slice(0, count);
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'AI_CAPTION_GENERATED',
        details: `Generated ${generatedCaptions.length} captions for: ${prompt.substring(0, 50)}...`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      captions: generatedCaptions,
      prompt,
      language,
      style,
      count: generatedCaptions.length,
    }, { status: 200 });

  } catch (error) {
    console.error('AI caption generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
