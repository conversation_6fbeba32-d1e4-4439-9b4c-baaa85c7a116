# Production Dockerfile for eWasl.com
# Enhanced Media Processing Pipeline - DigitalOcean Deployment

# Use the official Node.js 18 Alpine image
FROM node:18-alpine AS base

# Install system dependencies for Sharp and FFmpeg
RUN apk add --no-cache \
    libc6-compat \
    vips-dev \
    ffmpeg \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
FROM base AS deps
RUN npm ci --only=production --prefer-offline --no-audit

# Build stage
FROM base AS builder
COPY package*.json ./
RUN npm ci --prefer-offline --no-audit

COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN npm run build

# Production stage
FROM node:18-alpine AS runner
WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache \
    vips \
    ffmpeg \
    && rm -rf /var/cache/apk/*

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy Prisma files
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma

# Create temp directory for media processing
RUN mkdir -p /tmp/ewasl-media && chown nextjs:nodejs /tmp/ewasl-media

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/api/system/health || exit 1

CMD ["node", "server.js"]
