import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { EnhancedMediaProcessingService } from '@/lib/media/enhanced-media-processing-service';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('📤 Enhanced media upload API endpoint called');

    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const description = formData.get('description') as string;
    const tags = formData.get('tags') as string;
    const targetPlatforms = formData.get('targetPlatforms') as string;
    const autoOptimize = formData.get('autoOptimize') === 'true';

    console.log('📁 Enhanced upload request details:', {
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
      description: description?.substring(0, 50),
      autoOptimize,
      targetPlatforms
    });

    // Validate file
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!file.name || file.size === 0) {
      return NextResponse.json(
        { error: 'Invalid file' },
        { status: 400 }
      );
    }

    // Parse tags and platforms
    const parsedTags = tags ? tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];
    const parsedPlatforms = targetPlatforms ? targetPlatforms.split(',').map(p => p.trim()).filter(Boolean) : [];

    // Validate platforms
    const validPlatforms = ['facebook', 'linkedin', 'instagram', 'twitter'];
    const invalidPlatforms = parsedPlatforms.filter(p => !validPlatforms.includes(p));
    if (invalidPlatforms.length > 0) {
      return NextResponse.json(
        { error: `Invalid platforms: ${invalidPlatforms.join(', ')}` },
        { status: 400 }
      );
    }

    // Create upload request
    const uploadRequest = {
      file,
      userId: user.id,
      description: description || undefined,
      tags: parsedTags.length > 0 ? parsedTags : undefined,
      targetPlatforms: parsedPlatforms.length > 0 ? parsedPlatforms : undefined,
      autoOptimize
    };

    // Initialize enhanced media processing service
    const mediaService = new EnhancedMediaProcessingService();
    const result = await mediaService.uploadMedia(uploadRequest);

    if (!result.success) {
      console.error('❌ Enhanced media upload failed:', result.error);
      return NextResponse.json(
        { 
          error: 'Enhanced media upload failed',
          message: result.error,
          uploadId: result.uploadId
        },
        { status: 500 }
      );
    }

    console.log('✅ Enhanced media upload completed successfully:', {
      uploadId: result.uploadId,
      mediaId: result.mediaFile?.id,
      status: result.processingStatus
    });

    // Log activity
    try {
      await supabase
        .from('user_activities')
        .insert({
          user_id: user.id,
          activity_type: 'enhanced_media_uploaded',
          activity_data: {
            uploadId: result.uploadId,
            fileName: file.name,
            fileSize: file.size,
            mediaType: result.mediaFile?.mediaType,
            platforms: parsedPlatforms,
            autoOptimized: autoOptimize,
            platformVersions: result.mediaFile?.platformVersions?.length || 0
          },
          created_at: new Date().toISOString()
        });
    } catch (activityError) {
      console.error('Error logging activity:', activityError);
      // Don't fail the request for activity logging errors
    }

    return NextResponse.json({
      success: true,
      message: 'Enhanced media uploaded and processed successfully',
      data: {
        uploadId: result.uploadId,
        mediaFile: result.mediaFile,
        processingStatus: result.processingStatus,
        platformVersions: result.mediaFile?.platformVersions || [],
        metadata: result.mediaFile?.metadata || {}
      }
    });

  } catch (error: any) {
    console.error('❌ Enhanced media upload API error:', error);

    // Log error for debugging
    try {
      
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        await supabase
          .from('error_logs')
          .insert({
            user_id: user.id,
            error_type: 'enhanced_media_upload_error',
            error_message: error.message,
            error_stack: error.stack,
            created_at: new Date().toISOString()
          });
      }
    } catch (logError) {
      console.error('Error logging error:', logError);
    }

    return NextResponse.json(
      { 
        error: 'Enhanced media upload failed',
        message: error.message || 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('📁 Enhanced media list API endpoint called');

    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const mediaType = searchParams.get('mediaType');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') as 'uploadedAt' | 'fileName' | 'fileSize' || 'uploadedAt';
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc';

    // Validate parameters
    if (limit > 100) {
      return NextResponse.json(
        { error: 'Limit cannot exceed 100' },
        { status: 400 }
      );
    }

    if (mediaType && !['image', 'video', 'document', 'audio'].includes(mediaType)) {
      return NextResponse.json(
        { error: 'Invalid media type' },
        { status: 400 }
      );
    }

    // Get user media
    const mediaService = new EnhancedMediaProcessingService();
    const result = await mediaService.getUserMedia(user.id, {
      mediaType: mediaType || undefined,
      limit,
      offset,
      sortBy,
      sortOrder
    });

    console.log('✅ Enhanced media list retrieved:', {
      count: result.media.length,
      total: result.total,
      mediaType,
      sortBy,
      sortOrder
    });

    return NextResponse.json({
      success: true,
      data: {
        media: result.media,
        pagination: {
          limit,
          offset,
          total: result.total,
          hasMore: offset + limit < result.total
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Enhanced media list API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch enhanced media files' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('🗑️ Enhanced media delete API endpoint called');

    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get media ID from query parameters
    const { searchParams } = new URL(request.url);
    const mediaId = searchParams.get('mediaId');

    if (!mediaId) {
      return NextResponse.json(
        { error: 'Media ID is required' },
        { status: 400 }
      );
    }

    // Delete media
    const mediaService = new EnhancedMediaProcessingService();
    const success = await mediaService.deleteMedia(mediaId, user.id);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete enhanced media file' },
        { status: 500 }
      );
    }

    console.log('✅ Enhanced media deleted successfully:', mediaId);

    // Log activity
    try {
      await supabase
        .from('user_activities')
        .insert({
          user_id: user.id,
          activity_type: 'enhanced_media_deleted',
          activity_data: {
            mediaId
          },
          created_at: new Date().toISOString()
        });
    } catch (activityError) {
      console.error('Error logging activity:', activityError);
    }

    return NextResponse.json({
      success: true,
      message: 'Enhanced media file deleted successfully'
    });

  } catch (error: any) {
    console.error('❌ Enhanced media delete API error:', error);
    return NextResponse.json(
      { error: 'Failed to delete enhanced media file' },
      { status: 500 }
    );
  }
}
