import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { generateAPIReport } from '@/lib/config/api-validation';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

/**
 * Test all social media API connections
 * GET /api/admin/test-apis
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    // Check if user is authenticated and is admin
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is admin (you can modify this logic based on your admin system)
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(user.email || '');
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    console.log('Starting API validation tests...');
    
    // Generate comprehensive API report
    const report = await generateAPIReport();
    
    console.log('API validation completed:', {
      configured: report.summary.configured,
      valid: report.summary.valid,
      issues: report.summary.issues
    });

    // Log results for debugging
    report.results.forEach(result => {
      console.log(`${result.platform}: configured=${result.isConfigured}, valid=${result.isValid}`, 
        result.error ? `Error: ${result.error}` : '');
    });

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      report,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        hasTwitterKey: !!process.env.TWITTER_API_KEY,
        hasFacebookId: !!process.env.FACEBOOK_APP_ID,
        hasLinkedInId: !!process.env.LINKEDIN_CLIENT_ID,
      }
    });

  } catch (error) {
    console.error('API testing error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Test specific platform API
 * POST /api/admin/test-apis
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    // Check authentication
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(user.email || '');
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { platform, action } = body;

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform parameter required' },
        { status: 400 }
      );
    }

    let result;
    
    switch (platform.toLowerCase()) {
      case 'twitter':
        const { testTwitterAPI } = await import('@/lib/config/api-validation');
        result = await testTwitterAPI();
        break;
        
      case 'facebook':
        const { testFacebookAPI } = await import('@/lib/config/api-validation');
        result = await testFacebookAPI();
        break;
        
      case 'linkedin':
        const { testLinkedInAPI } = await import('@/lib/config/api-validation');

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
        result = await testLinkedInAPI();
        break;
        
      default:
        return NextResponse.json(
          { error: 'Unsupported platform' },
          { status: 400 }
        );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'API_TEST',
        details: `Tested ${platform} API: ${result.isValid ? 'SUCCESS' : 'FAILED'}`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      platform,
      result,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Platform API testing error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
