/**
 * Integration Tests for Advanced Scheduling System
 * Tests the complete workflow from UI to database
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, jest } from '@jest/globals';

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';

describe('Advanced Scheduling Integration Tests', () => {
  beforeAll(async () => {
    // Setup test database or mock
    console.log('Setting up integration test environment...');
  });

  afterAll(async () => {
    // Cleanup test database
    console.log('Cleaning up integration test environment...');
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Recurring Posts Workflow', () => {
    it('should create daily recurring posts end-to-end', async () => {
      // This would be a full integration test that:
      // 1. Creates a user session
      // 2. Sets up social accounts
      // 3. Creates recurring posts via API
      // 4. Verifies database state
      // 5. Checks generated instances

      const testData = {
        content: 'Integration test daily recurring post',
        social_account_ids: ['test-account-1'],
        recurring_pattern: {
          frequency: 'daily',
          interval: 1,
          time: '09:00',
        },
        start_date: '2024-01-01T09:00:00Z',
        end_date: '2024-01-05T09:00:00Z',
        time_zone: 'UTC',
      };

      // Mock the API call
      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          parentPost: { id: 'parent-post-id' },
          instancesCreated: 5,
          instances: [
            { id: 'instance-1', scheduled_at: '2024-01-01T09:00:00Z' },
            { id: 'instance-2', scheduled_at: '2024-01-02T09:00:00Z' },
            { id: 'instance-3', scheduled_at: '2024-01-03T09:00:00Z' },
            { id: 'instance-4', scheduled_at: '2024-01-04T09:00:00Z' },
            { id: 'instance-5', scheduled_at: '2024-01-05T09:00:00Z' },
          ],
        }),
      });

      global.fetch = mockFetch;

      const response = await fetch('/api/posts/recurring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      });

      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(result.success).toBe(true);
      expect(result.instancesCreated).toBe(5);
      expect(result.instances).toHaveLength(5);

      // Verify the API was called with correct data
      expect(mockFetch).toHaveBeenCalledWith('/api/posts/recurring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      });
    });

    it('should handle weekly recurring posts with specific days', async () => {
      const testData = {
        content: 'Integration test weekly recurring post',
        social_account_ids: ['test-account-1'],
        recurring_pattern: {
          frequency: 'weekly',
          interval: 1,
          days_of_week: [1, 3, 5], // Monday, Wednesday, Friday
          time: '14:00',
        },
        start_date: '2024-01-01T14:00:00Z', // Monday
        end_date: '2024-01-15T14:00:00Z',
        time_zone: 'UTC',
      };

      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          parentPost: { id: 'parent-post-id' },
          instancesCreated: 6,
          instances: [
            { id: 'instance-1', scheduled_at: '2024-01-01T14:00:00Z' },
            { id: 'instance-2', scheduled_at: '2024-01-03T14:00:00Z' },
            { id: 'instance-3', scheduled_at: '2024-01-05T14:00:00Z' },
            { id: 'instance-4', scheduled_at: '2024-01-08T14:00:00Z' },
            { id: 'instance-5', scheduled_at: '2024-01-10T14:00:00Z' },
            { id: 'instance-6', scheduled_at: '2024-01-12T14:00:00Z' },
          ],
        }),
      });

      global.fetch = mockFetch;

      const response = await fetch('/api/posts/recurring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      });

      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(result.success).toBe(true);
      expect(result.instancesCreated).toBe(6);

      // Verify that instances are created only on specified days
      const scheduledDays = result.instances.map((instance: any) => 
        new Date(instance.scheduled_at).getDay()
      );
      
      // All scheduled days should be Monday (1), Wednesday (3), or Friday (5)
      scheduledDays.forEach((day: number) => {
        expect([1, 3, 5]).toContain(day);
      });
    });
  });

  describe('Bulk Scheduling Workflow', () => {
    it('should handle bulk post scheduling end-to-end', async () => {
      const testData = {
        posts: [
          {
            content: 'Bulk post 1',
            scheduled_at: '2024-01-01T09:00:00Z',
            social_account_ids: ['test-account-1'],
            time_zone: 'UTC',
          },
          {
            content: 'Bulk post 2',
            scheduled_at: '2024-01-02T09:00:00Z',
            social_account_ids: ['test-account-1'],
            time_zone: 'UTC',
          },
          {
            content: 'Bulk post 3',
            scheduled_at: '2024-01-03T09:00:00Z',
            social_account_ids: ['test-account-1'],
            time_zone: 'UTC',
          },
        ],
      };

      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          bulkOperationId: 'bulk-operation-id',
          totalPosts: 3,
          completedItems: 3,
          failedItems: 0,
          errors: [],
        }),
      });

      global.fetch = mockFetch;

      const response = await fetch('/api/posts/bulk-schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      });

      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(result.success).toBe(true);
      expect(result.totalPosts).toBe(3);
      expect(result.completedItems).toBe(3);
      expect(result.failedItems).toBe(0);
    });

    it('should handle CSV import workflow', async () => {
      const testData = {
        action: 'csv_import',
        csv_data: 'content,media_url\n"CSV post 1","https://example.com/image1.jpg"\n"CSV post 2","https://example.com/image2.jpg"',
        social_account_ids: ['test-account-1'],
        time_zone: 'UTC',
        start_date: '2024-01-01T09:00:00Z',
        interval_hours: 24,
      };

      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          bulkOperationId: 'bulk-operation-id',
          totalPosts: 2,
          completedItems: 2,
          failedItems: 0,
          errors: [],
        }),
      });

      global.fetch = mockFetch;

      const response = await fetch('/api/posts/bulk-schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      });

      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(result.success).toBe(true);
      expect(result.totalPosts).toBe(2);
      expect(result.completedItems).toBe(2);
      expect(result.failedItems).toBe(0);
    });
  });

  describe('Calendar Integration', () => {
    it('should fetch and display scheduled posts correctly', async () => {
      const mockPosts = [
        {
          id: 'post-1',
          title: 'Test Post 1',
          start: new Date('2024-01-15T09:00:00Z'),
          end: new Date('2024-01-15T09:30:00Z'),
          status: 'SCHEDULED',
          platforms: ['TWITTER'],
          content: 'Test content 1',
          isRecurring: false,
        },
        {
          id: 'post-2',
          title: 'Test Post 2',
          start: new Date('2024-01-16T14:00:00Z'),
          end: new Date('2024-01-16T14:30:00Z'),
          status: 'SCHEDULED',
          platforms: ['FACEBOOK'],
          content: 'Test content 2',
          isRecurring: true,
          parentRecurringId: 'recurring-1',
        },
      ];

      // Mock calendar data fetch
      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          posts: mockPosts,
          pagination: {
            page: 1,
            limit: 50,
            total: 2,
            totalPages: 1,
          },
        }),
      });

      global.fetch = mockFetch;

      const response = await fetch('/api/posts?status=SCHEDULED&limit=50');
      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(result.success).toBe(true);
      expect(result.posts).toHaveLength(2);
      expect(result.posts[0].status).toBe('SCHEDULED');
      expect(result.posts[1].isRecurring).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const testData = {
        content: 'Test post',
        social_account_ids: ['invalid-account'],
        recurring_pattern: {
          frequency: 'daily',
          interval: 1,
          time: '09:00',
        },
        start_date: '2024-01-01T09:00:00Z',
      };

      const mockFetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 400,
        json: async () => ({
          error: 'Invalid social accounts',
        }),
      });

      global.fetch = mockFetch;

      const response = await fetch('/api/posts/recurring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      });

      const result = await response.json();

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);
      expect(result.error).toBe('Invalid social accounts');
    });

    it('should handle network errors', async () => {
      const mockFetch = jest.fn().mockRejectedValue(new Error('Network error'));
      global.fetch = mockFetch;

      try {
        await fetch('/api/posts/recurring', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        });
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });
  });

  describe('Performance Tests', () => {
    it('should handle large bulk operations efficiently', async () => {
      const largeBatch = Array(100).fill(null).map((_, index) => ({
        content: `Bulk post ${index + 1}`,
        scheduled_at: new Date(Date.now() + (index * 60 * 60 * 1000)).toISOString(),
        social_account_ids: ['test-account-1'],
        time_zone: 'UTC',
      }));

      const testData = { posts: largeBatch };

      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          bulkOperationId: 'bulk-operation-id',
          totalPosts: 100,
          completedItems: 100,
          failedItems: 0,
          errors: [],
        }),
      });

      global.fetch = mockFetch;

      const startTime = Date.now();
      const response = await fetch('/api/posts/bulk-schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData),
      });
      const endTime = Date.now();

      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(result.totalPosts).toBe(100);
      expect(result.completedItems).toBe(100);
      
      // Performance assertion - should complete within reasonable time
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(5000); // Less than 5 seconds
    });
  });
});
