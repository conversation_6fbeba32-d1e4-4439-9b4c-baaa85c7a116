import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { SchedulerIntegration } from '@/lib/scheduler/scheduler-integration';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Global scheduler instance
let schedulerInstance: SchedulerIntegration | null = null;

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Processing scheduled posts...');

    // Get user from Supabase Auth (for admin operations)
    
    
    // Check if this is an internal call or authenticated user
    const authHeader = request.headers.get('authorization');
    const isInternalCall = authHeader === `Bearer ${process.env.INTERNAL_API_KEY}`;
    
    if (!isInternalCall) {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }
    }

    // Initialize scheduler if not already done
    if (!schedulerInstance) {
      schedulerInstance = new SchedulerIntegration();
      await schedulerInstance.initialize();
      console.log('Scheduler integration initialized');
    }

    // Get current time
    const now = new Date();
    const nowISO = now.toISOString();

    // Find posts that are due for publishing
    const { data: duePosts, error: postsError } = await supabase
      .from('posts')
      .select(`
        id,
        user_id,
        content,
        media_url,
        platforms,
        scheduled_at,
        status
      `)
      .eq('status', 'SCHEDULED')
      .lte('scheduled_at', nowISO)
      .order('scheduled_at', { ascending: true })
      .limit(50); // Process up to 50 posts at a time

    if (postsError) {
      console.error('Error fetching due posts:', postsError);
      return NextResponse.json(
        { error: 'Failed to fetch due posts' },
        { status: 500 }
      );
    }

    if (!duePosts || duePosts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No posts due for publishing',
        processed: 0,
        timestamp: nowISO,
      });
    }

    console.log(`Found ${duePosts.length} posts due for publishing`);

    // Process each due post
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const post of duePosts) {
      try {
        // Update post status to PROCESSING
        await supabase
          .from('posts')
          .update({
            status: 'PROCESSING',
            updated_at: nowISO,
          })
          .eq('id', post.id);

        // Get user's social accounts for the platforms
        const { data: socialAccounts, error: accountsError } = await supabase
          .from('social_accounts')
          .select('id, platform, access_token, is_active')
          .eq('user_id', post.user_id)
          .in('platform', post.platforms)
          .eq('is_active', true);

        if (accountsError || !socialAccounts || socialAccounts.length === 0) {
          throw new Error('No active social accounts found for platforms: ' + post.platforms.join(', '));
        }

        // Process each platform
        const platformResults = [];
        for (const platform of post.platforms) {
          const socialAccount = socialAccounts.find(acc => acc.platform === platform);
          
          if (!socialAccount) {
            platformResults.push({
              platform,
              success: false,
              error: 'No active social account found',
            });
            continue;
          }

          try {
            // Create publish job for this platform
            const { data: job, error: jobError } = await supabase
              .from('job_queue')
              .insert({
                job_type: 'publish-post',
                job_data: {
                  postId: post.id,
                  userId: post.user_id,
                  content: post.content,
                  mediaUrl: post.media_url,
                  platform,
                  socialAccountId: socialAccount.id,
                  accessToken: socialAccount.access_token,
                },
                priority: 10, // High priority for immediate processing
                max_attempts: 3,
                scheduled_at: nowISO,
                created_by: post.user_id,
              })
              .select()
              .single();

            if (jobError) {
              throw new Error(`Failed to create job: ${jobError.message}`);
            }

            platformResults.push({
              platform,
              success: true,
              jobId: job.id,
            });

          } catch (platformError) {
            console.error(`Error processing platform ${platform}:`, platformError);
            platformResults.push({
              platform,
              success: false,
              error: platformError.message,
            });
          }
        }

        // Check if all platforms were successful
        const allSuccessful = platformResults.every(r => r.success);
        const anySuccessful = platformResults.some(r => r.success);

        if (allSuccessful) {
          // All platforms successful - keep as PROCESSING (jobs will update to PUBLISHED)
          successCount++;
        } else if (anySuccessful) {
          // Some platforms successful - keep as PROCESSING
          successCount++;
        } else {
          // All platforms failed - mark as FAILED
          await supabase
            .from('posts')
            .update({
              status: 'FAILED',
              updated_at: nowISO,
            })
            .eq('id', post.id);
          
          errorCount++;
        }

        results.push({
          postId: post.id,
          scheduledAt: post.scheduled_at,
          platforms: platformResults,
          success: anySuccessful,
        });

      } catch (postError) {
        console.error(`Error processing post ${post.id}:`, postError);
        
        // Mark post as failed
        await supabase
          .from('posts')
          .update({
            status: 'FAILED',
            updated_at: nowISO,
          })
          .eq('id', post.id);

        results.push({
          postId: post.id,
          scheduledAt: post.scheduled_at,
          success: false,
          error: postError.message,
        });

        errorCount++;
      }
    }

    console.log(`Processed ${duePosts.length} posts: ${successCount} successful, ${errorCount} failed`);

    return NextResponse.json({
      success: true,
      message: `Processed ${duePosts.length} scheduled posts`,
      processed: duePosts.length,
      successful: successCount,
      failed: errorCount,
      results,
      timestamp: nowISO,
    });

  } catch (error) {
    console.error('Error processing scheduled posts:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to process scheduled posts',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// GET /api/scheduler/process - Get processing status
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get scheduler status
    const status = schedulerInstance ? schedulerInstance.getStatus() : {
      isInitialized: false,
      schedulerStatus: null,
      queueStatus: null,
    };

    // Get recent processing statistics
    const { data: recentJobs, error: jobsError } = await supabase
      .from('job_queue')
      .select('status, job_type, created_at')
      .eq('job_type', 'publish-post')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
      .order('created_at', { ascending: false });

    const stats = {
      total: recentJobs?.length || 0,
      pending: recentJobs?.filter(j => j.status === 'pending').length || 0,
      processing: recentJobs?.filter(j => j.status === 'processing').length || 0,
      completed: recentJobs?.filter(j => j.status === 'completed').length || 0,
      failed: recentJobs?.filter(j => j.status === 'failed').length || 0,
    };

    return NextResponse.json({
      success: true,
      status,
      stats,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error getting processing status:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get processing status',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
