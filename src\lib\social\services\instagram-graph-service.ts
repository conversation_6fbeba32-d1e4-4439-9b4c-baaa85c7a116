/**
 * Instagram Graph API Service
 * Handles Instagram business account operations via Facebook Graph API
 * Replaces deprecated Instagram Basic Display API
 */

export interface InstagramAccount {
  id: string;
  pageId: string;
  pageName: string;
  username?: string;
  profilePictureUrl?: string;
  followersCount?: number;
  mediaCount?: number;
  accessToken: string;
}

export interface InstagramMediaContainer {
  id: string;
  status: 'IN_PROGRESS' | 'FINISHED' | 'ERROR';
}

export interface InstagramPublishResult {
  success: boolean;
  postId?: string;
  url?: string;
  error?: string;
}

export interface PostContent {
  content: string;
  mediaUrl?: string;
  mediaType?: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
}

export class InstagramGraphService {
  private readonly baseUrl = 'https://graph.facebook.com/v19.0';

  /**
   * Get Instagram business accounts connected to Facebook pages
   */
  async getBusinessAccounts(accessToken: string): Promise<InstagramAccount[]> {
    try {
      console.log('Fetching Instagram business accounts...');

      // Step 1: Get Facebook pages
      const pagesResponse = await fetch(
        `${this.baseUrl}/me/accounts?fields=id,name,access_token&access_token=${accessToken}`
      );

      if (!pagesResponse.ok) {
        throw new Error(`Failed to fetch Facebook pages: ${pagesResponse.statusText}`);
      }

      const pagesData = await pagesResponse.json();
      
      if (pagesData.error) {
        throw new Error(`Facebook API error: ${pagesData.error.message}`);
      }

      const instagramAccounts: InstagramAccount[] = [];

      // Step 2: Check each page for Instagram business account
      for (const page of pagesData.data || []) {
        try {
          const igResponse = await fetch(
            `${this.baseUrl}/${page.id}?fields=instagram_business_account{id,username,profile_picture_url,followers_count,media_count}&access_token=${page.access_token}`
          );

          if (igResponse.ok) {
            const igData = await igResponse.json();
            
            if (igData.instagram_business_account) {
              const igAccount = igData.instagram_business_account;
              instagramAccounts.push({
                id: igAccount.id,
                pageId: page.id,
                pageName: page.name,
                username: igAccount.username,
                profilePictureUrl: igAccount.profile_picture_url,
                followersCount: igAccount.followers_count,
                mediaCount: igAccount.media_count,
                accessToken: page.access_token
              });
            }
          }
        } catch (error) {
          console.warn(`Failed to check Instagram account for page ${page.id}:`, error);
        }
      }

      console.log(`Found ${instagramAccounts.length} Instagram business accounts`);
      return instagramAccounts;

    } catch (error) {
      console.error('Error fetching Instagram business accounts:', error);
      throw error;
    }
  }

  /**
   * Publish a post to Instagram
   */
  async publishPost(account: InstagramAccount, content: PostContent): Promise<InstagramPublishResult> {
    try {
      console.log('Publishing to Instagram:', {
        accountId: account.id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl
      });

      if (!content.mediaUrl) {
        throw new Error('Instagram posts require media (image or video)');
      }

      // Step 1: Create media container
      const containerData: any = {
        caption: content.content,
        access_token: account.accessToken
      };

      // Determine media type and set appropriate field
      if (content.mediaType === 'VIDEO' || this.isVideoUrl(content.mediaUrl)) {
        containerData.media_type = 'VIDEO';
        containerData.video_url = content.mediaUrl;
      } else {
        containerData.image_url = content.mediaUrl;
      }

      const containerResponse = await fetch(
        `${this.baseUrl}/${account.id}/media`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(containerData)
        }
      );

      if (!containerResponse.ok) {
        const errorData = await containerResponse.json();
        throw new Error(`Failed to create media container: ${errorData.error?.message || containerResponse.statusText}`);
      }

      const container = await containerResponse.json();

      if (container.error) {
        throw new Error(`Instagram API error: ${container.error.message}`);
      }

      // Step 2: Wait for container to be ready (for videos)
      if (content.mediaType === 'VIDEO' || this.isVideoUrl(content.mediaUrl)) {
        await this.waitForContainerReady(container.id, account.accessToken);
      }

      // Step 3: Publish the container
      const publishResponse = await fetch(
        `${this.baseUrl}/${account.id}/media_publish`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            creation_id: container.id,
            access_token: account.accessToken
          })
        }
      );

      if (!publishResponse.ok) {
        const errorData = await publishResponse.json();
        throw new Error(`Failed to publish media: ${errorData.error?.message || publishResponse.statusText}`);
      }

      const result = await publishResponse.json();

      if (result.error) {
        throw new Error(`Instagram publish error: ${result.error.message}`);
      }

      const postUrl = `https://www.instagram.com/p/${this.getInstagramPostCode(result.id)}/`;

      console.log('Instagram post published successfully:', {
        postId: result.id,
        postUrl
      });

      return {
        success: true,
        postId: result.id,
        url: postUrl
      };

    } catch (error) {
      console.error('Failed to publish Instagram post:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get Instagram post analytics
   */
  async getPostAnalytics(postId: string, accessToken: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/${postId}/insights?metric=impressions,reach,likes,comments,shares,saves&access_token=${accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(`Instagram API error: ${data.error.message}`);
      }

      return data;

    } catch (error) {
      console.error('Error fetching Instagram analytics:', error);
      throw error;
    }
  }

  /**
   * Get account insights
   */
  async getAccountInsights(accountId: string, accessToken: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/${accountId}/insights?metric=impressions,reach,profile_views,follower_count&period=day&access_token=${accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch account insights: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(`Instagram API error: ${data.error.message}`);
      }

      return data;

    } catch (error) {
      console.error('Error fetching Instagram account insights:', error);
      throw error;
    }
  }

  /**
   * Test Instagram account connection
   */
  async testConnection(account: InstagramAccount): Promise<any> {
    try {
      console.log('Testing Instagram connection:', {
        accountId: account.id,
        username: account.username
      });

      const response = await fetch(
        `${this.baseUrl}/${account.id}?fields=id,username,account_type,media_count,followers_count&access_token=${account.accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Connection test failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(`Instagram API error: ${data.error.message}`);
      }

      console.log('Instagram connection test successful:', {
        username: data.username,
        accountType: data.account_type,
        mediaCount: data.media_count,
        followersCount: data.followers_count
      });

      return {
        success: true,
        accountInfo: {
          id: data.id,
          username: data.username,
          accountType: data.account_type,
          mediaCount: data.media_count,
          followersCount: data.followers_count
        }
      };

    } catch (error) {
      console.error('Instagram connection test failed:', error);
      throw error;
    }
  }

  /**
   * Wait for media container to be ready (for video uploads)
   */
  private async waitForContainerReady(containerId: string, accessToken: string, maxAttempts: number = 10): Promise<void> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response = await fetch(
          `${this.baseUrl}/${containerId}?fields=status_code&access_token=${accessToken}`
        );

        if (response.ok) {
          const data = await response.json();
          
          if (data.status_code === 'FINISHED') {
            return;
          } else if (data.status_code === 'ERROR') {
            throw new Error('Media container processing failed');
          }
        }

        // Wait 2 seconds before next attempt
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.warn(`Container status check attempt ${attempt + 1} failed:`, error);
      }
    }

    throw new Error('Media container did not become ready within timeout');
  }

  /**
   * Check if URL is a video
   */
  private isVideoUrl(url: string): boolean {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm', '.mkv'];
    const urlLower = url.toLowerCase();
    return videoExtensions.some(ext => urlLower.includes(ext));
  }

  /**
   * Convert Instagram media ID to post code for URL
   */
  private getInstagramPostCode(mediaId: string): string {
    // Instagram media IDs can be converted to post codes
    // This is a simplified version - in production, you might want to use the actual Instagram API
    return mediaId.split('_')[0];
  }
}
