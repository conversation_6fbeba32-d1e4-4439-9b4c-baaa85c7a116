import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import SocialMediaConfigValidator from '@/lib/config/social-media-config-validator';
import { OAuthManager } from '@/lib/auth/oauth-manager';
import ContentFormatter from '@/lib/social/content-formatter';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const DEMO_USER_ID = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Running comprehensive social media integration tests...');

    
    const configValidator = new SocialMediaConfigValidator();
    const oauthManager = new OAuthManager();
    const contentFormatter = new ContentFormatter();

    const testResults = {
      configurationValidation: '⏳ Testing...',
      oauthManagerTest: '⏳ Testing...',
      contentFormatterTest: '⏳ Testing...',
      publisherIntegration: '⏳ Testing...',
      databaseIntegration: '⏳ Testing...',
      apiEndpointsTest: '⏳ Testing...',
    };

    // Test 1: Configuration Validation
    try {
      console.log('Testing configuration validation...');
      
      const validation = configValidator.validateAllPlatforms();
      const configuredPlatforms = configValidator.getConfiguredPlatforms();
      
      if (validation.isValid && configuredPlatforms.length > 0) {
        testResults.configurationValidation = `✅ Valid (${configuredPlatforms.length} platforms configured)`;
      } else {
        testResults.configurationValidation = `⚠️ Issues found (${validation.summary.invalid} invalid, ${validation.summary.warnings} warnings)`;
      }
    } catch (err) {
      console.error('Configuration validation test error:', err);
      testResults.configurationValidation = '❌ Configuration test failed';
    }

    // Test 2: OAuth Manager Test
    try {
      console.log('Testing OAuth manager...');
      
      // Test OAuth URL generation for each platform
      const platforms = ['twitter', 'facebook', 'instagram', 'linkedin'];
      let successCount = 0;
      
      for (const platform of platforms) {
        try {
          const result = await oauthManager.initiateOAuth(DEMO_USER_ID, platform);
          if (result.authUrl && result.state) {
            successCount++;
          }
        } catch (error) {
          console.warn(`OAuth initiation failed for ${platform}:`, error.message);
        }
      }
      
      if (successCount > 0) {
        testResults.oauthManagerTest = `✅ OAuth URLs generated (${successCount}/${platforms.length} platforms)`;
      } else {
        testResults.oauthManagerTest = '❌ No OAuth URLs could be generated';
      }
    } catch (err) {
      console.error('OAuth manager test error:', err);
      testResults.oauthManagerTest = '❌ OAuth manager test failed';
    }

    // Test 3: Content Formatter Test
    try {
      console.log('Testing content formatter...');
      
      const testContent = 'This is a test post for eWasl social media scheduler! 🚀 #eWasl #SocialMedia #Automation';
      const platforms = ['linkedin', 'twitter', 'facebook', 'instagram'];
      let formattingSuccessCount = 0;
      
      for (const platform of platforms) {
        try {
          const formatted = contentFormatter.formatForPlatform(testContent, platform as any);
          if (formatted.isValid) {
            formattingSuccessCount++;
          }
        } catch (error) {
          console.warn(`Content formatting failed for ${platform}:`, error.message);
        }
      }
      
      // Test Arabic content
      const arabicContent = 'هذا منشور تجريبي لمنصة eWasl لجدولة وسائل التواصل الاجتماعي! 🚀 #eWasl #وسائل_التواصل';
      const arabicFormatted = contentFormatter.formatArabicContent(arabicContent, 'linkedin');
      
      if (formattingSuccessCount === platforms.length && arabicFormatted.isValid) {
        testResults.contentFormatterTest = `✅ Content formatting works (${formattingSuccessCount}/${platforms.length} platforms + Arabic)`;
      } else {
        testResults.contentFormatterTest = `⚠️ Partial success (${formattingSuccessCount}/${platforms.length} platforms)`;
      }
    } catch (err) {
      console.error('Content formatter test error:', err);
      testResults.contentFormatterTest = '❌ Content formatter test failed';
    }

    // Test 4: Publisher Integration Test
    try {
      console.log('Testing publisher integration...');
      
      // Test if publishers can be instantiated
      const { SocialMediaPublisher } = await import('@/lib/scheduler/social-media-publisher');

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
      const publisher = new SocialMediaPublisher();
      
      const supportedPlatforms = publisher.getSupportedPlatforms();
      
      if (supportedPlatforms.length > 0) {
        testResults.publisherIntegration = `✅ Publishers loaded (${supportedPlatforms.length} platforms)`;
      } else {
        testResults.publisherIntegration = '❌ No publishers available';
      }
    } catch (err) {
      console.error('Publisher integration test error:', err);
      testResults.publisherIntegration = '❌ Publisher integration failed';
    }

    // Test 5: Database Integration Test
    try {
      console.log('Testing database integration...');
      
      // Test social accounts table
      const { data: accounts, error: accountsError } = await supabase
        .from('social_accounts')
        .select('id, platform, account_name')
        .limit(5);
      
      // Test posts table
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('id, content, status')
        .limit(5);
      
      // Test job queue table
      const { data: jobs, error: jobsError } = await supabase
        .from('job_queue')
        .select('id, job_type, status')
        .limit(5);
      
      const dbErrors = [accountsError, postsError, jobsError].filter(Boolean);
      
      if (dbErrors.length === 0) {
        testResults.databaseIntegration = `✅ Database accessible (${accounts?.length || 0} accounts, ${posts?.length || 0} posts, ${jobs?.length || 0} jobs)`;
      } else {
        testResults.databaseIntegration = `❌ Database errors (${dbErrors.length} table access issues)`;
      }
    } catch (err) {
      console.error('Database integration test error:', err);
      testResults.databaseIntegration = '❌ Database integration failed';
    }

    // Test 6: API Endpoints Test
    try {
      console.log('Testing API endpoints...');
      
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      const endpointsToTest = [
        '/api/social/config/validate',
        '/api/scheduler/health',
        '/api/demo/scheduler-test'
      ];
      
      let endpointSuccessCount = 0;
      
      for (const endpoint of endpointsToTest) {
        try {
          const response = await fetch(`${baseUrl}${endpoint}`, {
            method: endpoint.includes('demo') ? 'POST' : 'GET',
            headers: { 'Content-Type': 'application/json' }
          });
          
          if (response.ok || response.status === 401) { // 401 is expected for auth-protected endpoints
            endpointSuccessCount++;
          }
        } catch (error) {
          console.warn(`Endpoint test failed for ${endpoint}:`, error.message);
        }
      }
      
      if (endpointSuccessCount === endpointsToTest.length) {
        testResults.apiEndpointsTest = `✅ API endpoints accessible (${endpointSuccessCount}/${endpointsToTest.length})`;
      } else {
        testResults.apiEndpointsTest = `⚠️ Some endpoints inaccessible (${endpointSuccessCount}/${endpointsToTest.length})`;
      }
    } catch (err) {
      console.error('API endpoints test error:', err);
      testResults.apiEndpointsTest = '❌ API endpoints test failed';
    }

    // Generate summary
    const successCount = Object.values(testResults).filter(result => result.includes('✅')).length;
    const totalTests = Object.keys(testResults).length;
    const overallSuccess = successCount === totalTests;

    // Get additional system info
    const systemInfo = {
      nodeEnv: process.env.NODE_ENV,
      baseUrl: process.env.NEXT_PUBLIC_APP_URL,
      hasSupabaseConfig: !!(process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY),
      configuredPlatforms: configValidator.getConfiguredPlatforms(),
      timestamp: new Date().toISOString(),
    };

    console.log('Social media integration tests completed:', {
      overallSuccess,
      successCount,
      totalTests,
      configuredPlatforms: systemInfo.configuredPlatforms.length
    });

    return NextResponse.json({
      success: true,
      overallSuccess,
      testResults,
      summary: {
        total: totalTests,
        passed: successCount,
        failed: totalTests - successCount,
        successRate: Math.round((successCount / totalTests) * 100)
      },
      systemInfo,
      recommendations: generateRecommendations(testResults, systemInfo),
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Social media integration test error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Integration tests failed',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

function generateRecommendations(testResults: any, systemInfo: any): string[] {
  const recommendations: string[] = [];

  if (testResults.configurationValidation.includes('❌') || testResults.configurationValidation.includes('⚠️')) {
    recommendations.push('Configure missing environment variables for social media platforms');
  }

  if (testResults.oauthManagerTest.includes('❌')) {
    recommendations.push('Check OAuth app settings in respective developer portals');
  }

  if (testResults.publisherIntegration.includes('❌')) {
    recommendations.push('Verify publisher implementations and dependencies');
  }

  if (testResults.databaseIntegration.includes('❌')) {
    recommendations.push('Check Supabase connection and table permissions');
  }

  if (systemInfo.configuredPlatforms.length === 0) {
    recommendations.push('Configure at least one social media platform for testing');
  }

  if (systemInfo.nodeEnv === 'development' && systemInfo.baseUrl?.includes('localhost')) {
    recommendations.push('Ensure OAuth callback URLs in developer portals match localhost:3000');
  }

  if (recommendations.length === 0) {
    recommendations.push('All tests passed! System is ready for social media publishing');
  }

  return recommendations;
}
