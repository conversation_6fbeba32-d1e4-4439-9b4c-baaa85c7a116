/**
 * Production Configuration for Enhanced Media Processing
 * Handles environment-specific settings, monitoring, and performance optimization
 */

export interface ProductionConfig {
  environment: 'development' | 'staging' | 'production';
  mediaProcessing: MediaProcessingConfig;
  monitoring: MonitoringConfig;
  performance: PerformanceConfig;
  security: SecurityConfig;
  cdn: CDNConfig;
  database: DatabaseConfig;
}

export interface MediaProcessingConfig {
  maxConcurrentUploads: number;
  maxFileSize: number;
  allowedFormats: string[];
  processingTimeout: number;
  queueSize: number;
  retryAttempts: number;
  enableAIOptimization: boolean;
  enableBatchProcessing: boolean;
  tempDirectory: string;
}

export interface MonitoringConfig {
  enableMetrics: boolean;
  enableAlerts: boolean;
  enableHealthChecks: boolean;
  metricsInterval: number;
  alertThresholds: AlertThresholds;
  healthCheckEndpoints: string[];
  logLevel: 'error' | 'warn' | 'info' | 'debug';
}

export interface AlertThresholds {
  errorRate: number; // percentage
  responseTime: number; // milliseconds
  queueSize: number;
  diskUsage: number; // percentage
  memoryUsage: number; // percentage
  cpuUsage: number; // percentage
}

export interface PerformanceConfig {
  enableCaching: boolean;
  cacheSize: number;
  cacheTTL: number;
  enableCompression: boolean;
  enableLoadBalancing: boolean;
  maxWorkers: number;
  enablePreprocessing: boolean;
}

export interface SecurityConfig {
  enableRateLimit: boolean;
  rateLimitWindow: number;
  rateLimitMax: number;
  enableFileValidation: boolean;
  enableVirusScanning: boolean;
  allowedOrigins: string[];
  enableCSRF: boolean;
}

export interface CDNConfig {
  provider: 'aws' | 'cloudflare' | 'vercel' | 'digitalocean';
  region: string;
  bucket: string;
  domain: string;
  enablePurging: boolean;
  cacheTTL: number;
  enableGeoDistribution: boolean;
}

export interface DatabaseConfig {
  connectionPool: number;
  queryTimeout: number;
  enableReadReplicas: boolean;
  enableConnectionRetry: boolean;
  retryAttempts: number;
  retryDelay: number;
}

export class ProductionConfigManager {
  private config: ProductionConfig;

  constructor() {
    this.config = this.loadConfiguration();
  }

  /**
   * Load configuration based on environment
   */
  private loadConfiguration(): ProductionConfig {
    const environment = (process.env.NODE_ENV as any) || 'development';
    
    const baseConfig: ProductionConfig = {
      environment,
      mediaProcessing: this.getMediaProcessingConfig(),
      monitoring: this.getMonitoringConfig(),
      performance: this.getPerformanceConfig(),
      security: this.getSecurityConfig(),
      cdn: this.getCDNConfig(),
      database: this.getDatabaseConfig(),
    };

    return baseConfig;
  }









  /**
   * Get current configuration
   */
  getConfig(): ProductionConfig {
    return this.config;
  }

  /**
   * Get specific configuration section
   */
  getMediaProcessingConfig(): MediaProcessingConfig {
    if (!this.config?.mediaProcessing) {
      // Fallback configuration if config is not loaded
      return {
        maxConcurrentUploads: 5,
        maxFileSize: 50 * 1024 * 1024,
        allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi', 'webm'],
        processingTimeout: 30000,
        queueSize: 100,
        retryAttempts: 3,
        enableAIOptimization: true,
        enableBatchProcessing: false,
        tempDirectory: '/tmp/ewasl-media'
      };
    }
    return this.config.mediaProcessing;
  }

  getMonitoringConfig(): MonitoringConfig {
    return this.config?.monitoring || {
      enableMetrics: false,
      enableAlerts: false,
      enableHealthChecks: true,
      metricsInterval: 60000,
      alertThresholds: {
        errorRate: 10,
        responseTime: 5000,
        queueSize: 50,
        diskUsage: 80,
        memoryUsage: 80,
        cpuUsage: 80
      },
      healthCheckEndpoints: ['/api/health'],
      logLevel: 'debug'
    };
  }

  getPerformanceConfig(): PerformanceConfig {
    return this.config?.performance || {
      enableCaching: false,
      cacheSize: 100,
      cacheTTL: 300000,
      enableCompression: false,
      enableLoadBalancing: false,
      maxWorkers: 2,
      enablePreprocessing: false
    };
  }

  getSecurityConfig(): SecurityConfig {
    return this.config?.security || {
      enableRateLimit: false,
      rateLimitWindow: 900000,
      rateLimitMax: 100,
      enableFileValidation: true,
      enableVirusScanning: false,
      allowedOrigins: ['http://localhost:3000'],
      enableCSRF: false
    };
  }

  getCDNConfig(): CDNConfig {
    return this.config?.cdn || {
      provider: 'vercel',
      region: 'us-east-1',
      bucket: 'ewasl-media',
      domain: 'cdn.ewasl.com',
      enablePurging: false,
      cacheTTL: 3600,
      enableGeoDistribution: false
    };
  }

  getDatabaseConfig(): DatabaseConfig {
    return this.config?.database || {
      connectionPool: 5,
      queryTimeout: 30000,
      enableReadReplicas: false,
      enableConnectionRetry: true,
      retryAttempts: 3,
      retryDelay: 1000
    };
  }

  /**
   * Validate configuration
   */
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate media processing config
    if (this.config.mediaProcessing.maxFileSize <= 0) {
      errors.push('Invalid max file size');
    }

    if (this.config.mediaProcessing.allowedFormats.length === 0) {
      errors.push('No allowed formats specified');
    }

    // Validate monitoring config
    if (this.config.monitoring.metricsInterval <= 0) {
      errors.push('Invalid metrics interval');
    }

    // Validate performance config
    if (this.config.performance.maxWorkers <= 0) {
      errors.push('Invalid max workers count');
    }

    // Validate CDN config
    if (!this.config.cdn.bucket || !this.config.cdn.domain) {
      errors.push('CDN configuration incomplete');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get environment-specific feature flags
   */
  getFeatureFlags(): Record<string, boolean> {
    const env = this.config.environment;
    
    return {
      enableAIOptimization: this.config.mediaProcessing.enableAIOptimization,
      enableBatchProcessing: this.config.mediaProcessing.enableBatchProcessing,
      enableMetrics: this.config.monitoring.enableMetrics,
      enableAlerts: this.config.monitoring.enableAlerts,
      enableCaching: this.config.performance.enableCaching,
      enableLoadBalancing: this.config.performance.enableLoadBalancing,
      enableRateLimit: this.config.security.enableRateLimit,
      enableVirusScanning: this.config.security.enableVirusScanning,
      enableGeoDistribution: this.config.cdn.enableGeoDistribution,
      enableReadReplicas: this.config.database.enableReadReplicas
    };
  }
}

// Export singleton instance
export const productionConfig = new ProductionConfigManager();
export default productionConfig;
