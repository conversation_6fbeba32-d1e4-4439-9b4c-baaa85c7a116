'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  Heart,
  MessageCircle,
  Share2,
  MousePointer,
  Users,
  Clock,
  Target,
  Lightbulb,
  RefreshCw,
  Download,
  Filter,
} from 'lucide-react';
import { toast } from 'sonner';

interface AnalyticsDashboardProps {
  userId: string;
}

interface DashboardData {
  summary: {
    totalPosts: number;
    totalImpressions: number;
    totalEngagement: number;
    avgEngagementRate: number;
    totalReach: number;
    totalClicks: number;
    avgClickRate: number;
  };
  platformBreakdown: Record<string, any>;
  engagementTrends: Array<{
    date: string;
    posts: number;
    impressions: number;
    engagement: number;
    engagementRate: number;
  }>;
  topPosts: Array<{
    id: string;
    content: string;
    platform: string;
    engagementRate: number;
    impressions: number;
    likes: number;
    comments: number;
    shares: number;
    createdAt: string;
  }>;
  contentTypePerformance: Record<string, any>;
  insights: Array<{
    id: string;
    insight_type: string;
    title: string;
    description: string;
    priority: string;
    confidence_score: number;
    created_at: string;
  }>;
  optimalTimes: Array<{
    platform: string;
    optimal_times: Record<string, string[]>;
    confidence_score: number;
  }>;
  audienceData: Array<{
    platform: string;
    age_groups: Record<string, number>;
    gender_distribution: Record<string, number>;
    location_data: Record<string, number>;
    total_followers: number;
  }>;
}

export function AdvancedAnalyticsDashboard({ userId }: AnalyticsDashboardProps) {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('month');
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        timeframe,
        ...(selectedPlatform !== 'all' && { platform: selectedPlatform }),
      });

      const response = await fetch(`/api/analytics/dashboard?${params}`);
      const result = await response.json();

      if (result.success) {
        setData(result);
      } else {
        toast.error('فشل في تحميل بيانات التحليلات');
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    try {
      setRefreshing(true);
      
      // Trigger insights generation
      await fetch('/api/analytics/real-time', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'refresh_insights' }),
      });

      // Refresh dashboard data
      await fetchDashboardData();
      
      toast.success('تم تحديث البيانات بنجاح');
    } catch (error) {
      toast.error('فشل في تحديث البيانات');
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [timeframe, selectedPlatform]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل التحليلات...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">لا توجد بيانات تحليلات متاحة</p>
      </div>
    );
  }

  const platformColors = {
    TWITTER: '#1DA1F2',
    FACEBOOK: '#4267B2',
    LINKEDIN: '#0077B5',
    INSTAGRAM: '#E4405F',
    SNAPCHAT: '#FFFC00',
  };

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">لوحة التحليلات المتقدمة</h1>
          <p className="text-gray-600">تحليل شامل لأداء المحتوى والجمهور</p>
        </div>
        
        <div className="flex gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">اليوم</SelectItem>
              <SelectItem value="week">الأسبوع</SelectItem>
              <SelectItem value="month">الشهر</SelectItem>
              <SelectItem value="year">السنة</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع المنصات</SelectItem>
              <SelectItem value="TWITTER">تويتر</SelectItem>
              <SelectItem value="FACEBOOK">فيسبوك</SelectItem>
              <SelectItem value="LINKEDIN">لينكد إن</SelectItem>
              <SelectItem value="INSTAGRAM">إنستغرام</SelectItem>
              <SelectItem value="SNAPCHAT">سناب شات</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={refreshData} disabled={refreshing} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المنشورات</p>
                <p className="text-2xl font-bold">{data.summary.totalPosts.toLocaleString()}</p>
              </div>
              <Eye className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المشاهدات</p>
                <p className="text-2xl font-bold">{data.summary.totalImpressions.toLocaleString()}</p>
              </div>
              <Eye className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي التفاعل</p>
                <p className="text-2xl font-bold">{data.summary.totalEngagement.toLocaleString()}</p>
              </div>
              <Heart className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">معدل التفاعل</p>
                <p className="text-2xl font-bold">{data.summary.avgEngagementRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="performance">الأداء</TabsTrigger>
          <TabsTrigger value="audience">الجمهور</TabsTrigger>
          <TabsTrigger value="insights">الرؤى</TabsTrigger>
          <TabsTrigger value="optimization">التحسين</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Engagement Trends */}
            <Card>
              <CardHeader>
                <CardTitle>اتجاهات التفاعل</CardTitle>
                <CardDescription>تطور معدل التفاعل عبر الزمن</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data.engagementTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="engagementRate" 
                      stroke="#8884d8" 
                      strokeWidth={2}
                      name="معدل التفاعل %" 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Platform Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>توزيع المنصات</CardTitle>
                <CardDescription>أداء المحتوى عبر المنصات المختلفة</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={Object.entries(data.platformBreakdown).map(([platform, data]: [string, any]) => ({
                        name: platform,
                        value: data.posts,
                        color: platformColors[platform as keyof typeof platformColors] || '#8884d8',
                      }))}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {Object.entries(data.platformBreakdown).map(([platform], index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={platformColors[platform as keyof typeof platformColors] || '#8884d8'} 
                        />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Performing Posts */}
          <Card>
            <CardHeader>
              <CardTitle>أفضل المنشورات أداءً</CardTitle>
              <CardDescription>المنشورات الأكثر تفاعلاً في الفترة المحددة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.topPosts.slice(0, 5).map((post, index) => (
                  <div key={post.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{post.platform}</Badge>
                        <span className="text-sm text-gray-500">
                          {new Date(post.createdAt).toLocaleDateString('ar-SA')}
                        </span>
                      </div>
                      <p className="text-sm">{post.content}</p>
                    </div>
                    <div className="text-left">
                      <div className="text-lg font-bold text-green-600">
                        {post.engagementRate.toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-500">
                        {post.impressions.toLocaleString()} مشاهدة
                      </div>
                      <div className="flex gap-2 text-xs text-gray-500 mt-1">
                        <span>👍 {post.likes}</span>
                        <span>💬 {post.comments}</span>
                        <span>🔄 {post.shares}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          {/* Content Type Performance */}
          <Card>
            <CardHeader>
              <CardTitle>أداء أنواع المحتوى</CardTitle>
              <CardDescription>مقارنة أداء أنواع المحتوى المختلفة</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={Object.entries(data.contentTypePerformance).map(([type, data]: [string, any]) => ({
                  type: type === 'text' ? 'نص' : type === 'image' ? 'صورة' : type === 'video' ? 'فيديو' : 'رابط',
                  engagementRate: data.avgEngagementRate,
                  posts: data.posts,
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="type" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="engagementRate" fill="#8884d8" name="معدل التفاعل %" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audience" className="space-y-4">
          {/* Audience Demographics */}
          {data.audienceData.length > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>التوزيع العمري</CardTitle>
                  <CardDescription>توزيع الجمهور حسب الفئات العمرية</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={Object.entries(data.audienceData[0]?.age_groups || {}).map(([age, percentage]) => ({
                          name: age,
                          value: percentage,
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {Object.entries(data.audienceData[0]?.age_groups || {}).map((_, index) => (
                          <Cell key={`cell-${index}`} fill={`hsl(${index * 45}, 70%, 60%)`} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>التوزيع الجغرافي</CardTitle>
                  <CardDescription>أهم المواقع الجغرافية للجمهور</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(data.audienceData[0]?.location_data || {})
                      .sort(([,a], [,b]) => (b as number) - (a as number))
                      .slice(0, 5)
                      .map(([location, percentage]) => (
                        <div key={location} className="flex justify-between items-center">
                          <span className="text-sm">{location}</span>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-500 h-2 rounded-full" 
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium">{percentage}%</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {/* AI Insights */}
          <div className="grid gap-4">
            {data.insights.map((insight) => (
              <Card key={insight.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Lightbulb className="h-5 w-5" />
                      {insight.title}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={insight.priority === 'high' ? 'destructive' : 
                                insight.priority === 'medium' ? 'default' : 'secondary'}
                      >
                        {insight.priority === 'high' ? 'عالي' : 
                         insight.priority === 'medium' ? 'متوسط' : 'منخفض'}
                      </Badge>
                      <Badge variant="outline">
                        {Math.round(insight.confidence_score * 100)}% ثقة
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{insight.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          {/* Optimal Posting Times */}
          {data.optimalTimes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>أفضل أوقات النشر</CardTitle>
                <CardDescription>الأوقات المثلى للنشر على كل منصة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.optimalTimes.map((timeData, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">{timeData.platform}</h4>
                        <Badge variant="outline">
                          {Math.round(timeData.confidence_score * 100)}% ثقة
                        </Badge>
                      </div>
                      <div className="grid grid-cols-7 gap-2 text-sm">
                        {Object.entries(timeData.optimal_times).map(([day, times]) => (
                          <div key={day} className="text-center">
                            <div className="font-medium mb-1">{day}</div>
                            <div className="space-y-1">
                              {(times as string[]).map((time, timeIndex) => (
                                <div key={timeIndex} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                  {time}
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
