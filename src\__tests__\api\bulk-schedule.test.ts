/**
 * Tests for Bulk Scheduling API
 * Tests the /api/posts/bulk-schedule endpoint functionality
 */

import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';

// Mock Next.js request/response
const mockRequest = (body: any, method: string = 'POST') => ({
  json: jest.fn().mockResolvedValue(body),
  url: 'http://localhost:3000/api/posts/bulk-schedule',
  method,
});

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  single: jest.fn(),
  limit: jest.fn().mockReturnThis(),
};

jest.mock('@/lib/supabase/server', () => ({
  createClient: () => mockSupabase,
}));

describe('Bulk Scheduling API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default successful auth
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'test-user-id' } },
      error: null,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('POST /api/posts/bulk-schedule - Bulk Schedule', () => {
    it('should bulk schedule multiple posts successfully', async () => {
      const requestBody = {
        posts: [
          {
            content: 'Bulk post 1',
            scheduled_at: '2024-01-01T09:00:00Z',
            social_account_ids: ['account-1'],
            time_zone: 'UTC',
          },
          {
            content: 'Bulk post 2',
            scheduled_at: '2024-01-02T09:00:00Z',
            social_account_ids: ['account-1'],
            time_zone: 'UTC',
          },
          {
            content: 'Bulk post 3',
            scheduled_at: '2024-01-03T09:00:00Z',
            social_account_ids: ['account-1'],
            time_zone: 'UTC',
          },
        ],
      };

      // Mock bulk operation creation
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'bulk-operation-id',
          user_id: 'test-user-id',
          operation_type: 'bulk_schedule',
          total_items: 3,
          status: 'processing',
        },
        error: null,
      });

      // Mock social accounts validation for each post
      mockSupabase.select.mockResolvedValue({
        data: [{ id: 'account-1' }],
        error: null,
      });

      // Mock post creation for each post
      mockSupabase.single.mockResolvedValue({
        data: { id: 'new-post-id' },
        error: null,
      });

      // Mock social account associations
      mockSupabase.insert.mockResolvedValue({ error: null });

      // Mock bulk operation updates
      mockSupabase.update.mockResolvedValue({ error: null });

      const { POST } = await import('@/app/api/posts/bulk-schedule/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.totalPosts).toBe(3);
      expect(responseData.completedItems).toBe(3);
      expect(responseData.failedItems).toBe(0);
      expect(responseData.bulkOperationId).toBe('bulk-operation-id');
    });

    it('should handle partial failures in bulk scheduling', async () => {
      const requestBody = {
        posts: [
          {
            content: 'Valid post',
            scheduled_at: '2024-01-01T09:00:00Z',
            social_account_ids: ['account-1'],
            time_zone: 'UTC',
          },
          {
            content: 'Invalid post',
            scheduled_at: '2024-01-02T09:00:00Z',
            social_account_ids: ['invalid-account'],
            time_zone: 'UTC',
          },
        ],
      };

      // Mock bulk operation creation
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'bulk-operation-id',
          user_id: 'test-user-id',
          operation_type: 'bulk_schedule',
          total_items: 2,
          status: 'processing',
        },
        error: null,
      });

      // Mock social accounts validation - first succeeds, second fails
      mockSupabase.select
        .mockResolvedValueOnce({
          data: [{ id: 'account-1' }],
          error: null,
        })
        .mockResolvedValueOnce({
          data: [], // No accounts found for invalid-account
          error: null,
        });

      // Mock post creation for valid post
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'new-post-id' },
        error: null,
      });

      // Mock social account associations
      mockSupabase.insert.mockResolvedValue({ error: null });

      // Mock bulk operation updates
      mockSupabase.update.mockResolvedValue({ error: null });

      const { POST } = await import('@/app/api/posts/bulk-schedule/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.totalPosts).toBe(2);
      expect(responseData.completedItems).toBe(1);
      expect(responseData.failedItems).toBe(1);
      expect(responseData.errors).toBeDefined();
      expect(responseData.errors.length).toBeGreaterThan(0);
    });

    it('should reject bulk scheduling with too many posts', async () => {
      const requestBody = {
        posts: Array(101).fill({
          content: 'Test post',
          scheduled_at: '2024-01-01T09:00:00Z',
          social_account_ids: ['account-1'],
          time_zone: 'UTC',
        }),
      };

      const { POST } = await import('@/app/api/posts/bulk-schedule/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Invalid input');
    });
  });

  describe('POST /api/posts/bulk-schedule - CSV Import', () => {
    it('should import posts from CSV successfully', async () => {
      const requestBody = {
        action: 'csv_import',
        csv_data: 'content,media_url\n"First post","https://example.com/image1.jpg"\n"Second post","https://example.com/image2.jpg"\n"Third post",""',
        social_account_ids: ['account-1'],
        time_zone: 'UTC',
        start_date: '2024-01-01T09:00:00Z',
        interval_hours: 24,
      };

      // Mock bulk operation creation
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'bulk-operation-id',
          user_id: 'test-user-id',
          operation_type: 'bulk_schedule',
          total_items: 3,
          status: 'processing',
        },
        error: null,
      });

      // Mock social accounts validation
      mockSupabase.select.mockResolvedValue({
        data: [{ id: 'account-1' }],
        error: null,
      });

      // Mock post creation
      mockSupabase.single.mockResolvedValue({
        data: { id: 'new-post-id' },
        error: null,
      });

      // Mock social account associations
      mockSupabase.insert.mockResolvedValue({ error: null });

      // Mock bulk operation updates
      mockSupabase.update.mockResolvedValue({ error: null });

      const { POST } = await import('@/app/api/posts/bulk-schedule/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.totalPosts).toBe(3);
      expect(responseData.completedItems).toBe(3);
      expect(responseData.failedItems).toBe(0);
    });

    it('should reject CSV without content column', async () => {
      const requestBody = {
        action: 'csv_import',
        csv_data: 'title,description\n"Post 1","Description 1"\n"Post 2","Description 2"',
        social_account_ids: ['account-1'],
        time_zone: 'UTC',
        start_date: '2024-01-01T09:00:00Z',
        interval_hours: 24,
      };

      const { POST } = await import('@/app/api/posts/bulk-schedule/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('CSV must contain a "content" column');
    });

    it('should reject CSV with no valid posts', async () => {
      const requestBody = {
        action: 'csv_import',
        csv_data: 'content\n""\n""\n""', // All empty content
        social_account_ids: ['account-1'],
        time_zone: 'UTC',
        start_date: '2024-01-01T09:00:00Z',
        interval_hours: 24,
      };

      const { POST } = await import('@/app/api/posts/bulk-schedule/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('No valid posts found in CSV');
    });
  });

  describe('GET /api/posts/bulk-schedule', () => {
    it('should fetch all bulk operations for user', async () => {
      const mockOperations = [
        {
          id: 'operation-1',
          operation_type: 'bulk_schedule',
          total_items: 10,
          completed_items: 10,
          failed_items: 0,
          status: 'completed',
          created_at: '2024-01-01T09:00:00Z',
        },
        {
          id: 'operation-2',
          operation_type: 'bulk_import',
          total_items: 5,
          completed_items: 3,
          failed_items: 2,
          status: 'completed_with_errors',
          created_at: '2024-01-02T09:00:00Z',
        },
      ];

      mockSupabase.select.mockResolvedValue({
        data: mockOperations,
        error: null,
      });

      const { GET } = await import('@/app/api/posts/bulk-schedule/route');
      const req = {
        url: 'http://localhost:3000/api/posts/bulk-schedule',
      };

      const response = await GET(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.operations).toHaveLength(2);
    });

    it('should fetch specific bulk operation by ID', async () => {
      const mockOperation = {
        id: 'operation-1',
        operation_type: 'bulk_schedule',
        total_items: 10,
        completed_items: 10,
        failed_items: 0,
        status: 'completed',
        created_at: '2024-01-01T09:00:00Z',
        error_details: null,
      };

      mockSupabase.single.mockResolvedValue({
        data: mockOperation,
        error: null,
      });

      const { GET } = await import('@/app/api/posts/bulk-schedule/route');
      const req = {
        url: 'http://localhost:3000/api/posts/bulk-schedule?operation_id=operation-1',
      };

      const response = await GET(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.operation.id).toBe('operation-1');
    });

    it('should return 404 for non-existent operation', async () => {
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: new Error('Not found'),
      });

      const { GET } = await import('@/app/api/posts/bulk-schedule/route');
      const req = {
        url: 'http://localhost:3000/api/posts/bulk-schedule?operation_id=non-existent',
      };

      const response = await GET(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(404);
      expect(responseData.error).toBe('Operation not found');
    });
  });
});
