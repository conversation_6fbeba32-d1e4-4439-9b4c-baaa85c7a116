import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const schedulePostSchema = z.object({
  content: z.string().min(1).max(2800),
  media_url: z.string().url().optional(),
  scheduled_at: z.string().datetime(),
  platforms: z.array(z.string()).min(1),
  time_zone: z.string().default('UTC'),
});

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Scheduling post...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = schedulePostSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { content, media_url, scheduled_at, platforms, time_zone } = validation.data;

    // Validate scheduled time is in the future
    const scheduledDate = new Date(scheduled_at);
    const now = new Date();

    if (scheduledDate <= now) {
      return NextResponse.json(
        { error: 'Scheduled time must be in the future' },
        { status: 400 }
      );
    }

    // Check if user has social accounts for the requested platforms
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('id, platform, is_active')
      .eq('user_id', user.id)
      .in('platform', platforms)
      .eq('is_active', true);

    if (accountsError) {
      console.error('Error fetching social accounts:', accountsError);
      return NextResponse.json(
        { error: 'Failed to fetch social accounts' },
        { status: 500 }
      );
    }

    if (!socialAccounts || socialAccounts.length === 0) {
      return NextResponse.json(
        { error: 'No active social accounts found for the requested platforms' },
        { status: 400 }
      );
    }

    // Create the post
    const { data: post, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content,
        media_url,
        status: 'SCHEDULED',
        scheduled_at: scheduledDate.toISOString(),
        platforms,
        time_zone,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating post:', createError);
      return NextResponse.json(
        { error: 'Failed to create post' },
        { status: 500 }
      );
    }

    // Create a job for each platform
    const jobs = [];
    for (const platform of platforms) {
      const socialAccount = socialAccounts.find(acc => acc.platform === platform);
      
      if (socialAccount) {
        const { data: job, error: jobError } = await supabase
          .from('job_queue')
          .insert({
            job_type: 'publish-post',
            job_data: {
              postId: post.id,
              userId: user.id,
              content: post.content,
              mediaUrl: post.media_url,
              platform,
              socialAccountId: socialAccount.id,
            },
            priority: 5,
            max_attempts: 3,
            scheduled_at: scheduledDate.toISOString(),
            created_by: user.id,
          })
          .select()
          .single();

        if (jobError) {
          console.error(`Error creating job for ${platform}:`, jobError);
        } else {
          jobs.push(job);
        }
      }
    }

    console.log(`Post scheduled successfully with ${jobs.length} jobs created`);

    return NextResponse.json({
      success: true,
      data: {
        post: {
          id: post.id,
          content: post.content,
          media_url: post.media_url,
          scheduled_at: post.scheduled_at,
          platforms: post.platforms,
          status: post.status,
        },
        jobs: jobs.map(job => ({
          id: job.id,
          job_type: job.job_type,
          platform: job.job_data.platform,
          scheduled_at: job.scheduled_at,
          status: job.status,
        })),
        socialAccounts: socialAccounts.map(acc => ({
          id: acc.id,
          platform: acc.platform,
          is_active: acc.is_active,
        })),
      },
      message: `Post scheduled successfully for ${scheduledDate.toLocaleString()}`,
    });

  } catch (error) {
    console.error('Error scheduling post:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to schedule post',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// GET /api/posts/schedule - Get scheduled posts
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching scheduled posts...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const status = url.searchParams.get('status') || 'SCHEDULED';

    // Fetch scheduled posts
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select(`
        id,
        content,
        media_url,
        status,
        scheduled_at,
        published_at,
        platforms,
        time_zone,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .eq('status', status)
      .order('scheduled_at', { ascending: true })
      .range(offset, offset + limit - 1);

    if (postsError) {
      console.error('Error fetching posts:', postsError);
      return NextResponse.json(
        { error: 'Failed to fetch posts' },
        { status: 500 }
      );
    }

    // Get related jobs for these posts
    const postIds = posts?.map(p => p.id) || [];
    let jobs = [];

    if (postIds.length > 0) {
      const { data: jobsData, error: jobsError } = await supabase
        .from('job_queue')
        .select('id, job_type, job_data, status, priority, attempts, scheduled_at, created_at')
        .eq('job_type', 'publish-post')
        .in('job_data->>postId', postIds)
        .order('scheduled_at', { ascending: true });

      if (!jobsError) {
        jobs = jobsData || [];
      }
    }

    // Combine posts with their jobs
    const postsWithJobs = posts?.map(post => ({
      ...post,
      jobs: jobs.filter(job => job.job_data?.postId === post.id),
    })) || [];

    return NextResponse.json({
      success: true,
      data: {
        posts: postsWithJobs,
        pagination: {
          limit,
          offset,
          total: postsWithJobs.length,
        },
      },
      message: `Found ${postsWithJobs.length} scheduled posts`,
    });

  } catch (error) {
    console.error('Error fetching scheduled posts:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch scheduled posts',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
