import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';

/**
 * Account Selection API
 * Allows users to choose between personal and business accounts for social media platforms
 * GET - Get available account options for a platform
 * POST - Select and configure account type
 */

// GET - Get available account options
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform')?.toUpperCase();
    const userId = searchParams.get('userId') || '3ddaeb03-2d95-4fff-abad-2a2c7dd25037'; // Demo User

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform parameter is required' },
        { status: 400 }
      );
    }

    console.log(`Getting account options for ${platform}...`);

    const supabase = createServiceRoleClient();
    
    // Get connected accounts for this platform
    const { data: accounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', platform);

    if (error) {
      console.error('Error fetching accounts:', error);
      return NextResponse.json(
        { error: 'Failed to fetch accounts' },
        { status: 500 }
      );
    }

    // Get platform-specific account options
    let accountOptions = [];

    switch (platform) {
      case 'FACEBOOK':
        accountOptions = await getFacebookAccountOptions(accounts);
        break;
      case 'LINKEDIN':
        accountOptions = await getLinkedInAccountOptions(accounts);
        break;
      case 'INSTAGRAM':
        accountOptions = await getInstagramAccountOptions(accounts);
        break;
      default:
        accountOptions = accounts.map(account => ({
          id: account.id,
          type: 'personal',
          name: account.account_name,
          accountId: account.account_id,
          isSelected: true
        }));
    }

    return NextResponse.json({
      success: true,
      platform,
      accountOptions,
      totalAccounts: accounts.length,
      message: `Found ${accountOptions.length} account options for ${platform}`
    });

  } catch (error) {
    console.error('Account selection GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Select and configure account type
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { platform, accountId, accountType, pageId, organizationId, userId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037' } = body;

    if (!platform || !accountId || !accountType) {
      return NextResponse.json(
        { error: 'Platform, accountId, and accountType are required' },
        { status: 400 }
      );
    }

    console.log(`Configuring ${platform} account selection:`, {
      accountType,
      pageId,
      organizationId
    });

    const supabase = createServiceRoleClient();

    // Update account configuration based on selection
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (accountType === 'business') {
      if (platform === 'FACEBOOK' && pageId) {
        updateData.page_id = pageId;
        // TODO: Get page access token and update access_token
      } else if (platform === 'LINKEDIN' && organizationId) {
        updateData.account_id = organizationId; // Use organization ID for posting
        // TODO: Verify organization permissions
      }
    }

    const { error } = await supabase
      .from('social_accounts')
      .update(updateData)
      .eq('id', accountId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating account selection:', error);
      return NextResponse.json(
        { error: 'Failed to update account selection' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `${platform} account configured for ${accountType} posting`,
      accountType,
      configuration: {
        pageId: pageId || null,
        organizationId: organizationId || null
      }
    });

  } catch (error) {
    console.error('Account selection POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions for platform-specific account options

async function getFacebookAccountOptions(accounts: any[]) {
  const options = [];

  for (const account of accounts) {
    // Personal account option
    options.push({
      id: account.id,
      type: 'personal',
      name: account.account_name,
      accountId: account.account_id,
      isSelected: !account.page_id, // Selected if no page configured
      description: 'Post to your personal Facebook profile'
    });

    // Business page options
    if (account.page_id) {
      options.push({
        id: account.id,
        type: 'business',
        name: account.page_name || 'Facebook Page',
        accountId: account.account_id,
        pageId: account.page_id,
        isSelected: !!account.page_id, // Selected if page is configured
        description: `Post to ${account.page_name} page`,
        fanCount: account.fan_count,
        category: account.page_category
      });
    }
  }

  return options;
}

async function getLinkedInAccountOptions(accounts: any[]) {
  const options = [];

  for (const account of accounts) {
    // Personal profile option
    options.push({
      id: account.id,
      type: 'personal',
      name: account.account_name,
      accountId: account.account_id,
      isSelected: true, // Default selection
      description: 'Post to your personal LinkedIn profile'
    });

    // TODO: Add organization options when available
    // This would require fetching organizations from LinkedIn API
  }

  return options;
}

async function getInstagramAccountOptions(accounts: any[]) {
  const options = [];

  for (const account of accounts) {
    // Instagram business accounts only
    options.push({
      id: account.id,
      type: 'business',
      name: account.account_name,
      accountId: account.account_id,
      isSelected: true,
      description: 'Post to Instagram Business account',
      note: 'Instagram requires business accounts for API posting'
    });
  }

  return options;
}
