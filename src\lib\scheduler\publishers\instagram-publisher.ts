import { SocialAccount, PostContent } from '../social-media-publisher';
import { SchedulerLogger } from '../scheduler-logger';

export interface InstagramPublishResult {
  postId: string;
  url: string;
}

/**
 * Instagram publisher using Instagram Basic Display API
 */
export class InstagramPublisher {
  private logger: SchedulerLogger;
  private readonly API_BASE = 'https://graph.facebook.com/v18.0';

  constructor() {
    this.logger = new SchedulerLogger('instagram-publisher');
  }

  /**
   * Publish a post to Instagram
   */
  async publish(account: SocialAccount, content: PostContent): Promise<InstagramPublishResult> {
    try {
      this.logger.info('Publishing to Instagram', {
        accountId: account.account_id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
      });

      if (!content.mediaUrl) {
        throw new Error('Instagram posts require media (image or video)');
      }

      // Step 1: Create media container
      const mediaType = this.getMediaType(content.mediaUrl);
      const containerId = await this.createMediaContainer(account, content, mediaType);

      // Step 2: Publish the container
      const response = await this.publishMediaContainer(account, containerId);

      if (!response.id) {
        throw new Error('Invalid response from Instagram API');
      }

      const postUrl = `https://www.instagram.com/p/${response.id}`;

      this.logger.info('Instagram post published successfully', {
        postId: response.id,
        postUrl,
      });

      return {
        postId: response.id,
        url: postUrl,
      };

    } catch (error) {
      this.logger.error('Failed to publish Instagram post', error);
      throw error;
    }
  }

  /**
   * Create media container
   */
  private async createMediaContainer(
    account: SocialAccount,
    content: PostContent,
    mediaType: 'IMAGE' | 'VIDEO'
  ): Promise<string> {
    try {
      this.logger.info('Creating Instagram media container', { mediaType });

      const postData: any = {
        caption: content.content,
        access_token: account.access_token,
      };

      if (mediaType === 'IMAGE') {
        postData.image_url = content.mediaUrl;
      } else {
        postData.video_url = content.mediaUrl;
        postData.media_type = 'VIDEO';
      }

      const response = await this.makeInstagramRequest(
        account,
        'POST',
        `/${account.account_id}/media`,
        postData
      );

      if (!response.id) {
        throw new Error('Failed to create media container');
      }

      this.logger.info('Media container created successfully', {
        containerId: response.id,
      });

      return response.id;

    } catch (error) {
      this.logger.error('Failed to create media container', error);
      throw error;
    }
  }

  /**
   * Publish media container
   */
  private async publishMediaContainer(account: SocialAccount, containerId: string): Promise<any> {
    try {
      this.logger.info('Publishing Instagram media container', { containerId });

      const response = await this.makeInstagramRequest(
        account,
        'POST',
        `/${account.account_id}/media_publish`,
        {
          creation_id: containerId,
          access_token: account.access_token,
        }
      );

      this.logger.info('Media container published successfully', {
        postId: response.id,
      });

      return response;

    } catch (error) {
      this.logger.error('Failed to publish media container', error);
      throw error;
    }
  }

  /**
   * Determine media type from URL
   */
  private getMediaType(mediaUrl: string): 'IMAGE' | 'VIDEO' {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm'];
    const isVideo = videoExtensions.some(ext => mediaUrl.toLowerCase().includes(ext));
    return isVideo ? 'VIDEO' : 'IMAGE';
  }

  /**
   * Make authenticated request to Instagram API
   */
  private async makeInstagramRequest(
    account: SocialAccount,
    method: string,
    endpoint: string,
    data?: any
  ): Promise<any> {
    const url = `${this.API_BASE}${endpoint}`;

    const options: RequestInit = {
      method,
    };

    if (method === 'GET') {
      const params = new URLSearchParams(data || {});
      const fullUrl = `${url}?${params.toString()}`;
      const response = await fetch(fullUrl, options);
      return await this.handleResponse(response);
    } else {
      if (data) {
        const formData = new FormData();
        Object.keys(data).forEach(key => {
          formData.append(key, data[key]);
        });
        options.body = formData;
      }

      const response = await fetch(url, options);
      return await this.handleResponse(response);
    }
  }

  /**
   * Handle Instagram API response
   */
  private async handleResponse(response: Response): Promise<any> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Handle specific Instagram errors
      if (response.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }

      if (response.status === 401) {
        throw new Error('Instagram authentication failed. Token may be expired or invalid.');
      }

      if (response.status === 403) {
        throw new Error('Instagram API access forbidden. Check permissions.');
      }

      const errorMessage = errorData.error?.message || `Instagram API error: ${response.status}`;
      throw new Error(errorMessage);
    }

    return await response.json();
  }

  /**
   * Test connection to Instagram account
   */
  async testConnection(account: SocialAccount): Promise<any> {
    try {
      this.logger.info('Testing Instagram connection', {
        accountId: account.account_id,
      });

      const response = await this.makeInstagramRequest(
        account,
        'GET',
        `/${account.account_id}`,
        {
          fields: 'id,username,account_type,media_count',
          access_token: account.access_token,
        }
      );

      this.logger.info('Instagram connection test successful', {
        username: response.username,
        accountType: response.account_type,
      });

      return {
        success: true,
        accountInfo: {
          id: response.id,
          username: response.username,
          account_type: response.account_type,
          media_count: response.media_count,
        },
      };

    } catch (error) {
      this.logger.error('Instagram connection test failed', error);
      throw error;
    }
  }

  /**
   * Get account information
   */
  async getAccountInfo(account: SocialAccount): Promise<any> {
    try {
      const response = await this.makeInstagramRequest(
        account,
        'GET',
        `/${account.account_id}`,
        {
          fields: 'id,username,account_type,media_count,followers_count,follows_count',
          access_token: account.access_token,
        }
      );

      return {
        id: response.id,
        username: response.username,
        account_type: response.account_type,
        media_count: response.media_count,
        followers_count: response.followers_count,
        follows_count: response.follows_count,
      };

    } catch (error) {
      this.logger.error('Failed to get Instagram account info', error);
      throw error;
    }
  }

  /**
   * Get post analytics
   */
  async getPostAnalytics(account: SocialAccount, postId: string): Promise<any> {
    try {
      const response = await this.makeInstagramRequest(
        account,
        'GET',
        `/${postId}`,
        {
          fields: 'id,caption,media_type,media_url,permalink,timestamp,like_count,comments_count',
          access_token: account.access_token,
        }
      );

      return {
        id: response.id,
        caption: response.caption,
        media_type: response.media_type,
        media_url: response.media_url,
        permalink: response.permalink,
        timestamp: response.timestamp,
        metrics: {
          likes: response.like_count || 0,
          comments: response.comments_count || 0,
        },
      };

    } catch (error) {
      this.logger.error('Failed to get Instagram post analytics', error);
      throw error;
    }
  }

  /**
   * Get account insights
   */
  async getAccountInsights(account: SocialAccount, since?: Date, until?: Date): Promise<any> {
    try {
      const params: any = {
        metric: 'impressions,reach,profile_views',
        period: 'day',
        access_token: account.access_token,
      };

      if (since) {
        params.since = Math.floor(since.getTime() / 1000);
      }

      if (until) {
        params.until = Math.floor(until.getTime() / 1000);
      }

      const response = await this.makeInstagramRequest(
        account,
        'GET',
        `/${account.account_id}/insights`,
        params
      );

      return response.data;

    } catch (error) {
      this.logger.error('Failed to get Instagram account insights', error);
      throw error;
    }
  }

  /**
   * Get recent media
   */
  async getRecentMedia(account: SocialAccount, limit: number = 25): Promise<any[]> {
    try {
      const response = await this.makeInstagramRequest(
        account,
        'GET',
        `/${account.account_id}/media`,
        {
          fields: 'id,caption,media_type,media_url,permalink,timestamp,like_count,comments_count',
          limit,
          access_token: account.access_token,
        }
      );

      return response.data || [];

    } catch (error) {
      this.logger.error('Failed to get recent Instagram media', error);
      throw error;
    }
  }

  /**
   * Validate Instagram credentials
   */
  async validateCredentials(accessToken: string, accountId: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.API_BASE}/${accountId}?fields=id&access_token=${accessToken}`
      );

      return response.ok;

    } catch (error) {
      this.logger.error('Failed to validate Instagram credentials', error);
      return false;
    }
  }

  /**
   * Check if media URL is valid for Instagram
   */
  validateMediaUrl(mediaUrl: string): boolean {
    try {
      const url = new URL(mediaUrl);
      const pathname = url.pathname.toLowerCase();
      
      // Instagram supports specific formats
      const supportedFormats = ['.jpg', '.jpeg', '.png', '.mp4'];
      return supportedFormats.some(format => pathname.endsWith(format));

    } catch {
      return false;
    }
  }

  /**
   * Get media upload requirements
   */
  getMediaRequirements(): {
    image: { formats: string[]; maxSize: number; dimensions: string };
    video: { formats: string[]; maxSize: number; duration: string };
  } {
    return {
      image: {
        formats: ['JPEG', 'PNG'],
        maxSize: 8 * 1024 * 1024, // 8MB
        dimensions: 'Min: 320x320, Max: 1080x1080',
      },
      video: {
        formats: ['MP4'],
        maxSize: 100 * 1024 * 1024, // 100MB
        duration: 'Min: 3 seconds, Max: 60 seconds',
      },
    };
  }
}
