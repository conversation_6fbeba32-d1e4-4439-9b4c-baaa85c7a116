# 📧 Email Service Configuration Guide for eWasl

## **🎯 CURRENT ISSUES TO FIX**
1. **Email Confirmation Not Working**: Users not receiving verification emails
2. **Google OAuth SSL Error**: `ERR_SSL_PROTOCOL_ERROR` when redirecting
3. **Email Service Setup**: Configure proper SMTP with Supabase

---

## **🔧 STEP 1: SUPABASE EMAIL CONFIGURATION**

### **1.1: Access Supabase Dashboard**
1. **Open**: https://supabase.com/dashboard/project/ajpcbugydftdyhlbddpl
2. **Navigate to**: Authentication → Settings → Email Templates
3. **Configure SMTP Settings**

### **1.2: SMTP Configuration (SendGrid Recommended)**
```
Host: smtp.sendgrid.net
Port: 587
Username: apikey
Password: [Your SendGrid API Key]
Sender Email: <EMAIL>
Sender Name: eWasl Social Scheduler
```

### **1.3: Email Templates Configuration**
- **Confirm Signup**: Enable and customize
- **Reset Password**: Enable and customize
- **Email Change**: Enable and customize

---

## **🔧 STEP 2: SENDGRID SETUP**

### **2.1: Create SendGrid Account**
1. **Visit**: https://sendgrid.com/
2. **Sign up** for free account (100 emails/day free tier)
3. **Verify** your account and domain

### **2.2: Generate API Key**
1. **Navigate to**: Settings → API Keys
2. **Create API Key** with "Full Access" permissions
3. **Copy** the API key (starts with `SG.`)

### **2.3: Domain Authentication (Optional but Recommended)**
1. **Navigate to**: Settings → Sender Authentication
2. **Authenticate Domain**: ewasl.com
3. **Add DNS records** as instructed by SendGrid

---

## **🔧 STEP 3: ENVIRONMENT VARIABLES**

### **3.1: Local Development (.env.local)**
```bash
# Email Configuration
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=eWasl Social Scheduler

# Supabase Email Settings (if using custom SMTP)
SUPABASE_SMTP_HOST=smtp.sendgrid.net
SUPABASE_SMTP_PORT=587
SUPABASE_SMTP_USER=apikey
SUPABASE_SMTP_PASS=SG.your_sendgrid_api_key_here
```

### **3.2: Production Environment (DigitalOcean)**
Add these environment variables to your DigitalOcean app:
```bash
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=eWasl Social Scheduler
```

---

## **🔧 STEP 4: SUPABASE AUTH SETTINGS**

### **4.1: Email Confirmation Settings**
1. **Navigate to**: Authentication → Settings
2. **Enable Email Confirmations**: ✅ ON
3. **Confirm Email Changes**: ✅ ON
4. **Secure Password Changes**: ✅ ON

### **4.2: OAuth Settings**
1. **Navigate to**: Authentication → Providers
2. **Google OAuth**:
   - **Enabled**: ✅ ON
   - **Client ID**: Your Google OAuth Client ID
   - **Client Secret**: Your Google OAuth Client Secret
   - **Redirect URLs**: 
     - `http://localhost:3001/auth/callback` (development)
     - `https://app.ewasl.com/auth/callback` (production)

### **4.3: Site URL Configuration**
```
Site URL: https://app.ewasl.com
Additional Redirect URLs:
- http://localhost:3001/auth/callback
- https://app.ewasl.com/auth/callback
```

---

## **🔧 STEP 5: GOOGLE OAUTH CONFIGURATION**

### **5.1: Google Cloud Console**
1. **Visit**: https://console.cloud.google.com/
2. **Navigate to**: APIs & Services → Credentials
3. **Find your OAuth 2.0 Client**

### **5.2: Update Authorized Redirect URIs**
Add these URIs to your Google OAuth client:
```
http://localhost:3001/auth/callback
https://app.ewasl.com/auth/callback
https://ajpcbugydftdyhlbddpl.supabase.co/auth/v1/callback
```

### **5.3: Authorized JavaScript Origins**
```
http://localhost:3001
https://app.ewasl.com
https://ajpcbugydftdyhlbddpl.supabase.co
```

---

## **🔧 STEP 6: EMAIL TEMPLATE CUSTOMIZATION**

### **6.1: Confirmation Email Template**
```html
<h2>مرحباً بك في eWasl!</h2>
<p>شكراً لك على التسجيل في منصة eWasl لإدارة وسائل التواصل الاجتماعي.</p>
<p>يرجى النقر على الرابط أدناه لتأكيد بريدك الإلكتروني:</p>
<a href="{{ .ConfirmationURL }}" style="background: linear-gradient(135deg, #3B82F6, #8B5CF6); color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;">تأكيد البريد الإلكتروني</a>
<p>إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد.</p>
<p>مع تحيات فريق eWasl</p>
```

### **6.2: Password Reset Template**
```html
<h2>إعادة تعيين كلمة المرور</h2>
<p>تلقينا طلباً لإعادة تعيين كلمة المرور لحسابك في eWasl.</p>
<p>يرجى النقر على الرابط أدناه لإعادة تعيين كلمة المرور:</p>
<a href="{{ .ConfirmationURL }}" style="background: linear-gradient(135deg, #3B82F6, #8B5CF6); color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;">إعادة تعيين كلمة المرور</a>
<p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد.</p>
<p>مع تحيات فريق eWasl</p>
```

---

## **🧪 STEP 7: TESTING EMAIL FUNCTIONALITY**

### **7.1: Test Email Confirmation**
1. **Register** a new user with a real email address
2. **Check** email inbox for confirmation email
3. **Click** confirmation link
4. **Verify** user can sign in

### **7.2: Test Password Reset**
1. **Go to**: https://app.ewasl.com/auth/forgot-password
2. **Enter** a registered email address
3. **Check** email inbox for reset email
4. **Click** reset link and set new password
5. **Verify** can sign in with new password

### **7.3: Test Google OAuth**
1. **Go to**: https://app.ewasl.com/auth/signin
2. **Click**: "تسجيل الدخول بـ Google"
3. **Complete** Google OAuth flow
4. **Verify** successful redirect to dashboard

---

## **🚨 TROUBLESHOOTING**

### **Common Issues:**
1. **SSL Error**: Check redirect URLs in Google Console
2. **No Email Received**: Verify SMTP settings and SendGrid configuration
3. **OAuth Error**: Check client ID/secret and redirect URLs
4. **Email in Spam**: Configure SPF/DKIM records for your domain

### **Debug Steps:**
1. **Check Supabase Logs**: Authentication → Logs
2. **Check SendGrid Activity**: Activity → Email Activity
3. **Test SMTP Connection**: Use SendGrid's test email feature
4. **Verify DNS Records**: Use DNS checker tools

---

## **✅ SUCCESS CRITERIA**

After completing this configuration:
- ✅ Users receive email confirmations when signing up
- ✅ Password reset emails are delivered successfully
- ✅ Google OAuth works without SSL errors
- ✅ All emails have proper Arabic branding
- ✅ Email delivery is reliable and fast

---

## **📞 SUPPORT RESOURCES**

- **Supabase Docs**: https://supabase.com/docs/guides/auth/auth-smtp
- **SendGrid Docs**: https://docs.sendgrid.com/
- **Google OAuth Docs**: https://developers.google.com/identity/protocols/oauth2
