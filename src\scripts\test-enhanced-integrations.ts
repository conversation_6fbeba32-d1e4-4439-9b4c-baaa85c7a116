#!/usr/bin/env tsx

/**
 * Enhanced Integration Testing Script
 * Comprehensive testing for all social media integrations
 */

import { integrationManager } from '../lib/social/postiz-integration/integration-manager';
import { supabaseServiceRole as createClient } from '../lib/supabase/service-role';

interface TestConfig {
  platform: string;
  integrationId?: string;
  testContent: string;
  skipPosting?: boolean;
}

const TEST_CONFIGS: TestConfig[] = [
  {
    platform: 'linkedin',
    testContent: 'Test post from eWasl Enhanced LinkedIn Integration 🚀 #linkedin #testing',
  },
  {
    platform: 'facebook',
    testContent: 'Test post from eWasl Enhanced Facebook Integration 🚀 #facebook #testing',
  },
  {
    platform: 'instagram',
    testContent: 'Test post from eWasl Enhanced Instagram Integration 🚀 #instagram #testing',
  },
  {
    platform: 'twitter',
    testContent: 'Test post from eWasl Enhanced Twitter Integration 🚀 #twitter #testing',
  },
];

class EnhancedIntegrationTester {
  private supabase = createClient();
  private results: Record<string, any> = {};

  async runAllTests() {
    console.log('🚀 Starting Enhanced Integration Tests...\n');

    for (const config of TEST_CONFIGS) {
      console.log(`\n📱 Testing ${config.platform.toUpperCase()} Integration`);
      console.log('='.repeat(50));

      try {
        const platformResults = await this.testPlatform(config);
        this.results[config.platform] = platformResults;
        
        this.printPlatformResults(config.platform, platformResults);
      } catch (error) {
        console.error(`❌ Platform test failed: ${error}`);
        this.results[config.platform] = {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        };
      }
    }

    this.printSummary();
    await this.saveResults();
  }

  async testPlatform(config: TestConfig) {
    const results: any = {
      platform: config.platform,
      timestamp: new Date().toISOString(),
      tests: {},
    };

    // Test 1: Provider Availability
    console.log('🔍 Testing provider availability...');
    const provider = integrationManager.getProvider(config.platform);
    results.tests.providerAvailability = {
      success: !!provider,
      message: provider ? 'Provider available' : 'Provider not found',
      details: provider ? {
        name: provider.name,
        identifier: provider.identifier,
        scopes: provider.scopes,
        isBetweenSteps: provider.isBetweenSteps,
      } : null,
    };

    if (!provider) {
      results.success = false;
      return results;
    }

    // Test 2: Auth URL Generation
    console.log('🔐 Testing auth URL generation...');
    try {
      const authUrl = await integrationManager.generateAuthUrl(config.platform);
      results.tests.authUrlGeneration = {
        success: !!authUrl,
        message: authUrl ? 'Auth URL generated successfully' : 'Failed to generate auth URL',
        details: {
          hasUrl: !!authUrl?.url,
          hasCodeVerifier: !!authUrl?.codeVerifier,
          hasState: !!authUrl?.state,
          urlLength: authUrl?.url?.length || 0,
        },
      };
    } catch (error) {
      results.tests.authUrlGeneration = {
        success: false,
        message: 'Auth URL generation failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }

    // Test 3: Integration Connection (if integration ID provided)
    if (config.integrationId) {
      console.log('🔗 Testing integration connection...');
      try {
        const connectionTest = await integrationManager.testConnection(config.integrationId);
        results.tests.connectionTest = connectionTest;
      } catch (error) {
        results.tests.connectionTest = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }

      // Test 4: Posting (if not skipped)
      if (!config.skipPosting) {
        console.log('📝 Testing posting functionality...');
        try {
          const postDetails = [{
            id: `test_${Date.now()}`,
            message: config.testContent,
            settings: {},
          }];

          const postResults = await integrationManager.post(config.integrationId, postDetails);
          results.tests.posting = {
            success: postResults.length > 0 && postResults[0].status === 'posted',
            message: postResults.length > 0 && postResults[0].status === 'posted' 
              ? 'Post published successfully' 
              : 'Post failed to publish',
            details: {
              results: postResults,
              postCount: postResults.length,
              successCount: postResults.filter(r => r.status === 'posted').length,
            },
          };
        } catch (error) {
          results.tests.posting = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      }
    }

    // Test 5: Environment Variables Check
    console.log('⚙️ Testing environment configuration...');
    results.tests.environmentConfig = this.checkEnvironmentConfig(config.platform);

    // Calculate overall success
    const testKeys = Object.keys(results.tests);
    const successfulTests = testKeys.filter(key => results.tests[key].success).length;
    results.success = successfulTests === testKeys.length;
    results.testsRun = testKeys.length;
    results.testsSuccessful = successfulTests;
    results.testsFailed = testKeys.length - successfulTests;

    return results;
  }

  checkEnvironmentConfig(platform: string): any {
    const envVars: Record<string, string[]> = {
      linkedin: ['LINKEDIN_CLIENT_ID', 'LINKEDIN_CLIENT_SECRET'],
      facebook: ['FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET'],
      instagram: ['FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET'], // Uses Facebook API
      twitter: ['TWITTER_CLIENT_ID', 'TWITTER_CLIENT_SECRET'],
    };

    const requiredVars = envVars[platform] || [];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    return {
      success: missingVars.length === 0,
      message: missingVars.length === 0 
        ? 'All environment variables configured' 
        : `Missing environment variables: ${missingVars.join(', ')}`,
      details: {
        required: requiredVars,
        missing: missingVars,
        configured: requiredVars.filter(varName => !!process.env[varName]),
      },
    };
  }

  printPlatformResults(platform: string, results: any) {
    console.log(`\n📊 ${platform.toUpperCase()} Results:`);
    
    Object.entries(results.tests).forEach(([testName, result]: [string, any]) => {
      const icon = result.success ? '✅' : '❌';
      console.log(`  ${icon} ${testName}: ${result.message}`);
      
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
    });

    const overallIcon = results.success ? '✅' : '❌';
    console.log(`\n${overallIcon} Overall: ${results.testsSuccessful}/${results.testsRun} tests passed`);
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 ENHANCED INTEGRATION TEST SUMMARY');
    console.log('='.repeat(60));

    const platforms = Object.keys(this.results);
    const successfulPlatforms = platforms.filter(p => this.results[p].success).length;

    console.log(`\n🎯 Overall Results: ${successfulPlatforms}/${platforms.length} platforms passed`);

    platforms.forEach(platform => {
      const result = this.results[platform];
      const icon = result.success ? '✅' : '❌';
      const status = result.success ? 'PASSED' : 'FAILED';
      
      console.log(`  ${icon} ${platform.toUpperCase()}: ${status}`);
      
      if (result.tests) {
        const failed = Object.entries(result.tests)
          .filter(([_, test]: [string, any]) => !test.success)
          .map(([name, _]) => name);
        
        if (failed.length > 0) {
          console.log(`     Failed tests: ${failed.join(', ')}`);
        }
      }
    });

    console.log('\n📝 Recommendations:');
    platforms.forEach(platform => {
      const result = this.results[platform];
      if (!result.success) {
        console.log(`\n  ${platform.toUpperCase()}:`);
        
        if (result.tests?.environmentConfig && !result.tests.environmentConfig.success) {
          console.log(`    - Configure missing environment variables: ${result.tests.environmentConfig.details.missing.join(', ')}`);
        }
        
        if (result.tests?.providerAvailability && !result.tests.providerAvailability.success) {
          console.log(`    - Implement or register the ${platform} provider`);
        }
        
        if (result.tests?.authUrlGeneration && !result.tests.authUrlGeneration.success) {
          console.log(`    - Fix OAuth configuration for ${platform}`);
        }
        
        if (result.tests?.connectionTest && !result.tests.connectionTest.success) {
          console.log(`    - Verify integration tokens and permissions for ${platform}`);
        }
        
        if (result.tests?.posting && !result.tests.posting.success) {
          console.log(`    - Check posting permissions and API limits for ${platform}`);
        }
      }
    });

    console.log('\n🚀 Next Steps:');
    console.log('  1. Fix any failed tests based on recommendations above');
    console.log('  2. Test OAuth flows manually using the testing dashboard');
    console.log('  3. Create test integrations for each platform');
    console.log('  4. Run posting tests with real content');
    console.log('  5. Monitor error logs and performance metrics');
  }

  async saveResults() {
    try {
      const { error } = await this.supabase
        .from('test_results')
        .insert({
          test_type: 'comprehensive_enhanced',
          platform: 'all',
          results: this.results,
          success: Object.values(this.results).every((r: any) => r.success),
          created_at: new Date().toISOString(),
        });

      if (error) {
        console.error('Failed to save test results:', error);
      } else {
        console.log('\n💾 Test results saved to database');
      }
    } catch (error) {
      console.error('Failed to save test results:', error);
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new EnhancedIntegrationTester();
  tester.runAllTests().catch(console.error);
}

export { EnhancedIntegrationTester };
