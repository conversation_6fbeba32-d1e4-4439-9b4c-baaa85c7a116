// =====================================================
// eWasl Bulk Scheduler - CSV Import & Bulk Operations
// =====================================================

import { createClient } from '@/lib/supabase/server';
import {
  BulkOperation,
  BulkScheduleItem,
  BulkImportResult,
  CSVImportResult,
  CSVValidationError,
  CSV_TEMPLATE
} from '@/lib/types/scheduling';

export class BulkScheduler {
  private supabase = createClient();

  /**
   * Import posts from CSV file
   */
  async importFromCSV(file: File, userId: string): Promise<BulkImportResult> {
    try {
      console.log('Starting CSV import for user:', userId);

      // 1. Parse CSV file
      const csvData = await this.parseCSVFile(file);

      // 2. Validate CSV data
      const validationResult = await this.validateBulkData(csvData);

      // 3. Create bulk operation record
      const { data: operation, error } = await this.supabase
        .from('bulk_operations')
        .insert({
          user_id: userId,
          operation_type: 'import',
          status: 'pending',
          total_items: validationResult.validRows.length,
          operation_config: {
            source_file: file.name,
            file_size: file.size,
            import_type: 'csv',
          },
        })
        .select()
        .single();

      if (error) throw error;

      // 4. Log activity
      await this.supabase
        .from('activities')
        .insert({
          user_id: userId,
          action: 'BULK_IMPORT_STARTED',
          metadata: {
            operationId: operation.id,
            totalItems: validationResult.validRows.length,
            invalidItems: validationResult.invalidRows.length,
            fileName: file.name,
          },
        });

      return {
        operation: this.mapBulkOperation(operation),
        validItems: validationResult.validRows,
        invalidItems: validationResult.invalidRows.map(row => ({
          item: row.data,
          errors: row.errors.map(err => err.error),
        })),
        summary: {
          totalItems: csvData.length,
          validItems: validationResult.validRows.length,
          invalidItems: validationResult.invalidRows.length,
          estimatedProcessingTime: Math.ceil(validationResult.validRows.length / 10), // 10 posts per second
        },
      };

    } catch (error) {
      console.error('Error importing CSV:', error);
      throw error;
    }
  }

  /**
   * Process bulk schedule operation
   */
  async processBulkSchedule(operationId: string): Promise<void> {
    try {
      console.log('Processing bulk schedule operation:', operationId);

      // 1. Get operation details
      const { data: operation, error: opError } = await this.supabase
        .from('bulk_operations')
        .select('*')
        .eq('id', operationId)
        .single();

      if (opError || !operation) {
        throw new Error('Operation not found');
      }

      // 2. Update status to processing
      await this.updateOperationStatus(operationId, 'processing', {
        started_at: new Date().toISOString(),
      });

      // 3. Get the bulk items to process (this would come from a temporary storage)
      // For now, we'll simulate the processing
      const batchSize = 50;
      let processedItems = 0;
      let successfulItems = 0;
      let failedItems = 0;
      const errors: any[] = [];

      // Simulate processing in batches
      for (let i = 0; i < operation.total_items; i += batchSize) {
        const batchEnd = Math.min(i + batchSize, operation.total_items);
        const batchItems = batchEnd - i;

        try {
          // Process batch (this would be actual post creation)
          await this.processBatch(operation.user_id, i, batchItems);

          successfulItems += batchItems;
          processedItems += batchItems;

          // Update progress
          await this.updateOperationProgress(operationId, processedItems, successfulItems, failedItems);

          // Add processing log
          await this.addProcessingLog(operationId, {
            batch: Math.floor(i / batchSize) + 1,
            processed: batchItems,
            successful: batchItems,
            failed: 0,
            timestamp: new Date().toISOString(),
          });

        } catch (batchError: any) {
          console.error('Batch processing error:', batchError);
          failedItems += batchItems;
          processedItems += batchItems;
          errors.push({
            batch: Math.floor(i / batchSize) + 1,
            error: batchError.message,
            items: batchItems,
          });

          await this.updateOperationProgress(operationId, processedItems, successfulItems, failedItems);
        }
      }

      // 4. Complete operation
      const finalStatus = failedItems === 0 ? 'completed' : 'failed';
      await this.updateOperationStatus(operationId, finalStatus, {
        completed_at: new Date().toISOString(),
        result_summary: {
          totalProcessed: processedItems,
          successful: successfulItems,
          failed: failedItems,
          errors: errors,
        },
      });

      // 5. Log completion
      await this.supabase
        .from('activities')
        .insert({
          user_id: operation.user_id,
          action: 'BULK_IMPORT_COMPLETED',
          metadata: {
            operationId,
            totalProcessed: processedItems,
            successful: successfulItems,
            failed: failedItems,
            status: finalStatus,
          },
        });

      console.log(`Bulk operation completed: ${successfulItems}/${operation.total_items} successful`);

    } catch (error) {
      console.error('Error processing bulk schedule:', error);

      // Update operation status to failed
      await this.updateOperationStatus(operationId, 'failed', {
        completed_at: new Date().toISOString(),
        error_log: [{ error: error instanceof Error ? error.message : String(error), timestamp: new Date().toISOString() }],
      });

      throw error;
    }
  }

  /**
   * Get bulk operation status
   */
  async getBulkOperationStatus(operationId: string): Promise<BulkOperation | null> {
    try {
      const { data: operation, error } = await this.supabase
        .from('bulk_operations')
        .select('*')
        .eq('id', operationId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      return this.mapBulkOperation(operation);
    } catch (error) {
      console.error('Error getting operation status:', error);
      throw error;
    }
  }

  /**
   * Parse CSV file content
   */
  private async parseCSVFile(file: File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        try {
          const csvText = event.target?.result as string;
          const lines = csvText.split('\n').filter(line => line.trim());

          if (lines.length < 2) {
            throw new Error('CSV file must contain at least a header and one data row');
          }

          const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
          const data = [];

          for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',').map(v => v.trim());
            const row: any = {};

            headers.forEach((header, index) => {
              row[header] = values[index] || '';
            });

            row._rowNumber = i + 1;
            data.push(row);
          }

          resolve(data);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error('Failed to read CSV file'));
      reader.readAsText(file);
    });
  }

  /**
   * Validate bulk data
   */
  private async validateBulkData(csvData: any[]): Promise<CSVImportResult> {
    const validRows: BulkScheduleItem[] = [];
    const invalidRows: Array<{ row: number; data: any; errors: CSVValidationError[] }> = [];
    const warnings: string[] = [];

    for (const row of csvData) {
      const errors: CSVValidationError[] = [];

      // Validate required fields
      if (!row.content || row.content.trim() === '') {
        errors.push({
          row: row._rowNumber,
          column: 'content',
          value: row.content,
          error: 'Content is required',
        });
      }

      if (!row.platforms || row.platforms.trim() === '') {
        errors.push({
          row: row._rowNumber,
          column: 'platforms',
          value: row.platforms,
          error: 'At least one platform is required',
        });
      }

      if (!row.scheduled_date || row.scheduled_date.trim() === '') {
        errors.push({
          row: row._rowNumber,
          column: 'scheduled_date',
          value: row.scheduled_date,
          error: 'Scheduled date is required',
        });
      }

      if (!row.scheduled_time || row.scheduled_time.trim() === '') {
        errors.push({
          row: row._rowNumber,
          column: 'scheduled_time',
          value: row.scheduled_time,
          error: 'Scheduled time is required',
        });
      }

      // Validate content length
      if (row.content && row.content.length > 2000) {
        errors.push({
          row: row._rowNumber,
          column: 'content',
          value: row.content,
          error: 'Content must be less than 2000 characters',
        });
      }

      // Validate platforms
      if (row.platforms) {
        const platforms = row.platforms.split(',').map((p: string) => p.trim().toUpperCase());
        const validPlatforms = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'];
        const invalidPlatforms = platforms.filter((p: string) => !validPlatforms.includes(p));

        if (invalidPlatforms.length > 0) {
          errors.push({
            row: row._rowNumber,
            column: 'platforms',
            value: row.platforms,
            error: `Invalid platforms: ${invalidPlatforms.join(', ')}`,
          });
        }
      }

      // Validate date and time
      if (row.scheduled_date && row.scheduled_time) {
        try {
          const dateTimeStr = `${row.scheduled_date}T${row.scheduled_time}:00`;
          const scheduledAt = new Date(dateTimeStr);

          if (isNaN(scheduledAt.getTime())) {
            errors.push({
              row: row._rowNumber,
              column: 'scheduled_date',
              value: `${row.scheduled_date} ${row.scheduled_time}`,
              error: 'Invalid date or time format',
            });
          } else if (scheduledAt < new Date()) {
            warnings.push(`Row ${row._rowNumber}: Scheduled time is in the past`);
          }
        } catch (error) {
          errors.push({
            row: row._rowNumber,
            column: 'scheduled_date',
            value: `${row.scheduled_date} ${row.scheduled_time}`,
            error: 'Invalid date or time format',
          });
        }
      }

      // Validate media URL if provided
      if (row.media_url && row.media_url.trim() !== '') {
        try {
          new URL(row.media_url);
        } catch (error) {
          errors.push({
            row: row._rowNumber,
            column: 'media_url',
            value: row.media_url,
            error: 'Invalid URL format',
          });
        }
      }

      // If no errors, add to valid rows
      if (errors.length === 0) {
        const platforms = row.platforms.split(',').map((p: string) => p.trim().toUpperCase());
        const scheduledAt = new Date(`${row.scheduled_date}T${row.scheduled_time}:00`);
        const tags = row.tags ? row.tags.split(',').map((t: string) => t.trim()) : [];

        validRows.push({
          content: row.content.trim(),
          mediaUrl: row.media_url && row.media_url.trim() !== '' ? row.media_url.trim() : undefined,
          platforms,
          scheduledAt,
          tags,
          metadata: {
            rowNumber: row._rowNumber,
            originalData: row,
          },
        });
      } else {
        invalidRows.push({
          row: row._rowNumber,
          data: row,
          errors,
        });
      }
    }

    return {
      isValid: invalidRows.length === 0,
      validRows,
      invalidRows,
      summary: {
        totalRows: csvData.length,
        validRows: validRows.length,
        invalidRows: invalidRows.length,
        warnings,
      },
    };
  }

  /**
   * Process a batch of posts
   */
  private async processBatch(userId: string, startIndex: number, batchSize: number): Promise<void> {
    // This is a placeholder for actual batch processing
    // In a real implementation, this would create the actual posts
    console.log(`Processing batch: ${startIndex} to ${startIndex + batchSize} for user ${userId}`);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Update operation status
   */
  private async updateOperationStatus(operationId: string, status: string, additionalData: any = {}): Promise<void> {
    await this.supabase
      .from('bulk_operations')
      .update({
        status,
        ...additionalData,
      })
      .eq('id', operationId);
  }

  /**
   * Update operation progress
   */
  private async updateOperationProgress(
    operationId: string,
    processedItems: number,
    successfulItems: number,
    failedItems: number
  ): Promise<void> {
    await this.supabase
      .from('bulk_operations')
      .update({
        processed_items: processedItems,
        successful_items: successfulItems,
        failed_items: failedItems,
      })
      .eq('id', operationId);
  }

  /**
   * Add processing log entry
   */
  private async addProcessingLog(operationId: string, logEntry: any): Promise<void> {
    const { data: operation } = await this.supabase
      .from('bulk_operations')
      .select('processing_log')
      .eq('id', operationId)
      .single();

    const currentLog = operation?.processing_log || [];
    currentLog.push(logEntry);

    await this.supabase
      .from('bulk_operations')
      .update({ processing_log: currentLog })
      .eq('id', operationId);
  }

  /**
   * Map database record to BulkOperation type
   */
  private mapBulkOperation(operation: any): BulkOperation {
    return {
      id: operation.id,
      userId: operation.user_id,
      operationType: operation.operation_type,
      status: operation.status,
      totalItems: operation.total_items,
      processedItems: operation.processed_items,
      successfulItems: operation.successful_items,
      failedItems: operation.failed_items,
      sourceFileUrl: operation.source_file_url,
      operationConfig: operation.operation_config,
      errorLog: operation.error_log || [],
      resultSummary: operation.result_summary,
      processingLog: operation.processing_log || [],
      startedAt: operation.started_at ? new Date(operation.started_at) : undefined,
      completedAt: operation.completed_at ? new Date(operation.completed_at) : undefined,
      estimatedCompletionAt: operation.estimated_completion_at ? new Date(operation.estimated_completion_at) : undefined,
      createdAt: new Date(operation.created_at),
      updatedAt: new Date(operation.updated_at),
    };
  }

  /**
   * Get user's bulk operations
   */
  async getUserBulkOperations(userId: string, limit: number = 20): Promise<BulkOperation[]> {
    try {
      const { data: operations, error } = await this.supabase
        .from('bulk_operations')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return (operations || []).map(op => this.mapBulkOperation(op));
    } catch (error) {
      console.error('Error fetching bulk operations:', error);
      throw error;
    }
  }

  /**
   * Cancel bulk operation
   */
  async cancelBulkOperation(operationId: string, userId: string): Promise<void> {
    try {
      await this.supabase
        .from('bulk_operations')
        .update({ status: 'cancelled' })
        .eq('id', operationId)
        .eq('user_id', userId);

      await this.supabase
        .from('activities')
        .insert({
          user_id: userId,
          action: 'BULK_OPERATION_CANCELLED',
          metadata: { operationId },
        });
    } catch (error) {
      console.error('Error cancelling bulk operation:', error);
      throw error;
    }
  }
}
