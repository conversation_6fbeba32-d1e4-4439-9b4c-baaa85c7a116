/**
 * Stripe Subscription Service for eWasl
 * Handles all subscription-related operations with Stripe integration
 */

import { getStripeInstance, SUBSCRIPTION_PLANS, getPlanByStripePriceId, STRIPE_WEBHOOK_EVENTS } from './config';
import { createServiceRoleClient } from '@/lib/supabase/server';
import type Stripe from 'stripe';

export class SubscriptionService {
  private stripe = getStripeInstance();
  private supabase = createServiceRoleClient();

  /**
   * Create a Stripe customer for a user
   */
  async createCustomer(userId: string, email: string, name?: string) {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: { 
          userId,
          source: 'ewasl_app'
        }
      });

      // Update user with Stripe customer ID
      const { error } = await this.supabase
        .from('users')
        .update({ stripe_customer_id: customer.id })
        .eq('id', userId);

      if (error) {
        console.error('Failed to update user with Stripe customer ID:', error);
        throw error;
      }

      return customer;
    } catch (error) {
      console.error('Failed to create Stripe customer:', error);
      throw error;
    }
  }

  /**
   * Get or create a Stripe customer for a user
   */
  async getOrCreateCustomer(userId: string, email: string, name?: string) {
    // Check if user already has a Stripe customer ID
    const { data: user } = await this.supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (user?.stripe_customer_id) {
      try {
        // Verify customer exists in Stripe
        const customer = await this.stripe.customers.retrieve(user.stripe_customer_id);
        if (!customer.deleted) {
          return customer as Stripe.Customer;
        }
      } catch (error) {
        console.warn('Stripe customer not found, creating new one:', error);
      }
    }

    // Create new customer
    return await this.createCustomer(userId, email, name);
  }

  /**
   * Create a subscription for a customer
   */
  async createSubscription(
    customerId: string, 
    priceId: string, 
    options: {
      trialDays?: number;
      paymentMethodId?: string;
      couponId?: string;
    } = {}
  ) {
    try {
      const subscriptionData: Stripe.SubscriptionCreateParams = {
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { 
          save_default_payment_method: 'on_subscription' 
        },
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          source: 'ewasl_app'
        }
      };

      // Add trial period if specified
      if (options.trialDays) {
        subscriptionData.trial_period_days = options.trialDays;
      }

      // Add payment method if specified
      if (options.paymentMethodId) {
        subscriptionData.default_payment_method = options.paymentMethodId;
      }

      // Add coupon if specified
      if (options.couponId) {
        (subscriptionData as any).coupon = options.couponId;
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionData);

      return subscription;
    } catch (error) {
      console.error('Failed to create subscription:', error);
      throw error;
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true) {
    try {
      if (cancelAtPeriodEnd) {
        // Cancel at period end
        const subscription = await this.stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true,
          metadata: {
            canceled_by: 'user',
            canceled_at: new Date().toISOString()
          }
        });

        // Update database
        await this.updateSubscriptionInDatabase(subscription);

        return subscription;
      } else {
        // Cancel immediately
        const subscription = await this.stripe.subscriptions.cancel(subscriptionId);

        // Update database
        await this.updateSubscriptionInDatabase(subscription);

        return subscription;
      }
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
      throw error;
    }
  }

  /**
   * Update subscription (change plan, billing cycle, etc.)
   */
  async updateSubscription(subscriptionId: string, newPriceId: string) {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      
      const updatedSubscription = await this.stripe.subscriptions.update(subscriptionId, {
        items: [{
          id: subscription.items.data[0].id,
          price: newPriceId,
        }],
        proration_behavior: 'create_prorations',
      });

      // Update database
      await this.updateSubscriptionInDatabase(updatedSubscription);

      return updatedSubscription;
    } catch (error) {
      console.error('Failed to update subscription:', error);
      throw error;
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhook(event: Stripe.Event) {
    try {
      console.log(`Processing webhook event: ${event.type}`);

      switch (event.type) {
        case STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_CREATED:
        case STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_UPDATED:
          await this.handleSubscriptionChange(event.data.object as Stripe.Subscription);
          break;

        case STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_DELETED:
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        case STRIPE_WEBHOOK_EVENTS.INVOICE_PAYMENT_SUCCEEDED:
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;

        case STRIPE_WEBHOOK_EVENTS.INVOICE_PAYMENT_FAILED:
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        case STRIPE_WEBHOOK_EVENTS.CUSTOMER_CREATED:
        case STRIPE_WEBHOOK_EVENTS.CUSTOMER_UPDATED:
          await this.handleCustomerChange(event.data.object as Stripe.Customer);
          break;

        default:
          console.log(`Unhandled webhook event type: ${event.type}`);
      }
    } catch (error) {
      console.error('Webhook processing failed:', error);
      throw error;
    }
  }

  /**
   * Handle subscription creation/update
   */
  private async handleSubscriptionChange(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    
    // Get user by Stripe customer ID
    const { data: user } = await this.supabase
      .from('users')
      .select('id')
      .eq('stripe_customer_id', customerId)
      .single();

    if (!user) {
      console.error('User not found for Stripe customer:', customerId);
      return;
    }

    // Get plan information
    const priceId = subscription.items.data[0]?.price.id;
    const plan = getPlanByStripePriceId(priceId);
    
    if (!plan) {
      console.error('Plan not found for price ID:', priceId);
      return;
    }

    // Get plan from database
    const { data: dbPlan } = await this.supabase
      .from('subscription_plans')
      .select('id')
      .eq('plan_type', plan.id)
      .single();

    if (!dbPlan) {
      console.error('Database plan not found for:', plan.id);
      return;
    }

    // Upsert subscription
    await this.supabase
      .from('user_subscriptions')
      .upsert({
        user_id: user.id,
        plan_id: dbPlan.id,
        stripe_customer_id: customerId,
        stripe_subscription_id: subscription.id,
        status: subscription.status,
        billing_cycle: subscription.items.data[0]?.price.recurring?.interval === 'year' ? 'yearly' : 'monthly',
        current_period_start: new Date((subscription as any).current_period_start * 1000).toISOString(),
        current_period_end: new Date((subscription as any).current_period_end * 1000).toISOString(),
        trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000).toISOString() : null,
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
        cancel_at_period_end: subscription.cancel_at_period_end,
        canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
      }, {
        onConflict: 'user_id'
      });

    // Update user's current plan
    await this.supabase
      .from('users')
      .update({
        current_plan_id: dbPlan.id,
        subscription_status: subscription.status,
      })
      .eq('id', user.id);
  }

  /**
   * Handle subscription deletion
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    
    // Get user by Stripe customer ID
    const { data: user } = await this.supabase
      .from('users')
      .select('id')
      .eq('stripe_customer_id', customerId)
      .single();

    if (!user) {
      console.error('User not found for Stripe customer:', customerId);
      return;
    }

    // Get free plan
    const { data: freePlan } = await this.supabase
      .from('subscription_plans')
      .select('id')
      .eq('plan_type', 'free')
      .single();

    if (!freePlan) {
      console.error('Free plan not found in database');
      return;
    }

    // Update subscription status
    await this.supabase
      .from('user_subscriptions')
      .update({
        status: 'canceled',
        canceled_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', subscription.id);

    // Revert user to free plan
    await this.supabase
      .from('users')
      .update({
        current_plan_id: freePlan.id,
        subscription_status: 'free',
      })
      .eq('id', user.id);
  }

  /**
   * Handle successful payment
   */
  private async handlePaymentSucceeded(invoice: Stripe.Invoice) {
    const customerId = invoice.customer as string;
    
    // Get user by Stripe customer ID
    const { data: user } = await this.supabase
      .from('users')
      .select('id')
      .eq('stripe_customer_id', customerId)
      .single();

    if (!user) {
      console.error('User not found for Stripe customer:', customerId);
      return;
    }

    // Get subscription
    const { data: subscription } = await this.supabase
      .from('user_subscriptions')
      .select('id')
      .eq('stripe_subscription_id', (invoice as any).subscription as string)
      .single();

    // Record payment
    await this.supabase
      .from('payment_history')
      .insert({
        user_id: user.id,
        subscription_id: subscription?.id,
        stripe_payment_intent_id: (invoice as any).payment_intent as string,
        stripe_invoice_id: invoice.id,
        amount: (invoice.amount_paid || 0) / 100, // Convert from cents
        currency: invoice.currency,
        status: 'succeeded',
        billing_reason: invoice.billing_reason || 'subscription_cycle',
        invoice_pdf_url: invoice.invoice_pdf,
        metadata: {
          invoice_number: invoice.number,
          period_start: invoice.period_start,
          period_end: invoice.period_end,
        },
      });
  }

  /**
   * Handle failed payment
   */
  private async handlePaymentFailed(invoice: Stripe.Invoice) {
    const customerId = invoice.customer as string;
    
    // Get user by Stripe customer ID
    const { data: user } = await this.supabase
      .from('users')
      .select('id, email')
      .eq('stripe_customer_id', customerId)
      .single();

    if (!user) {
      console.error('User not found for Stripe customer:', customerId);
      return;
    }

    // Get subscription
    const { data: subscription } = await this.supabase
      .from('user_subscriptions')
      .select('id')
      .eq('stripe_subscription_id', (invoice as any).subscription as string)
      .single();

    // Record failed payment
    await this.supabase
      .from('payment_history')
      .insert({
        user_id: user.id,
        subscription_id: subscription?.id,
        stripe_payment_intent_id: (invoice as any).payment_intent as string,
        stripe_invoice_id: invoice.id,
        amount: (invoice.amount_due || 0) / 100, // Convert from cents
        currency: invoice.currency,
        status: 'failed',
        billing_reason: invoice.billing_reason || 'subscription_cycle',
        metadata: {
          invoice_number: invoice.number,
          failure_reason: 'payment_failed',
        },
      });

    // TODO: Send payment failure notification email
    console.log(`Payment failed for user ${user.email}, invoice ${invoice.id}`);
  }

  /**
   * Handle customer changes
   */
  private async handleCustomerChange(customer: Stripe.Customer) {
    // Update user information if needed
    await this.supabase
      .from('users')
      .update({
        email: customer.email,
        name: customer.name,
      })
      .eq('stripe_customer_id', customer.id);
  }

  /**
   * Update subscription in database
   */
  private async updateSubscriptionInDatabase(subscription: Stripe.Subscription) {
    await this.supabase
      .from('user_subscriptions')
      .update({
        status: subscription.status,
        current_period_start: new Date((subscription as any).current_period_start * 1000).toISOString(),
        current_period_end: new Date((subscription as any).current_period_end * 1000).toISOString(),
        cancel_at_period_end: subscription.cancel_at_period_end,
        canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
      })
      .eq('stripe_subscription_id', subscription.id);
  }
}
