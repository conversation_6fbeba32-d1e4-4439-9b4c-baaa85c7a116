#!/usr/bin/env node

/**
 * Complete LinkedIn OAuth Integration Test
 * Tests the entire flow from authorization URL generation to account storage
 */

const baseUrl = 'http://localhost:3001';

async function testLinkedInIntegration() {
  console.log('🔍 TESTING LINKEDIN OAUTH INTEGRATION');
  console.log('=====================================\n');

  try {
    // Step 1: Test OAuth configuration
    console.log('📋 Step 1: Testing OAuth Configuration...');
    const configResponse = await fetch(`${baseUrl}/api/social/oauth-status`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ platform: 'linkedin', action: 'check-credentials' })
    });
    const configData = await configResponse.json();
    
    if (!configData.success) {
      throw new Error(`OAuth configuration failed: ${configData.message}`);
    }
    console.log('✅ OAuth Configuration: PASSED');
    console.log(`   - Client ID: ${configData.hasClientId ? 'Present' : 'Missing'}`);
    console.log(`   - Client Secret: ${configData.hasClientSecret ? 'Present' : 'Missing'}`);
    console.log(`   - Scopes: ${configData.scopes.join(', ')}\n`);

    // Step 2: Test authorization URL generation
    console.log('🔗 Step 2: Testing Authorization URL Generation...');
    const urlResponse = await fetch(`${baseUrl}/api/social/oauth-status`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ platform: 'linkedin', action: 'test-auth-url' })
    });
    const urlData = await urlResponse.json();
    
    if (!urlData.success) {
      throw new Error(`URL generation failed: ${urlData.message}`);
    }
    console.log('✅ Authorization URL Generation: PASSED');
    console.log(`   - URL: ${urlData.authUrl.substring(0, 80)}...\n`);

    // Step 3: Test user lookup (service role client)
    console.log('👤 Step 3: Testing User Lookup (Service Role)...');
    const userResponse = await fetch(`${baseUrl}/api/test-user-lookup`);
    const userData = await userResponse.json();
    
    if (!userData.success) {
      throw new Error(`User lookup failed: ${userData.message}`);
    }
    console.log('✅ User Lookup: PASSED');
    console.log(`   - User ID: ${userData.userData.id}`);
    console.log(`   - Email: ${userData.userData.email}`);
    console.log(`   - Name: ${userData.userData.name}\n`);

    // Step 4: Test callback with simulated LinkedIn response
    console.log('🔄 Step 4: Testing OAuth Callback (Simulated)...');
    console.log('   Note: This will fail at token exchange (expected with fake code)');
    
    const callbackResponse = await fetch(
      `${baseUrl}/api/linkedin/callback?code=fake-test-code&state=test-state`,
      { redirect: 'manual' }
    );
    
    console.log(`   - Response Status: ${callbackResponse.status}`);
    console.log(`   - Response Type: ${callbackResponse.status === 307 ? 'Redirect (Expected)' : 'Other'}`);
    
    if (callbackResponse.status === 307) {
      const location = callbackResponse.headers.get('location');
      console.log(`   - Redirect Location: ${location}`);
      
      if (location && location.includes('/auth/error?error=token_exchange_failed')) {
        console.log('✅ OAuth Callback: PASSED (Expected token exchange failure)');
      } else if (location && location.includes('/auth/error?error=user_not_found')) {
        console.log('❌ OAuth Callback: FAILED (User not found - RLS issue)');
        throw new Error('User lookup failed in callback - RLS policy blocking access');
      } else {
        console.log('⚠️  OAuth Callback: Unexpected redirect location');
      }
    }

    console.log('\n🎉 LINKEDIN OAUTH INTEGRATION TEST RESULTS');
    console.log('==========================================');
    console.log('✅ OAuth Configuration: WORKING');
    console.log('✅ Authorization URL Generation: WORKING');
    console.log('✅ Service Role User Lookup: WORKING');
    console.log('✅ OAuth Callback Flow: WORKING (Ready for real LinkedIn auth)');
    console.log('\n🚀 INTEGRATION STATUS: READY FOR PRODUCTION TESTING!');
    console.log('\nNext Steps:');
    console.log('1. Visit: http://localhost:3001/test-linkedin');
    console.log('2. Click "Generate Authorization URL"');
    console.log('3. Click "Start LinkedIn OAuth"');
    console.log('4. Complete LinkedIn authorization');
    console.log('5. Verify successful account connection');

  } catch (error) {
    console.error('\n❌ LINKEDIN OAUTH INTEGRATION TEST FAILED');
    console.error('=========================================');
    console.error(`Error: ${error.message}`);
    console.error('\nPlease check the server logs and fix the issues before proceeding.');
    process.exit(1);
  }
}

// Run the test
testLinkedInIntegration();
