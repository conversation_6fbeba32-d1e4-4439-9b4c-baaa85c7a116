'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  Star,
  Users,
  Eye,
  Copy,
  Heart,
  TrendingUp,
  FileText,
  Sparkles,
  Target,
  Globe,
  Calendar,
  Hash,
} from 'lucide-react';
import { toast } from 'sonner';

interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  platform: string[];
  language: string;
  content: string;
  variables: any[];
  hashtags: string[];
  tone: string;
  industry: string[];
  isPublic: boolean;
  usageCount: number;
  rating: number;
  tags: string[];
  createdAt: Date;
}

interface TemplatesBrowserProps {
  onSelectTemplate?: (template: ContentTemplate) => void;
  onUseTemplate?: (template: ContentTemplate, variables: Record<string, string>) => void;
}

export function TemplatesBrowser({ onSelectTemplate, onUseTemplate }: TemplatesBrowserProps) {
  const [templates, setTemplates] = useState<ContentTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<ContentTemplate[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState<ContentTemplate | null>(null);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});

  // Filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPlatform, setSelectedPlatform] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [selectedTone, setSelectedTone] = useState('all');
  const [sortBy, setSortBy] = useState('popular');

  useEffect(() => {
    fetchTemplates();
    fetchCategories();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [templates, searchQuery, selectedCategory, selectedPlatform, selectedLanguage, selectedTone, sortBy]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        action: 'search',
        limit: '50',
      });

      const response = await fetch(`/api/content/templates?${params}`);
      const result = await response.json();

      if (result.success) {
        setTemplates(result.templates);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('فشل في تحميل القوالب');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/content/templates?action=categories');
      const result = await response.json();

      if (result.success) {
        setCategories(result.categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const applyFilters = () => {
    let filtered = [...templates];

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.content.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Platform filter
    if (selectedPlatform !== 'all') {
      filtered = filtered.filter(template => template.platform.includes(selectedPlatform));
    }

    // Language filter
    if (selectedLanguage !== 'all') {
      filtered = filtered.filter(template =>
        template.language === selectedLanguage || template.language === 'both'
      );
    }

    // Tone filter
    if (selectedTone !== 'all') {
      filtered = filtered.filter(template => template.tone === selectedTone);
    }

    // Sort
    switch (sortBy) {
      case 'popular':
        filtered.sort((a, b) => b.usageCount - a.usageCount);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    setFilteredTemplates(filtered);
  };

  const handleUseTemplate = async (template: ContentTemplate) => {
    if (template.variables.length === 0) {
      // No variables needed, use template directly
      try {
        const response = await fetch('/api/content/templates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'process',
            templateId: template.id,
            variables: {},
          }),
        });

        const result = await response.json();

        if (result.success) {
          onUseTemplate?.(template, {});
          toast.success('تم تطبيق القالب بنجاح');
        } else {
          toast.error(result.error || 'فشل في تطبيق القالب');
        }
      } catch (error) {
        console.error('Error using template:', error);
        toast.error('خطأ في تطبيق القالب');
      }
    } else {
      // Template has variables, show dialog
      setSelectedTemplate(template);
      setTemplateVariables({});
    }
  };

  const processTemplateWithVariables = async () => {
    if (!selectedTemplate) return;

    try {
      const response = await fetch('/api/content/templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'process',
          templateId: selectedTemplate.id,
          variables: templateVariables,
        }),
      });

      const result = await response.json();

      if (result.success) {
        onUseTemplate?.(selectedTemplate, templateVariables);
        setSelectedTemplate(null);
        setTemplateVariables({});
        toast.success('تم تطبيق القالب بنجاح');
      } else {
        toast.error(result.error || 'فشل في تطبيق القالب');
      }
    } catch (error) {
      console.error('Error processing template:', error);
      toast.error('خطأ في تطبيق القالب');
    }
  };

  const copyTemplateContent = (template: ContentTemplate) => {
    navigator.clipboard.writeText(template.content);
    toast.success('تم نسخ محتوى القالب');
  };

  const getPlatformIcon = (platform: string) => {
    const icons = {
      facebook: '📘',
      instagram: '📷',
      twitter: '🐦',
      linkedin: '💼',
      snapchat: '👻',
    };
    return icons[platform as keyof typeof icons] || '📱';
  };

  const getToneColor = (tone: string) => {
    const colors = {
      professional: 'bg-blue-100 text-blue-800',
      casual: 'bg-green-100 text-green-800',
      friendly: 'bg-yellow-100 text-yellow-800',
      formal: 'bg-gray-100 text-gray-800',
      humorous: 'bg-purple-100 text-purple-800',
      inspiring: 'bg-pink-100 text-pink-800',
    };
    return colors[tone as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل القوالب...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">مكتبة القوالب</h2>
          <p className="text-gray-600">اختر من مجموعة واسعة من القوالب الجاهزة</p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {filteredTemplates.length} قالب
        </Badge>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            البحث والتصفية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Search */}
            <div>
              <Label>البحث</Label>
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="ابحث في القوالب..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>

            {/* Category */}
            <div>
              <Label>الفئة</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Platform */}
            <div>
              <Label>المنصة</Label>
              <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المنصات</SelectItem>
                  <SelectItem value="facebook">فيسبوك</SelectItem>
                  <SelectItem value="instagram">إنستغرام</SelectItem>
                  <SelectItem value="twitter">تويتر</SelectItem>
                  <SelectItem value="linkedin">لينكد إن</SelectItem>
                  <SelectItem value="snapchat">سناب شات</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Language */}
            <div>
              <Label>اللغة</Label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع اللغات</SelectItem>
                  <SelectItem value="ar">العربية</SelectItem>
                  <SelectItem value="en">الإنجليزية</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Tone */}
            <div>
              <Label>النبرة</Label>
              <Select value={selectedTone} onValueChange={setSelectedTone}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع النبرات</SelectItem>
                  <SelectItem value="professional">مهني</SelectItem>
                  <SelectItem value="casual">عادي</SelectItem>
                  <SelectItem value="friendly">ودود</SelectItem>
                  <SelectItem value="formal">رسمي</SelectItem>
                  <SelectItem value="humorous">فكاهي</SelectItem>
                  <SelectItem value="inspiring">ملهم</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort */}
            <div>
              <Label>الترتيب</Label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popular">الأكثر استخداماً</SelectItem>
                  <SelectItem value="rating">الأعلى تقييماً</SelectItem>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="name">الاسم</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg mb-2">{template.name}</CardTitle>
                  <CardDescription className="text-sm line-clamp-2">
                    {template.description}
                  </CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyTemplateContent(template)}
                  className="shrink-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Platforms */}
              <div className="flex flex-wrap gap-1">
                {(template.platform || []).slice(0, 3).map((platform: string) => (
                  <Badge key={platform} variant="outline" className="text-xs">
                    {getPlatformIcon(platform)} {platform}
                  </Badge>
                ))}
                {(template.platform || []).length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{(template.platform || []).length - 3}
                  </Badge>
                )}
              </div>

              {/* Tone and Language */}
              <div className="flex gap-2">
                <Badge className={`text-xs ${getToneColor(template.tone)}`}>
                  {template.tone}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {template.language === 'ar' ? 'عربي' : template.language === 'en' ? 'إنجليزي' : 'متعدد'}
                </Badge>
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center gap-3">
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {template.usageCount}
                  </span>
                  <span className="flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    {template.rating.toFixed(1)}
                  </span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {template.category}
                </Badge>
              </div>

              {/* Hashtags */}
              {template.hashtags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {(template.hashtags || []).slice(0, 3).map((hashtag: string) => (
                    <Badge key={hashtag} variant="secondary" className="text-xs">
                      #{hashtag}
                    </Badge>
                  ))}
                  {(template.hashtags || []).length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{(template.hashtags || []).length - 3}
                    </Badge>
                  )}
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                <Button
                  size="sm"
                  onClick={() => handleUseTemplate(template)}
                  className="flex-1"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  استخدام
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onSelectTemplate?.(template)}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold mb-2">لا توجد قوالب</h3>
          <p className="text-gray-600 mb-4">لم يتم العثور على قوالب تطابق معايير البحث</p>
          <Button variant="outline" onClick={() => {
            setSearchQuery('');
            setSelectedCategory('all');
            setSelectedPlatform('all');
            setSelectedLanguage('all');
            setSelectedTone('all');
          }}>
            إعادة تعيين الفلاتر
          </Button>
        </div>
      )}

      {/* Template Variables Dialog */}
      {selectedTemplate && (
        <Dialog open={!!selectedTemplate} onOpenChange={() => setSelectedTemplate(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{selectedTemplate.name}</DialogTitle>
              <DialogDescription>{selectedTemplate.description}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                يحتاج هذا القالب إلى بعض المعلومات لتخصيص المحتوى:
              </p>
              {(selectedTemplate.variables || []).map((variable: any) => (
                <div key={variable.name}>
                  <Label>{variable.label}</Label>
                  {variable.type === 'select' ? (
                    <Select
                      value={templateVariables[variable.name] || ''}
                      onValueChange={(value) => setTemplateVariables(prev => ({ ...prev, [variable.name]: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={variable.placeholder} />
                      </SelectTrigger>
                      <SelectContent>
                        {variable.options?.map((option: string) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      type={variable.type}
                      value={templateVariables[variable.name] || ''}
                      onChange={(e) => setTemplateVariables(prev => ({ ...prev, [variable.name]: e.target.value }))}
                      placeholder={variable.placeholder}
                    />
                  )}
                </div>
              ))}
              <div className="flex gap-2 pt-4">
                <Button onClick={processTemplateWithVariables} className="flex-1">
                  تطبيق القالب
                </Button>
                <Button variant="outline" onClick={() => setSelectedTemplate(null)}>
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
