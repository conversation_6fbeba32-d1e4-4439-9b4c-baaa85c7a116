'use client';

import { useRouter } from 'next/router';
import { useState } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLocale, switchLocale, type Locale } from '@/lib/i18n';
import { cn } from '@/lib/utils';

interface LanguageSwitcherProps {
  className?: string;
  variant?: 'default' | 'minimal' | 'icon-only';
  size?: 'sm' | 'md' | 'lg';
}

const languages = {
  ar: {
    name: 'العربية',
    nativeName: 'العربية',
    flag: '🇸🇦',
    dir: 'rtl' as const,
  },
  en: {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    dir: 'ltr' as const,
  },
} as const;

export function LanguageSwitcher({ 
  className, 
  variant = 'default',
  size = 'md' 
}: LanguageSwitcherProps) {
  const router = useRouter();
  const currentLocale = useLocale();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (locale: Locale) => {
    switchLocale(router, locale);
    setIsOpen(false);
    
    // Update document direction
    document.documentElement.dir = languages[locale].dir;
    document.documentElement.lang = locale;
  };

  const currentLanguage = languages[currentLocale];

  if (variant === 'icon-only') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size={size}
            className={cn(
              'h-9 w-9 p-0',
              className
            )}
          >
            <Globe className="h-4 w-4" />
            <span className="sr-only">Switch language</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {Object.entries(languages).map(([locale, language]) => (
            <DropdownMenuItem
              key={locale}
              onClick={() => handleLanguageChange(locale as Locale)}
              className={cn(
                'flex items-center gap-2 cursor-pointer',
                currentLocale === locale && 'bg-accent'
              )}
            >
              <span className="text-lg">{language.flag}</span>
              <span className="flex-1">{language.nativeName}</span>
              {currentLocale === locale && (
                <div className="h-2 w-2 rounded-full bg-primary" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (variant === 'minimal') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size={size}
            className={cn(
              'gap-1 px-2',
              className
            )}
          >
            <span className="text-sm">{currentLanguage.flag}</span>
            <span className="text-sm font-medium">{currentLanguage.name}</span>
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          {Object.entries(languages).map(([locale, language]) => (
            <DropdownMenuItem
              key={locale}
              onClick={() => handleLanguageChange(locale as Locale)}
              className={cn(
                'flex items-center gap-2 cursor-pointer',
                currentLocale === locale && 'bg-accent'
              )}
            >
              <span>{language.flag}</span>
              <span className="flex-1">{language.nativeName}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size={size}
          className={cn(
            'gap-2 min-w-[120px] justify-between',
            className
          )}
        >
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            <span className="text-sm font-medium">{currentLanguage.nativeName}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {Object.entries(languages).map(([locale, language]) => (
          <DropdownMenuItem
            key={locale}
            onClick={() => handleLanguageChange(locale as Locale)}
            className={cn(
              'flex items-center gap-3 cursor-pointer p-3',
              currentLocale === locale && 'bg-accent'
            )}
          >
            <span className="text-lg">{language.flag}</span>
            <div className="flex-1">
              <div className="font-medium">{language.nativeName}</div>
              <div className="text-xs text-muted-foreground">{language.name}</div>
            </div>
            {currentLocale === locale && (
              <div className="h-2 w-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Hook for getting language information
export function useLanguageInfo() {
  const locale = useLocale();
  return {
    current: languages[locale],
    all: languages,
    isRTL: languages[locale].dir === 'rtl',
  };
}

// Utility component for conditional RTL rendering
interface RTLWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export function RTLWrapper({ children, className }: RTLWrapperProps) {
  const { isRTL } = useLanguageInfo();
  
  return (
    <div 
      className={cn(
        isRTL && 'rtl',
        className
      )}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      {children}
    </div>
  );
}

export default LanguageSwitcher;
