import { useState, useEffect } from 'react';

// Supported locales
export const locales = ['ar', 'en'] as const;
export type Locale = typeof locales[number];

// Default locale
export const defaultLocale: Locale = 'ar';

// Translation keys and values
const translations = {
  ar: {
    // Authentication
    'auth.welcome': 'مرحباً بعودتك',
    'auth.signIn': 'تسجيل الدخول',
    'auth.signUp': 'إنشاء حساب',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.forgotPassword': 'نسيت كلمة المرور؟',
    'auth.noAccount': 'ليس لديك حساب؟',
    'auth.hasAccount': 'لديك حساب بالفعل؟',
    'auth.createAccount': 'إنشاء حساب جديد',
    'auth.signInToAccount': 'تسجيل الدخول',
    'auth.invalidCredentials': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    'auth.loginSuccess': 'تم تسجيل الدخول بنجاح',
    'auth.signingIn': 'جاري تسجيل الدخول...',
    'auth.continueWith': 'المتابعة مع',
    'auth.or': 'أو',
    
    // Navigation
    'nav.dashboard': 'لوحة التحكم',
    'nav.posts': 'المنشورات',
    'nav.schedule': 'الجدولة',
    'nav.social': 'الحسابات',
    'nav.analytics': 'التحليلات',
    'nav.settings': 'الإعدادات',
    'nav.logout': 'تسجيل الخروج',
    
    // Common
    'common.loading': 'جاري التحميل...',
    'common.save': 'حفظ',
    'common.cancel': 'إلغاء',
    'common.delete': 'حذف',
    'common.edit': 'تعديل',
    'common.view': 'عرض',
    'common.create': 'إنشاء',
    'common.update': 'تحديث',
    'common.refresh': 'تحديث',
    'common.search': 'بحث',
    'common.filter': 'تصفية',
    'common.all': 'الكل',
    'common.active': 'نشط',
    'common.inactive': 'غير نشط',
    'common.connected': 'متصل',
    'common.disconnected': 'غير متصل',
    'common.success': 'نجح',
    'common.error': 'خطأ',
    'common.warning': 'تحذير',
    'common.info': 'معلومات',
    
    // Errors
    'errors.general': 'حدث خطأ غير متوقع',
    'errors.network': 'خطأ في الاتصال بالشبكة',
    'errors.unauthorized': 'غير مصرح لك بالوصول',
    'errors.notFound': 'الصفحة غير موجودة',
    'errors.validation': 'خطأ في التحقق من البيانات',
    
    // Posts
    'posts.title': 'المنشورات',
    'posts.create': 'إنشاء منشور جديد',
    'posts.draft': 'مسودة',
    'posts.scheduled': 'مجدول',
    'posts.published': 'منشور',
    'posts.failed': 'فشل',
    'posts.noPosts': 'لا توجد منشورات',
    'posts.createFirst': 'ابدأ بإنشاء منشورك الأول',
    
    // Social
    'social.title': 'الحسابات الاجتماعية',
    'social.connect': 'ربط الحساب',
    'social.disconnect': 'قطع الاتصال',
    'social.connected': 'متصل',
    'social.notConnected': 'غير متصل',
    'social.followers': 'المتابعون',
    'social.lastSync': 'آخر مزامنة',
    'social.status': 'الحالة',
    'social.sync': 'مزامنة',
    
    // Analytics
    'analytics.title': 'التحليلات',
    'analytics.views': 'المشاهدات',
    'analytics.engagement': 'التفاعل',
    'analytics.followers': 'المتابعون',
    'analytics.growth': 'النمو',
  },
  en: {
    // Authentication
    'auth.welcome': 'Welcome Back',
    'auth.signIn': 'Sign In',
    'auth.signUp': 'Sign Up',
    'auth.email': 'Email Address',
    'auth.password': 'Password',
    'auth.forgotPassword': 'Forgot password?',
    'auth.noAccount': "Don't have an account?",
    'auth.hasAccount': 'Already have an account?',
    'auth.createAccount': 'Create account',
    'auth.signInToAccount': 'Sign in to your account',
    'auth.invalidCredentials': 'Invalid email or password',
    'auth.loginSuccess': 'Successfully signed in',
    'auth.signingIn': 'Signing in...',
    'auth.continueWith': 'Continue with',
    'auth.or': 'or',
    
    // Navigation
    'nav.dashboard': 'Dashboard',
    'nav.posts': 'Posts',
    'nav.schedule': 'Schedule',
    'nav.social': 'Social Accounts',
    'nav.analytics': 'Analytics',
    'nav.settings': 'Settings',
    'nav.logout': 'Logout',
    
    // Common
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.create': 'Create',
    'common.update': 'Update',
    'common.refresh': 'Refresh',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.all': 'All',
    'common.active': 'Active',
    'common.inactive': 'Inactive',
    'common.connected': 'Connected',
    'common.disconnected': 'Disconnected',
    'common.success': 'Success',
    'common.error': 'Error',
    'common.warning': 'Warning',
    'common.info': 'Info',
    
    // Errors
    'errors.general': 'An unexpected error occurred',
    'errors.network': 'Network connection error',
    'errors.unauthorized': 'Unauthorized access',
    'errors.notFound': 'Page not found',
    'errors.validation': 'Validation error',
    
    // Posts
    'posts.title': 'Posts',
    'posts.create': 'Create New Post',
    'posts.draft': 'Draft',
    'posts.scheduled': 'Scheduled',
    'posts.published': 'Published',
    'posts.failed': 'Failed',
    'posts.noPosts': 'No posts found',
    'posts.createFirst': 'Create your first post',
    
    // Social
    'social.title': 'Social Accounts',
    'social.connect': 'Connect Account',
    'social.disconnect': 'Disconnect',
    'social.connected': 'Connected',
    'social.notConnected': 'Not Connected',
    'social.followers': 'Followers',
    'social.lastSync': 'Last Sync',
    'social.status': 'Status',
    'social.sync': 'Sync',
    
    // Analytics
    'analytics.title': 'Analytics',
    'analytics.views': 'Views',
    'analytics.engagement': 'Engagement',
    'analytics.followers': 'Followers',
    'analytics.growth': 'Growth',
  },
};

// Get direction for locale
export function getDirection(locale: Locale): 'ltr' | 'rtl' {
  return locale === 'ar' ? 'rtl' : 'ltr';
}

// Get locale from localStorage or default
export function getStoredLocale(): Locale {
  if (typeof window === 'undefined') return defaultLocale;
  
  const stored = localStorage.getItem('locale') as Locale;
  return locales.includes(stored) ? stored : defaultLocale;
}

// Store locale in localStorage
export function setStoredLocale(locale: Locale) {
  if (typeof window === 'undefined') return;
  localStorage.setItem('locale', locale);
}

// Translation hook
export function useTranslations() {
  const [locale, setLocale] = useState<Locale>(defaultLocale);

  useEffect(() => {
    setLocale(getStoredLocale());
  }, []);

  const changeLocale = (newLocale: Locale) => {
    setLocale(newLocale);
    setStoredLocale(newLocale);
    
    // Update document direction and lang
    document.documentElement.dir = getDirection(newLocale);
    document.documentElement.lang = newLocale;
  };

  const t = (key: string): string => {
    const keys = key.split('.');
    let value: any = translations[locale];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  return {
    locale,
    changeLocale,
    t,
    isRTL: locale === 'ar',
    direction: getDirection(locale),
  };
}

// Locale hook
export function useLocale() {
  const [locale, setLocale] = useState<Locale>(defaultLocale);

  useEffect(() => {
    setLocale(getStoredLocale());
  }, []);

  return locale;
}

// Change locale function
export function changeLocale(locale: Locale) {
  setStoredLocale(locale);
  
  // Update document direction and lang
  document.documentElement.dir = getDirection(locale);
  document.documentElement.lang = locale;
  
  // Reload page to apply changes
  window.location.reload();
}
