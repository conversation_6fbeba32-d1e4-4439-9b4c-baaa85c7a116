import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Simple health check that doesn't require database connection
    return NextResponse.json(
      { 
        status: "healthy", 
        timestamp: new Date().toISOString(),
        service: "ewasl-social-scheduler"
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Health check failed:", error);
    return NextResponse.json(
      { 
        status: "unhealthy", 
        timestamp: new Date().toISOString(),
        error: "Health check failed"
      },
      { status: 500 }
    );
  }
}