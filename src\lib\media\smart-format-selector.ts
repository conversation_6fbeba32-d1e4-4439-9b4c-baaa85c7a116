/**
 * Smart Format Selection Engine
 * Intelligently selects optimal file formats based on content analysis,
 * browser support, platform requirements, and performance metrics
 */

import { ContentAnalysisResult, VideoContentAnalysis } from './content-analyzer';

export interface FormatSupport {
  webp: boolean;
  avif: boolean;
  heic: boolean;
  jxl: boolean; // JPEG XL
  av1: boolean;
  vp9: boolean;
  h265: boolean;
}

export interface PlatformRequirements {
  platform: string;
  maxFileSize: number;
  preferredFormats: string[];
  supportedFormats: string[];
  qualityThreshold: number;
  compressionPriority: 'size' | 'quality' | 'speed';
}

export interface FormatRecommendation {
  primaryFormat: string;
  fallbackFormats: string[];
  quality: number;
  reasoning: string[];
  expectedSizeReduction: number;
  compatibilityScore: number;
  performanceScore: number;
  confidence: number;
}

export interface OptimizationStrategy {
  strategy: 'aggressive' | 'balanced' | 'conservative' | 'lossless';
  targetSizeReduction: number;
  qualityThreshold: number;
  speedPriority: 'fast' | 'balanced' | 'quality';
  multiFormat: boolean;
}

export class SmartFormatSelector {
  private platformRequirements: Map<string, PlatformRequirements> = new Map();
  private formatCapabilities: Map<string, any> = new Map();

  constructor() {
    this.initializePlatformRequirements();
    this.initializeFormatCapabilities();
  }

  /**
   * Select optimal format based on content analysis and requirements
   */
  async selectOptimalFormat(
    contentAnalysis: ContentAnalysisResult | VideoContentAnalysis,
    isVideo: boolean = false,
    targetPlatform?: string,
    browserSupport?: FormatSupport,
    strategy?: OptimizationStrategy
  ): Promise<FormatRecommendation> {
    try {
      console.log('Selecting optimal format:', {
        isVideo,
        targetPlatform,
        strategy: strategy?.strategy || 'balanced'
      });

      if (isVideo) {
        return this.selectVideoFormat(
          contentAnalysis as VideoContentAnalysis,
          targetPlatform,
          browserSupport,
          strategy
        );
      } else {
        return this.selectImageFormat(
          contentAnalysis as ContentAnalysisResult,
          targetPlatform,
          browserSupport,
          strategy
        );
      }

    } catch (error) {
      console.error('Format selection error:', error);
      return this.getFallbackRecommendation(isVideo);
    }
  }

  /**
   * Select optimal image format
   */
  private selectImageFormat(
    analysis: ContentAnalysisResult,
    targetPlatform?: string,
    browserSupport?: FormatSupport,
    strategy?: OptimizationStrategy
  ): FormatRecommendation {
    const reasoning: string[] = [];
    let primaryFormat = 'jpeg';
    let quality = analysis.recommendedQuality;
    const fallbackFormats: string[] = [];

    // Get platform requirements
    const platformReqs = targetPlatform ? this.platformRequirements.get(targetPlatform) : null;

    // Content-based format selection
    if (analysis.hasTransparency) {
      if (browserSupport?.webp) {
        primaryFormat = 'webp';
        reasoning.push('WebP selected for transparency support with better compression');
      } else {
        primaryFormat = 'png';
        reasoning.push('PNG selected for transparency support (WebP not supported)');
      }
      fallbackFormats.push('png');
    } else if (analysis.contentType === 'photo') {
      // Photo content optimization
      if (browserSupport?.avif && strategy?.strategy !== 'fast') {
        primaryFormat = 'avif';
        quality = Math.max(quality - 5, 70); // AVIF can use lower quality
        reasoning.push('AVIF selected for photo content - superior compression');
        fallbackFormats.push('webp', 'jpeg');
      } else if (browserSupport?.webp) {
        primaryFormat = 'webp';
        reasoning.push('WebP selected for photo content - good compression and support');
        fallbackFormats.push('jpeg');
      } else {
        primaryFormat = 'jpeg';
        reasoning.push('JPEG selected for photo content - universal support');
      }
    } else if (analysis.contentType === 'graphics' || analysis.contentType === 'text') {
      // Graphics and text optimization
      if (analysis.colorProfile === 'limited' && browserSupport?.webp) {
        primaryFormat = 'webp';
        quality = Math.min(quality + 5, 95); // Higher quality for graphics
        reasoning.push('WebP selected for graphics - excellent for limited color palettes');
        fallbackFormats.push('png');
      } else {
        primaryFormat = 'png';
        reasoning.push('PNG selected for graphics/text - lossless compression');
      }
    } else {
      // Mixed content
      if (browserSupport?.webp) {
        primaryFormat = 'webp';
        reasoning.push('WebP selected for mixed content - versatile format');
        fallbackFormats.push('jpeg');
      } else {
        primaryFormat = 'jpeg';
        reasoning.push('JPEG selected for mixed content - universal support');
      }
    }

    // Apply platform constraints
    if (platformReqs) {
      if (!platformReqs.supportedFormats.includes(primaryFormat)) {
        const supportedFormat = this.findBestSupportedFormat(
          platformReqs.supportedFormats,
          analysis
        );
        reasoning.push(`Platform constraint: switched from ${primaryFormat} to ${supportedFormat}`);
        primaryFormat = supportedFormat;
      }

      // Adjust quality based on platform requirements
      if (platformReqs.qualityThreshold) {
        quality = Math.max(quality, platformReqs.qualityThreshold);
        reasoning.push(`Quality adjusted to meet platform threshold: ${platformReqs.qualityThreshold}`);
      }
    }

    // Apply optimization strategy
    if (strategy) {
      const strategyAdjustment = this.applyOptimizationStrategy(
        strategy,
        primaryFormat,
        quality,
        analysis
      );
      primaryFormat = strategyAdjustment.format;
      quality = strategyAdjustment.quality;
      reasoning.push(...strategyAdjustment.reasoning);
    }

    // Calculate scores
    const compatibilityScore = this.calculateCompatibilityScore(primaryFormat, browserSupport);
    const performanceScore = this.calculatePerformanceScore(primaryFormat, analysis);
    const expectedSizeReduction = this.estimateImageSizeReduction(primaryFormat, quality, analysis);

    return {
      primaryFormat,
      fallbackFormats,
      quality,
      reasoning,
      expectedSizeReduction,
      compatibilityScore,
      performanceScore,
      confidence: analysis.confidence
    };
  }

  /**
   * Select optimal video format
   */
  private selectVideoFormat(
    analysis: VideoContentAnalysis,
    targetPlatform?: string,
    browserSupport?: FormatSupport,
    strategy?: OptimizationStrategy
  ): FormatRecommendation {
    const reasoning: string[] = [];
    let primaryFormat = 'mp4';
    let quality = 80;
    const fallbackFormats: string[] = [];

    // Get platform requirements
    const platformReqs = targetPlatform ? this.platformRequirements.get(targetPlatform) : null;

    // Content-based codec selection
    if (analysis.motionComplexity === 'high' || analysis.sceneChanges === 'frequent') {
      if (browserSupport?.av1 && strategy?.strategy !== 'fast') {
        primaryFormat = 'webm'; // AV1 in WebM container
        reasoning.push('AV1 codec selected for high motion complexity - superior compression');
        fallbackFormats.push('mp4');
      } else if (browserSupport?.vp9) {
        primaryFormat = 'webm'; // VP9 in WebM container
        reasoning.push('VP9 codec selected for high motion complexity - good compression');
        fallbackFormats.push('mp4');
      } else {
        primaryFormat = 'mp4'; // H.264 in MP4 container
        reasoning.push('H.264 codec selected - universal support for complex content');
      }
    } else {
      // Lower complexity content
      if (browserSupport?.h265 && strategy?.strategy === 'quality') {
        primaryFormat = 'mp4'; // H.265 in MP4 container
        reasoning.push('H.265 codec selected for efficient compression of simple content');
      } else {
        primaryFormat = 'mp4'; // H.264 in MP4 container
        reasoning.push('H.264 codec selected - optimal balance for simple content');
      }
    }

    // Apply platform constraints
    if (platformReqs) {
      if (!platformReqs.supportedFormats.includes(primaryFormat)) {
        const supportedFormat = platformReqs.supportedFormats[0] || 'mp4';
        reasoning.push(`Platform constraint: switched from ${primaryFormat} to ${supportedFormat}`);
        primaryFormat = supportedFormat;
      }
    }

    // Calculate scores
    const compatibilityScore = this.calculateVideoCompatibilityScore(primaryFormat, browserSupport);
    const performanceScore = this.calculateVideoPerformanceScore(primaryFormat, analysis);
    const expectedSizeReduction = this.estimateVideoSizeReduction(primaryFormat, analysis);

    return {
      primaryFormat,
      fallbackFormats,
      quality,
      reasoning,
      expectedSizeReduction,
      compatibilityScore,
      performanceScore,
      confidence: analysis.confidence
    };
  }

  /**
   * Initialize platform requirements
   */
  private initializePlatformRequirements(): void {
    // Instagram requirements
    this.platformRequirements.set('instagram', {
      platform: 'instagram',
      maxFileSize: 30 * 1024 * 1024, // 30MB
      preferredFormats: ['jpeg', 'mp4'],
      supportedFormats: ['jpeg', 'png', 'mp4', 'mov'],
      qualityThreshold: 80,
      compressionPriority: 'size'
    });

    // Facebook requirements
    this.platformRequirements.set('facebook', {
      platform: 'facebook',
      maxFileSize: 100 * 1024 * 1024, // 100MB
      preferredFormats: ['jpeg', 'webp', 'mp4'],
      supportedFormats: ['jpeg', 'png', 'webp', 'mp4', 'mov', 'avi'],
      qualityThreshold: 75,
      compressionPriority: 'quality'
    });

    // Twitter requirements
    this.platformRequirements.set('twitter', {
      platform: 'twitter',
      maxFileSize: 5 * 1024 * 1024, // 5MB for images, 512MB for videos
      preferredFormats: ['jpeg', 'webp', 'mp4'],
      supportedFormats: ['jpeg', 'png', 'webp', 'gif', 'mp4', 'mov'],
      qualityThreshold: 85,
      compressionPriority: 'size'
    });

    // LinkedIn requirements
    this.platformRequirements.set('linkedin', {
      platform: 'linkedin',
      maxFileSize: 20 * 1024 * 1024, // 20MB
      preferredFormats: ['jpeg', 'png', 'mp4'],
      supportedFormats: ['jpeg', 'png', 'gif', 'mp4', 'mov', 'wmv'],
      qualityThreshold: 85,
      compressionPriority: 'quality'
    });

    // Web general requirements
    this.platformRequirements.set('web', {
      platform: 'web',
      maxFileSize: 50 * 1024 * 1024, // 50MB
      preferredFormats: ['webp', 'avif', 'mp4'],
      supportedFormats: ['jpeg', 'png', 'webp', 'avif', 'mp4', 'webm'],
      qualityThreshold: 80,
      compressionPriority: 'balanced' as any
    });
  }

  /**
   * Initialize format capabilities
   */
  private initializeFormatCapabilities(): void {
    this.formatCapabilities.set('jpeg', {
      compression: 'lossy',
      transparency: false,
      animation: false,
      maxQuality: 100,
      browserSupport: 100,
      compressionEfficiency: 70
    });

    this.formatCapabilities.set('png', {
      compression: 'lossless',
      transparency: true,
      animation: false,
      maxQuality: 100,
      browserSupport: 100,
      compressionEfficiency: 50
    });

    this.formatCapabilities.set('webp', {
      compression: 'both',
      transparency: true,
      animation: true,
      maxQuality: 100,
      browserSupport: 95,
      compressionEfficiency: 85
    });

    this.formatCapabilities.set('avif', {
      compression: 'both',
      transparency: true,
      animation: true,
      maxQuality: 100,
      browserSupport: 75,
      compressionEfficiency: 95
    });
  }

  /**
   * Find best supported format for platform
   */
  private findBestSupportedFormat(
    supportedFormats: string[],
    analysis: ContentAnalysisResult
  ): string {
    // Priority order based on content type
    const priorities = {
      photo: ['webp', 'jpeg', 'png'],
      graphics: ['png', 'webp', 'jpeg'],
      text: ['png', 'webp', 'jpeg'],
      mixed: ['webp', 'jpeg', 'png'],
      animation: ['webp', 'gif', 'png']
    };

    const priority = priorities[analysis.contentType] || priorities.mixed;

    for (const format of priority) {
      if (supportedFormats.includes(format)) {
        return format;
      }
    }

    return supportedFormats[0] || 'jpeg';
  }

  /**
   * Apply optimization strategy adjustments
   */
  private applyOptimizationStrategy(
    strategy: OptimizationStrategy,
    format: string,
    quality: number,
    analysis: ContentAnalysisResult
  ): { format: string; quality: number; reasoning: string[] } {
    const reasoning: string[] = [];
    let adjustedFormat = format;
    let adjustedQuality = quality;

    switch (strategy.strategy) {
      case 'aggressive':
        adjustedQuality = Math.max(quality - 15, 60);
        reasoning.push('Aggressive compression: reduced quality for maximum size reduction');
        break;

      case 'conservative':
        adjustedQuality = Math.min(quality + 10, 95);
        reasoning.push('Conservative compression: increased quality to preserve detail');
        break;

      case 'lossless':
        if (format === 'jpeg') {
          adjustedFormat = 'png';
          reasoning.push('Lossless strategy: switched to PNG for lossless compression');
        }
        adjustedQuality = 100;
        reasoning.push('Lossless compression applied');
        break;

      case 'balanced':
      default:
        reasoning.push('Balanced compression strategy applied');
        break;
    }

    return { format: adjustedFormat, quality: adjustedQuality, reasoning };
  }

  /**
   * Calculate compatibility score
   */
  private calculateCompatibilityScore(format: string, browserSupport?: FormatSupport): number {
    const capabilities = this.formatCapabilities.get(format);
    if (!capabilities) return 50;

    let score = capabilities.browserSupport;

    // Adjust based on actual browser support
    if (browserSupport) {
      if (format === 'webp' && !browserSupport.webp) score -= 30;
      if (format === 'avif' && !browserSupport.avif) score -= 40;
    }

    return Math.max(score, 0);
  }

  /**
   * Calculate performance score
   */
  private calculatePerformanceScore(format: string, analysis: ContentAnalysisResult): number {
    const capabilities = this.formatCapabilities.get(format);
    if (!capabilities) return 50;

    let score = capabilities.compressionEfficiency;

    // Adjust based on content type
    if (analysis.contentType === 'photo' && format === 'webp') score += 10;
    if (analysis.contentType === 'graphics' && format === 'png') score += 10;
    if (analysis.hasTransparency && !capabilities.transparency) score -= 20;

    return Math.min(score, 100);
  }

  /**
   * Calculate video compatibility score
   */
  private calculateVideoCompatibilityScore(format: string, browserSupport?: FormatSupport): number {
    let score = 80; // Base score

    if (format === 'mp4') score = 95; // Universal support
    if (format === 'webm' && browserSupport?.vp9) score = 85;
    if (format === 'webm' && !browserSupport?.vp9) score = 60;

    return score;
  }

  /**
   * Calculate video performance score
   */
  private calculateVideoPerformanceScore(format: string, analysis: VideoContentAnalysis): number {
    let score = 70; // Base score

    if (format === 'webm' && analysis.motionComplexity === 'high') score += 20;
    if (format === 'mp4' && analysis.motionComplexity === 'low') score += 15;

    return Math.min(score, 100);
  }

  /**
   * Estimate image size reduction
   */
  private estimateImageSizeReduction(
    format: string,
    quality: number,
    analysis: ContentAnalysisResult
  ): number {
    let reduction = 20; // Base reduction

    // Format-based adjustments
    if (format === 'webp') reduction += 25;
    if (format === 'avif') reduction += 40;
    if (format === 'png' && analysis.contentType === 'photo') reduction -= 10;

    // Quality-based adjustments
    if (quality < 70) reduction += 20;
    if (quality > 90) reduction -= 10;

    // Content-based adjustments
    if (analysis.complexity === 'low') reduction += 10;
    if (analysis.contentType === 'graphics') reduction += 15;

    return Math.min(Math.max(reduction, 5), 70);
  }

  /**
   * Estimate video size reduction
   */
  private estimateVideoSizeReduction(format: string, analysis: VideoContentAnalysis): number {
    let reduction = 30; // Base reduction

    if (format === 'webm') reduction += 20;
    if (analysis.motionComplexity === 'low') reduction += 15;
    if (analysis.sceneChanges === 'few') reduction += 10;

    return Math.min(Math.max(reduction, 10), 60);
  }

  /**
   * Get fallback recommendation
   */
  private getFallbackRecommendation(isVideo: boolean): FormatRecommendation {
    return {
      primaryFormat: isVideo ? 'mp4' : 'webp',
      fallbackFormats: isVideo ? ['mp4'] : ['jpeg'],
      quality: 80,
      reasoning: ['Fallback recommendation applied'],
      expectedSizeReduction: 25,
      compatibilityScore: 85,
      performanceScore: 75,
      confidence: 0.5
    };
  }

  /**
   * Get platform requirements
   */
  getPlatformRequirements(platform: string): PlatformRequirements | undefined {
    return this.platformRequirements.get(platform);
  }

  /**
   * Get supported platforms
   */
  getSupportedPlatforms(): string[] {
    return Array.from(this.platformRequirements.keys());
  }
}

export default SmartFormatSelector;
