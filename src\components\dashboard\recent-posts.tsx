import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { arSA } from "date-fns/locale";

interface Post {
  id: string;
  content: string;
  status: "DRAFT" | "SCHEDULED" | "PUBLISHED" | "FAILED";
  scheduledAt?: Date;
  publishedAt?: Date;
  createdAt: Date;
}

interface RecentPostsProps {
  posts: Post[];
}

export function RecentPosts({ posts }: RecentPostsProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "bg-gray-500";
      case "SCHEDULED":
        return "bg-blue-500";
      case "PUBLISHED":
        return "bg-green-500";
      case "FAILED":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "مسودة";
      case "SCHEDULED":
        return "مجدول";
      case "PUBLISHED":
        return "منشور";
      case "FAILED":
        return "فشل";
      default:
        return status;
    }
  };

  const formatDate = (date?: Date) => {
    if (!date) return "";
    return formatDistanceToNow(date, { addSuffix: true, locale: arSA });
  };

  return (
    <Card className="col-span-3 border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-white to-gray-50/50">
      <CardHeader className="pb-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-lg">
        <div className="flex items-center gap-3">
          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-sm"></div>
          <CardTitle className="text-xl font-bold text-gray-800">آخر المنشورات</CardTitle>
        </div>
        <CardDescription className="text-gray-600 font-medium">آخر 5 منشورات تم إنشاؤها</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {posts.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-muted-foreground">لا توجد منشورات حتى الآن</p>
            </div>
          ) : (
            posts.map((post, index) => (
              <div
                key={post.id}
                className="group p-4 rounded-xl border border-gray-100 hover:border-blue-300 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/30 transition-all duration-300 cursor-pointer hover:shadow-md"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-3">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white text-sm font-bold shadow-sm">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-semibold leading-relaxed text-gray-900 group-hover:text-blue-900 mb-2">
                          {post.content.length > 80
                            ? post.content.substring(0, 80) + "..."
                            : post.content}
                        </p>
                        <div className="flex items-center gap-2">
                          <p className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                            {post.status === "SCHEDULED"
                              ? `🕒 مجدول ${formatDate(post.scheduledAt)}`
                              : post.status === "PUBLISHED"
                              ? `✅ نُشر ${formatDate(post.publishedAt)}`
                              : `📝 أُنشئ ${formatDate(post.createdAt)}`}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={`inline-flex items-center rounded-full px-4 py-2 text-xs font-semibold text-white shadow-lg ${getStatusColor(post.status)}`}>
                    {getStatusLabel(post.status)}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
