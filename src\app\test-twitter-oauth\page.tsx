"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ExternalLink, Twitter } from "lucide-react";

export default function TestTwitterOAuthPage() {
  const [oauthUrl, setOauthUrl] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [oauthData, setOauthData] = useState<any>(null);

  const generateTwitterOAuthUrl = async () => {
    setIsLoading(true);
    setError("");
    setOauthUrl("");
    setOauthData(null);

    try {
      const response = await fetch('/api/test/twitter-oauth-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setOauthUrl(data.authUrl);
        setOauthData(data);
      } else {
        setError(data.error || 'Failed to generate OAuth URL');
      }
    } catch (err: any) {
      setError(err.message || 'Network error');
    } finally {
      setIsLoading(false);
    }
  };

  const testTwitterOAuth = () => {
    if (oauthUrl) {
      window.open(oauthUrl, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-400 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <Twitter className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
            Twitter OAuth 2.0 PKCE Test
          </h1>
          <p className="text-gray-600 text-lg">Test Twitter OAuth 2.0 flow with PKCE implementation</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <Card>
            <CardHeader>
              <CardTitle>🐦 Twitter OAuth Test</CardTitle>
              <CardDescription>Generate and test Twitter OAuth 2.0 URL with PKCE</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={generateTwitterOAuthUrl} 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Generating..." : "🚀 Generate Twitter OAuth URL"}
              </Button>

              {oauthUrl && (
                <Button 
                  onClick={testTwitterOAuth} 
                  variant="outline"
                  className="w-full"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Test OAuth Flow
                </Button>
              )}

              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-2">❌ Error</h4>
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Results */}
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>OAuth URL generation and PKCE parameters</CardDescription>
            </CardHeader>
            <CardContent>
              {oauthData ? (
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-2">✅ OAuth URL Generated</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <strong>State:</strong> 
                        <code className="ml-2 px-2 py-1 bg-gray-100 rounded">{oauthData.state}</code>
                      </div>
                      <div>
                        <strong>Code Verifier:</strong> 
                        <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-xs">
                          {oauthData.codeVerifier?.substring(0, 20)}...
                        </code>
                      </div>
                      <div>
                        <strong>Code Challenge:</strong> 
                        <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-xs">
                          {oauthData.codeChallenge?.substring(0, 20)}...
                        </code>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">🔗 OAuth URL</h4>
                    <div className="text-xs break-all bg-white p-2 rounded border">
                      {oauthUrl}
                    </div>
                  </div>

                  <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                    <h4 className="font-medium text-purple-800 mb-2">📋 PKCE Parameters</h4>
                    <div className="text-sm space-y-1">
                      <div>✅ <strong>Code Challenge Method:</strong> S256</div>
                      <div>✅ <strong>Response Type:</strong> code</div>
                      <div>✅ <strong>Scopes:</strong> tweet.read tweet.write users.read media.upload offline.access</div>
                      <div>✅ <strong>Redirect URI:</strong> {oauthData.redirectUri}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Twitter className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Generate OAuth URL to see results</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">📋 Testing Instructions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">✅ What This Tests:</h4>
              <ul className="space-y-1">
                <li>• PKCE code verifier generation</li>
                <li>• PKCE code challenge creation (SHA256)</li>
                <li>• OAuth 2.0 URL formatting</li>
                <li>• Twitter API v2 compatibility</li>
                <li>• Callback URL configuration</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔧 Expected Results:</h4>
              <ul className="space-y-1">
                <li>• OAuth URL should be generated successfully</li>
                <li>• PKCE parameters should be present</li>
                <li>• Twitter login page should load</li>
                <li>• Callback should handle PKCE verification</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
