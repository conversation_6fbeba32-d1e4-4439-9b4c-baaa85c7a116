import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for workflow step
const workflowStepSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  approvers: z.array(z.string().uuid()).default([]),
  required_approvals: z.number().min(1).default(1),
  auto_approve_conditions: z.record(z.any()).default({}),
  escalation_hours: z.number().optional(),
  escalation_to: z.array(z.string().uuid()).optional(),
});

// Validation schema for workflow template creation
const createWorkflowTemplateSchema = z.object({
  name: z.string().min(2).max(100),
  description: z.string().optional(),
  is_default: z.boolean().default(false),
  is_active: z.boolean().default(true),
  steps: z.array(workflowStepSchema).min(1),
  settings: z.object({
    require_all_approvers: z.boolean().default(false),
    allow_parallel_approval: z.boolean().default(true),
    auto_approve_author: z.boolean().default(false),
    deadline_hours: z.number().min(1).max(168).default(24), // Max 1 week
    reminder_hours: z.array(z.number()).default([12, 2]),
    allow_comments: z.boolean().default(true),
    allow_edits_during_approval: z.boolean().default(false),
  }).default({}),
});

// Validation schema for workflow template updates
const updateWorkflowTemplateSchema = createWorkflowTemplateSchema.partial();

// GET - Get workflow templates for workspace
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching workflow templates...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get workspace ID from query params
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspace_id');

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    // Check if user has access to this workspace
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember) {
      return NextResponse.json(
        { error: 'Access denied to workspace' },
        { status: 403 }
      );
    }

    // Get workflow templates
    const { data: templates, error: templatesError } = await supabase
      .from('workflow_templates')
      .select(`
        id,
        name,
        description,
        is_default,
        is_active,
        steps,
        settings,
        usage_count,
        last_used_at,
        created_at,
        updated_at,
        created_by
      `)
      .eq('workspace_id', workspaceId)
      .order('is_default', { ascending: false })
      .order('usage_count', { ascending: false })
      .order('created_at', { ascending: false });

    if (templatesError) {
      console.error('Error fetching workflow templates:', templatesError);
      return NextResponse.json(
        { error: 'Failed to fetch workflow templates' },
        { status: 500 }
      );
    }

    // Get creator information for each template
    const creatorIds = templates?.map(t => t.created_by).filter(Boolean) || [];
    const { data: creators } = await supabase.auth.admin.listUsers();
    
    const creatorProfiles = creators?.users?.reduce((acc, profile) => {
      acc[profile.id] = {
        email: profile.email,
        user_metadata: profile.user_metadata
      };
      return acc;
    }, {} as Record<string, any>) || {};

    // Enhance templates with creator info
    const templatesWithCreators = templates?.map(template => ({
      ...template,
      creator: creatorProfiles[template.created_by] || null
    })) || [];

    console.log(`Found ${templatesWithCreators.length} workflow templates for workspace ${workspaceId}`);

    return NextResponse.json({
      success: true,
      templates: templatesWithCreators,
      user_role: userMember.role
    }, { status: 200 });

  } catch (error) {
    console.error('Workflow templates fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new workflow template
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Creating new workflow template...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createWorkflowTemplateSchema.parse(body);

    // Get workspace ID from body
    const workspaceId = body.workspace_id;
    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    console.log('Creating workflow template:', validatedData);

    // Check if user has permission to create workflow templates
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember || !['OWNER', 'ADMIN', 'MANAGER'].includes(userMember.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create workflow templates' },
        { status: 403 }
      );
    }

    // If setting as default, unset other defaults
    if (validatedData.is_default) {
      await supabase
        .from('workflow_templates')
        .update({ is_default: false })
        .eq('workspace_id', workspaceId);
    }

    // Create workflow template
    const { data: template, error: templateError } = await supabase
      .from('workflow_templates')
      .insert({
        workspace_id: workspaceId,
        name: validatedData.name,
        description: validatedData.description,
        is_default: validatedData.is_default,
        is_active: validatedData.is_active,
        steps: validatedData.steps,
        settings: validatedData.settings,
        created_by: user.id
      })
      .select()
      .single();

    if (templateError) {
      console.error('Error creating workflow template:', templateError);
      return NextResponse.json(
        { error: 'Failed to create workflow template' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: workspaceId,
        action: 'WORKFLOW_TEMPLATE_CREATED',
        details: `Created workflow template "${template.name}"`,
        created_at: new Date().toISOString(),
      });

    console.log('Workflow template created successfully:', template.id);

    return NextResponse.json({
      success: true,
      template,
      message: 'Workflow template created successfully'
    }, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid workflow template data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Workflow template creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update workflow template
export async function PUT(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Updating workflow template...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get template ID from query params
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateWorkflowTemplateSchema.parse(body);

    console.log('Updating workflow template:', templateId, validatedData);

    // Get existing template to check permissions
    const { data: existingTemplate } = await supabase
      .from('workflow_templates')
      .select('workspace_id')
      .eq('id', templateId)
      .single();

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to update workflow templates
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', existingTemplate.workspace_id)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember || !['OWNER', 'ADMIN', 'MANAGER'].includes(userMember.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update workflow templates' },
        { status: 403 }
      );
    }

    // If setting as default, unset other defaults
    if (validatedData.is_default) {
      await supabase
        .from('workflow_templates')
        .update({ is_default: false })
        .eq('workspace_id', existingTemplate.workspace_id);
    }

    // Update workflow template
    const { data: template, error: updateError } = await supabase
      .from('workflow_templates')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating workflow template:', updateError);
      return NextResponse.json(
        { error: 'Failed to update workflow template' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: existingTemplate.workspace_id,
        action: 'WORKFLOW_TEMPLATE_UPDATED',
        details: `Updated workflow template "${template.name}"`,
        created_at: new Date().toISOString(),
      });

    console.log('Workflow template updated successfully:', templateId);

    return NextResponse.json({
      success: true,
      template,
      message: 'Workflow template updated successfully'
    }, { status: 200 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid workflow template data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Workflow template update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete workflow template
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Deleting workflow template...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get template ID from query params
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Get existing template to check permissions
    const { data: existingTemplate } = await supabase
      .from('workflow_templates')
      .select('workspace_id, name, is_default')
      .eq('id', templateId)
      .single();

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Workflow template not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to delete workflow templates
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', existingTemplate.workspace_id)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember || !['OWNER', 'ADMIN', 'MANAGER'].includes(userMember.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete workflow templates' },
        { status: 403 }
      );
    }

    // Check if template is being used in active approval requests
    const { count: activeRequests } = await supabase
      .from('content_approval_requests')
      .select('*', { count: 'exact', head: true })
      .eq('workflow_template_id', templateId)
      .in('status', ['pending', 'in_review']);

    if ((activeRequests || 0) > 0) {
      return NextResponse.json(
        { error: 'Cannot delete workflow template with active approval requests' },
        { status: 400 }
      );
    }

    // Delete workflow template
    const { error: deleteError } = await supabase
      .from('workflow_templates')
      .delete()
      .eq('id', templateId);

    if (deleteError) {
      console.error('Error deleting workflow template:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete workflow template' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: existingTemplate.workspace_id,
        action: 'WORKFLOW_TEMPLATE_DELETED',
        details: `Deleted workflow template "${existingTemplate.name}"`,
        created_at: new Date().toISOString(),
      });

    console.log('Workflow template deleted successfully:', templateId);

    return NextResponse.json({
      success: true,
      message: 'Workflow template deleted successfully'
    }, { status: 200 });

  } catch (error) {
    console.error('Workflow template deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
