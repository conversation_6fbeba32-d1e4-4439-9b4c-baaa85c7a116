import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { contentTemplateManager } from '@/lib/templates/content-templates';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const searchTemplatesSchema = z.object({
  category: z.string().optional(),
  platform: z.string().optional(),
  language: z.enum(['ar', 'en']).optional(),
  industry: z.string().optional(),
  tone: z.string().optional(),
  tags: z.array(z.string()).optional(),
  query: z.string().optional(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
});

const processTemplateSchema = z.object({
  templateId: z.string().min(1, 'Template ID is required'),
  variables: z.record(z.string()),
});

const createTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().min(1, 'Template description is required'),
  category: z.string().min(1, 'Category is required'),
  platform: z.array(z.string()).min(1, 'At least one platform is required'),
  language: z.enum(['ar', 'en', 'both']),
  content: z.string().min(1, 'Template content is required'),
  variables: z.array(z.object({
    name: z.string(),
    type: z.string(),
    label: z.string(),
    placeholder: z.string(),
    required: z.boolean(),
    options: z.array(z.string()).optional(),
    defaultValue: z.string().optional(),
    validation: z.object({
      minLength: z.number().optional(),
      maxLength: z.number().optional(),
      pattern: z.string().optional(),
    }).optional(),
  })),
  hashtags: z.array(z.string()),
  tone: z.string(),
  industry: z.array(z.string()),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()),
});

/**
 * Get templates
 * GET /api/content/templates
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'search':
        return await handleSearchTemplates(searchParams);
      case 'popular':
        return await handleGetPopularTemplates(searchParams);
      case 'categories':
        return await handleGetCategories();
      case 'my-templates':
        return await handleGetUserTemplates(user.id);
      default:
        return await handleSearchTemplates(searchParams);
    }

  } catch (error) {
    console.error('Templates fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function handleSearchTemplates(searchParams: URLSearchParams) {
  const filters = {
    category: searchParams.get('category') as any || undefined,
    platform: searchParams.get('platform') as any || undefined,
    language: searchParams.get('language') as 'ar' | 'en' || undefined,
    industry: searchParams.get('industry') as any || undefined,
    tone: searchParams.get('tone') as any || undefined,
    query: searchParams.get('query') || undefined,
    tags: searchParams.get('tags')?.split(',') || undefined,
  };

  const limit = parseInt(searchParams.get('limit') || '20');
  const offset = parseInt(searchParams.get('offset') || '0');

  const allTemplates = contentTemplateManager.searchTemplates(filters);
  const templates = allTemplates.slice(offset, offset + limit);

  return NextResponse.json({
    success: true,
    templates,
    total: allTemplates.length,
    hasMore: offset + limit < allTemplates.length,
  });
}

async function handleGetPopularTemplates(searchParams: URLSearchParams) {
  const limit = parseInt(searchParams.get('limit') || '10');
  const templates = contentTemplateManager.getPopularTemplates(limit);

  return NextResponse.json({
    success: true,
    templates,
  });
}

async function handleGetCategories() {
  const categories = [
    { id: 'promotional', name: 'ترويجي', description: 'قوالب للترويج والإعلانات' },
    { id: 'educational', name: 'تعليمي', description: 'قوالب للمحتوى التعليمي والنصائح' },
    { id: 'entertainment', name: 'ترفيهي', description: 'قوالب للمحتوى الترفيهي' },
    { id: 'news', name: 'إخباري', description: 'قوالب للأخبار والإعلانات' },
    { id: 'personal', name: 'شخصي', description: 'قوالب للمحتوى الشخصي' },
    { id: 'business', name: 'تجاري', description: 'قوالب للأعمال والشركات' },
    { id: 'event', name: 'فعاليات', description: 'قوالب للفعاليات والمؤتمرات' },
    { id: 'product', name: 'منتجات', description: 'قوالب لعرض المنتجات' },
    { id: 'service', name: 'خدمات', description: 'قوالب لعرض الخدمات' },
    { id: 'announcement', name: 'إعلانات', description: 'قوالب للإعلانات المهمة' },
  ];

  return NextResponse.json({
    success: true,
    categories,
  });
}

async function handleGetUserTemplates(userId: string) {
  // In a real implementation, this would fetch from database
  // For now, return empty array as we're using default templates
  return NextResponse.json({
    success: true,
    templates: [],
  });
}

/**
 * Process template with variables
 * POST /api/content/templates
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'process':
        return await handleProcessTemplate(body, user);
      case 'create':
        return await handleCreateTemplate(body, user);
      case 'validate':
        return await handleValidateTemplate(body);
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Template processing error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function handleProcessTemplate(body: any, user: any) {
  const validation = processTemplateSchema.safeParse(body);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { templateId, variables } = validation.data;

  // Get template
  const template = contentTemplateManager.getTemplate(templateId);
  if (!template) {
    return NextResponse.json({ error: 'Template not found' }, { status: 404 });
  }

  // Validate variables
  const validationResult = contentTemplateManager.validateTemplateVariables(template, variables);
  if (!validationResult.isValid) {
    return NextResponse.json(
      { error: 'Invalid variables', details: validationResult.errors },
      { status: 400 }
    );
  }

  // Process template
  const processedContent = contentTemplateManager.processTemplate(template, variables);

  // Log template usage
  await logTemplateUsage(user.id, templateId);

  return NextResponse.json({
    success: true,
    content: processedContent,
    hashtags: template.hashtags,
    template: {
      id: template.id,
      name: template.name,
      category: template.category,
      platform: template.platform,
    },
  });
}

async function handleCreateTemplate(body: any, user: any) {
  const validation = createTemplateSchema.safeParse(body);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  // In a real implementation, this would save to database
  // For now, return success response
  return NextResponse.json({
    success: true,
    message: 'Template created successfully',
    templateId: `custom-${Date.now()}`,
  });
}

async function handleValidateTemplate(body: any) {
  const { templateId, variables } = body;

  const template = contentTemplateManager.getTemplate(templateId);
  if (!template) {
    return NextResponse.json({ error: 'Template not found' }, { status: 404 });
  }

  const validationResult = contentTemplateManager.validateTemplateVariables(template, variables);

  return NextResponse.json({
    success: true,
    validation: validationResult,
  });
}

async function logTemplateUsage(userId: string, templateId: string) {
  try {
    

    await supabase
      .from('template_usage_logs')
      .insert({
        user_id: userId,
        template_id: templateId,
        timestamp: new Date().toISOString(),
      });

  } catch (error) {
    console.error('Failed to log template usage:', error);
    // Don't throw error as this is not critical
  }
}
