const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAzMzgwNiwiZXhwIjoyMDYzNjA5ODA2fQ.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createBasicTables() {
  console.log('🚀 Creating basic tables for eWasl...');
  
  try {
    // Create User table
    console.log('📝 Creating User table...');
    const userTableSQL = `
      CREATE TABLE IF NOT EXISTS "User" (
        id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
        name TEXT,
        email TEXT UNIQUE NOT NULL,
        password TEXT,
        role TEXT DEFAULT 'USER' NOT NULL,
        "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        "updatedAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL
      );
    `;
    
    const { error: userError } = await supabase.rpc('exec_sql', { sql: userTableSQL });
    if (userError) console.error('User table error:', userError);
    else console.log('✅ User table created');
    
    // Test connection
    const { data, error } = await supabase.from('User').select('count').limit(1);
    if (error) {
      console.error('❌ Connection test failed:', error);
    } else {
      console.log('✅ Database connection successful!');
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  }
}

createBasicTables();