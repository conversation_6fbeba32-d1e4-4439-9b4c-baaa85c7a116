#!/usr/bin/env node

/**
 * Simple API Test - Basic connectivity test for DigitalOcean and Stripe
 */

const https = require('https');

// API Keys
const DO_TOKEN = '***********************************************************************';
const STRIPE_KEY = '***********************************************************************************************************';

/**
 * Test DigitalOcean API
 */
function testDigitalOcean() {
  return new Promise((resolve) => {
    console.log('🌊 Testing DigitalOcean API...');
    
    const options = {
      hostname: 'api.digitalocean.com',
      port: 443,
      path: '/v2/account',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${DO_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode === 200) {
            console.log('✅ DigitalOcean API: SUCCESS');
            console.log(`   Account Email: ${result.account?.email || 'N/A'}`);
            console.log(`   Account Status: ${result.account?.status || 'N/A'}`);
            console.log(`   Droplet Limit: ${result.account?.droplet_limit || 'N/A'}`);
            resolve({ success: true, data: result });
          } else {
            console.log('❌ DigitalOcean API: FAILED');
            console.log(`   Status: ${res.statusCode}`);
            console.log(`   Error: ${result.message || 'Unknown error'}`);
            resolve({ success: false, error: result });
          }
        } catch (e) {
          console.log('❌ DigitalOcean API: PARSE ERROR');
          console.log(`   Status: ${res.statusCode}`);
          console.log(`   Raw Response: ${data.substring(0, 200)}...`);
          resolve({ success: false, error: e.message });
        }
      });
    });

    req.on('error', (error) => {
      console.log('💥 DigitalOcean API: CONNECTION ERROR');
      console.log(`   Error: ${error.message}`);
      resolve({ success: false, error: error.message });
    });

    req.setTimeout(10000, () => {
      console.log('⏰ DigitalOcean API: TIMEOUT');
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });

    req.end();
  });
}

/**
 * Test Stripe API
 */
function testStripe() {
  return new Promise((resolve) => {
    console.log('\n💳 Testing Stripe API...');
    
    const auth = Buffer.from(`${STRIPE_KEY}:`).toString('base64');
    
    const options = {
      hostname: 'api.stripe.com',
      port: 443,
      path: '/v1/balance',
      method: 'GET',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Stripe-Version': '2023-10-16'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (res.statusCode === 200) {
            console.log('✅ Stripe API: SUCCESS');
            console.log(`   Available Balance:`);
            if (result.available) {
              result.available.forEach(bal => {
                console.log(`     ${bal.currency.toUpperCase()}: ${(bal.amount / 100).toFixed(2)}`);
              });
            }
            console.log(`   Pending Balance:`);
            if (result.pending) {
              result.pending.forEach(bal => {
                console.log(`     ${bal.currency.toUpperCase()}: ${(bal.amount / 100).toFixed(2)}`);
              });
            }
            resolve({ success: true, data: result });
          } else {
            console.log('❌ Stripe API: FAILED');
            console.log(`   Status: ${res.statusCode}`);
            console.log(`   Error: ${result.error?.message || 'Unknown error'}`);
            resolve({ success: false, error: result });
          }
        } catch (e) {
          console.log('❌ Stripe API: PARSE ERROR');
          console.log(`   Status: ${res.statusCode}`);
          console.log(`   Raw Response: ${data.substring(0, 200)}...`);
          resolve({ success: false, error: e.message });
        }
      });
    });

    req.on('error', (error) => {
      console.log('💥 Stripe API: CONNECTION ERROR');
      console.log(`   Error: ${error.message}`);
      resolve({ success: false, error: error.message });
    });

    req.setTimeout(10000, () => {
      console.log('⏰ Stripe API: TIMEOUT');
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });

    req.end();
  });
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Simple API Connectivity Test');
  console.log('================================');
  
  const doResult = await testDigitalOcean();
  const stripeResult = await testStripe();
  
  console.log('\n📋 SUMMARY');
  console.log('==========');
  console.log(`🌊 DigitalOcean: ${doResult.success ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`💳 Stripe: ${stripeResult.success ? '✅ WORKING' : '❌ FAILED'}`);
  
  if (doResult.success && stripeResult.success) {
    console.log('\n🎉 ALL APIS WORKING! Ready to proceed with integration.');
    return true;
  } else {
    console.log('\n⚠️  Some APIs failed. Check the errors above.');
    return false;
  }
}

// Run tests
if (require.main === module) {
  runTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 Fatal error:', error);
      process.exit(2);
    });
}

module.exports = { runTests, testDigitalOcean, testStripe };
