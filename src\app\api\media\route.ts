import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET - Fetch user's media files
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching user media...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'image' or 'video'
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Fetching media for user:', user.id);

    // Build query
    let query = supabase
      .from('media')
      .select(`
        id,
        file_name,
        file_type,
        file_size,
        public_url,
        created_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Add type filter if provided
    if (type) {
      if (type === 'image') {
        query = query.like('file_type', 'image/%');
      } else if (type === 'video') {
        query = query.like('file_type', 'video/%');
      }
    }

    const { data: media, error } = await query;

    if (error) {
      console.error('Error fetching media:', error);
      return NextResponse.json(
        { error: 'Failed to fetch media' },
        { status: 500 }
      );
    }

    console.log(`Fetched ${media?.length || 0} media files`);

    // Get total count for pagination
    let countQuery = supabase
      .from('media')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    if (type) {
      if (type === 'image') {
        countQuery = countQuery.like('file_type', 'image/%');
      } else if (type === 'video') {
        countQuery = countQuery.like('file_type', 'video/%');
      }
    }

    const { count } = await countQuery;

    return NextResponse.json({
      media: media || [],
      total: count || 0,
      hasMore: (offset + limit) < (count || 0)
    });

  } catch (error) {
    console.error('Media fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete media file
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Deleting media file...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get media ID from request body
    const { mediaId } = await request.json();
    
    if (!mediaId) {
      return NextResponse.json(
        { error: 'Media ID is required' },
        { status: 400 }
      );
    }

    console.log('Deleting media:', mediaId);

    // Get media record to verify ownership and get file path
    const { data: mediaRecord, error: fetchError } = await supabase
      .from('media')
      .select('id, file_path, file_name')
      .eq('id', mediaId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching media record:', fetchError);
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Media not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to fetch media record' },
        { status: 500 }
      );
    }

    // Delete file from storage
    const { error: storageError } = await supabase.storage
      .from('media')
      .remove([mediaRecord.file_path]);

    if (storageError) {
      console.error('Storage deletion error:', storageError);
      // Continue with database deletion even if storage fails
    }

    // Delete media record from database
    const { error: deleteError } = await supabase
      .from('media')
      .delete()
      .eq('id', mediaId)
      .eq('user_id', user.id);

    if (deleteError) {
      console.error('Database deletion error:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete media record' },
        { status: 500 }
      );
    }

    console.log('Media deleted successfully:', mediaId);

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'MEDIA_DELETED',
        details: `Deleted media file: ${mediaRecord.file_name}`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      message: 'Media deleted successfully'
    });

  } catch (error) {
    console.error('Media deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
