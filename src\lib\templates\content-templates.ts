// Content Templates System
export interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  platform: Platform[];
  language: 'ar' | 'en' | 'both';
  content: string;
  variables: TemplateVariable[];
  hashtags: string[];
  tone: ContentTone;
  industry: Industry[];
  isPublic: boolean;
  createdBy?: string;
  organizationId?: string;
  usageCount: number;
  rating: number;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'date' | 'url' | 'email' | 'select';
  label: string;
  placeholder: string;
  required: boolean;
  options?: string[]; // For select type
  defaultValue?: string;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

export type TemplateCategory =
  | 'promotional'
  | 'educational'
  | 'entertainment'
  | 'news'
  | 'personal'
  | 'business'
  | 'event'
  | 'product'
  | 'service'
  | 'announcement';

export type Platform = 'twitter' | 'facebook' | 'instagram' | 'linkedin' | 'snapchat';

export type ContentTone = 'professional' | 'casual' | 'friendly' | 'formal' | 'humorous' | 'inspiring';

export type Industry =
  | 'technology'
  | 'healthcare'
  | 'education'
  | 'finance'
  | 'retail'
  | 'food'
  | 'travel'
  | 'fashion'
  | 'sports'
  | 'entertainment'
  | 'real-estate'
  | 'automotive'
  | 'business';

export interface TemplateSearchFilters {
  category?: TemplateCategory;
  platform?: Platform;
  language?: 'ar' | 'en';
  industry?: Industry;
  tone?: ContentTone;
  isPublic?: boolean;
  tags?: string[];
  query?: string;
}

export class ContentTemplateManager {
  private templates: Map<string, ContentTemplate> = new Map();

  constructor() {
    this.initializeDefaultTemplates();
  }

  /**
   * Initialize default templates
   */
  private initializeDefaultTemplates(): void {
    const defaultTemplates: ContentTemplate[] = [
      // Promotional Templates
      {
        id: 'promo-product-launch-ar',
        name: 'إطلاق منتج جديد',
        description: 'قالب للإعلان عن إطلاق منتج جديد',
        category: 'promotional',
        platform: ['facebook', 'instagram', 'linkedin'],
        language: 'ar',
        content: '🎉 نحن متحمسون للإعلان عن إطلاق {{productName}}!\n\n{{productDescription}}\n\n✨ المميزات الرئيسية:\n{{features}}\n\n💰 السعر الخاص: {{price}}\n📅 متوفر من: {{launchDate}}\n\n{{callToAction}}',
        variables: [
          {
            name: 'productName',
            type: 'text',
            label: 'اسم المنتج',
            placeholder: 'أدخل اسم المنتج',
            required: true,
          },
          {
            name: 'productDescription',
            type: 'text',
            label: 'وصف المنتج',
            placeholder: 'وصف مختصر للمنتج',
            required: true,
            validation: { maxLength: 200 },
          },
          {
            name: 'features',
            type: 'text',
            label: 'المميزات',
            placeholder: 'اذكر المميزات الرئيسية',
            required: true,
          },
          {
            name: 'price',
            type: 'text',
            label: 'السعر',
            placeholder: '999 ريال',
            required: false,
          },
          {
            name: 'launchDate',
            type: 'date',
            label: 'تاريخ الإطلاق',
            placeholder: 'اختر التاريخ',
            required: true,
          },
          {
            name: 'callToAction',
            type: 'select',
            label: 'دعوة للعمل',
            placeholder: 'اختر دعوة للعمل',
            required: true,
            options: [
              'احصل عليه الآن!',
              'اطلب نسختك اليوم',
              'تسوق الآن',
              'اكتشف المزيد',
              'احجز مسبقاً'
            ],
          },
        ],
        hashtags: ['منتج_جديد', 'إطلاق', 'تسوق', 'عرض_خاص'],
        tone: 'friendly',
        industry: ['retail', 'technology'],
        isPublic: true,
        usageCount: 0,
        rating: 4.5,
        tags: ['product', 'launch', 'promotion'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Educational Templates
      {
        id: 'edu-tip-sharing-ar',
        name: 'مشاركة نصيحة تعليمية',
        description: 'قالب لمشاركة النصائح والمعلومات التعليمية',
        category: 'educational',
        platform: ['twitter', 'linkedin', 'facebook'],
        language: 'ar',
        content: '💡 نصيحة اليوم: {{tipTitle}}\n\n{{tipContent}}\n\n🔍 لماذا هذا مهم؟\n{{importance}}\n\n📝 كيفية التطبيق:\n{{implementation}}\n\n{{question}}',
        variables: [
          {
            name: 'tipTitle',
            type: 'text',
            label: 'عنوان النصيحة',
            placeholder: 'عنوان جذاب للنصيحة',
            required: true,
            validation: { maxLength: 100 },
          },
          {
            name: 'tipContent',
            type: 'text',
            label: 'محتوى النصيحة',
            placeholder: 'اشرح النصيحة بالتفصيل',
            required: true,
            validation: { maxLength: 300 },
          },
          {
            name: 'importance',
            type: 'text',
            label: 'أهمية النصيحة',
            placeholder: 'لماذا هذه النصيحة مهمة؟',
            required: true,
          },
          {
            name: 'implementation',
            type: 'text',
            label: 'كيفية التطبيق',
            placeholder: 'خطوات عملية للتطبيق',
            required: true,
          },
          {
            name: 'question',
            type: 'select',
            label: 'سؤال للتفاعل',
            placeholder: 'اختر سؤال',
            required: false,
            options: [
              'ما رأيكم في هذه النصيحة؟',
              'هل جربتم هذا من قبل؟',
              'شاركونا تجاربكم!',
              'أي نصائح أخرى تودون إضافتها؟'
            ],
          },
        ],
        hashtags: ['نصائح', 'تعلم', 'تطوير', 'معرفة'],
        tone: 'professional',
        industry: ['education', 'technology'],
        isPublic: true,
        usageCount: 0,
        rating: 4.8,
        tags: ['education', 'tips', 'learning'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Event Templates
      {
        id: 'event-announcement-ar',
        name: 'إعلان عن فعالية',
        description: 'قالب للإعلان عن الفعاليات والمؤتمرات',
        category: 'event',
        platform: ['facebook', 'linkedin', 'instagram'],
        language: 'ar',
        content: '📅 {{eventType}}: {{eventName}}\n\n{{eventDescription}}\n\n📍 المكان: {{location}}\n🗓️ التاريخ: {{date}}\n⏰ الوقت: {{time}}\n\n🎯 ما ستتعلمه:\n{{learningOutcomes}}\n\n{{registrationInfo}}\n\n{{eventHashtag}}',
        variables: [
          {
            name: 'eventType',
            type: 'select',
            label: 'نوع الفعالية',
            placeholder: 'اختر نوع الفعالية',
            required: true,
            options: ['مؤتمر', 'ورشة عمل', 'ندوة', 'دورة تدريبية', 'معرض', 'لقاء'],
          },
          {
            name: 'eventName',
            type: 'text',
            label: 'اسم الفعالية',
            placeholder: 'اسم الفعالية',
            required: true,
          },
          {
            name: 'eventDescription',
            type: 'text',
            label: 'وصف الفعالية',
            placeholder: 'وصف مختصر للفعالية',
            required: true,
            validation: { maxLength: 250 },
          },
          {
            name: 'location',
            type: 'text',
            label: 'المكان',
            placeholder: 'مكان انعقاد الفعالية',
            required: true,
          },
          {
            name: 'date',
            type: 'date',
            label: 'التاريخ',
            placeholder: 'تاريخ الفعالية',
            required: true,
          },
          {
            name: 'time',
            type: 'text',
            label: 'الوقت',
            placeholder: '10:00 صباحاً - 4:00 مساءً',
            required: true,
          },
          {
            name: 'learningOutcomes',
            type: 'text',
            label: 'مخرجات التعلم',
            placeholder: 'ما سيتعلمه المشاركون',
            required: true,
          },
          {
            name: 'registrationInfo',
            type: 'text',
            label: 'معلومات التسجيل',
            placeholder: 'كيفية التسجيل ورابط التسجيل',
            required: true,
          },
          {
            name: 'eventHashtag',
            type: 'text',
            label: 'هاشتاق الفعالية',
            placeholder: '#اسم_الفعالية',
            required: false,
          },
        ],
        hashtags: ['فعالية', 'مؤتمر', 'تدريب', 'تعلم'],
        tone: 'professional',
        industry: ['education', 'business'],
        isPublic: true,
        usageCount: 0,
        rating: 4.6,
        tags: ['event', 'conference', 'training'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Business Templates
      {
        id: 'business-milestone-ar',
        name: 'إنجاز تجاري',
        description: 'قالب للاحتفال بالإنجازات التجارية',
        category: 'business',
        platform: ['linkedin', 'facebook', 'twitter'],
        language: 'ar',
        content: '🎉 نحن فخورون بالإعلان عن {{milestone}}!\n\n{{achievementDetails}}\n\n📈 الأرقام:\n{{statistics}}\n\n🙏 شكر خاص لـ:\n{{acknowledgments}}\n\n{{futureGoals}}\n\n#{{companyHashtag}}',
        variables: [
          {
            name: 'milestone',
            type: 'text',
            label: 'الإنجاز',
            placeholder: 'وصولنا لـ 10,000 عميل',
            required: true,
          },
          {
            name: 'achievementDetails',
            type: 'text',
            label: 'تفاصيل الإنجاز',
            placeholder: 'تفاصيل حول الإنجاز المحقق',
            required: true,
            validation: { maxLength: 200 },
          },
          {
            name: 'statistics',
            type: 'text',
            label: 'الإحصائيات',
            placeholder: 'أرقام ومعلومات إحصائية',
            required: false,
          },
          {
            name: 'acknowledgments',
            type: 'text',
            label: 'الشكر والتقدير',
            placeholder: 'شكر للفريق والعملاء',
            required: true,
          },
          {
            name: 'futureGoals',
            type: 'text',
            label: 'الأهداف المستقبلية',
            placeholder: 'ما هي الخطوة التالية؟',
            required: false,
          },
          {
            name: 'companyHashtag',
            type: 'text',
            label: 'هاشتاق الشركة',
            placeholder: 'اسم_الشركة',
            required: false,
          },
        ],
        hashtags: ['إنجاز', 'نجاح', 'شكر', 'فريق_العمل'],
        tone: 'professional',
        industry: ['business'],
        isPublic: true,
        usageCount: 0,
        rating: 4.4,
        tags: ['business', 'milestone', 'achievement'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    // Add templates to the map
    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * Search templates based on filters
   */
  searchTemplates(filters: TemplateSearchFilters): ContentTemplate[] {
    const templates = Array.from(this.templates.values());

    return templates.filter(template => {
      // Category filter
      if (filters.category && template.category !== filters.category) {
        return false;
      }

      // Platform filter
      if (filters.platform && !template.platform.includes(filters.platform)) {
        return false;
      }

      // Language filter
      if (filters.language && template.language !== 'both' && template.language !== filters.language) {
        return false;
      }

      // Industry filter
      if (filters.industry && !template.industry.includes(filters.industry)) {
        return false;
      }

      // Tone filter
      if (filters.tone && template.tone !== filters.tone) {
        return false;
      }

      // Public filter
      if (filters.isPublic !== undefined && template.isPublic !== filters.isPublic) {
        return false;
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(tag =>
          template.tags.includes(tag)
        );
        if (!hasMatchingTag) {
          return false;
        }
      }

      // Query filter (search in name, description, content)
      if (filters.query) {
        const query = filters.query.toLowerCase();
        const searchableText = `${template.name} ${template.description} ${template.content}`.toLowerCase();
        if (!searchableText.includes(query)) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Get template by ID
   */
  getTemplate(id: string): ContentTemplate | undefined {
    return this.templates.get(id);
  }

  /**
   * Get popular templates
   */
  getPopularTemplates(limit: number = 10): ContentTemplate[] {
    return Array.from(this.templates.values())
      .filter(template => template.isPublic)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit);
  }

  /**
   * Get templates by category
   */
  getTemplatesByCategory(category: TemplateCategory): ContentTemplate[] {
    return Array.from(this.templates.values())
      .filter(template => template.category === category && template.isPublic);
  }

  /**
   * Process template with variables
   */
  processTemplate(template: ContentTemplate, variables: Record<string, string>): string {
    let processedContent = template.content;

    // Replace variables in content
    template.variables.forEach(variable => {
      const value = variables[variable.name] || variable.defaultValue || '';
      const placeholder = `{{${variable.name}}}`;
      processedContent = processedContent.replace(new RegExp(placeholder, 'g'), value);
    });

    return processedContent;
  }

  /**
   * Validate template variables
   */
  validateTemplateVariables(template: ContentTemplate, variables: Record<string, string>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    template.variables.forEach(variable => {
      const value = variables[variable.name];

      // Check required fields
      if (variable.required && (!value || value.trim() === '')) {
        errors.push(`${variable.label} مطلوب`);
        return;
      }

      if (value && variable.validation) {
        // Check minimum length
        if (variable.validation.minLength && value.length < variable.validation.minLength) {
          errors.push(`${variable.label} يجب أن يكون على الأقل ${variable.validation.minLength} أحرف`);
        }

        // Check maximum length
        if (variable.validation.maxLength && value.length > variable.validation.maxLength) {
          errors.push(`${variable.label} يجب أن يكون أقل من ${variable.validation.maxLength} حرف`);
        }

        // Check pattern
        if (variable.validation.pattern && !new RegExp(variable.validation.pattern).test(value)) {
          errors.push(`${variable.label} غير صحيح`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const contentTemplateManager = new ContentTemplateManager();
