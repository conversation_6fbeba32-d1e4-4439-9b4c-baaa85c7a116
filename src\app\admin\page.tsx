"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { StatsCard } from "@/components/dashboard/stats-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  MessageSquare, 
  Calendar, 
  BarChart3, 
  Shield, 
  Settings, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Activity,
  Database
} from "lucide-react";
import { mockUsers, mockPosts, mockSocialAccounts } from "@/lib/mock-data";

export default function AdminDashboardPage() {
  const [isMounted, setIsMounted] = useState(false);

  // Admin statistics
  const totalUsers = mockUsers.length;
  const totalPosts = mockPosts.length;
  const totalAccounts = mockSocialAccounts.length;
  const activeUsers = mockUsers.filter(user => user.role === "USER").length;
  const adminUsers = mockUsers.filter(user => user.role === "ADMIN").length;
  const publishedPosts = mockPosts.filter(post => post.status === "PUBLISHED").length;
  const scheduledPosts = mockPosts.filter(post => post.status === "SCHEDULED").length;
  const draftPosts = mockPosts.filter(post => post.status === "DRAFT").length;

  return (
    <DashboardLayout title="لوحة الإدارة">
      <div className="space-y-8" dir="rtl">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl p-8 shadow-lg">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-6">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold flex items-center gap-3">
                <Shield className="w-8 h-8" />
                لوحة الإدارة 🛡️
              </h1>
              <p className="text-purple-100 text-lg">
                إدارة شاملة لمنصة eWasl ومراقبة الأنشطة
              </p>
              <p className="text-sm text-purple-200">
                مراقبة المستخدمين والمحتوى والأنشطة في الوقت الفعلي
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="secondary" size="lg" className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                <Settings className="ml-2 h-5 w-5" />
                إعدادات النظام
              </Button>
              <Button variant="secondary" size="lg" className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                <Activity className="ml-2 h-5 w-5" />
                سجل الأنشطة
              </Button>
            </div>
          </div>
        </div>

        {/* System Status */}
        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-green-800">حالة النظام: ممتاز</h3>
                <p className="text-sm text-green-700 mt-1">
                  جميع الخدمات تعمل بشكل طبيعي • آخر تحديث: منذ دقيقتين
                </p>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                99.9% وقت التشغيل
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Admin Statistics */}
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="إجمالي المستخدمين"
            value={totalUsers.toString()}
            icon={Users}
            description={`${activeUsers} مستخدم عادي • ${adminUsers} مدير`}
            trend={{ value: 12, isPositive: true }}
            color="blue"
          />
          <StatsCard
            title="إجمالي المنشورات"
            value={totalPosts.toString()}
            icon={MessageSquare}
            description={`${publishedPosts} منشور • ${scheduledPosts} مجدول • ${draftPosts} مسودة`}
            trend={{ value: 8, isPositive: true }}
            color="purple"
          />
          <StatsCard
            title="الحسابات المتصلة"
            value={totalAccounts.toString()}
            icon={BarChart3}
            description="حسابات وسائل التواصل النشطة"
            trend={{ value: 25, isPositive: true }}
            color="green"
          />
          <StatsCard
            title="معدل النشاط"
            value="94%"
            icon={TrendingUp}
            description="نشاط المستخدمين هذا الأسبوع"
            trend={{ value: 5, isPositive: true }}
            color="orange"
          />
        </div>

        {/* Management Cards */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* User Management */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">إدارة المستخدمين</CardTitle>
                    <CardDescription>مراقبة وإدارة حسابات المستخدمين</CardDescription>
                  </div>
                </div>
                <Badge variant="secondary">{totalUsers}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>مستخدمون نشطون</span>
                  <span className="font-medium">{activeUsers}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>مديرون</span>
                  <span className="font-medium">{adminUsers}</span>
                </div>
                <Button className="w-full mt-4" variant="outline">
                  عرض جميع المستخدمين
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Content Management */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">إدارة المحتوى</CardTitle>
                    <CardDescription>مراجعة ومراقبة المنشورات</CardDescription>
                  </div>
                </div>
                <Badge variant="secondary">{totalPosts}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>منشورات منشورة</span>
                  <span className="font-medium text-green-600">{publishedPosts}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>منشورات مجدولة</span>
                  <span className="font-medium text-blue-600">{scheduledPosts}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>مسودات</span>
                  <span className="font-medium text-gray-600">{draftPosts}</span>
                </div>
                <Button className="w-full mt-4" variant="outline">
                  مراجعة المحتوى
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* System Settings */}
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Settings className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">إعدادات النظام</CardTitle>
                    <CardDescription>تكوين وإدارة النظام</CardDescription>
                  </div>
                </div>
                <Badge variant="secondary" className="bg-green-100 text-green-800">نشط</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>قاعدة البيانات</span>
                  <span className="font-medium text-green-600">متصلة</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>خدمات API</span>
                  <span className="font-medium text-green-600">تعمل</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>النسخ الاحتياطي</span>
                  <span className="font-medium text-blue-600">يومي</span>
                </div>
                <Button className="w-full mt-4" variant="outline">
                  إعدادات متقدمة
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
