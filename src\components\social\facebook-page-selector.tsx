'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CheckCircle, Users, Globe, Verified, RefreshCw, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { BusinessAccount } from '@/lib/social/business-accounts/business-account-types';

interface FacebookPageSelectorProps {
  userId: string;
  socialAccountId: string;
  onPageSelected?: (pageId: string) => void;
  className?: string;
}

export function FacebookPageSelector({ 
  userId, 
  socialAccountId, 
  onPageSelected,
  className 
}: FacebookPageSelectorProps) {
  const [pages, setPages] = useState<BusinessAccount[]>([]);
  const [selectedPageId, setSelectedPageId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPages();
  }, [socialAccountId]);

  const loadPages = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/social/business-accounts?platform=facebook&userId=${userId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load Facebook Pages');
      }

      setPages(data.businessAccounts || []);
      setSelectedPageId(data.selectedAccount?.id || null);

    } catch (error: any) {
      console.error('Error loading Facebook Pages:', error);
      setError(error.message);
      toast.error('فشل في تحميل صفحات فيسبوك');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPages = async () => {
    try {
      setIsRefreshing(true);
      setError(null);

      const response = await fetch('/api/social/business-accounts/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'facebook',
          userId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to refresh Facebook Pages');
      }

      setPages(data.businessAccounts || []);
      toast.success('تم تحديث صفحات فيسبوك بنجاح');

    } catch (error: any) {
      console.error('Error refreshing Facebook Pages:', error);
      setError(error.message);
      toast.error('فشل في تحديث صفحات فيسبوك');
    } finally {
      setIsRefreshing(false);
    }
  };

  const selectPage = async (pageId: string) => {
    try {
      const response = await fetch('/api/social/business-accounts/select', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'facebook',
          userId,
          businessAccountId: pageId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to select Facebook Page');
      }

      setSelectedPageId(pageId);
      onPageSelected?.(pageId);
      toast.success('تم اختيار صفحة فيسبوك بنجاح');

    } catch (error: any) {
      console.error('Error selecting Facebook Page:', error);
      toast.error('فشل في اختيار صفحة فيسبوك');
    }
  };

  const formatFollowerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-500 rounded"></div>
            صفحات فيسبوك
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
            <span className="mr-2 text-muted-foreground">جاري تحميل الصفحات...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-500 rounded"></div>
            صفحات فيسبوك
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadPages} variant="outline">
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (pages.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-500 rounded"></div>
            صفحات فيسبوك
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="w-12 h-12 text-yellow-500 mb-4" />
            <p className="text-muted-foreground mb-4">
              لم يتم العثور على صفحات فيسبوك قابلة للإدارة
            </p>
            <p className="text-sm text-muted-foreground mb-4">
              تأكد من أن لديك صلاحيات إدارة الصفحات في فيسبوك
            </p>
            <Button onClick={refreshPages} variant="outline" disabled={isRefreshing}>
              {isRefreshing ? (
                <>
                  <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
                  جاري التحديث...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 ml-2" />
                  تحديث الصفحات
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-500 rounded"></div>
            صفحات فيسبوك ({pages.length})
          </CardTitle>
          <Button 
            onClick={refreshPages} 
            variant="outline" 
            size="sm"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {pages.map((page) => (
            <div
              key={page.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                selectedPageId === page.id
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => selectPage(page.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={page.profilePictureUrl} alt={page.name} />
                    <AvatarFallback>
                      {page.name.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-sm">{page.name}</h3>
                      {page.metadata?.isVerified && (
                        <Verified className="w-4 h-4 text-blue-500" />
                      )}
                      {selectedPageId === page.id && (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 mt-1">
                      {page.followerCount && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Users className="w-3 h-3" />
                          {formatFollowerCount(page.followerCount)}
                        </div>
                      )}
                      
                      {page.category && (
                        <Badge variant="secondary" className="text-xs">
                          {page.category}
                        </Badge>
                      )}
                      
                      {page.website && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Globe className="w-3 h-3" />
                          موقع إلكتروني
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {selectedPageId === page.id && (
                  <Badge variant="default" className="bg-green-500">
                    مختارة
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {selectedPageId && (
          <div className="mt-4 p-3 bg-green-50 dark:bg-green-950 border border-green-200 rounded-lg">
            <p className="text-sm text-green-700 dark:text-green-300">
              ✅ سيتم نشر المنشورات على الصفحة المختارة
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
