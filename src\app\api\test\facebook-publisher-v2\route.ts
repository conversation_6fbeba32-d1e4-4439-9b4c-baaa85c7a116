import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';
import FacebookPublisherV2 from '@/lib/social/publishers/facebook-publisher-v2';
import FacebookGraphService from '@/lib/social/services/facebook-graph-service';
import ContentFormatter from '@/lib/social/content-formatter';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const DEMO_USER_ID = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';

export async function POST(request: NextRequest) {
  try {
    console.log('Running Facebook Publisher V2 comprehensive tests...');

    const supabase = createServiceRoleClient();
    const facebookPublisher = new FacebookPublisherV2();
    const facebookService = new FacebookGraphService();
    const contentFormatter = new ContentFormatter();

    const testResults = {
      configurationCheck: '⏳ Testing...',
      serviceInstantiation: '⏳ Testing...',
      contentFormatting: '⏳ Testing...',
      mockAccountTest: '⏳ Testing...',
      databaseIntegration: '⏳ Testing...',
      apiStructureValidation: '⏳ Testing...',
    };

    // Test 1: Configuration Check
    try {
      console.log('Testing Facebook configuration...');
      
      const hasAppId = !!(process.env.FACEBOOK_APP_ID);
      const hasAppSecret = !!(process.env.FACEBOOK_APP_SECRET);
      
      if (hasAppId && hasAppSecret) {
        testResults.configurationCheck = '✅ Facebook credentials configured';
      } else {
        testResults.configurationCheck = `⚠️ Missing credentials (App ID: ${hasAppId}, Secret: ${hasAppSecret})`;
      }
    } catch (err) {
      console.error('Configuration check error:', err);
      testResults.configurationCheck = '❌ Configuration check failed';
    }

    // Test 2: Service Instantiation
    try {
      console.log('Testing service instantiation...');
      
      // Test if services can be created without errors
      const testService = new FacebookGraphService();
      const testPublisher = new FacebookPublisherV2();
      
      // Test if methods exist
      const hasRequiredMethods = [
        typeof testService.getUserInfo === 'function',
        typeof testService.getUserPages === 'function',
        typeof testService.publishPost === 'function',
        typeof testPublisher.publishPost === 'function',
        typeof testPublisher.testConnection === 'function'
      ].every(Boolean);
      
      if (hasRequiredMethods) {
        testResults.serviceInstantiation = '✅ All services instantiated with required methods';
      } else {
        testResults.serviceInstantiation = '❌ Missing required methods';
      }
    } catch (err) {
      console.error('Service instantiation error:', err);
      testResults.serviceInstantiation = '❌ Service instantiation failed';
    }

    // Test 3: Content Formatting
    try {
      console.log('Testing Facebook content formatting...');
      
      const testContent = 'This is a test Facebook post for eWasl social media scheduler! 🚀 #eWasl #Facebook #SocialMedia #Automation';
      const formatted = contentFormatter.formatForPlatform(testContent, 'facebook');
      
      // Test Arabic content
      const arabicContent = 'هذا منشور تجريبي لمنصة eWasl على فيسبوك! 🚀 #eWasl #فيسبوك #وسائل_التواصل';
      const arabicFormatted = contentFormatter.formatArabicContent(arabicContent, 'facebook');
      
      // Test long content (Facebook has 63,206 character limit)
      const longContent = 'A'.repeat(100);
      const longFormatted = contentFormatter.formatForPlatform(longContent, 'facebook');
      
      if (formatted.isValid && arabicFormatted.isValid && longFormatted.isValid) {
        testResults.contentFormatting = '✅ Content formatting works (English, Arabic, long content)';
      } else {
        testResults.contentFormatting = `⚠️ Formatting issues (EN: ${formatted.isValid}, AR: ${arabicFormatted.isValid}, Long: ${longFormatted.isValid})`;
      }
    } catch (err) {
      console.error('Content formatting error:', err);
      testResults.contentFormatting = '❌ Content formatting failed';
    }

    // Test 4: Mock Account Test
    try {
      console.log('Testing with mock Facebook account...');
      
      const mockAccount = {
        id: 'test-facebook-account',
        platform: 'FACEBOOK',
        access_token: 'mock-token-for-testing',
        account_id: 'mock-page-id',
        account_name: 'Test Facebook Page',
        page_id: 'mock-page-id',
        page_name: 'Test Page'
      };

      const mockContent = {
        content: 'Test post from eWasl Facebook Publisher V2',
        mediaUrl: 'https://example.com/test-image.jpg',
        mediaType: 'IMAGE' as const
      };

      // Test content validation
      const validation = facebookPublisher.validatePostContent(mockContent);
      
      if (validation.isValid) {
        testResults.mockAccountTest = '✅ Mock account structure and validation working';
      } else {
        testResults.mockAccountTest = `❌ Validation failed: ${validation.errors.join(', ')}`;
      }
    } catch (err) {
      console.error('Mock account test error:', err);
      testResults.mockAccountTest = '❌ Mock account test failed';
    }

    // Test 5: Database Integration
    try {
      console.log('Testing database integration...');
      
      // Test if we can query social_accounts table with new Facebook fields
      const { data: accounts, error: accountsError } = await supabase
        .from('social_accounts')
        .select('id, platform, account_name, page_id, page_name, page_category, fan_count')
        .eq('platform', 'FACEBOOK')
        .limit(5);
      
      // Test if we can insert a test Facebook account
      const testAccountData = {
        user_id: DEMO_USER_ID,
        platform: 'FACEBOOK',
        account_id: 'test-facebook-' + Date.now(),
        account_name: 'Test Facebook Account',
        access_token: 'encrypted-test-token',
        page_id: 'test-page-id',
        page_name: 'Test Page',
        page_category: 'Business',
        fan_count: 1000
      };

      const { data: insertedAccount, error: insertError } = await supabase
        .from('social_accounts')
        .insert(testAccountData)
        .select()
        .single();

      if (!accountsError && !insertError && insertedAccount) {
        testResults.databaseIntegration = `✅ Database integration working (${accounts?.length || 0} existing accounts, test account created)`;
        
        // Clean up test account
        await supabase
          .from('social_accounts')
          .delete()
          .eq('id', insertedAccount.id);
      } else {
        testResults.databaseIntegration = `❌ Database errors (Query: ${!!accountsError}, Insert: ${!!insertError})`;
      }
    } catch (err) {
      console.error('Database integration error:', err);
      testResults.databaseIntegration = '❌ Database integration failed';
    }

    // Test 6: API Structure Validation
    try {
      console.log('Testing API structure validation...');
      
      // Test Facebook Graph API URL construction
      const testUrls = [
        'https://graph.facebook.com/v19.0/me',
        'https://graph.facebook.com/v19.0/me/accounts',
        'https://graph.facebook.com/v19.0/PAGE_ID/feed',
        'https://graph.facebook.com/v19.0/PAGE_ID/photos',
        'https://graph.facebook.com/v19.0/PAGE_ID/videos'
      ];

      const urlValidation = testUrls.every(url => {
        try {
          new URL(url);
          return true;
        } catch {
          return false;
        }
      });

      // Test required Facebook permissions
      const requiredPermissions = [
        'pages_manage_posts',
        'pages_read_engagement', 
        'business_management'
      ];

      if (urlValidation && requiredPermissions.length > 0) {
        testResults.apiStructureValidation = `✅ API structure valid (${testUrls.length} endpoints, ${requiredPermissions.length} permissions)`;
      } else {
        testResults.apiStructureValidation = '❌ API structure validation failed';
      }
    } catch (err) {
      console.error('API structure validation error:', err);
      testResults.apiStructureValidation = '❌ API structure validation failed';
    }

    // Generate summary
    const successCount = Object.values(testResults).filter(result => result.includes('✅')).length;
    const totalTests = Object.keys(testResults).length;
    const overallSuccess = successCount === totalTests;

    // Get additional system info
    const systemInfo = {
      facebookApiVersion: 'v19.0',
      hasCredentials: !!(process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET),
      supportedFeatures: [
        'Text Posts',
        'Image Posts', 
        'Video Posts',
        'Link Sharing',
        'Page Management',
        'Analytics Collection'
      ],
      timestamp: new Date().toISOString(),
    };

    console.log('Facebook Publisher V2 tests completed:', {
      overallSuccess,
      successCount,
      totalTests
    });

    return NextResponse.json({
      success: true,
      overallSuccess,
      testResults,
      summary: {
        total: totalTests,
        passed: successCount,
        failed: totalTests - successCount,
        successRate: Math.round((successCount / totalTests) * 100)
      },
      systemInfo,
      recommendations: generateFacebookRecommendations(testResults, systemInfo),
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Facebook Publisher V2 test error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Facebook Publisher V2 tests failed',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

function generateFacebookRecommendations(testResults: any, systemInfo: any): string[] {
  const recommendations: string[] = [];

  if (testResults.configurationCheck.includes('❌') || testResults.configurationCheck.includes('⚠️')) {
    recommendations.push('Configure Facebook App ID and App Secret in environment variables');
  }

  if (testResults.serviceInstantiation.includes('❌')) {
    recommendations.push('Check Facebook service implementations and dependencies');
  }

  if (testResults.databaseIntegration.includes('❌')) {
    recommendations.push('Verify database schema includes Facebook-specific fields');
  }

  if (!systemInfo.hasCredentials) {
    recommendations.push('Set up Facebook Developer App and configure OAuth settings');
  }

  if (recommendations.length === 0) {
    recommendations.push('Facebook Publisher V2 is ready for production use!');
    recommendations.push('Next: Test with real Facebook account and page');
    recommendations.push('Consider setting up Facebook App Review for production permissions');
  }

  return recommendations;
}
