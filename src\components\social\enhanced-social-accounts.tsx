'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  ExternalLink,
  RefreshCw,
  Settings,
  TrendingUp,
  Users,
  Zap,
  Shield,
  Activity,
  BarChart3,
  Link as LinkIcon,
  Unlink,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'sonner';

interface SocialAccount {
  id: string;
  platform: string;
  account_name: string;
  account_id: string;
  is_active: boolean;
  access_token?: string;
  refresh_token?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: {
    followers_count?: number;
    following_count?: number;
    posts_count?: number;
    page_id?: string;
    page_name?: string;
    page_category?: string;
    fan_count?: number;
    organization_id?: string;
    organization_name?: string;
  };
}

interface ConnectionHealth {
  platform: string;
  status: 'healthy' | 'warning' | 'error' | 'expired';
  lastChecked: string;
  issues: string[];
  recommendations: string[];
}

interface EnhancedSocialAccountsProps {
  userId: string;
}

const EnhancedSocialAccounts: React.FC<EnhancedSocialAccountsProps> = ({
  userId
}) => {
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);
  const [connectionHealth, setConnectionHealth] = useState<ConnectionHealth[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const text = {
    title: 'إدارة الحسابات الاجتماعية',
    description: 'إدارة ومراقبة حساباتك على منصات التواصل الاجتماعي',
    overview: 'نظرة عامة',
    health: 'صحة الاتصال',
    analytics: 'التحليلات',
    settings: 'الإعدادات',
    connected: 'متصل',
    disconnected: 'غير متصل',
    healthy: 'سليم',
    warning: 'تحذير',
    error: 'خطأ',
    expired: 'منتهي الصلاحية',
    refresh: 'تحديث',
    connect: 'ربط',
    disconnect: 'قطع الاتصال',
    configure: 'إعداد',
    viewAnalytics: 'عرض التحليلات',
    lastUpdated: 'آخر تحديث',
    followers: 'المتابعون',
    posts: 'المنشورات',
    engagement: 'التفاعل',
    connectionStatus: 'حالة الاتصال',
    accountHealth: 'صحة الحساب',
    recommendations: 'التوصيات',
    issues: 'المشاكل',
    noAccounts: 'لا توجد حسابات متصلة',
    connectFirst: 'قم بربط حساباتك على منصات التواصل الاجتماعي للبدء',
    loading: 'جاري التحميل...'
  };

  useEffect(() => {
    fetchAccounts();
    fetchConnectionHealth();
  }, [userId]);

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/social/accounts?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setAccounts(data.accounts || []);
      } else {
        toast.error('فشل في تحميل الحسابات');
      }
    } catch (error) {
      console.error('Error fetching accounts:', error);
      toast.error('خطأ في تحميل الحسابات');
    } finally {
      setLoading(false);
    }
  };

  const fetchConnectionHealth = async () => {
    try {
      const response = await fetch(`/api/social/health?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setConnectionHealth(data.health || []);
      }
    } catch (error) {
      console.error('Error fetching connection health:', error);
    }
  };

  const refreshConnections = async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchAccounts(), fetchConnectionHealth()]);
      toast.success('تم تحديث الاتصالات بنجاح');
    } catch (error) {
      toast.error('فشل في تحديث الاتصالات');
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'expired': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4" />;
      case 'warning': return <AlertCircle className="w-4 h-4" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      case 'expired': return <Clock className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'facebook': return '📘';
      case 'instagram': return '📷';
      case 'linkedin': return '💼';
      case 'twitter': case 'x': return '🐦';
      default: return '🔗';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>{text.loading}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{text.title}</h1>
          <p className="text-muted-foreground">{text.description}</p>
        </div>
        <Button
          onClick={refreshConnections}
          disabled={refreshing}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          {text.refresh}
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.connected}</p>
                <p className="text-2xl font-bold">{accounts.filter(a => a.is_active).length}</p>
              </div>
              <LinkIcon className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.followers}</p>
                <p className="text-2xl font-bold">
                  {accounts.reduce((sum, acc) => sum + (acc.metadata?.followers_count || 0), 0).toLocaleString()}
                </p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.posts}</p>
                <p className="text-2xl font-bold">
                  {accounts.reduce((sum, acc) => sum + (acc.metadata?.posts_count || 0), 0).toLocaleString()}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.accountHealth}</p>
                <p className="text-2xl font-bold">
                  {Math.round((connectionHealth.filter(h => h.status === 'healthy').length / Math.max(connectionHealth.length, 1)) * 100)}%
                </p>
              </div>
              <Shield className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{text.overview}</TabsTrigger>
          <TabsTrigger value="health">{text.health}</TabsTrigger>
          <TabsTrigger value="analytics">{text.analytics}</TabsTrigger>
          <TabsTrigger value="settings">{text.settings}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {accounts.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Unlink className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">{text.noAccounts}</h3>
                <p className="text-muted-foreground mb-4">{text.connectFirst}</p>
                <Button>
                  <LinkIcon className="w-4 h-4 mr-2" />
                  {text.connect}
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {accounts.map((account) => (
                <Card key={account.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{getPlatformIcon(account.platform)}</span>
                        <div>
                          <CardTitle className="text-lg">{account.account_name}</CardTitle>
                          <CardDescription className="capitalize">{account.platform}</CardDescription>
                        </div>
                      </div>
                      <Badge
                        variant={account.is_active ? "default" : "secondary"}
                        className={account.is_active ? "bg-green-100 text-green-800" : ""}
                      >
                        {account.is_active ? text.connected : text.disconnected}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {/* Account Metrics */}
                      {account.metadata && (
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          {account.metadata.followers_count && (
                            <div>
                              <p className="text-muted-foreground">{text.followers}</p>
                              <p className="font-semibold">{account.metadata.followers_count.toLocaleString()}</p>
                            </div>
                          )}
                          {account.metadata.posts_count && (
                            <div>
                              <p className="text-muted-foreground">{text.posts}</p>
                              <p className="font-semibold">{account.metadata.posts_count.toLocaleString()}</p>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          <Settings className="w-4 h-4 mr-1" />
                          {text.configure}
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          <BarChart3 className="w-4 h-4 mr-1" />
                          {text.viewAnalytics}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          {/* Connection Health will be implemented in the next part */}
          <Card>
            <CardHeader>
              <CardTitle>{text.connectionStatus}</CardTitle>
              <CardDescription>مراقبة صحة اتصالات منصات التواصل الاجتماعي</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">مراقبة صحة الاتصال قريباً...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Analytics will be implemented in the next part */}
          <Card>
            <CardHeader>
              <CardTitle>{text.analytics}</CardTitle>
              <CardDescription>تحليلات مفصلة لحساباتك على منصات التواصل الاجتماعي</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">التحليلات المتقدمة قريباً...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          {/* Settings will be implemented in the next part */}
          <Card>
            <CardHeader>
              <CardTitle>{text.settings}</CardTitle>
              <CardDescription>إعداد حساباتك على منصات التواصل الاجتماعي</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">إعدادات الحساب قريباً...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedSocialAccounts;
