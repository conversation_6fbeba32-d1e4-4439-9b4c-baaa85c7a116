import { z } from 'zod';

// Common validation patterns
const emailSchema = z.string().email('Invalid email address');
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number');

const uuidSchema = z.string().uuid('Invalid UUID format');
const urlSchema = z.string().url('Invalid URL format');

// Authentication schemas
export const registerSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required')
});

// Social media schemas
export const socialAccountSchema = z.object({
  platform: z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK']),
  accountId: z.string().min(1, 'Account ID is required'),
  accountName: z.string().min(1, 'Account name is required'),
  accessToken: z.string().min(1, 'Access token is required'),
  refreshToken: z.string().optional(),
  expiresAt: z.string().datetime().optional()
});

export const disconnectSocialSchema = z.object({
  accountId: uuidSchema
});

// Post schemas
export const createPostSchema = z.object({
  content: z.string()
    .min(1, 'Post content is required')
    .max(2800, 'Post content is too long'), // Twitter's limit
  mediaUrl: urlSchema.optional(),
  scheduledAt: z.string().datetime().optional(),
  socialAccountIds: z.array(uuidSchema).min(1, 'At least one social account must be selected')
});

export const publishPostSchema = z.object({
  postId: uuidSchema
});

export const updatePostSchema = z.object({
  id: uuidSchema,
  content: z.string()
    .min(1, 'Post content is required')
    .max(2800, 'Post content is too long').optional(),
  mediaUrl: urlSchema.optional(),
  scheduledAt: z.string().datetime().optional(),
  socialAccountIds: z.array(uuidSchema).optional()
});

// Billing schemas
export const createCheckoutSessionSchema = z.object({
  priceId: z.string().min(1, 'Price ID is required'),
  successUrl: urlSchema.optional(),
  cancelUrl: urlSchema.optional()
});

// Admin schemas
export const setupDatabaseSchema = z.object({
  confirm: z.boolean().refine(val => val === true, 'Confirmation required')
});

// Query parameter schemas
export const paginationSchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0, 'Page must be positive').optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0 && n <= 100, 'Limit must be between 1 and 100').optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

export const dateRangeSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: "End date must be after start date",
  path: ["endDate"],
});

// AI caption generation schema
export const generateCaptionSchema = z.object({
  bulletPoints: z.array(z.string().min(1, 'Bullet point cannot be empty')).min(1, 'At least one bullet point is required'),
  options: z.object({
    tone: z.enum(['professional', 'casual', 'friendly', 'formal']).optional(),
    language: z.enum(['ar', 'en']).default('ar'),
    includeHashtags: z.boolean().default(true),
    includeEmojis: z.boolean().default(true)
  }).optional()
});

// Validation helper functions
export function validateRequestBody<T>(
  schema: z.ZodSchema<T>,
  body: unknown
): { success: true; data: T } | { success: false; error: string } {
  try {
    const result = schema.safeParse(body);
    
    if (result.success) {
      return { success: true, data: result.data };
    } else {
      const errorMessage = result.error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join(', ');
      
      return { success: false, error: errorMessage };
    }
  } catch (error) {
    return { success: false, error: 'Invalid request format' };
  }
}

export function validateQueryParams<T>(
  schema: z.ZodSchema<T>,
  searchParams: URLSearchParams
): { success: true; data: T } | { success: false; error: string } {
  try {
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
    
    const result = schema.safeParse(params);
    
    if (result.success) {
      return { success: true, data: result.data };
    } else {
      const errorMessage = result.error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join(', ');
      
      return { success: false, error: errorMessage };
    }
  } catch (error) {
    return { success: false, error: 'Invalid query parameters' };
  }
}

// Sanitization helpers
export function sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000); // Limit length
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

export function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url);
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol');
    }
    return parsed.toString();
  } catch {
    throw new Error('Invalid URL');
  }
}
