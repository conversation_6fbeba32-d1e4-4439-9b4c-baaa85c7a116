#!/usr/bin/env node

/**
 * Complete LinkedIn OAuth Integration Test
 * Tests the entire LinkedIn OAuth flow end-to-end
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:3000';

// Test utilities
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

function logTest(name, success, details = '') {
  const status = success ? '✅' : '❌';
  console.log(`${status} ${name}${details ? ': ' + details : ''}`);
  return success;
}

async function runTests() {
  console.log('🔗 LinkedIn OAuth Integration Test Suite\n');
  
  let allPassed = true;
  
  // Test 1: System Health Check
  console.log('📊 Phase 1: System Health Check\n');
  
  try {
    const healthResponse = await makeRequest(`${BASE_URL}/api/system/health`);
    const healthPassed = logTest(
      'System health check', 
      healthResponse.status === 200 && healthResponse.data.overall === 'healthy',
      healthResponse.data.overall
    );
    allPassed = allPassed && healthPassed;
  } catch (error) {
    logTest('System health check', false, error.message);
    allPassed = false;
  }

  // Test 2: OAuth Configuration Check
  console.log('\n🔧 Phase 2: OAuth Configuration Check\n');
  
  try {
    const oauthResponse = await makeRequest(`${BASE_URL}/api/social/oauth-status`);
    const oauthPassed = logTest(
      'OAuth status endpoint', 
      oauthResponse.status === 200,
      `Status: ${oauthResponse.status}`
    );
    allPassed = allPassed && oauthPassed;

    if (oauthResponse.data && oauthResponse.data.platforms) {
      const linkedinPlatform = oauthResponse.data.platforms.find(p => p.platform === 'linkedin');
      const linkedinConfigured = logTest(
        'LinkedIn platform configured',
        linkedinPlatform && linkedinPlatform.enabled && linkedinPlatform.configured,
        linkedinPlatform ? `enabled: ${linkedinPlatform.enabled}, configured: ${linkedinPlatform.configured}` : 'not found'
      );
      allPassed = allPassed && linkedinConfigured;
    }
  } catch (error) {
    logTest('OAuth configuration check', false, error.message);
    allPassed = false;
  }

  // Test 3: LinkedIn OAuth URL Generation
  console.log('\n🔗 Phase 3: LinkedIn OAuth URL Generation\n');
  
  try {
    const urlResponse = await makeRequest(`${BASE_URL}/api/social/oauth-status`, {
      method: 'POST',
      body: { platform: 'linkedin', action: 'test-auth-url' }
    });
    
    const urlGenerated = logTest(
      'LinkedIn OAuth URL generation',
      urlResponse.status === 200 && urlResponse.data.success && urlResponse.data.authUrl,
      urlResponse.data.success ? 'URL generated' : urlResponse.data.message
    );
    allPassed = allPassed && urlGenerated;

    if (urlResponse.data.authUrl) {
      const authUrl = urlResponse.data.authUrl;
      const hasCorrectDomain = authUrl.includes('linkedin.com');
      const hasClientId = authUrl.includes('client_id=787coegnsdocvq');
      const hasCorrectRedirect = authUrl.includes('localhost:3000');
      
      logTest('OAuth URL has correct domain', hasCorrectDomain);
      logTest('OAuth URL has client ID', hasClientId);
      logTest('OAuth URL has correct redirect URI', hasCorrectRedirect);
      
      allPassed = allPassed && hasCorrectDomain && hasClientId && hasCorrectRedirect;
      
      console.log(`\n📋 Generated OAuth URL:\n${authUrl}\n`);
    }
  } catch (error) {
    logTest('LinkedIn OAuth URL generation', false, error.message);
    allPassed = false;
  }

  // Test 4: Test Page Accessibility
  console.log('\n📄 Phase 4: Test Page Accessibility\n');
  
  try {
    const pageResponse = await makeRequest(`${BASE_URL}/test-linkedin`);
    const pageAccessible = logTest(
      'LinkedIn test page accessible',
      pageResponse.status === 200,
      `Status: ${pageResponse.status}`
    );
    allPassed = allPassed && pageAccessible;
  } catch (error) {
    logTest('LinkedIn test page accessible', false, error.message);
    allPassed = false;
  }

  // Test 5: OAuth Callback Handling
  console.log('\n🔄 Phase 5: OAuth Callback Handling\n');
  
  try {
    // Test error callback
    const errorResponse = await makeRequest(`${BASE_URL}/api/linkedin/callback?error=access_denied&error_description=User%20denied%20access`);
    const errorHandled = logTest(
      'OAuth error callback handled',
      errorResponse.status === 307, // Redirect expected
      `Status: ${errorResponse.status}`
    );
    allPassed = allPassed && errorHandled;

    // Test success callback (with fake code)
    const successResponse = await makeRequest(`${BASE_URL}/api/linkedin/callback?code=fake-code&state=test-state`);
    const successHandled = logTest(
      'OAuth success callback handled',
      successResponse.status === 307, // Redirect expected
      `Status: ${successResponse.status}`
    );
    allPassed = allPassed && successHandled;
  } catch (error) {
    logTest('OAuth callback handling', false, error.message);
    allPassed = false;
  }

  // Test 6: Success/Error Pages
  console.log('\n📋 Phase 6: Success/Error Pages\n');
  
  try {
    const successPageResponse = await makeRequest(`${BASE_URL}/auth/success?platform=linkedin&account=Test%20User`);
    const successPageWorks = logTest(
      'Success page accessible',
      successPageResponse.status === 200,
      `Status: ${successPageResponse.status}`
    );
    allPassed = allPassed && successPageWorks;

    const errorPageResponse = await makeRequest(`${BASE_URL}/auth/error?error=test_error`);
    const errorPageWorks = logTest(
      'Error page accessible',
      errorPageResponse.status === 200,
      `Status: ${errorPageResponse.status}`
    );
    allPassed = allPassed && errorPageWorks;
  } catch (error) {
    logTest('Success/Error pages', false, error.message);
    allPassed = false;
  }

  // Final Results
  console.log('\n' + '='.repeat(60));
  console.log('🎯 LINKEDIN OAUTH INTEGRATION TEST RESULTS');
  console.log('='.repeat(60));
  
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! LinkedIn OAuth integration is ready!');
    console.log('\n✅ Ready for production testing:');
    console.log('   1. Visit: http://localhost:3000/test-linkedin');
    console.log('   2. Click "Generate Authorization URL"');
    console.log('   3. Click "Start LinkedIn OAuth"');
    console.log('   4. Complete LinkedIn authorization');
    console.log('   5. Verify success page and database storage');
  } else {
    console.log('❌ Some tests failed. Please check the issues above.');
  }
  
  console.log('\n📊 Test Summary:');
  console.log(`   • System Health: ${allPassed ? 'HEALTHY' : 'ISSUES DETECTED'}`);
  console.log(`   • OAuth Configuration: ${allPassed ? 'CONFIGURED' : 'NEEDS ATTENTION'}`);
  console.log(`   • LinkedIn Integration: ${allPassed ? 'READY' : 'NOT READY'}`);
  
  process.exit(allPassed ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
