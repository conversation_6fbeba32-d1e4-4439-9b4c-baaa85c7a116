import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const createWorkspaceSchema = z.object({
  organization_id: z.string().uuid(),
  name: z.string().min(1).max(200),
  slug: z.string().min(1).max(100).regex(/^[a-z0-9-]+$/),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).default('#3b82f6'),
});

const updateWorkspaceSchema = z.object({
  name: z.string().min(1).max(200).optional(),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  settings: z.record(z.any()).optional(),
  workflow_settings: z.record(z.any()).optional(),
});

/**
 * Get workspaces for organization
 * GET /api/teams/workspaces?organization_id=xxx
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
    }

    // Check if user has access to organization
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role, status')
      .eq('organization_id', organizationId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get workspaces user has access to
    const { data: workspaces, error } = await supabase
      .from('workspaces')
      .select(`
        *,
        workspace_members!inner(
          role,
          status,
          added_at
        )
      `)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .eq('workspace_members.user_id', user.id)
      .eq('workspace_members.status', 'active')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching workspaces:', error);
      return NextResponse.json({ error: 'Failed to fetch workspaces' }, { status: 500 });
    }

    // Get additional data for each workspace
    const workspacesWithData = await Promise.all(
      workspaces.map(async (workspace) => {
        // Get member count
        const { count: memberCount } = await supabase
          .from('workspace_members')
          .select('*', { count: 'exact', head: true })
          .eq('workspace_id', workspace.id)
          .eq('status', 'active');

        // Get post count
        const { count: postCount } = await supabase
          .from('posts')
          .select('*', { count: 'exact', head: true })
          .eq('workspace_id', workspace.id);

        // Get pending approval count
        const { data: workspacePosts } = await supabase
          .from('posts')
          .select('id')
          .eq('workspace_id', workspace.id);

        const postIds = (workspacePosts || []).map((post: any) => post.id);

        const { count: pendingApprovals } = await supabase
          .from('content_approvals')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'pending')
          .in('post_id', postIds);

        return {
          ...workspace,
          member_count: memberCount || 0,
          post_count: postCount || 0,
          pending_approvals: pendingApprovals || 0,
          user_role: workspace.workspace_members[0]?.role,
        };
      })
    );

    return NextResponse.json({
      success: true,
      workspaces: workspacesWithData,
    });

  } catch (error) {
    console.error('Workspaces fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Create new workspace
 * POST /api/teams/workspaces
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validation = createWorkspaceSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { organization_id, name, slug, description, color } = validation.data;

    // Check if user has permission to create workspaces in organization
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!membership || !['owner', 'admin'].includes(membership.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Check if slug is unique within organization
    const { data: existingWorkspace } = await supabase
      .from('workspaces')
      .select('id')
      .eq('organization_id', organization_id)
      .eq('slug', slug)
      .single();

    if (existingWorkspace) {
      return NextResponse.json({ error: 'Workspace slug already exists in this organization' }, { status: 409 });
    }

    // Create workspace
    const { data: workspace, error: createError } = await supabase
      .from('workspaces')
      .insert({
        organization_id,
        name,
        slug,
        description,
        color,
        created_by: user.id,
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating workspace:', createError);
      return NextResponse.json({ error: 'Failed to create workspace' }, { status: 500 });
    }

    // Add creator as admin
    const { error: memberError } = await supabase
      .from('workspace_members')
      .insert({
        workspace_id: workspace.id,
        user_id: user.id,
        role: 'admin',
        status: 'active',
        added_by: user.id,
      });

    if (memberError) {
      console.error('Error adding workspace admin:', memberError);
      // Rollback workspace creation
      await supabase.from('workspaces').delete().eq('id', workspace.id);
      return NextResponse.json({ error: 'Failed to create workspace' }, { status: 500 });
    }

    // Create default approval workflow
    const { error: workflowError } = await supabase
      .from('approval_workflows')
      .insert({
        workspace_id: workspace.id,
        name: 'Default Approval',
        description: 'Default approval workflow for this workspace',
        steps: [
          {
            step: 1,
            name: 'Content Review',
            required_approvers: 1,
            approver_roles: ['admin', 'editor'],
            auto_approve: false,
          }
        ],
        is_default: true,
        created_by: user.id,
      });

    if (workflowError) {
      console.error('Error creating default workflow:', workflowError);
    }

    return NextResponse.json({
      success: true,
      workspace: {
        ...workspace,
        member_count: 1,
        post_count: 0,
        pending_approvals: 0,
        user_role: 'admin',
      },
    });

  } catch (error) {
    console.error('Workspace creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Update workspace
 * PUT /api/teams/workspaces?id=xxx
 */
export async function PUT(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('id');

    if (!workspaceId) {
      return NextResponse.json({ error: 'Workspace ID required' }, { status: 400 });
    }

    // Check if user has permission to update workspace
    const { data: membership } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!membership || !['admin'].includes(membership.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validation = updateWorkspaceSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    // Update workspace
    const { data: workspace, error: updateError } = await supabase
      .from('workspaces')
      .update(validation.data)
      .eq('id', workspaceId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating workspace:', updateError);
      return NextResponse.json({ error: 'Failed to update workspace' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      workspace,
    });

  } catch (error) {
    console.error('Workspace update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Archive workspace
 * DELETE /api/teams/workspaces?id=xxx
 */
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('id');

    if (!workspaceId) {
      return NextResponse.json({ error: 'Workspace ID required' }, { status: 400 });
    }

    // Check if user has permission to archive workspace
    const { data: membership } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!membership || !['admin'].includes(membership.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Archive workspace (soft delete)
    const { error: archiveError } = await supabase
      .from('workspaces')
      .update({
        is_active: false,
        archived_at: new Date().toISOString(),
      })
      .eq('id', workspaceId);

    if (archiveError) {
      console.error('Error archiving workspace:', archiveError);
      return NextResponse.json({ error: 'Failed to archive workspace' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Workspace archived successfully',
    });

  } catch (error) {
    console.error('Workspace archive error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
