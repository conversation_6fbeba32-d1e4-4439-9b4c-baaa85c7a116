// Browser Console Test Script for Analytics System
// Copy and paste this into the browser console after signing in

console.log('🧪 Starting Analytics System Browser Tests...\n');

// Test 1: Dashboard Analytics API
async function testDashboardAPI() {
  console.log('📊 Testing Dashboard Analytics API...');
  
  try {
    const response = await fetch('/api/analytics/dashboard');
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Dashboard API successful!');
      console.log('Dashboard data:', data);
      console.log('Overview metrics:', data.data?.overview);
      return data;
    } else {
      console.log('❌ Dashboard API failed:', data.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Dashboard API error:', error.message);
    return null;
  }
}

// Test 2: Detailed Analytics API
async function testDetailedAnalytics() {
  console.log('\n📈 Testing Detailed Analytics API...');
  
  const periods = ['week', 'month', 'year'];
  const results = {};
  
  for (const period of periods) {
    try {
      const response = await fetch(`/api/analytics/detailed?period=${period}`);
      const data = await response.json();
      
      if (response.ok) {
        console.log(`✅ ${period} analytics successful!`);
        console.log(`${period} data overview:`, data.data?.overview);
        results[period] = data;
      } else {
        console.log(`❌ ${period} analytics failed:`, data.error);
        results[period] = null;
      }
    } catch (error) {
      console.log(`❌ ${period} analytics error:`, error.message);
      results[period] = null;
    }
  }
  
  return results;
}

// Test 3: Test dashboard data loading
async function testDashboardDataLoading() {
  console.log('\n🏠 Testing Dashboard Data Loading...');
  
  try {
    // Check if dashboard elements exist
    const dashboardElements = {
      statsCards: document.querySelectorAll('[style*="background: linear-gradient"]').length > 0,
      recentPosts: document.querySelector('h3')?.textContent?.includes('المنشورات الأخيرة') || false,
      activities: document.querySelector('h3')?.textContent?.includes('النشاطات الأخيرة') || false
    };
    
    console.log('Dashboard elements check:', dashboardElements);
    
    // Test API call from dashboard
    const dashboardData = await testDashboardAPI();
    
    if (dashboardData) {
      console.log('✅ Dashboard data loading working');
      return true;
    } else {
      console.log('❌ Dashboard data loading failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Dashboard data loading error:', error.message);
    return false;
  }
}

// Test 4: Test analytics page functionality
async function testAnalyticsPageFunctionality() {
  console.log('\n📊 Testing Analytics Page Functionality...');
  
  try {
    // Test period selection if on analytics page
    const periodButtons = document.querySelectorAll('button');
    const hasPeriodButtons = Array.from(periodButtons).some(btn => 
      btn.textContent?.includes('الأسبوع') || 
      btn.textContent?.includes('الشهر') || 
      btn.textContent?.includes('السنة')
    );
    
    console.log('Period selection buttons found:', hasPeriodButtons);
    
    // Test detailed analytics API
    const analyticsData = await testDetailedAnalytics();
    const workingPeriods = Object.values(analyticsData).filter(data => data !== null).length;
    
    console.log(`Analytics periods working: ${workingPeriods}/3`);
    
    return workingPeriods >= 2; // At least 2 periods should work
  } catch (error) {
    console.log('❌ Analytics page functionality error:', error.message);
    return false;
  }
}

// Test 5: Test real-time data updates
async function testRealTimeUpdates() {
  console.log('\n🔄 Testing Real-time Data Updates...');
  
  try {
    // Get initial data
    const initialData = await fetch('/api/analytics/dashboard').then(r => r.json());
    
    // Wait a moment and get data again
    await new Promise(resolve => setTimeout(resolve, 1000));
    const updatedData = await fetch('/api/analytics/dashboard').then(r => r.json());
    
    if (initialData.success && updatedData.success) {
      console.log('✅ Real-time data updates working');
      console.log('Initial total posts:', initialData.data?.overview?.totalPosts);
      console.log('Updated total posts:', updatedData.data?.overview?.totalPosts);
      return true;
    } else {
      console.log('❌ Real-time data updates failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Real-time updates error:', error.message);
    return false;
  }
}

// Test 6: Test error handling
async function testErrorHandling() {
  console.log('\n🛡️ Testing Error Handling...');
  
  try {
    // Test invalid period
    const invalidResponse = await fetch('/api/analytics/detailed?period=invalid');
    const invalidData = await invalidResponse.json();
    
    console.log('Invalid period response:', invalidResponse.status);
    
    // Test missing authentication (if applicable)
    // This would depend on the current auth state
    
    console.log('✅ Error handling tests completed');
    return true;
  } catch (error) {
    console.log('❌ Error handling test error:', error.message);
    return false;
  }
}

// Run all analytics tests
async function runAllAnalyticsTests() {
  console.log('🚀 Running comprehensive analytics system tests...\n');
  
  const results = [];
  let passedTests = 0;
  let totalTests = 0;

  // Test 1: Dashboard API
  totalTests++;
  const dashboardResult = await testDashboardAPI();
  const dashboardPassed = !!dashboardResult;
  results.push({ name: 'Dashboard API', passed: dashboardPassed });
  if (dashboardPassed) passedTests++;

  // Test 2: Detailed Analytics API
  totalTests++;
  const analyticsResult = await testDetailedAnalytics();
  const analyticsPassed = Object.values(analyticsResult).some(data => data !== null);
  results.push({ name: 'Detailed Analytics API', passed: analyticsPassed });
  if (analyticsPassed) passedTests++;

  // Test 3: Dashboard Data Loading
  totalTests++;
  const dashboardLoadingPassed = await testDashboardDataLoading();
  results.push({ name: 'Dashboard Data Loading', passed: dashboardLoadingPassed });
  if (dashboardLoadingPassed) passedTests++;

  // Test 4: Analytics Page Functionality
  totalTests++;
  const analyticsPagePassed = await testAnalyticsPageFunctionality();
  results.push({ name: 'Analytics Page Functionality', passed: analyticsPagePassed });
  if (analyticsPagePassed) passedTests++;

  // Test 5: Real-time Updates
  totalTests++;
  const realTimePassed = await testRealTimeUpdates();
  results.push({ name: 'Real-time Updates', passed: realTimePassed });
  if (realTimePassed) passedTests++;

  // Test 6: Error Handling
  totalTests++;
  const errorHandlingPassed = await testErrorHandling();
  results.push({ name: 'Error Handling', passed: errorHandlingPassed });
  if (errorHandlingPassed) passedTests++;

  console.log('\n' + '═'.repeat(50));
  console.log('🎯 ANALYTICS SYSTEM BROWSER TEST RESULTS');
  console.log('═'.repeat(50));
  console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`📊 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  console.log('\nDetailed Results:');
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });

  if (passedTests >= 4) {
    console.log('\n🎉 ANALYTICS SYSTEM WORKING!');
    console.log('✅ Browser tests completed successfully');
    console.log('\n📊 Key Features Verified:');
    console.log('   • Dashboard analytics API functional');
    console.log('   • Detailed analytics with period selection');
    console.log('   • Real-time data loading');
    console.log('   • Error handling implemented');
    console.log('   • UI components working correctly');
  } else {
    console.log('\n⚠️  Some tests failed - check authentication and data');
  }

  return passedTests >= 4;
}

// Auto-run tests
console.log('To run tests, execute: runAllAnalyticsTests()');
console.log('Or run individual tests:');
console.log('- testDashboardAPI()');
console.log('- testDetailedAnalytics()');
console.log('- testDashboardDataLoading()');
console.log('- testAnalyticsPageFunctionality()');

// Make functions available globally
window.runAllAnalyticsTests = runAllAnalyticsTests;
window.testDashboardAPI = testDashboardAPI;
window.testDetailedAnalytics = testDetailedAnalytics;
window.testDashboardDataLoading = testDashboardDataLoading;
window.testAnalyticsPageFunctionality = testAnalyticsPageFunctionality;
