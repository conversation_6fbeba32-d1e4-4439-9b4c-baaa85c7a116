import { NextRequest, NextResponse } from 'next/server';
import { createEnhancedStripeClient } from '@/lib/stripe/enhanced-client';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Test Stripe webhook configuration
 * GET /api/stripe/test-webhook
 */
export async function GET(request: NextRequest) {
  try {
    const stripeClient = createEnhancedStripeClient();
    const stripe = stripeClient.getStripeInstance();

    // List webhook endpoints
    const webhookEndpoints = await stripe.webhookEndpoints.list({
      limit: 10,
    });

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
    const expectedWebhookUrl = `${appUrl}/api/stripe/webhook`;

    // Check if our webhook endpoint exists
    const ourWebhook = webhookEndpoints.data.find(endpoint => 
      endpoint.url === expectedWebhookUrl
    );

    const response = {
      webhookSecret: webhookSecret ? 'Configured' : 'Missing',
      expectedWebhookUrl,
      webhookEndpoints: webhookEndpoints.data.map(endpoint => ({
        id: endpoint.id,
        url: endpoint.url,
        status: endpoint.status,
        enabled_events: endpoint.enabled_events,
        created: new Date(endpoint.created * 1000).toISOString(),
      })),
      ourWebhookExists: !!ourWebhook,
      ourWebhookDetails: ourWebhook ? {
        id: ourWebhook.id,
        status: ourWebhook.status,
        enabled_events: ourWebhook.enabled_events,
        created: new Date(ourWebhook.created * 1000).toISOString(),
      } : null,
      recommendations: []
    };

    // Add recommendations
    if (!webhookSecret) {
      response.recommendations.push('Configure STRIPE_WEBHOOK_SECRET environment variable');
    }

    if (!ourWebhook) {
      response.recommendations.push(`Create webhook endpoint for ${expectedWebhookUrl}`);
      response.recommendations.push('Enable events: checkout.session.completed, customer.subscription.updated, customer.subscription.deleted, invoice.payment_succeeded, invoice.payment_failed');
    } else if (ourWebhook.status !== 'enabled') {
      response.recommendations.push('Enable the webhook endpoint in Stripe dashboard');
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Webhook test failed:', error);
    return NextResponse.json(
      {
        error: 'Failed to test webhook configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Create webhook endpoint in Stripe
 * POST /api/stripe/test-webhook
 */
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    if (action !== 'create-webhook') {
      return NextResponse.json(
        { error: 'Invalid action. Use "create-webhook"' },
        { status: 400 }
      );
    }

    const stripeClient = createEnhancedStripeClient();
    const stripe = stripeClient.getStripeInstance();

    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
    const webhookUrl = `${appUrl}/api/stripe/webhook`;

    // Check if webhook already exists
    const existingWebhooks = await stripe.webhookEndpoints.list({
      limit: 100,
    });

    const existingWebhook = existingWebhooks.data.find(endpoint => 
      endpoint.url === webhookUrl
    );

    if (existingWebhook) {
      return NextResponse.json({
        success: false,
        message: 'Webhook endpoint already exists',
        webhook: {
          id: existingWebhook.id,
          url: existingWebhook.url,
          status: existingWebhook.status,
        }
      });
    }

    // Create new webhook endpoint
    const webhook = await stripe.webhookEndpoints.create({
      url: webhookUrl,
      enabled_events: [
        'checkout.session.completed',
        'customer.subscription.created',
        'customer.subscription.updated',
        'customer.subscription.deleted',
        'invoice.payment_succeeded',
        'invoice.payment_failed',
        'customer.subscription.trial_will_end',
        'invoice.upcoming',
        'payment_method.attached',
        'payment_method.detached',
      ],
    });

    return NextResponse.json({
      success: true,
      message: 'Webhook endpoint created successfully',
      webhook: {
        id: webhook.id,
        url: webhook.url,
        status: webhook.status,
        secret: webhook.secret,
        enabled_events: webhook.enabled_events,
      },
      nextSteps: [
        `Add STRIPE_WEBHOOK_SECRET=${webhook.secret} to your environment variables`,
        'Test the webhook endpoint',
        'Monitor webhook events in Stripe dashboard'
      ]
    });

  } catch (error) {
    console.error('Webhook creation failed:', error);
    return NextResponse.json(
      {
        error: 'Failed to create webhook endpoint',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
