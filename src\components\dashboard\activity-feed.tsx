"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface ActivityItem {
  id: string;
  type: 'success' | 'scheduled' | 'failed' | 'info';
  message: string;
  timestamp: string;
  platform?: string;
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'success': return '✅';
    case 'scheduled': return '📅';
    case 'failed': return '❌';
    case 'info': return 'ℹ️';
    default: return '📝';
  }
};

const getActivityColor = (type: string) => {
  switch (type) {
    case 'success': return 'text-green-600';
    case 'scheduled': return 'text-blue-600';
    case 'failed': return 'text-red-600';
    case 'info': return 'text-gray-600';
    default: return 'text-gray-600';
  }
};

export function ActivityFeed() {
  // Mock data - replace with real data from API
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'success',
      message: 'تم نشر منشور جديد بنجاح على Facebook',
      timestamp: 'منذ ساعتين',
      platform: 'Facebook'
    },
    {
      id: '2',
      type: 'scheduled',
      message: 'تم جدولة منشور جديد بنجاح لـ Instagram',
      timestamp: 'منذ 5 ساعات',
      platform: 'Instagram'
    },
    {
      id: '3',
      type: 'failed',
      message: 'فشل في نشر المنشور على Twitter',
      timestamp: 'منذ 8 ساعات',
      platform: 'Twitter'
    },
    {
      id: '4',
      type: 'info',
      message: 'تم تحديث إعدادات LinkedIn',
      timestamp: 'منذ 12 ساعة',
      platform: 'LinkedIn'
    }
  ];

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-gray-900">النشاط الأخير</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-xl mt-0.5">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1">
                <p className={`font-medium ${getActivityColor(activity.type)}`}>
                  {activity.message}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm text-gray-500">{activity.timestamp}</span>
                  {activity.platform && (
                    <Badge variant="outline" className="text-xs">
                      {activity.platform}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
