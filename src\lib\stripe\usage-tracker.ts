/**
 * Usage Tracking Service for eWasl
 * Tracks user usage and enforces plan limits
 */

import { createServiceRoleClient } from '@/lib/supabase/service-role';
import { USAGE_LIMITS } from './config';

export interface UsageData {
  postsCreated: number;
  postsScheduled: number;
  apiCalls: number;
  storageUsedMb: number;
  aiGenerationsUsed: number;
  reportsGenerated: number;
}

export interface PlanLimits {
  postsPerMonth: number | null; // null = unlimited
  socialAccounts: number | null;
  users: number | null;
  aiGenerations: number | null;
  reportsPerMonth: number | null;
}

export class UsageTracker {
  private supabase = createServiceRoleClient();

  /**
   * Get current month usage for a user
   */
  async getCurrentUsage(userId: string): Promise<UsageData> {
    const monthYear = this.getCurrentMonthYear();

    const { data, error } = await this.supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('month_year', monthYear)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Failed to get usage data:', error);
      throw error;
    }

    return {
      postsCreated: data?.posts_created || 0,
      postsScheduled: data?.posts_scheduled || 0,
      apiCalls: data?.api_calls || 0,
      storageUsedMb: data?.storage_used_mb || 0,
      aiGenerationsUsed: data?.ai_generations_used || 0,
      reportsGenerated: data?.reports_generated || 0,
    };
  }

  /**
   * Get user's plan limits
   */
  async getUserPlanLimits(userId: string): Promise<PlanLimits> {
    const { data: user, error } = await this.supabase
      .from('users')
      .select(`
        current_plan_id,
        subscription_plans!inner(
          plan_type,
          max_social_accounts,
          max_users,
          max_posts_per_month,
          features
        )
      `)
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Failed to get user plan:', error);
      // Default to free plan limits
      return USAGE_LIMITS.FREE;
    }

    const planType = (user.subscription_plans as any)?.[0]?.plan_type?.toUpperCase() || 'FREE';
    return USAGE_LIMITS[planType as keyof typeof USAGE_LIMITS] || USAGE_LIMITS.FREE;
  }

  /**
   * Check if user can perform an action based on their plan limits
   */
  async canPerformAction(
    userId: string, 
    action: 'create_post' | 'schedule_post' | 'generate_ai' | 'generate_report' | 'add_social_account' | 'add_user'
  ): Promise<{ allowed: boolean; reason?: string; currentUsage?: number; limit?: number }> {
    try {
      const [usage, limits] = await Promise.all([
        this.getCurrentUsage(userId),
        this.getUserPlanLimits(userId)
      ]);

      switch (action) {
        case 'create_post':
          if (limits.postsPerMonth === null) {
            return { allowed: true }; // Unlimited
          }
          const totalPosts = usage.postsCreated + usage.postsScheduled;
          if (totalPosts >= limits.postsPerMonth) {
            return {
              allowed: false,
              reason: 'Monthly post limit reached',
              currentUsage: totalPosts,
              limit: limits.postsPerMonth
            };
          }
          return { allowed: true, currentUsage: totalPosts, limit: limits.postsPerMonth };

        case 'schedule_post':
          if (limits.postsPerMonth === null) {
            return { allowed: true }; // Unlimited
          }
          const totalScheduled = usage.postsCreated + usage.postsScheduled;
          if (totalScheduled >= limits.postsPerMonth) {
            return {
              allowed: false,
              reason: 'Monthly post limit reached',
              currentUsage: totalScheduled,
              limit: limits.postsPerMonth
            };
          }
          return { allowed: true, currentUsage: totalScheduled, limit: limits.postsPerMonth };

        case 'generate_ai':
          if (limits.aiGenerations === null) {
            return { allowed: true }; // Unlimited
          }
          if (usage.aiGenerationsUsed >= limits.aiGenerations) {
            return {
              allowed: false,
              reason: 'Monthly AI generation limit reached',
              currentUsage: usage.aiGenerationsUsed,
              limit: limits.aiGenerations
            };
          }
          return { allowed: true, currentUsage: usage.aiGenerationsUsed, limit: limits.aiGenerations };

        case 'generate_report':
          if (limits.reportsPerMonth === null) {
            return { allowed: true }; // Unlimited
          }
          if (usage.reportsGenerated >= limits.reportsPerMonth) {
            return {
              allowed: false,
              reason: 'Monthly report generation limit reached',
              currentUsage: usage.reportsGenerated,
              limit: limits.reportsPerMonth
            };
          }
          return { allowed: true, currentUsage: usage.reportsGenerated, limit: limits.reportsPerMonth };

        case 'add_social_account':
          if (limits.socialAccounts === null) {
            return { allowed: true }; // Unlimited
          }
          
          // Count current social accounts
          const { count: socialAccountCount } = await this.supabase
            .from('social_accounts')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', userId);

          if ((socialAccountCount || 0) >= limits.socialAccounts) {
            return {
              allowed: false,
              reason: 'Social account limit reached',
              currentUsage: socialAccountCount || 0,
              limit: limits.socialAccounts
            };
          }
          return { allowed: true, currentUsage: socialAccountCount || 0, limit: limits.socialAccounts };

        case 'add_user':
          if (limits.users === null) {
            return { allowed: true }; // Unlimited
          }
          
          // Count current team members
          const { count: teamMemberCount } = await this.supabase
            .from('team_members')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', userId)
            .eq('status', 'active');

          // Add 1 for the owner
          const totalUsers = (teamMemberCount || 0) + 1;
          
          if (totalUsers >= limits.users) {
            return {
              allowed: false,
              reason: 'User limit reached',
              currentUsage: totalUsers,
              limit: limits.users
            };
          }
          return { allowed: true, currentUsage: totalUsers, limit: limits.users };

        default:
          return { allowed: true };
      }
    } catch (error) {
      console.error('Failed to check action permission:', error);
      return { allowed: false, reason: 'Failed to check permissions' };
    }
  }

  /**
   * Track usage for a specific action
   */
  async trackUsage(
    userId: string,
    action: 'create_post' | 'schedule_post' | 'api_call' | 'generate_ai' | 'generate_report',
    amount: number = 1
  ): Promise<void> {
    const monthYear = this.getCurrentMonthYear();

    try {
      // Get current usage or create new record
      const { data: existingUsage } = await this.supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('month_year', monthYear)
        .single();

      const updateData: any = {};

      switch (action) {
        case 'create_post':
          updateData.posts_created = (existingUsage?.posts_created || 0) + amount;
          break;
        case 'schedule_post':
          updateData.posts_scheduled = (existingUsage?.posts_scheduled || 0) + amount;
          break;
        case 'api_call':
          updateData.api_calls = (existingUsage?.api_calls || 0) + amount;
          break;
        case 'generate_ai':
          updateData.ai_generations_used = (existingUsage?.ai_generations_used || 0) + amount;
          break;
        case 'generate_report':
          updateData.reports_generated = (existingUsage?.reports_generated || 0) + amount;
          break;
      }

      if (existingUsage) {
        // Update existing record
        const { error } = await this.supabase
          .from('usage_tracking')
          .update(updateData)
          .eq('user_id', userId)
          .eq('month_year', monthYear);

        if (error) {
          console.error('Failed to update usage:', error);
          throw error;
        }
      } else {
        // Create new record
        const { error } = await this.supabase
          .from('usage_tracking')
          .insert({
            user_id: userId,
            month_year: monthYear,
            ...updateData,
          });

        if (error) {
          console.error('Failed to create usage record:', error);
          throw error;
        }
      }
    } catch (error) {
      console.error('Failed to track usage:', error);
      throw error;
    }
  }

  /**
   * Get usage history for a user
   */
  async getUsageHistory(userId: string, months: number = 6): Promise<UsageData[]> {
    const monthYears = this.getLastNMonths(months);

    const { data, error } = await this.supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .in('month_year', monthYears)
      .order('month_year', { ascending: false });

    if (error) {
      console.error('Failed to get usage history:', error);
      throw error;
    }

    return (data || []).map(record => ({
      postsCreated: record.posts_created || 0,
      postsScheduled: record.posts_scheduled || 0,
      apiCalls: record.api_calls || 0,
      storageUsedMb: record.storage_used_mb || 0,
      aiGenerationsUsed: record.ai_generations_used || 0,
      reportsGenerated: record.reports_generated || 0,
    }));
  }

  /**
   * Reset usage for a new billing period
   */
  async resetUsageForNewPeriod(userId: string): Promise<void> {
    const monthYear = this.getCurrentMonthYear();

    // Create new usage record for the current month
    const { error } = await this.supabase
      .from('usage_tracking')
      .upsert({
        user_id: userId,
        month_year: monthYear,
        posts_created: 0,
        posts_scheduled: 0,
        api_calls: 0,
        storage_used_mb: 0,
        ai_generations_used: 0,
        reports_generated: 0,
      }, {
        onConflict: 'user_id,month_year'
      });

    if (error) {
      console.error('Failed to reset usage:', error);
      throw error;
    }
  }

  /**
   * Get current month-year string
   */
  private getCurrentMonthYear(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * Get last N months as month-year strings
   */
  private getLastNMonths(n: number): string[] {
    const months: string[] = [];
    const now = new Date();

    for (let i = 0; i < n; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      months.push(`${year}-${month}`);
    }

    return months;
  }

  /**
   * Check if user has exceeded any limits
   */
  async checkLimitExceeded(userId: string): Promise<{
    exceeded: boolean;
    limits: Array<{
      type: string;
      current: number;
      limit: number;
      percentage: number;
    }>;
  }> {
    const [usage, limits] = await Promise.all([
      this.getCurrentUsage(userId),
      this.getUserPlanLimits(userId)
    ]);

    const limitChecks = [];
    let exceeded = false;

    // Check posts limit
    if (limits.postsPerMonth !== null) {
      const totalPosts = usage.postsCreated + usage.postsScheduled;
      const percentage = (totalPosts / limits.postsPerMonth) * 100;
      limitChecks.push({
        type: 'posts',
        current: totalPosts,
        limit: limits.postsPerMonth,
        percentage
      });
      if (percentage >= 100) exceeded = true;
    }

    // Check AI generations limit
    if (limits.aiGenerations !== null) {
      const percentage = (usage.aiGenerationsUsed / limits.aiGenerations) * 100;
      limitChecks.push({
        type: 'ai_generations',
        current: usage.aiGenerationsUsed,
        limit: limits.aiGenerations,
        percentage
      });
      if (percentage >= 100) exceeded = true;
    }

    // Check reports limit
    if (limits.reportsPerMonth !== null) {
      const percentage = (usage.reportsGenerated / limits.reportsPerMonth) * 100;
      limitChecks.push({
        type: 'reports',
        current: usage.reportsGenerated,
        limit: limits.reportsPerMonth,
        percentage
      });
      if (percentage >= 100) exceeded = true;
    }

    return {
      exceeded,
      limits: limitChecks
    };
  }
}
