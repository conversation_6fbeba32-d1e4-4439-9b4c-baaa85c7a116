{"name": "ewasl-app", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "mcpServers": {"browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"]}}, "scripts": {"dev": "next dev", "build": "next build", "build:production": "npm ci --production=false && next build", "build:do": "npm ci --production=false --prefer-offline --no-audit && next build", "start": "next start -p ${PORT:-3000}", "postbuild": "echo 'Build completed successfully'", "db:generate": "echo 'Using Supabase - Prisma generation not needed'", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:teams": "jest --config=jest.config.teams.js", "test:teams:watch": "jest --config=jest.config.teams.js --watch", "test:teams:coverage": "jest --config=jest.config.teams.js --coverage", "test:teams:ci": "jest --config=jest.config.teams.js --ci --coverage --watchAll=false", "test:e2e": "playwright test src/__tests__/teams/e2e.test.ts", "test:e2e:headed": "playwright test src/__tests__/teams/e2e.test.ts --headed", "test:performance": "jest --config=jest.config.teams.js --testPathPattern=performance", "test:all": "npm run test:teams && npm run test:e2e", "test:all:ci": "npm run test:teams:ci && npm run test:e2e", "analyze": "cross-env ANALYZE=true npm run build", "analyze:server": "cross-env BUNDLE_ANALYZE=server npm run build", "analyze:browser": "cross-env BUNDLE_ANALYZE=browser npm run build", "db:push": "echo 'Using Supabase - database managed via Supabase Dashboard'", "db:seed": "echo 'Using Supabase - seeding managed via Supabase'", "db:setup": "echo 'Using Supabase - setup managed via Supabase Dashboard'", "test:enhanced-integration": "tsx src/scripts/test-enhanced-integrations.ts", "test:enhanced-integration:watch": "tsx watch src/scripts/test-enhanced-integrations.ts", "db:enhanced-schema": "psql $DATABASE_URL -f src/lib/database/enhanced-integration-schema.sql"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.9.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.3.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-table": "^8.21.3", "@types/react-big-calendar": "^1.16.1", "@types/sharp": "^0.31.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "facebook-nodejs-business-sdk": "^22.0.3", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.3.0", "lucide-react": "^0.511.0", "mime-types": "^2.1.35", "moment": "^2.30.1", "next": "^15.3.2", "node-fetch": "^2.7.0", "openai": "^4.103.0", "pg": "^8.16.0", "react": "^18.2.0", "react-big-calendar": "^1.18.0", "react-day-picker": "^9.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "recharts": "^2.15.3", "sharp": "^0.33.5", "sonner": "^1.7.1", "stripe": "^18.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "twitter-api-v2": "^1.23.2", "web-vitals": "^4.2.4", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^14.2.29", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/fluent-ffmpeg": "^2.1.27", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/stripe": "^8.0.416", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5"}}