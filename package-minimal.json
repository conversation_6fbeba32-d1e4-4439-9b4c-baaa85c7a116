{"name": "ewasl-app", "version": "0.1.0", "private": true, "engines": {"node": "18.x", "npm": "8.x"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p ${PORT:-3000}", "postbuild": "echo 'Build completed successfully'", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "lucide-react": "^0.511.0", "next": "^15.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8.5.3", "typescript": "^5"}}