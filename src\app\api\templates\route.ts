import { NextRequest, NextResponse } from 'next/server';
import { supabaseServiceRole as supabase } from '@/lib/supabase/service-role';

// GET /api/templates - Get templates with filtering and search
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const platform = searchParams.get('platform');
    const language = searchParams.get('language') || 'ar';
    const tone = searchParams.get('tone');
    const industry = searchParams.get('industry');
    const search = searchParams.get('search');
    const featured = searchParams.get('featured');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    let query = supabase
      .from('content_templates')
      .select(`
        *,
        template_categories!inner(name, name_ar, icon)
      `)
      .eq('is_public', true)
      .eq('language', language);

    // Apply filters
    if (category) {
      query = query.eq('category', category);
    }

    if (platform) {
      query = query.contains('platform_compatibility', [platform]);
    }

    if (tone) {
      query = query.eq('tone', tone);
    }

    if (industry) {
      query = query.contains('industry_tags', [industry]);
    }

    if (featured === 'true') {
      query = query.eq('is_featured', true);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: templates, error } = await query;

    if (error) {
      console.error('Error fetching templates:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch templates' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('content_templates')
      .select('*', { count: 'exact', head: true })
      .eq('is_public', true)
      .eq('language', language);

    if (category) countQuery = countQuery.eq('category', category);
    if (platform) countQuery = countQuery.contains('platform_compatibility', [platform]);
    if (tone) countQuery = countQuery.eq('tone', tone);
    if (industry) countQuery = countQuery.contains('industry_tags', [industry]);
    if (featured === 'true') countQuery = countQuery.eq('is_featured', true);
    if (search) countQuery = countQuery.or(`name.ilike.%${search}%,description.ilike.%${search}%`);

    const { count } = await countQuery;

    return NextResponse.json({
      success: true,
      templates,
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (offset + limit) < (count || 0)
      }
    });

  } catch (error) {
    console.error('Templates API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/templates - Create new template
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      category,
      platform_compatibility,
      language = 'ar',
      content_body,
      variables = [],
      hashtags = [],
      tone = 'friendly',
      industry_tags = [],
      is_public = true,
      metadata = {}
    } = body;

    // Validate required fields
    if (!name || !content_body || !category) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: name, content_body, category' },
        { status: 400 }
      );
    }

    // For now, we'll use a dummy user ID since we don't have auth implemented
    const dummyUserId = '00000000-0000-0000-0000-000000000000';

    const { data: template, error } = await supabase
      .from('content_templates')
      .insert({
        name,
        description,
        category,
        platform_compatibility,
        language,
        content_body,
        variables,
        hashtags,
        tone,
        industry_tags,
        is_public,
        metadata,
        created_by: dummyUserId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating template:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create template' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      template
    });

  } catch (error) {
    console.error('Template creation error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
