// This component forces Tailwind to include all classes used in the app
// It should never be rendered, but ensures all classes are in the final CSS

export function ForceTailwindClasses() {
  return (
    <div className="hidden">
      {/* Force all gradient classes */}
      <div className="bg-gradient-to-br from-blue-50/30 via-white to-purple-50/30" />
      <div className="bg-gradient-to-r from-blue-600 to-purple-600" />
      <div className="bg-gradient-to-r from-blue-700 to-purple-700" />
      <div className="bg-gradient-to-r from-emerald-50 to-teal-50" />
      <div className="bg-gradient-to-r from-emerald-500 to-teal-500" />
      
      {/* Force all shadow classes */}
      <div className="shadow-lg hover:shadow-xl" />
      <div className="shadow-sm" />
      
      {/* Force all backdrop classes */}
      <div className="backdrop-blur-sm bg-white/70" />
      <div className="bg-white/50" />
      
      {/* Force all border radius classes */}
      <div className="rounded-2xl rounded-xl rounded-lg rounded-full" />
      
      {/* Force all color classes */}
      <div className="text-blue-600 text-purple-600 text-emerald-600 text-orange-600 text-red-600" />
      <div className="bg-blue-500/10 bg-emerald-500/10 bg-purple-500/10 bg-orange-500/10 bg-red-500/10" />
      <div className="border-blue-200/50 border-emerald-200/50 border-purple-200/50 border-orange-200/50 border-red-200/50" />
      
      {/* Force all transition classes */}
      <div className="transition-all duration-300 hover:-translate-y-1" />
      <div className="transition-shadow duration-200" />
      
      {/* Force all text gradient classes */}
      <div className="bg-clip-text text-transparent" />
      
      {/* Force all hover effects */}
      <div className="hover:border-blue-300 hover:border-emerald-300" />
      <div className="hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/30" />
      <div className="hover:from-emerald-50/50 hover:to-teal-50/30" />
      
      {/* Force all trend indicator classes */}
      <div className="text-emerald-700 bg-emerald-100 text-red-700 bg-red-100" />
      
      {/* Force all spacing classes */}
      <div className="p-6 p-4 p-3 p-2 px-4 py-2 px-2 py-1" />
      <div className="gap-6 gap-4 gap-3 gap-2 gap-1" />
      <div className="space-y-8 space-y-6 space-y-4 space-y-3 space-y-2" />
      
      {/* Force all grid classes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 lg:grid-cols-7" />
      
      {/* Force all flex classes */}
      <div className="flex items-center justify-between items-start items-baseline" />
      
      {/* Force all text classes */}
      <div className="text-3xl text-xl text-lg text-sm text-xs font-bold font-semibold font-medium" />
      <div className="leading-relaxed tracking-tight" />
    </div>
  );
}
