// Mock data for development purposes

export const mockUsers = [
  {
    id: "1",
    name: "أحمد محمد",
    email: "<EMAIL>",
    role: "ADMIN",
    image: "https://github.com/shadcn.png",
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
  },
  {
    id: "2",
    name: "سارة أحمد",
    email: "<EMAIL>",
    role: "USER",
    image: null,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15), // 15 days ago
  },
];

export const mockSocialAccounts = [
  {
    id: "1",
    userId: "1",
    platform: "TWITTER",
    accountId: "12345",
    accountName: "@ewasl_app",
    accessToken: "mock-token",
    expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30), // 30 days from now
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10), // 10 days ago
    isConnected: true,
    isActive: true,
    followers: 5200,
    avatar: "https://github.com/shadcn.png",
  },
  {
    id: "2",
    userId: "1",
    platform: "INSTAGRAM",
    accountId: "67890",
    accountName: "@ewasl_app",
    accessToken: "mock-token",
    expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 60), // 60 days from now
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5), // 5 days ago
    isConnected: true,
    isActive: true,
    followers: 7300,
    avatar: "https://github.com/shadcn.png",
  },
];

export const mockPosts = [
  {
    id: "1",
    userId: "1",
    content: "أهلاً بكم في منصة eWasl لإدارة وسائل التواصل الاجتماعي!",
    mediaUrl: null,
    status: "PUBLISHED",
    scheduledAt: null,
    publishedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
    platforms: ["TWITTER", "FACEBOOK"],
  },
  {
    id: "2",
    userId: "1",
    content: "نحن نعمل على تحسين المنصة لتوفير تجربة أفضل للمستخدمين.",
    mediaUrl: null,
    status: "SCHEDULED",
    scheduledAt: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1 day from now
    publishedAt: null,
    createdAt: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    platforms: ["TWITTER", "INSTAGRAM"],
  },
  {
    id: "3",
    userId: "1",
    content: "هذا منشور تجريبي لاختبار واجهة المستخدم.",
    mediaUrl: null,
    status: "DRAFT",
    scheduledAt: null,
    publishedAt: null,
    createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    platforms: ["FACEBOOK"],
  },
  {
    id: "4",
    userId: "1",
    content: "استخدم الذكاء الاصطناعي لإنشاء محتوى جذاب لوسائل التواصل الاجتماعي.",
    mediaUrl: null,
    status: "FAILED",
    scheduledAt: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
    publishedAt: null,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 7), // 7 hours ago
    platforms: ["LINKEDIN"],
  },
];

export const mockActivities = [
  {
    id: "1",
    userId: "1",
    postId: "1",
    action: "POST_PUBLISHED",
    details: "تم نشر المنشور بنجاح",
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
  },
  {
    id: "2",
    userId: "1",
    postId: "2",
    action: "POST_SCHEDULED",
    details: "تم جدولة المنشور",
    createdAt: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
  },
  {
    id: "3",
    userId: "1",
    postId: null,
    action: "CONNECT_SOCIAL",
    details: "تم ربط حساب تويتر",
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10), // 10 days ago
  },
  {
    id: "4",
    userId: "1",
    postId: "4",
    action: "POST_FAILED",
    details: "فشل نشر المنشور: خطأ في الاتصال",
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
  },
];

export const mockEngagementData = [
  { date: "الأحد", likes: 120, comments: 45, shares: 20, views: 500 },
  { date: "الإثنين", likes: 150, comments: 60, shares: 30, views: 600 },
  { date: "الثلاثاء", likes: 200, comments: 80, shares: 40, views: 700 },
  { date: "الأربعاء", likes: 180, comments: 70, shares: 35, views: 650 },
  { date: "الخميس", likes: 250, comments: 90, shares: 50, views: 800 },
  { date: "الجمعة", likes: 300, comments: 100, shares: 60, views: 900 },
  { date: "السبت", likes: 280, comments: 95, shares: 55, views: 850 },
];

export const mockPlatformData = [
  { name: "TWITTER", value: 45, color: "#1DA1F2" },
  { name: "FACEBOOK", value: 30, color: "#4267B2" },
  { name: "INSTAGRAM", value: 20, color: "#C13584" },
  { name: "LINKEDIN", value: 5, color: "#0077B5" },
];
