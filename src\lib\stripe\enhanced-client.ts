/**
 * Enhanced Stripe Client
 * Provides utilities for Stripe payment processing with error handling
 */

import Stripe from 'stripe';

interface StripeConfig {
  secretKey: string;
  publishableKey?: string;
  webhookSecret?: string;
  apiVersion?: string;
}

interface StripeResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
}

export class EnhancedStripeClient {
  private stripe: Stripe;
  private config: StripeConfig;

  constructor(config: StripeConfig) {
    this.config = config;
    this.stripe = new Stripe(config.secretKey, {
      apiVersion: (config.apiVersion as any) || '2023-10-16',
      typescript: true,
    });
  }

  /**
   * Handle Stripe errors consistently
   */
  private handleStripeError(error: any): StripeResponse {
    if (error.type === 'StripeCardError') {
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    } else if (error.type === 'StripeRateLimitError') {
      return {
        success: false,
        error: 'Too many requests made to the API too quickly',
        code: 'rate_limit'
      };
    } else if (error.type === 'StripeInvalidRequestError') {
      return {
        success: false,
        error: error.message,
        code: 'invalid_request'
      };
    } else if (error.type === 'StripeAPIError') {
      return {
        success: false,
        error: 'An error occurred with our API',
        code: 'api_error'
      };
    } else if (error.type === 'StripeConnectionError') {
      return {
        success: false,
        error: 'A network error occurred',
        code: 'connection_error'
      };
    } else if (error.type === 'StripeAuthenticationError') {
      return {
        success: false,
        error: 'Authentication with Stripe failed',
        code: 'authentication_error'
      };
    } else {
      return {
        success: false,
        error: error.message || 'An unknown error occurred',
        code: 'unknown_error'
      };
    }
  }

  /**
   * Get account balance
   */
  async getBalance(): Promise<StripeResponse<Stripe.Balance>> {
    try {
      const balance = await this.stripe.balance.retrieve();
      return {
        success: true,
        data: balance
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Get account information
   */
  async getAccount(): Promise<StripeResponse<Stripe.Account>> {
    try {
      const account = await this.stripe.accounts.retrieve();
      return {
        success: true,
        data: account
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Create a customer
   */
  async createCustomer(params: {
    email: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<StripeResponse<Stripe.Customer>> {
    try {
      const customer = await this.stripe.customers.create(params);
      return {
        success: true,
        data: customer
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomer(customerId: string): Promise<StripeResponse<Stripe.Customer>> {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);
      return {
        success: true,
        data: customer as Stripe.Customer
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Create a checkout session
   */
  async createCheckoutSession(params: {
    customerId?: string;
    priceId: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<StripeResponse<Stripe.Checkout.Session>> {
    try {
      const session = await this.stripe.checkout.sessions.create({
        customer: params.customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: params.priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: params.successUrl,
        cancel_url: params.cancelUrl,
        metadata: params.metadata,
      });

      return {
        success: true,
        data: session
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Create billing portal session
   */
  async createBillingPortalSession(params: {
    customerId: string;
    returnUrl: string;
  }): Promise<StripeResponse<Stripe.BillingPortal.Session>> {
    try {
      const session = await this.stripe.billingPortal.sessions.create({
        customer: params.customerId,
        return_url: params.returnUrl,
      });

      return {
        success: true,
        data: session
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * List customer subscriptions
   */
  async getCustomerSubscriptions(customerId: string): Promise<StripeResponse<Stripe.Subscription[]>> {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: 'all',
      });

      return {
        success: true,
        data: subscriptions.data
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<StripeResponse<Stripe.Subscription>> {
    try {
      const subscription = await this.stripe.subscriptions.cancel(subscriptionId);
      return {
        success: true,
        data: subscription
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * List products
   */
  async getProducts(): Promise<StripeResponse<Stripe.Product[]>> {
    try {
      const products = await this.stripe.products.list({
        active: true,
        limit: 100,
      });

      return {
        success: true,
        data: products.data
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * List prices
   */
  async getPrices(): Promise<StripeResponse<Stripe.Price[]>> {
    try {
      const prices = await this.stripe.prices.list({
        active: true,
        limit: 100,
      });

      return {
        success: true,
        data: prices.data
      };
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Health check - verify API connectivity
   */
  async healthCheck(): Promise<StripeResponse<{ healthy: boolean; account: Stripe.Account; balance: Stripe.Balance }>> {
    try {
      const [accountResponse, balanceResponse] = await Promise.all([
        this.getAccount(),
        this.getBalance()
      ]);

      if (accountResponse.success && balanceResponse.success) {
        return {
          success: true,
          data: {
            healthy: true,
            account: accountResponse.data!,
            balance: balanceResponse.data!
          }
        };
      } else {
        return {
          success: false,
          error: accountResponse.error || balanceResponse.error || 'Health check failed'
        };
      }
    } catch (error) {
      return this.handleStripeError(error);
    }
  }

  /**
   * Get raw Stripe instance for advanced operations
   */
  getStripeInstance(): Stripe {
    return this.stripe;
  }
}

/**
 * Create enhanced Stripe client instance
 */
export function createEnhancedStripeClient(): EnhancedStripeClient {
  const secretKey = process.env.STRIPE_SECRET_KEY;
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  
  if (!secretKey) {
    throw new Error('STRIPE_SECRET_KEY environment variable is required');
  }

  return new EnhancedStripeClient({
    secretKey,
    publishableKey,
    webhookSecret,
  });
}

/**
 * Default export for convenience
 */
export default EnhancedStripeClient;
