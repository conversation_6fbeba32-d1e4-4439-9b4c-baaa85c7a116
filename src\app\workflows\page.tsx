'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  GitBranch, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Plus,
  Settings,
  Users,
  FileText
} from 'lucide-react';

interface ApprovalRequest {
  id: string;
  title: string;
  content_type: string;
  priority: string;
  status: string;
  current_step: number;
  deadline_at: string;
  requester: {
    email: string;
    user_metadata?: {
      full_name?: string;
    };
  };
  current_approvers_info: Array<{
    id: string;
    email: string;
    user_metadata?: {
      full_name?: string;
    };
  }>;
  is_assigned_to_user: boolean;
  can_approve: boolean;
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  is_default: boolean;
  is_active: boolean;
  steps: any[];
  usage_count: number;
}

export default function WorkflowsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [approvalRequests, setApprovalRequests] = useState<ApprovalRequest[]>([]);
  const [workflowTemplates, setWorkflowTemplates] = useState<WorkflowTemplate[]>([]);
  const [currentWorkspace, setCurrentWorkspace] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'pending' | 'templates'>('pending');

  useEffect(() => {
    loadWorkflowData();
  }, []);

  const loadWorkflowData = async () => {
    setIsLoading(true);
    try {
      // Get current workspace
      const workspacesResponse = await fetch('/api/workspaces');
      const workspacesData = await workspacesResponse.json();
      
      if (workspacesData.success && workspacesData.workspaces.length > 0) {
        const workspace = workspacesData.workspaces[0];
        setCurrentWorkspace(workspace);
        
        // Load approval requests
        const approvalsResponse = await fetch(`/api/workflows/approvals?workspace_id=${workspace.id}`);
        const approvalsData = await approvalsResponse.json();
        
        if (approvalsData.success) {
          setApprovalRequests(approvalsData.requests);
        }

        // Load workflow templates
        const templatesResponse = await fetch(`/api/workflows/templates?workspace_id=${workspace.id}`);
        const templatesData = await templatesResponse.json();
        
        if (templatesData.success) {
          setWorkflowTemplates(templatesData.templates);
        }
      }
    } catch (error) {
      console.error('Error loading workflow data:', error);
      toast.error('فشل في تحميل بيانات سير العمل');
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprovalAction = async (requestId: string, action: 'approved' | 'rejected', comments?: string) => {
    try {
      const response = await fetch(`/api/workflows/approvals?id=${requestId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          comments,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`تم ${action === 'approved' ? 'الموافقة على' : 'رفض'} الطلب بنجاح`);
        loadWorkflowData(); // Reload data
      } else {
        toast.error(data.error || 'فشل في معالجة الطلب');
      }
    } catch (error) {
      console.error('Error processing approval:', error);
      toast.error('حدث خطأ أثناء معالجة الطلب');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'in_review':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'approved':
        return 'موافق عليه';
      case 'rejected':
        return 'مرفوض';
      case 'in_review':
        return 'قيد المراجعة';
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'عاجل';
      case 'high':
        return 'عالي';
      case 'medium':
        return 'متوسط';
      case 'low':
        return 'منخفض';
      default:
        return priority;
    }
  };

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
        direction: 'rtl',
        fontFamily: 'Inter, sans-serif',
        padding: '2rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          textAlign: 'center'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #e2e8f0',
            borderTop: '4px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>جاري تحميل سير العمل...</p>
        </div>
      </div>
    );
  }

  const pendingRequests = approvalRequests.filter(r => r.status === 'pending');
  const myPendingRequests = pendingRequests.filter(r => r.is_assigned_to_user);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif',
      padding: '2rem'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '0.5rem'
            }}>
              🔄 سير العمل والموافقات
            </h1>
            <p style={{ color: '#64748b' }}>
              إدارة طلبات الموافقة وقوالب سير العمل
            </p>
          </div>
          
          <Button
            onClick={() => setActiveTab(activeTab === 'pending' ? 'templates' : 'pending')}
            style={{
              background: 'linear-gradient(to right, #10b981, #059669)',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.5rem',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            {activeTab === 'pending' ? <Settings className="h-4 w-4" /> : <FileText className="h-4 w-4" />}
            {activeTab === 'pending' ? 'إدارة القوالب' : 'طلبات الموافقة'}
          </Button>
        </div>

        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          <Card>
            <CardHeader style={{ paddingBottom: '0.5rem' }}>
              <CardTitle style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '1rem' }}>
                <Clock className="h-5 w-5 text-yellow-500" />
                طلبات الموافقة المعلقة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b' }}>
                {pendingRequests.length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader style={{ paddingBottom: '0.5rem' }}>
              <CardTitle style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '1rem' }}>
                <Users className="h-5 w-5 text-blue-500" />
                المطلوب موافقتي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6' }}>
                {myPendingRequests.length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader style={{ paddingBottom: '0.5rem' }}>
              <CardTitle style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '1rem' }}>
                <GitBranch className="h-5 w-5 text-green-500" />
                قوالب سير العمل
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981' }}>
                {workflowTemplates.length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'pending' ? (
          <Card>
            <CardHeader>
              <CardTitle>طلبات الموافقة ({approvalRequests.length})</CardTitle>
              <CardDescription>
                إدارة طلبات الموافقة والمراجعة
              </CardDescription>
            </CardHeader>
            <CardContent>
              {approvalRequests.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '3rem',
                  color: '#64748b'
                }}>
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>لا توجد طلبات موافقة حالياً</p>
                </div>
              ) : (
                <div style={{
                  display: 'grid',
                  gap: '1rem'
                }}>
                  {approvalRequests.map((request) => (
                    <div
                      key={request.id}
                      style={{
                        padding: '1rem',
                        background: request.is_assigned_to_user ? '#eff6ff' : '#f8fafc',
                        borderRadius: '0.5rem',
                        border: `1px solid ${request.is_assigned_to_user ? '#bfdbfe' : '#e2e8f0'}`
                      }}
                    >
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        marginBottom: '1rem'
                      }}>
                        <div style={{ flex: 1 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            marginBottom: '0.5rem'
                          }}>
                            <h3 style={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                              {request.title}
                            </h3>
                            <Badge className={getPriorityColor(request.priority)}>
                              {getPriorityText(request.priority)}
                            </Badge>
                          </div>
                          
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '1rem',
                            fontSize: '0.875rem',
                            color: '#64748b',
                            marginBottom: '0.5rem'
                          }}>
                            <span>
                              بواسطة: {request.requester?.user_metadata?.full_name || request.requester?.email}
                            </span>
                            <span>
                              النوع: {request.content_type}
                            </span>
                            <span>
                              الخطوة: {request.current_step}
                            </span>
                          </div>

                          {request.deadline_at && (
                            <div style={{
                              fontSize: '0.875rem',
                              color: '#64748b'
                            }}>
                              الموعد النهائي: {new Date(request.deadline_at).toLocaleString('ar')}
                            </div>
                          )}
                        </div>

                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem'
                        }}>
                          <Badge
                            variant="outline"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.25rem'
                            }}
                          >
                            {getStatusIcon(request.status)}
                            {getStatusText(request.status)}
                          </Badge>

                          {request.can_approve && (
                            <div style={{ display: 'flex', gap: '0.5rem' }}>
                              <Button
                                size="sm"
                                onClick={() => handleApprovalAction(request.id, 'approved')}
                                style={{
                                  background: '#10b981',
                                  color: 'white',
                                  border: 'none',
                                  fontSize: '0.875rem'
                                }}
                              >
                                موافقة
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleApprovalAction(request.id, 'rejected')}
                                style={{
                                  color: '#ef4444',
                                  borderColor: '#ef4444',
                                  fontSize: '0.875rem'
                                }}
                              >
                                رفض
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>

                      {request.current_approvers_info.length > 0 && (
                        <div style={{
                          fontSize: '0.875rem',
                          color: '#64748b'
                        }}>
                          المراجعون الحاليون: {request.current_approvers_info.map(approver => 
                            approver.user_metadata?.full_name || approver.email
                          ).join(', ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>قوالب سير العمل ({workflowTemplates.length})</CardTitle>
              <CardDescription>
                إدارة قوالب سير العمل والموافقات
              </CardDescription>
            </CardHeader>
            <CardContent>
              {workflowTemplates.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '3rem',
                  color: '#64748b'
                }}>
                  <GitBranch className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>لا توجد قوالب سير عمل</p>
                </div>
              ) : (
                <div style={{
                  display: 'grid',
                  gap: '1rem'
                }}>
                  {workflowTemplates.map((template) => (
                    <div
                      key={template.id}
                      style={{
                        padding: '1rem',
                        background: template.is_default ? '#f0fdf4' : '#f8fafc',
                        borderRadius: '0.5rem',
                        border: `1px solid ${template.is_default ? '#bbf7d0' : '#e2e8f0'}`
                      }}
                    >
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}>
                        <div>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            marginBottom: '0.5rem'
                          }}>
                            <h3 style={{ fontWeight: 'bold' }}>{template.name}</h3>
                            {template.is_default && (
                              <Badge style={{ background: '#10b981', color: 'white' }}>
                                افتراضي
                              </Badge>
                            )}
                            {!template.is_active && (
                              <Badge variant="outline" style={{ color: '#64748b' }}>
                                غير نشط
                              </Badge>
                            )}
                          </div>
                          
                          {template.description && (
                            <p style={{ color: '#64748b', fontSize: '0.875rem', marginBottom: '0.5rem' }}>
                              {template.description}
                            </p>
                          )}
                          
                          <div style={{
                            display: 'flex',
                            gap: '1rem',
                            fontSize: '0.875rem',
                            color: '#64748b'
                          }}>
                            <span>الخطوات: {template.steps.length}</span>
                            <span>مرات الاستخدام: {template.usage_count}</span>
                          </div>
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem'
                          }}
                        >
                          <Settings className="h-4 w-4" />
                          تحرير
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
