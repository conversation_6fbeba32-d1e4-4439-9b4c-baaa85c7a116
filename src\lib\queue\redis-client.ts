/**
 * Redis Client Stub
 * Redis functionality removed - using in-memory job queue instead
 */

// Stub implementation - Redis functionality removed
class RedisClient {
  /**
   * Get Redis client instance (always returns null)
   */
  static getInstance(): null {
    console.log('Redis not available - using in-memory job queue');
    return null;
  }

  /**
   * Check if Redis is connected (always false)
   */
  static isRedisConnected(): boolean {
    return false;
  }

  /**
   * Test Redis connection (always false)
   */
  static async testConnection(): Promise<boolean> {
    return false;
  }

  /**
   * Close Redis connection (no-op)
   */
  static async disconnect(): Promise<void> {
    // No-op
  }

  /**
   * Get Redis info for monitoring (returns null)
   */
  static async getRedisInfo(): Promise<any> {
    return {
      connected: false,
      info: {
        version: 'not-available',
        mode: 'disabled',
        uptime: '0',
      },
      memory: {
        used: '0',
        peak: '0',
      },
      stats: {
        connections: '0',
        commands: '0',
      }
    };
  }
}

export default RedisClient;
