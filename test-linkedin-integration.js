#!/usr/bin/env node

/**
 * LinkedIn Integration Testing Suite
 * Comprehensive testing of LinkedIn Company Pages integration
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-LinkedIn-Tester/1.0',
        ...options.headers
      },
      timeout: 15000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function testLinkedInBusinessAccountsEndpoint() {
  console.log('🔗 TESTING LINKEDIN BUSINESS ACCOUNTS ENDPOINT...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/business-accounts?platform=linkedin&userId=test-user`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ LinkedIn business accounts endpoint working');
      
      if (response.data) {
        console.log('📊 Response structure:');
        console.log(`  Platform: ${response.data.platform}`);
        console.log(`  Is Configured: ${response.data.isConfigured}`);
        console.log(`  Has Business Accounts: ${response.data.hasBusinessAccounts}`);
        console.log(`  Business Accounts Count: ${response.data.businessAccounts?.length || 0}`);
        console.log(`  Selected Account: ${response.data.selectedAccount ? 'Yes' : 'No'}`);
        console.log(`  Requires Reconnection: ${response.data.requiresReconnection}`);
        console.log(`  Missing Permissions: ${response.data.missingPermissions?.join(', ') || 'None'}`);
        
        if (response.data.businessAccounts && response.data.businessAccounts.length > 0) {
          console.log('\n📋 LinkedIn Companies Found:');
          response.data.businessAccounts.forEach((company, index) => {
            console.log(`  ${index + 1}. ${company.name}`);
            console.log(`     ID: ${company.id}`);
            console.log(`     Type: ${company.type}`);
            console.log(`     Platform: ${company.platform}`);
            console.log(`     Selected: ${company.isSelected ? 'Yes' : 'No'}`);
            console.log(`     Followers: ${company.followerCount || 'N/A'}`);
            console.log(`     Category: ${company.category || 'N/A'}`);
            if (company.metadata) {
              console.log(`     Industry: ${company.metadata.industry || 'N/A'}`);
              console.log(`     Employee Count: ${company.metadata.employeeCount || 'N/A'}`);
              console.log(`     Website: ${company.metadata.website || 'N/A'}`);
            }
            console.log('');
          });
        }
      }
      
      return { status: 'PASS', data: response.data };
    } else if (response.status === 401) {
      console.log('🔐 Authentication required (expected for production)');
      return { status: 'AUTH_REQUIRED', httpStatus: response.status };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testLinkedInRefreshEndpoint() {
  console.log('\n🔄 TESTING LINKEDIN REFRESH ENDPOINT...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/business-accounts/refresh`, {
      method: 'POST',
      body: {
        platform: 'linkedin',
        userId: 'test-user'
      }
    });
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ LinkedIn refresh endpoint working');
      
      if (response.data) {
        console.log('📊 Refresh response:');
        console.log(`  Success: ${response.data.success}`);
        console.log(`  Message: ${response.data.message}`);
        console.log(`  Platform: ${response.data.platform}`);
        console.log(`  Business Accounts Count: ${response.data.businessAccounts?.length || 0}`);
      }
      
      return { status: 'PASS', data: response.data };
    } else if (response.status === 401) {
      console.log('🔐 Authentication required (expected for production)');
      return { status: 'AUTH_REQUIRED', httpStatus: response.status };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testLinkedInSelectionEndpoint() {
  console.log('\n🎯 TESTING LINKEDIN SELECTION ENDPOINT...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/business-accounts/select`, {
      method: 'POST',
      body: {
        platform: 'linkedin',
        userId: 'test-user',
        businessAccountId: 'test-company-id'
      }
    });
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ LinkedIn selection endpoint working');
      
      if (response.data) {
        console.log('📊 Selection response:');
        console.log(`  Success: ${response.data.success}`);
        console.log(`  Message: ${response.data.message}`);
        console.log(`  Platform: ${response.data.platform}`);
        console.log(`  Selected Account: ${response.data.selectedAccount ? 'Yes' : 'No'}`);
      }
      
      return { status: 'PASS', data: response.data };
    } else if (response.status === 401) {
      console.log('🔐 Authentication required (expected for production)');
      return { status: 'AUTH_REQUIRED', httpStatus: response.status };
    } else if (response.status === 400) {
      console.log('⚠️  Validation error (expected for test data)');
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'VALIDATION_ERROR', httpStatus: response.status };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testLinkedInConfigValidation() {
  console.log('\n⚙️ TESTING LINKEDIN CONFIG VALIDATION...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/config/validate`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Config validation endpoint working');
      
      if (response.data.platforms && response.data.platforms.LINKEDIN) {
        const linkedinConfig = response.data.platforms.LINKEDIN;
        console.log('📊 LinkedIn Configuration:');
        console.log(`  Configured: ${linkedinConfig.configured ? 'Yes' : 'No'}`);
        console.log(`  Missing Env Vars: ${linkedinConfig.missingEnvVars?.join(', ') || 'None'}`);
        console.log(`  Client ID Available: ${linkedinConfig.clientId ? 'Yes' : 'No'}`);
        console.log(`  Client Secret Available: ${linkedinConfig.clientSecret ? 'Yes' : 'No'}`);
        
        return { 
          status: 'PASS', 
          configured: linkedinConfig.configured,
          missingVars: linkedinConfig.missingEnvVars || []
        };
      } else {
        console.log('❌ LinkedIn configuration not found in response');
        return { status: 'FAIL', issue: 'linkedin_config_missing' };
      }
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testLinkedInOAuthCallback() {
  console.log('\n🔗 TESTING LINKEDIN OAUTH CALLBACK...\n');
  
  try {
    const callbackUrl = `${BASE_URL}/api/social/callback/linkedin`;
    const response = await makeRequest(callbackUrl);
    
    console.log(`Status: ${response.status}`);
    
    // We expect 307 (redirect) or 400 (missing params), not 404
    if (response.status === 404) {
      console.log('❌ LinkedIn callback endpoint not found');
      return { status: 'FAIL', issue: 'callback_not_found' };
    } else if ([307, 302, 400, 401].includes(response.status)) {
      console.log('✅ LinkedIn callback endpoint exists');
      console.log(`  Response: ${response.status} (expected for missing parameters)`);
      return { status: 'PASS', httpStatus: response.status };
    } else {
      console.log(`⚠️  Unexpected callback response: ${response.status}`);
      return { status: 'WARN', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testLinkedInConnectEndpoint() {
  console.log('\n🔌 TESTING LINKEDIN CONNECT ENDPOINT...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/connect`, {
      method: 'POST',
      body: { platform: 'linkedin' }
    });
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('🔐 Authentication required (expected for production)');
      return { status: 'AUTH_REQUIRED', httpStatus: response.status };
    } else if (response.status === 200 && response.data && response.data.authUrl) {
      console.log('✅ LinkedIn connect endpoint working');
      console.log(`  Auth URL generated: ${response.data.authUrl.substring(0, 100)}...`);
      
      // Check if callback URL is consistent
      const expectedCallback = `${BASE_URL}/api/social/callback/linkedin`;
      if (response.data.authUrl.includes(encodeURIComponent(expectedCallback))) {
        console.log('✅ Callback URL consistent in auth URL');
        return { status: 'PASS', authUrl: response.data.authUrl };
      } else {
        console.log('⚠️  Callback URL may be inconsistent');
        return { status: 'WARN', authUrl: response.data.authUrl };
      }
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function runLinkedInIntegrationTests() {
  console.log('🔗 LINKEDIN INTEGRATION COMPREHENSIVE TESTING');
  console.log('=' .repeat(80));
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  const results = {
    businessAccounts: null,
    refresh: null,
    selection: null,
    configValidation: null,
    oauthCallback: null,
    connect: null,
    summary: { total: 0, passed: 0, authRequired: 0, failed: 0, warnings: 0 }
  };
  
  // Run all LinkedIn integration tests
  results.businessAccounts = await testLinkedInBusinessAccountsEndpoint();
  results.refresh = await testLinkedInRefreshEndpoint();
  results.selection = await testLinkedInSelectionEndpoint();
  results.configValidation = await testLinkedInConfigValidation();
  results.oauthCallback = await testLinkedInOAuthCallback();
  results.connect = await testLinkedInConnectEndpoint();
  
  // Calculate summary
  const allResults = Object.values(results).filter(r => r && typeof r === 'object' && r.status);
  
  allResults.forEach(result => {
    results.summary.total++;
    switch (result.status) {
      case 'PASS':
        results.summary.passed++;
        break;
      case 'AUTH_REQUIRED':
      case 'VALIDATION_ERROR':
        results.summary.authRequired++;
        break;
      case 'WARN':
        results.summary.warnings++;
        break;
      case 'FAIL':
        results.summary.failed++;
        break;
    }
  });
  
  // Generate final report
  console.log('\n📊 LINKEDIN INTEGRATION TEST SUMMARY');
  console.log('=' .repeat(80));
  console.log(`✅ Passed: ${results.summary.passed}`);
  console.log(`🔐 Auth Required: ${results.summary.authRequired}`);
  console.log(`⚠️  Warnings: ${results.summary.warnings}`);
  console.log(`❌ Failed: ${results.summary.failed}`);
  console.log(`📈 Success Rate: ${((results.summary.passed / results.summary.total) * 100).toFixed(1)}%`);
  
  // Detailed results
  console.log('\n📋 DETAILED TEST RESULTS:');
  console.log(`🏢 Business Accounts Endpoint: ${results.businessAccounts.status}`);
  console.log(`🔄 Refresh Endpoint: ${results.refresh.status}`);
  console.log(`🎯 Selection Endpoint: ${results.selection.status}`);
  console.log(`⚙️  Config Validation: ${results.configValidation.status}`);
  console.log(`🔗 OAuth Callback: ${results.oauthCallback.status}`);
  console.log(`🔌 Connect Endpoint: ${results.connect.status}`);
  
  // Configuration analysis
  if (results.configValidation.status === 'PASS') {
    console.log('\n⚙️ LINKEDIN CONFIGURATION ANALYSIS:');
    console.log(`  Configured: ${results.configValidation.configured ? 'Yes' : 'No'}`);
    if (results.configValidation.missingVars.length > 0) {
      console.log(`  Missing Variables: ${results.configValidation.missingVars.join(', ')}`);
    } else {
      console.log('  All environment variables present');
    }
  }
  
  console.log('\n🎯 LINKEDIN INTEGRATION ASSESSMENT:');
  
  if (results.summary.failed === 0) {
    console.log('✅ All LinkedIn integration endpoints are working correctly');
    console.log('✅ LinkedIn OAuth callback URL is consistent');
    console.log('✅ LinkedIn business accounts system is ready');
  } else {
    console.log('⚠️  Some LinkedIn integration issues detected');
  }
  
  if (results.summary.authRequired > 0) {
    console.log('🔐 Authentication required for full testing (expected in production)');
    console.log('📝 Manual testing with real LinkedIn account needed for complete validation');
  }
  
  console.log('\n🏁 LINKEDIN INTEGRATION TESTING COMPLETE');
  console.log(`Finished at: ${new Date().toISOString()}`);
  
  return results;
}

if (require.main === module) {
  runLinkedInIntegrationTests().then(results => {
    const successRate = (results.summary.passed / results.summary.total) * 100;
    process.exit(successRate >= 70 ? 0 : 1);
  }).catch(error => {
    console.error('❌ CRITICAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = { runLinkedInIntegrationTests };
