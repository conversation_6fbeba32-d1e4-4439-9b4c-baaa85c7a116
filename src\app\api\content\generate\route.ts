import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { aiContentGenerator } from '@/lib/ai/content-generator';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const generateContentSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required'),
  platform: z.enum(['twitter', 'facebook', 'instagram', 'linkedin', 'snapchat']),
  tone: z.enum(['professional', 'casual', 'friendly', 'formal', 'humorous', 'inspiring']),
  language: z.enum(['ar', 'en']).default('ar'),
  includeHashtags: z.boolean().default(true),
  includeEmojis: z.boolean().default(true),
  maxLength: z.number().optional(),
  keywords: z.array(z.string()).optional(),
  targetAudience: z.string().optional(),
});

const optimizeContentSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  fromPlatform: z.enum(['twitter', 'facebook', 'instagram', 'linkedin', 'snapchat']),
  toPlatform: z.enum(['twitter', 'facebook', 'instagram', 'linkedin', 'snapchat']),
  language: z.enum(['ar', 'en']).default('ar'),
});

const hashtagsSchema = z.object({
  content: z.string().min(1, 'Content is required'),
  platform: z.enum(['twitter', 'facebook', 'instagram', 'linkedin', 'snapchat']),
  language: z.enum(['ar', 'en']).default('ar'),
});

/**
 * Generate AI content
 * POST /api/content/generate
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'generate':
        return await handleGenerateContent(body, user);
      case 'optimize':
        return await handleOptimizeContent(body, user);
      case 'hashtags':
        return await handleGenerateHashtags(body, user);
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Content generation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function handleGenerateContent(body: any, user: any) {
  const validation = generateContentSchema.safeParse(body);
  
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  try {
    const result = await aiContentGenerator.generateContent(validation.data);

    // Log usage for analytics
    await logContentGeneration(user.id, 'generate', validation.data.platform, validation.data.language);

    return NextResponse.json({
      success: true,
      result,
    });

  } catch (error) {
    console.error('Content generation failed:', error);
    return NextResponse.json(
      { error: 'Failed to generate content. Please try again.' },
      { status: 500 }
    );
  }
}

async function handleOptimizeContent(body: any, user: any) {
  const validation = optimizeContentSchema.safeParse(body);
  
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  try {
    const optimizedContent = await aiContentGenerator.optimizeForPlatform(
      validation.data.content,
      validation.data.fromPlatform,
      validation.data.toPlatform,
      validation.data.language
    );

    // Log usage for analytics
    await logContentGeneration(user.id, 'optimize', validation.data.toPlatform, validation.data.language);

    return NextResponse.json({
      success: true,
      optimizedContent,
    });

  } catch (error) {
    console.error('Content optimization failed:', error);
    return NextResponse.json(
      { error: 'Failed to optimize content. Please try again.' },
      { status: 500 }
    );
  }
}

async function handleGenerateHashtags(body: any, user: any) {
  const validation = hashtagsSchema.safeParse(body);
  
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  try {
    const hashtags = await aiContentGenerator.generateHashtagSuggestions(
      validation.data.content,
      validation.data.platform,
      validation.data.language
    );

    // Log usage for analytics
    await logContentGeneration(user.id, 'hashtags', validation.data.platform, validation.data.language);

    return NextResponse.json({
      success: true,
      hashtags,
    });

  } catch (error) {
    console.error('Hashtag generation failed:', error);
    return NextResponse.json(
      { error: 'Failed to generate hashtags. Please try again.' },
      { status: 500 }
    );
  }
}

async function logContentGeneration(
  userId: string, 
  action: string, 
  platform: string, 
  language: string
) {
  try {
    
    
    await supabase
      .from('ai_usage_logs')
      .insert({
        user_id: userId,
        action,
        platform,
        language,
        timestamp: new Date().toISOString(),
      });

  } catch (error) {
    console.error('Failed to log AI usage:', error);
    // Don't throw error as this is not critical
  }
}

/**
 * Get AI usage statistics
 * GET /api/content/generate?stats=true
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const getStats = searchParams.get('stats') === 'true';

    if (!getStats) {
      return NextResponse.json({ error: 'Invalid request' }, { status: 400 });
    }

    // Get user's AI usage statistics
    const { data: usageStats, error } = await supabase
      .from('ai_usage_logs')
      .select('action, platform, language, timestamp')
      .eq('user_id', user.id)
      .gte('timestamp', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching usage stats:', error);
      return NextResponse.json({ error: 'Failed to fetch statistics' }, { status: 500 });
    }

    // Process statistics
    const stats = {
      totalUsage: usageStats?.length || 0,
      byAction: {} as Record<string, number>,
      byPlatform: {} as Record<string, number>,
      byLanguage: {} as Record<string, number>,
      dailyUsage: {} as Record<string, number>,
    };

    usageStats?.forEach(log => {
      // Count by action
      stats.byAction[log.action] = (stats.byAction[log.action] || 0) + 1;
      
      // Count by platform
      stats.byPlatform[log.platform] = (stats.byPlatform[log.platform] || 0) + 1;
      
      // Count by language
      stats.byLanguage[log.language] = (stats.byLanguage[log.language] || 0) + 1;
      
      // Count by day
      const day = new Date(log.timestamp).toISOString().split('T')[0];
      stats.dailyUsage[day] = (stats.dailyUsage[day] || 0) + 1;
    });

    return NextResponse.json({
      success: true,
      stats,
    });

  } catch (error) {
    console.error('Stats fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
