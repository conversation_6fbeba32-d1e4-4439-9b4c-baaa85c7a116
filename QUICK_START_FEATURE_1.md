# 🚀 QUICK START: Feature 1 - Advanced Scheduling System
## Ready-to-Implement Guide

**Estimated Time**: 8-10 days
**Priority**: CRITICAL
**Dependencies**: None (can start immediately)

---

## 📋 **DAY 1-2: DATABASE FOUNDATION**

### **Step 1: Database Schema Updates**

Create migration file: `/src/lib/database/migrations/001_advanced_scheduling.sql`

```sql
-- Add recurring support to existing posts table
ALTER TABLE posts ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS recurring_pattern JSONB;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS parent_post_id TEXT;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS recurring_end_date TIMESTAMP;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS optimal_time_score DECIMAL(3,2);

-- Create recurring posts management table
CREATE TABLE IF NOT EXISTS recurring_posts (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  media_url TEXT,
  platforms TEXT[] NOT NULL,
  pattern_type TEXT NOT NULL CHECK (pattern_type IN ('daily', 'weekly', 'monthly', 'custom')),
  pattern_config JSONB NOT NULL,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP,
  timezone TEXT DEFAULT 'UTC',
  is_active BOOLEAN DEFAULT TRUE,
  posts_generated INTEGER DEFAULT 0,
  last_generated_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create bulk operations tracking
CREATE TABLE IF NOT EXISTS bulk_operations (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  operation_type TEXT NOT NULL CHECK (operation_type IN ('import', 'schedule', 'update')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  total_items INTEGER NOT NULL,
  processed_items INTEGER DEFAULT 0,
  successful_items INTEGER DEFAULT 0,
  failed_items INTEGER DEFAULT 0,
  source_file_url TEXT,
  operation_config JSONB,
  error_log JSONB,
  result_summary JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add performance indexes
CREATE INDEX IF NOT EXISTS idx_posts_recurring ON posts(is_recurring, parent_post_id);
CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON posts(scheduled_at) WHERE status = 'SCHEDULED';
CREATE INDEX IF NOT EXISTS idx_recurring_posts_user_active ON recurring_posts(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_bulk_operations_user_status ON bulk_operations(user_id, status);
```

### **Step 2: Apply Migration**

```bash
# Run the migration
cd ewasl-app
npx supabase db push

# Verify tables created
npx supabase db diff
```

---

## 📋 **DAY 3-4: CORE LOGIC IMPLEMENTATION**

### **Step 3: Recurring Manager Core Logic**

Create file: `/src/lib/scheduling/recurring-manager.ts`

```typescript
import { createClient } from '@/lib/supabase/server';

export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  config: {
    interval?: number;
    time?: string;
    days?: string[];
    dayOfMonth?: number;
    dates?: string[];
  };
}

export interface CreateRecurringPostData {
  title: string;
  content: string;
  mediaUrl?: string;
  platforms: string[];
  pattern: RecurringPattern;
  startDate: Date;
  endDate?: Date;
  timezone: string;
}

export class RecurringManager {
  private supabase = createClient();

  async createRecurringPost(userId: string, data: CreateRecurringPostData) {
    try {
      // 1. Create recurring post record
      const { data: recurringPost, error } = await this.supabase
        .from('recurring_posts')
        .insert({
          user_id: userId,
          title: data.title,
          content: data.content,
          media_url: data.mediaUrl,
          platforms: data.platforms,
          pattern_type: data.pattern.type,
          pattern_config: data.pattern.config,
          start_date: data.startDate.toISOString(),
          end_date: data.endDate?.toISOString(),
          timezone: data.timezone,
        })
        .select()
        .single();

      if (error) throw error;

      // 2. Generate initial batch of posts (next 30 days)
      const generatedPosts = await this.generateNextBatch(recurringPost.id, 30);

      return {
        recurringPost,
        generatedPosts,
        previewCount: generatedPosts.length,
      };
    } catch (error) {
      console.error('Error creating recurring post:', error);
      throw error;
    }
  }

  async generateNextBatch(recurringPostId: string, days: number = 30) {
    try {
      // 1. Get recurring post configuration
      const { data: recurringPost, error } = await this.supabase
        .from('recurring_posts')
        .select('*')
        .eq('id', recurringPostId)
        .single();

      if (error || !recurringPost) throw new Error('Recurring post not found');

      // 2. Generate dates based on pattern
      const dates = this.generateDatesFromPattern(
        recurringPost.pattern_type,
        recurringPost.pattern_config,
        new Date(recurringPost.start_date),
        recurringPost.end_date ? new Date(recurringPost.end_date) : undefined,
        days
      );

      // 3. Create individual posts
      const postsToCreate = dates.map(date => ({
        user_id: recurringPost.user_id,
        content: recurringPost.content,
        media_url: recurringPost.media_url,
        scheduled_at: date.toISOString(),
        status: 'SCHEDULED',
        is_recurring: true,
        parent_post_id: recurringPostId,
        platforms: recurringPost.platforms,
      }));

      const { data: createdPosts, error: createError } = await this.supabase
        .from('posts')
        .insert(postsToCreate)
        .select();

      if (createError) throw createError;

      // 4. Update recurring post statistics
      await this.supabase
        .from('recurring_posts')
        .update({
          posts_generated: recurringPost.posts_generated + createdPosts.length,
          last_generated_at: new Date().toISOString(),
        })
        .eq('id', recurringPostId);

      return createdPosts;
    } catch (error) {
      console.error('Error generating batch:', error);
      throw error;
    }
  }

  private generateDatesFromPattern(
    type: string,
    config: any,
    startDate: Date,
    endDate?: Date,
    maxDays: number = 30
  ): Date[] {
    const dates: Date[] = [];
    const maxDate = endDate || new Date(Date.now() + maxDays * 24 * 60 * 60 * 1000);
    let currentDate = new Date(startDate);

    switch (type) {
      case 'daily':
        while (currentDate <= maxDate && dates.length < 100) {
          dates.push(new Date(currentDate));
          currentDate.setDate(currentDate.getDate() + (config.interval || 1));
        }
        break;

      case 'weekly':
        const targetDays = config.days || ['monday'];
        while (currentDate <= maxDate && dates.length < 100) {
          const dayName = currentDate.toLocaleDateString('en', { weekday: 'lowercase' });
          if (targetDays.includes(dayName)) {
            const postDate = new Date(currentDate);
            const [hours, minutes] = (config.time || '09:00').split(':');
            postDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            dates.push(postDate);
          }
          currentDate.setDate(currentDate.getDate() + 1);
        }
        break;

      case 'monthly':
        while (currentDate <= maxDate && dates.length < 100) {
          const postDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), config.dayOfMonth || 1);
          const [hours, minutes] = (config.time || '09:00').split(':');
          postDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);
          
          if (postDate >= startDate && postDate <= maxDate) {
            dates.push(postDate);
          }
          currentDate.setMonth(currentDate.getMonth() + 1);
        }
        break;

      case 'custom':
        (config.dates || []).forEach((dateStr: string) => {
          const date = new Date(dateStr);
          if (date >= startDate && date <= maxDate) {
            dates.push(date);
          }
        });
        break;
    }

    return dates.sort((a, b) => a.getTime() - b.getTime());
  }
}
```

---

## 📋 **DAY 5-6: API ENDPOINTS**

### **Step 4: Recurring Posts API**

Create file: `/src/app/api/posts/recurring/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { RecurringManager } from '@/lib/scheduling/recurring-manager';
import { z } from 'zod';

export const dynamic = 'force-dynamic';

const createRecurringSchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(1).max(2000),
  mediaUrl: z.string().url().optional(),
  platforms: z.array(z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'])).min(1),
  pattern: z.object({
    type: z.enum(['daily', 'weekly', 'monthly', 'custom']),
    config: z.object({
      interval: z.number().optional(),
      time: z.string().optional(),
      days: z.array(z.string()).optional(),
      dayOfMonth: z.number().min(1).max(31).optional(),
      dates: z.array(z.string()).optional(),
    }),
  }),
  startDate: z.string().datetime(),
  endDate: z.string().datetime().optional(),
  timezone: z.string().default('UTC'),
});

// POST /api/posts/recurring - Create recurring post
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const body = await request.json();
    const validation = createRecurringSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({
        error: 'Invalid data',
        details: validation.error.errors,
      }, { status: 400 });
    }

    const recurringManager = new RecurringManager();
    const result = await recurringManager.createRecurringPost(user.id, {
      ...validation.data,
      startDate: new Date(validation.data.startDate),
      endDate: validation.data.endDate ? new Date(validation.data.endDate) : undefined,
    });

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error: any) {
    console.error('Error creating recurring post:', error);
    return NextResponse.json({
      error: 'Failed to create recurring post',
      details: error.message,
    }, { status: 500 });
  }
}

// GET /api/posts/recurring - List user's recurring posts
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { data: recurringPosts, error } = await supabase
      .from('recurring_posts')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return NextResponse.json({
      success: true,
      data: recurringPosts,
    });

  } catch (error: any) {
    console.error('Error fetching recurring posts:', error);
    return NextResponse.json({
      error: 'Failed to fetch recurring posts',
      details: error.message,
    }, { status: 500 });
  }
}
```

---

## 📋 **DAY 7-8: UI COMPONENTS**

### **Step 5: Recurring Post Form Component**

Create file: `/src/components/posts/recurring-post-form.tsx`

```typescript
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

export function RecurringPostForm({ onSuccess }: { onSuccess?: () => void }) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    mediaUrl: '',
    platforms: [] as string[],
    patternType: 'daily',
    patternConfig: {
      interval: 1,
      time: '09:00',
      days: ['monday'],
      dayOfMonth: 1,
    },
    startDate: '',
    endDate: '',
    timezone: 'UTC',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/posts/recurring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          content: formData.content,
          mediaUrl: formData.mediaUrl || undefined,
          platforms: formData.platforms,
          pattern: {
            type: formData.patternType,
            config: formData.patternConfig,
          },
          startDate: new Date(formData.startDate).toISOString(),
          endDate: formData.endDate ? new Date(formData.endDate).toISOString() : undefined,
          timezone: formData.timezone,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create recurring post');
      }

      toast.success(`تم إنشاء المنشور المتكرر بنجاح! سيتم إنشاء ${result.data.previewCount} منشور`);
      onSuccess?.();
    } catch (error: any) {
      toast.error(`خطأ في إنشاء المنشور المتكرر: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>إنشاء منشور متكرر</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <Label htmlFor="title">عنوان المنشور</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="أدخل عنوان المنشور المتكرر"
              required
            />
          </div>

          {/* Content */}
          <div>
            <Label htmlFor="content">محتوى المنشور</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              placeholder="أدخل محتوى المنشور"
              rows={4}
              required
            />
          </div>

          {/* Platforms */}
          <div>
            <Label>المنصات</Label>
            <div className="flex gap-4 mt-2">
              {['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'].map((platform) => (
                <div key={platform} className="flex items-center space-x-2">
                  <Checkbox
                    id={platform}
                    checked={formData.platforms.includes(platform)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFormData({
                          ...formData,
                          platforms: [...formData.platforms, platform],
                        });
                      } else {
                        setFormData({
                          ...formData,
                          platforms: formData.platforms.filter(p => p !== platform),
                        });
                      }
                    }}
                  />
                  <Label htmlFor={platform}>{platform}</Label>
                </div>
              ))}
            </div>
          </div>

          {/* Pattern Type */}
          <div>
            <Label htmlFor="patternType">نوع التكرار</Label>
            <Select
              value={formData.patternType}
              onValueChange={(value) => setFormData({ ...formData, patternType: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">يومي</SelectItem>
                <SelectItem value="weekly">أسبوعي</SelectItem>
                <SelectItem value="monthly">شهري</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Pattern Configuration */}
          {formData.patternType === 'daily' && (
            <div>
              <Label htmlFor="interval">كل كم يوم</Label>
              <Input
                id="interval"
                type="number"
                min="1"
                value={formData.patternConfig.interval}
                onChange={(e) => setFormData({
                  ...formData,
                  patternConfig: { ...formData.patternConfig, interval: parseInt(e.target.value) }
                })}
              />
            </div>
          )}

          {/* Start Date */}
          <div>
            <Label htmlFor="startDate">تاريخ البداية</Label>
            <Input
              id="startDate"
              type="datetime-local"
              value={formData.startDate}
              onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
              required
            />
          </div>

          {/* End Date */}
          <div>
            <Label htmlFor="endDate">تاريخ النهاية (اختياري)</Label>
            <Input
              id="endDate"
              type="datetime-local"
              value={formData.endDate}
              onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
            />
          </div>

          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? 'جاري الإنشاء...' : 'إنشاء منشور متكرر'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
```

---

## 🧪 **DAY 9-10: TESTING & INTEGRATION**

### **Step 6: Integration Testing**

Create test file: `/src/__tests__/recurring-posts.test.ts`

```typescript
import { RecurringManager } from '@/lib/scheduling/recurring-manager';

describe('Recurring Posts System', () => {
  test('should create daily recurring post', async () => {
    // Test daily pattern generation
  });

  test('should create weekly recurring post', async () => {
    // Test weekly pattern generation
  });

  test('should handle bulk CSV import', async () => {
    // Test CSV import functionality
  });
});
```

### **Step 7: Add to Navigation**

Update `/src/components/layout/sidebar.tsx` to include recurring posts:

```typescript
// Add to navigation items
{
  title: 'المنشورات المتكررة',
  href: '/posts/recurring',
  icon: RefreshCw,
}
```

---

## ✅ **COMPLETION CHECKLIST**

### **Day 1-2: Database Foundation**
- [ ] Database migration created and applied
- [ ] Tables created: recurring_posts, bulk_operations
- [ ] Indexes added for performance
- [ ] Schema validated in Supabase

### **Day 3-4: Core Logic**
- [ ] RecurringManager class implemented
- [ ] Pattern generation logic working
- [ ] Batch generation functionality
- [ ] Error handling implemented

### **Day 5-6: API Endpoints**
- [ ] POST /api/posts/recurring endpoint
- [ ] GET /api/posts/recurring endpoint
- [ ] Input validation with Zod
- [ ] Authentication checks

### **Day 7-8: UI Components**
- [ ] RecurringPostForm component
- [ ] Pattern selection interface
- [ ] Form validation and submission
- [ ] Success/error handling

### **Day 9-10: Testing & Integration**
- [ ] Unit tests for core logic
- [ ] API endpoint testing
- [ ] UI component testing
- [ ] End-to-end workflow testing

### **Success Criteria**
- [ ] Users can create daily/weekly/monthly recurring posts
- [ ] System generates accurate post schedules
- [ ] UI is intuitive and responsive
- [ ] All tests pass with 90%+ coverage

This quick-start guide provides everything needed to implement the first critical feature. Once completed, move to Feature 3 (Production Scheduler) as it's the next highest priority for core functionality.
