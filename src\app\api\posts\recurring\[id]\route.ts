import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { RecurringManager } from '@/lib/scheduling/recurring-manager';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for updating recurring posts
const updateRecurringSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  content: z.string().min(1).max(2000).optional(),
  mediaUrl: z.string().url().optional(),
  platforms: z.array(z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'])).min(1).optional(),
  pattern: z.object({
    type: z.enum(['daily', 'weekly', 'monthly', 'custom']),
    config: z.object({
      interval: z.number().min(1).max(365).optional(),
      time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      days: z.array(z.enum(['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'])).optional(),
      dayOfMonth: z.number().min(1).max(31).optional(),
      dates: z.array(z.string().datetime()).optional(),
    }),
  }).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  timezone: z.string().optional(),
});

// GET /api/posts/recurring/[id] - Get specific recurring post
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Fetching recurring post:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get recurring post
    const { data: recurringPost, error } = await supabase
      .from('recurring_posts')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Recurring post not found' },
          { status: 404 }
        );
      }
      throw error;
    }

    // Get generated posts statistics
    const { count: scheduledCount } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('parent_post_id', id)
      .eq('status', 'SCHEDULED');

    const { count: publishedCount } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('parent_post_id', id)
      .eq('status', 'PUBLISHED');

    const { count: failedCount } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('parent_post_id', id)
      .eq('status', 'FAILED');

    // Get next few scheduled posts
    const { data: upcomingPosts } = await supabase
      .from('posts')
      .select('id, content, scheduled_at, platforms, status')
      .eq('parent_post_id', id)
      .eq('status', 'SCHEDULED')
      .order('scheduled_at', { ascending: true })
      .limit(5);

    return NextResponse.json({
      success: true,
      data: {
        recurringPost: {
          id: recurringPost.id,
          title: recurringPost.title,
          content: recurringPost.content,
          mediaUrl: recurringPost.media_url,
          platforms: recurringPost.platforms,
          patternType: recurringPost.pattern_type,
          patternConfig: recurringPost.pattern_config,
          startDate: recurringPost.start_date,
          endDate: recurringPost.end_date,
          timezone: recurringPost.timezone,
          isActive: recurringPost.is_active,
          postsGenerated: recurringPost.posts_generated,
          lastGeneratedAt: recurringPost.last_generated_at,
          nextGenerationAt: recurringPost.next_generation_at,
          createdAt: recurringPost.created_at,
          updatedAt: recurringPost.updated_at,
        },
        statistics: {
          scheduledPosts: scheduledCount || 0,
          publishedPosts: publishedCount || 0,
          failedPosts: failedCount || 0,
          totalGenerated: recurringPost.posts_generated,
        },
        upcomingPosts: upcomingPosts || [],
      },
    });

  } catch (error: any) {
    console.error('Error fetching recurring post:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch recurring post',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/posts/recurring/[id] - Update recurring post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Updating recurring post:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = updateRecurringSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validation.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    // Update recurring post using RecurringManager
    const recurringManager = new RecurringManager();
    const updatedPost = await recurringManager.updateRecurringPost(id, user.id, {
      title: data.title,
      content: data.content,
      mediaUrl: data.mediaUrl,
      platforms: data.platforms,
      pattern: data.pattern,
      startDate: data.startDate ? new Date(data.startDate) : undefined,
      endDate: data.endDate ? new Date(data.endDate) : undefined,
      timezone: data.timezone,
    });

    return NextResponse.json({
      success: true,
      data: updatedPost,
      message: 'تم تحديث المنشور المتكرر بنجاح',
    });

  } catch (error: any) {
    console.error('Error updating recurring post:', error);
    return NextResponse.json(
      {
        error: 'Failed to update recurring post',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/posts/recurring/[id] - Delete recurring post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Deleting recurring post:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Delete recurring post using RecurringManager
    const recurringManager = new RecurringManager();
    await recurringManager.deleteRecurringPost(id, user.id);

    return NextResponse.json({
      success: true,
      message: 'تم حذف المنشور المتكرر وجميع المنشورات المجدولة المرتبطة به',
    });

  } catch (error: any) {
    console.error('Error deleting recurring post:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete recurring post',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/posts/recurring/[id]/generate - Generate next batch of posts
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Generating next batch for recurring post:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body for batch size
    const body = await request.json().catch(() => ({}));
    const batchDays = body.days || 30;

    if (batchDays < 1 || batchDays > 365) {
      return NextResponse.json(
        { error: 'Batch days must be between 1 and 365' },
        { status: 400 }
      );
    }

    // Generate next batch using RecurringManager
    const recurringManager = new RecurringManager();
    const generatedPosts = await recurringManager.generateNextBatch(id, batchDays);

    return NextResponse.json({
      success: true,
      data: {
        generatedPosts,
        batchSize: generatedPosts.length,
        batchDays,
      },
      message: `تم إنشاء ${generatedPosts.length} منشور جديد للأيام القادمة`,
    });

  } catch (error: any) {
    console.error('Error generating batch:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate batch',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
