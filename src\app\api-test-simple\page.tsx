'use client';

import { useState } from 'react';

export default function SimpleAPITestPage() {
  const [results, setResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [`${new Date().toLocaleTimeString()}: ${message}`, ...prev]);
  };

  const testHealthAPI = async () => {
    setLoading(true);
    addResult('🔄 Testing Health API...');
    
    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      
      if (response.ok) {
        addResult(`✅ Health API Success: ${JSON.stringify(data)}`);
      } else {
        addResult(`❌ Health API Failed: ${response.status} - ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addResult(`❌ Health API Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testConnectionAPI = async () => {
    setLoading(true);
    addResult('🔄 Testing Connection API...');
    
    try {
      const response = await fetch('/api/social-accounts/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: 'TWITTER',
          accessToken: 'test_token_123',
          accountId: 'test_account'
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        addResult(`✅ Connection API Success: ${JSON.stringify(data)}`);
      } else {
        addResult(`❌ Connection API Failed: ${response.status} - ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addResult(`❌ Connection API Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPublishAPI = async () => {
    setLoading(true);
    addResult('🔄 Testing Publish API...');
    
    try {
      const response = await fetch('/api/posts/test-publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: 'TWITTER',
          content: '[TEST] Hello from eWasl API Test! 🚀',
          accessToken: 'test_token_123',
          isTest: true
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        addResult(`✅ Publish API Success: ${JSON.stringify(data)}`);
      } else {
        addResult(`❌ Publish API Failed: ${response.status} - ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addResult(`❌ Publish API Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testSocialConnectAPI = async () => {
    setLoading(true);
    addResult('🔄 Testing Social Connect API...');
    
    try {
      const response = await fetch('/api/social/connect');
      const data = await response.json();
      
      if (response.ok) {
        addResult(`✅ Social Connect API Success: ${JSON.stringify(data)}`);
      } else {
        addResult(`❌ Social Connect API Failed: ${response.status} - ${JSON.stringify(data)}`);
      }
    } catch (error: any) {
      addResult(`❌ Social Connect API Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '2rem',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          marginBottom: '2rem',
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: '0.5rem'
          }}>
            🧪 eWasl API Tester
          </h1>
          <p style={{ color: '#666', fontSize: '1.1rem' }}>
            Simple API Testing Interface - Priority 2 Implementation
          </p>
        </div>

        {/* Test Buttons */}
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          marginBottom: '2rem'
        }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#333' }}>
            🚀 API Tests
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            marginBottom: '1rem'
          }}>
            <button
              onClick={testHealthAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: loading ? '#ccc' : 'linear-gradient(45deg, #4CAF50, #45a049)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🏥 Health API
            </button>

            <button
              onClick={testSocialConnectAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: loading ? '#ccc' : 'linear-gradient(45deg, #2196F3, #1976D2)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🔗 Social Connect
            </button>

            <button
              onClick={testConnectionAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: loading ? '#ccc' : 'linear-gradient(45deg, #9C27B0, #7B1FA2)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🧪 Test Connection
            </button>

            <button
              onClick={testPublishAPI}
              disabled={loading}
              style={{
                padding: '1rem',
                background: loading ? '#ccc' : 'linear-gradient(45deg, #FF9800, #F57C00)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              📤 Test Publish
            </button>
          </div>

          {loading && (
            <div style={{
              padding: '1rem',
              background: '#e3f2fd',
              borderRadius: '0.5rem',
              textAlign: 'center',
              color: '#1976d2',
              fontWeight: 'bold'
            }}>
              ⏳ Running test...
            </div>
          )}
        </div>

        {/* Results */}
        {results.length > 0 && (
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '1rem',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', color: '#333', margin: 0 }}>
                📊 Test Results
              </h2>
              <button
                onClick={() => setResults([])}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#f44336',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}
              >
                🗑️ Clear
              </button>
            </div>
            
            <div style={{
              maxHeight: '400px',
              overflowY: 'auto',
              background: '#f5f5f5',
              padding: '1rem',
              borderRadius: '0.5rem',
              fontFamily: 'monospace'
            }}>
              {results.map((result, index) => (
                <div
                  key={index}
                  style={{
                    padding: '0.5rem',
                    marginBottom: '0.5rem',
                    background: result.includes('✅') ? '#e8f5e8' : 
                               result.includes('❌') ? '#ffeaea' : '#fff',
                    borderRadius: '0.25rem',
                    fontSize: '0.9rem'
                  }}
                >
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status */}
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          background: 'rgba(255,255,255,0.9)',
          borderRadius: '0.5rem',
          textAlign: 'center'
        }}>
          <p style={{ margin: 0, color: '#666' }}>
            🎯 <strong>Priority 2 Status:</strong> API Integration Testing - 85% Complete
          </p>
        </div>
      </div>
    </div>
  );
}
