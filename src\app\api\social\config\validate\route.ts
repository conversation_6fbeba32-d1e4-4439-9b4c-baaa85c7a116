import { NextRequest, NextResponse } from 'next/server';
import SocialMediaConfigValidator from '@/lib/config/social-media-config-validator';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * GET /api/social/config/validate
 * Validate social media platform configurations
 */
export async function GET(request: NextRequest) {
  try {
    console.log('Validating social media configurations...');

    const validator = new SocialMediaConfigValidator();
    const validation = validator.validateAllPlatforms();

    // Generate detailed report
    const report = validator.generateReport();
    const configuredPlatforms = validator.getConfiguredPlatforms();

    console.log('Configuration validation completed:', {
      isValid: validation.isValid,
      validPlatforms: validation.summary.valid,
      totalPlatforms: validation.summary.total,
      configuredPlatforms
    });

    return NextResponse.json({
      success: true,
      isValid: validation.isValid,
      validation,
      report,
      configuredPlatforms,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      baseUrl: process.env.NEXT_PUBLIC_APP_URL || process.env.NEXTAUTH_URL,
    });

  } catch (error) {
    console.error('Configuration validation error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to validate configuration',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/social/config/validate
 * Validate specific platform configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { platform } = body;

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform parameter is required' },
        { status: 400 }
      );
    }

    console.log('Validating specific platform configuration:', platform);

    const validator = new SocialMediaConfigValidator();
    const isConfigured = validator.isPlatformConfigured(platform);

    // Get detailed validation for the platform
    const allValidation = validator.validateAllPlatforms();
    const platformValidation = allValidation.platforms.find(p => 
      p.platform.toLowerCase().includes(platform.toLowerCase())
    );

    if (!platformValidation) {
      return NextResponse.json(
        { error: `Unsupported platform: ${platform}` },
        { status: 400 }
      );
    }

    console.log('Platform validation completed:', {
      platform,
      isConfigured,
      isValid: platformValidation.isValid
    });

    return NextResponse.json({
      success: true,
      platform,
      isConfigured,
      validation: platformValidation,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Platform validation error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to validate platform configuration',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
