/**
 * Enhanced Facebook Publisher V2
 * Real API implementation for Facebook Graph API posting with media support
 * Integrates with FacebookGraphService for comprehensive functionality
 */

import FacebookGraphService, { FacebookPage, PostContent as FacebookPostContent } from '@/lib/social/services/facebook-graph-service';

export interface SocialAccount {
  id: string;
  platform: string;
  access_token: string;
  account_id: string;
  account_name: string;
  page_id?: string;
  page_name?: string;
}

export interface PostContent {
  content: string;
  mediaUrl?: string;
  mediaType?: 'IMAGE' | 'VIDEO';
  link?: string;
  linkPreview?: {
    title?: string;
    description?: string;
    imageUrl?: string;
  };
}

export interface PublishResult {
  success: boolean;
  postId?: string;
  url?: string;
  error?: string;
  platformResponse?: any;
}

export class FacebookPublisherV2 {
  private facebookService: FacebookGraphService;

  constructor() {
    this.facebookService = new FacebookGraphService();
  }

  /**
   * Publish a post to Facebook using Graph API
   */
  async publishPost(account: SocialAccount, content: PostContent): Promise<PublishResult> {
    try {
      console.log('Publishing to Facebook with V2 publisher:', {
        accountId: account.account_id,
        pageId: account.page_id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
        hasLink: !!content.link
      });

      // Validate token first
      const isValidToken = await this.facebookService.validateToken(account.access_token);
      if (!isValidToken) {
        throw new Error('Facebook access token is invalid or expired');
      }

      // If we have a page_id, we're posting to a page, otherwise to user's timeline
      if (account.page_id) {
        // Get page access token and details
        const pages = await this.facebookService.getUserPages(account.access_token);
        const targetPage = pages.find(page => page.id === account.page_id);

        if (!targetPage) {
          throw new Error(`Facebook page not found: ${account.page_id}`);
        }

        // Prepare content for Facebook service
        const facebookContent: FacebookPostContent = {
          content: content.content,
          mediaUrl: content.mediaUrl,
          mediaType: content.mediaType,
          link: content.link,
          linkPreview: content.linkPreview
        };

        // Publish to page
        const result = await this.facebookService.publishPost(targetPage, facebookContent);

        if (!result.success) {
          throw new Error(result.error || 'Facebook publishing failed');
        }

        console.log('Facebook page post published successfully:', {
          postId: result.postId,
          postUrl: result.url
        });

        return {
          success: true,
          postId: result.postId!,
          url: result.url!,
          platformResponse: result.platformResponse
        };

      } else {
        // Post to user's timeline (personal profile)
        const postData: any = {
          message: content.content,
          access_token: account.access_token
        };

        // Handle media for personal posts
        if (content.mediaUrl) {
          if (content.mediaType === 'IMAGE') {
            postData.url = content.mediaUrl;
          } else if (content.mediaType === 'VIDEO') {
            postData.source = content.mediaUrl;
          }
        }

        // Handle link sharing
        if (content.link) {
          postData.link = content.link;
          if (content.linkPreview) {
            postData.name = content.linkPreview.title;
            postData.description = content.linkPreview.description;
            if (content.linkPreview.imageUrl) {
              postData.picture = content.linkPreview.imageUrl;
            }
          }
        }

        // Determine endpoint based on content type
        let endpoint = 'https://graph.facebook.com/v19.0/me/feed';
        if (content.mediaUrl && content.mediaType === 'IMAGE') {
          endpoint = 'https://graph.facebook.com/v19.0/me/photos';
        } else if (content.mediaUrl && content.mediaType === 'VIDEO') {
          endpoint = 'https://graph.facebook.com/v19.0/me/videos';
        }

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(postData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Facebook API error: ${errorData.error?.message || response.statusText}`);
        }

        const result = await response.json();

        if (result.error) {
          throw new Error(`Facebook posting error: ${result.error.message}`);
        }

        const postUrl = `https://www.facebook.com/${result.id}`;

        console.log('Facebook personal post published successfully:', {
          postId: result.id,
          postUrl
        });

        return {
          success: true,
          postId: result.id,
          url: postUrl,
          platformResponse: result
        };
      }

    } catch (error) {
      console.error('Facebook publishing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get Facebook post analytics
   */
  async getPostAnalytics(postId: string, accessToken: string): Promise<any> {
    try {
      return await this.facebookService.getPostInsights(postId, accessToken);
    } catch (error) {
      console.error('Facebook post analytics fetch error:', error);
      throw error;
    }
  }

  /**
   * Test Facebook connection
   */
  async testConnection(account: SocialAccount): Promise<any> {
    try {
      console.log('Testing Facebook connection:', {
        accountId: account.account_id,
        pageId: account.page_id
      });

      // Validate token
      const isValidToken = await this.facebookService.validateToken(account.access_token);
      if (!isValidToken) {
        throw new Error('Facebook access token is invalid or expired');
      }

      if (account.page_id) {
        // Test page connection
        const pages = await this.facebookService.getUserPages(account.access_token);
        const targetPage = pages.find(page => page.id === account.page_id);

        if (!targetPage) {
          throw new Error(`Facebook page not found: ${account.page_id}`);
        }

        const result = await this.facebookService.testConnection(targetPage);
        
        console.log('Facebook page connection test successful:', {
          pageName: result.pageInfo?.name,
          fanCount: result.pageInfo?.fanCount
        });

        return result;

      } else {
        // Test user connection
        const userInfo = await this.facebookService.getUserInfo(account.access_token);

        console.log('Facebook user connection test successful:', {
          name: userInfo.name,
          id: userInfo.id
        });

        return {
          success: true,
          accountInfo: {
            id: userInfo.id,
            name: userInfo.name,
            email: userInfo.email,
            profilePictureUrl: userInfo.profilePictureUrl
          }
        };
      }

    } catch (error) {
      console.error('Facebook connection test failed:', error);
      throw error;
    }
  }

  /**
   * Get Facebook pages for account
   */
  async getPages(accessToken: string): Promise<FacebookPage[]> {
    try {
      return await this.facebookService.getUserPages(accessToken);
    } catch (error) {
      console.error('Facebook pages fetch error:', error);
      throw error;
    }
  }

  /**
   * Get Facebook rate limit status
   */
  async getRateLimitStatus(accessToken: string): Promise<any> {
    try {
      return await this.facebookService.getRateLimitStatus(accessToken);
    } catch (error) {
      console.error('Error checking Facebook rate limits:', error);
      return null;
    }
  }

  /**
   * Format content for Facebook (character limits, etc.)
   */
  formatContent(content: string): { text: string; isValid: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let formattedText = content;

    // Facebook has a 63,206 character limit for posts
    if (formattedText.length > 63206) {
      formattedText = formattedText.substring(0, 63203) + '...';
      warnings.push('Content was truncated to fit Facebook character limit');
    }

    // Check for optimal length (Facebook recommends shorter posts for better engagement)
    if (formattedText.length > 500) {
      warnings.push('Consider shorter content for better Facebook engagement (recommended: under 500 characters)');
    }

    // Check hashtag usage (Facebook supports hashtags but they're less effective than other platforms)
    const hashtags = formattedText.match(/#\w+/g) || [];
    if (hashtags.length > 5) {
      warnings.push('Consider using fewer hashtags on Facebook (recommended: 1-3 hashtags)');
    }

    return {
      text: formattedText,
      isValid: formattedText.length > 0 && formattedText.length <= 63206,
      warnings
    };
  }

  /**
   * Get optimal posting times for Facebook
   */
  getOptimalPostingTimes(): string[] {
    // Based on general Facebook engagement data
    return [
      '09:00', // 9 AM - Morning engagement
      '13:00', // 1 PM - Lunch break
      '15:00', // 3 PM - Afternoon peak
      '19:00', // 7 PM - Evening engagement
      '21:00'  // 9 PM - Prime time
    ];
  }

  /**
   * Validate Facebook post content
   */
  validatePostContent(content: PostContent): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if content is provided
    if (!content.content || content.content.trim().length === 0) {
      errors.push('Post content cannot be empty');
    }

    // Check content length
    if (content.content && content.content.length > 63206) {
      errors.push('Content exceeds Facebook character limit (63,206 characters)');
    }

    // Validate media URL if provided
    if (content.mediaUrl) {
      try {
        new URL(content.mediaUrl);
      } catch {
        errors.push('Invalid media URL format');
      }
    }

    // Validate link if provided
    if (content.link) {
      try {
        new URL(content.link);
      } catch {
        errors.push('Invalid link URL format');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default FacebookPublisherV2;
