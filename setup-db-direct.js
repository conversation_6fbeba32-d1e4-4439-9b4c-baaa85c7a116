const { Client } = require('pg');
const fs = require('fs');

// Supabase connection details
const client = new Client({
  host: 'aws-0-eu-central-1.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.ajpcbugydftdyhlbddpl',
  password: 'eWasl2025!Secure',
  ssl: {
    rejectUnauthorized: false
  }
});

async function setupDatabase() {
  try {
    console.log('🔌 Connecting to Supabase database...');
    await client.connect();
    console.log('✅ Connected successfully!');

    // Create basic tables first
    console.log('📝 Creating User table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "User" (
        id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
        name TEXT,
        email TEXT UNIQUE NOT NULL,
        password TEXT,
        role TEXT DEFAULT 'USER' NOT NULL,
        "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        "updatedAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL
      );
    `);
    console.log('✅ User table created');

    console.log('📝 Creating Post table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "Post" (
        id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
        "userId" TEXT NOT NULL,
        content TEXT NOT NULL,
        status TEXT DEFAULT 'DRAFT' NOT NULL,
        "scheduledAt" TIMESTAMPTZ,
        "publishedAt" TIMESTAMPTZ,
        "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        "updatedAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
      );
    `);
    console.log('✅ Post table created');

    console.log('📝 Creating SocialAccount table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "SocialAccount" (
        id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
        "userId" TEXT NOT NULL,
        platform TEXT NOT NULL,
        "accountName" TEXT NOT NULL,
        "isActive" BOOLEAN DEFAULT true NOT NULL,
        "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
        FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
      );
    `);
    console.log('✅ SocialAccount table created');

    // Insert admin user
    console.log('👤 Creating admin user...');
    await client.query(`
      INSERT INTO "User" (name, email, password, role) VALUES 
      ('Admin User', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uDxy', 'ADMIN')
      ON CONFLICT (email) DO NOTHING;
    `);
    console.log('✅ Admin user created');

    // Test the setup
    const result = await client.query('SELECT COUNT(*) FROM "User"');
    console.log(`📊 Total users: ${result.rows[0].count}`);

    console.log('🎉 Database setup completed successfully!');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
  } finally {
    await client.end();
  }
}

setupDatabase();