'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  RefreshCw, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Calendar,
  Clock,
  BarChart3,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

interface RecurringPost {
  id: string;
  title: string;
  content: string;
  platforms: string[];
  patternType: string;
  patternConfig: any;
  startDate: string;
  endDate?: string;
  timezone: string;
  isActive: boolean;
  postsGenerated: number;
  lastGeneratedAt?: string;
  nextGenerationAt?: string;
  createdAt: string;
  statistics: {
    scheduledPosts: number;
    publishedPosts: number;
    totalGenerated: number;
  };
}

interface RecurringPostsListProps {
  onEdit?: (post: RecurringPost) => void;
  onCreateNew?: () => void;
}

export function RecurringPostsList({ onEdit, onCreateNew }: RecurringPostsListProps) {
  const [posts, setPosts] = useState<RecurringPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; post?: RecurringPost }>({ open: false });
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchRecurringPosts();
  }, []);

  const fetchRecurringPosts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/posts/recurring');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في جلب المنشورات المتكررة');
      }

      setPosts(result.data.recurringPosts || []);
    } catch (error: any) {
      console.error('Error fetching recurring posts:', error);
      toast.error(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePostStatus = async (postId: string, isActive: boolean) => {
    try {
      setProcessingIds(prev => new Set(prev).add(postId));

      const response = await fetch(`/api/posts/recurring/${postId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في تحديث حالة المنشور');
      }

      // Update local state
      setPosts(prev => prev.map(post => 
        post.id === postId ? { ...post, isActive } : post
      ));

      toast.success(isActive ? 'تم تفعيل المنشور المتكرر' : 'تم إيقاف المنشور المتكرر');
    } catch (error: any) {
      console.error('Error toggling post status:', error);
      toast.error(error.message);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(postId);
        return newSet;
      });
    }
  };

  const generateNextBatch = async (postId: string) => {
    try {
      setProcessingIds(prev => new Set(prev).add(postId));

      const response = await fetch(`/api/posts/recurring/${postId}/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ days: 30 }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في إنشاء المنشورات');
      }

      toast.success(result.message);
      fetchRecurringPosts(); // Refresh the list
    } catch (error: any) {
      console.error('Error generating batch:', error);
      toast.error(error.message);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(postId);
        return newSet;
      });
    }
  };

  const deletePost = async (postId: string) => {
    try {
      setProcessingIds(prev => new Set(prev).add(postId));

      const response = await fetch(`/api/posts/recurring/${postId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في حذف المنشور');
      }

      // Remove from local state
      setPosts(prev => prev.filter(post => post.id !== postId));
      toast.success(result.message);
    } catch (error: any) {
      console.error('Error deleting post:', error);
      toast.error(error.message);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(postId);
        return newSet;
      });
      setDeleteDialog({ open: false });
    }
  };

  const getPatternDescription = (patternType: string, config: any) => {
    switch (patternType) {
      case 'daily':
        return `كل ${config.interval || 1} يوم في ${config.time || '09:00'}`;
      case 'weekly':
        const days = config.days?.join('، ') || 'الإثنين';
        return `أسبوعياً في ${days} في ${config.time || '09:00'}`;
      case 'monthly':
        return `شهرياً في اليوم ${config.dayOfMonth || 1} في ${config.time || '09:00'}`;
      case 'custom':
        return `${config.dates?.length || 0} تاريخ مخصص`;
      default:
        return 'نمط غير محدد';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ar 
      });
    } catch {
      return 'تاريخ غير صالح';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        جاري تحميل المنشورات المتكررة...
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">المنشورات المتكررة</h2>
          <p className="text-muted-foreground">
            إدارة المنشورات التي يتم نشرها تلقائياً حسب جدول زمني محدد
          </p>
        </div>
        {onCreateNew && (
          <Button onClick={onCreateNew}>
            <Plus className="h-4 w-4 mr-2" />
            إنشاء منشور متكرر
          </Button>
        )}
      </div>

      {/* Posts List */}
      {posts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">لا توجد منشورات متكررة</h3>
            <p className="text-muted-foreground text-center mb-4">
              ابدأ بإنشاء منشور متكرر لجدولة المحتوى تلقائياً
            </p>
            {onCreateNew && (
              <Button onClick={onCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                إنشاء أول منشور متكرر
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {posts.map((post) => (
            <Card key={post.id} className={`transition-opacity ${processingIds.has(post.id) ? 'opacity-50' : ''}`}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{post.title}</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                      {post.content}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={post.isActive}
                      onCheckedChange={(checked) => togglePostStatus(post.id, checked)}
                      disabled={processingIds.has(post.id)}
                    />
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit?.(post)}>
                          <Edit className="h-4 w-4 mr-2" />
                          تحرير
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => generateNextBatch(post.id)}>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          إنشاء دفعة جديدة
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => setDeleteDialog({ open: true, post })}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          حذف
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Platforms */}
                  <div className="flex flex-wrap gap-2">
                    {post.platforms.map(platform => (
                      <Badge key={platform} variant="secondary">
                        {platform}
                      </Badge>
                    ))}
                  </div>

                  {/* Pattern Info */}
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {getPatternDescription(post.patternType, post.patternConfig)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      بدأ {formatDate(post.startDate)}
                    </div>
                  </div>

                  {/* Statistics */}
                  <div className="grid grid-cols-3 gap-4 p-3 bg-muted/50 rounded-lg">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-blue-600">
                        {post.statistics.scheduledPosts}
                      </div>
                      <div className="text-xs text-muted-foreground">مجدول</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-600">
                        {post.statistics.publishedPosts}
                      </div>
                      <div className="text-xs text-muted-foreground">منشور</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-600">
                        {post.statistics.totalGenerated}
                      </div>
                      <div className="text-xs text-muted-foreground">إجمالي</div>
                    </div>
                  </div>

                  {/* Status */}
                  <div className="flex items-center justify-between">
                    <Badge variant={post.isActive ? 'default' : 'secondary'}>
                      {post.isActive ? (
                        <>
                          <Play className="h-3 w-3 mr-1" />
                          نشط
                        </>
                      ) : (
                        <>
                          <Pause className="h-3 w-3 mr-1" />
                          متوقف
                        </>
                      )}
                    </Badge>
                    {post.nextGenerationAt && (
                      <div className="text-xs text-muted-foreground">
                        الدفعة التالية: {formatDate(post.nextGenerationAt)}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialog.open} onOpenChange={(open) => setDeleteDialog({ open })}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من حذف المنشور المتكرر "{deleteDialog.post?.title}"؟
              سيتم حذف جميع المنشورات المجدولة المرتبطة به أيضاً.
              هذا الإجراء لا يمكن التراجع عنه.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteDialog.post && deletePost(deleteDialog.post.id)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
