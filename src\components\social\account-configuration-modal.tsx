'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { FacebookPageSelector } from './facebook-page-selector';
import { LinkedInCompanySelector } from './linkedin-company-selector';
import { BusinessAccountConfig } from '@/lib/social/business-accounts/business-account-types';

interface AccountConfigurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  initialPlatform?: string;
}

export function AccountConfigurationModal({
  isOpen,
  onClose,
  userId,
  initialPlatform = 'facebook'
}: AccountConfigurationModalProps) {
  const [activeTab, setActiveTab] = useState(initialPlatform);
  const [configurations, setConfigurations] = useState<Record<string, BusinessAccountConfig>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [socialAccounts, setSocialAccounts] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      loadConfigurations();
    }
  }, [isOpen, userId]);

  const loadConfigurations = async () => {
    try {
      setIsLoading(true);

      // First, get social accounts to get their IDs
      const accountsResponse = await fetch('/api/social/accounts');
      const accountsData = await accountsResponse.json();

      if (accountsResponse.ok && accountsData.accounts) {
        const accountsMap: Record<string, string> = {};
        accountsData.accounts.forEach((account: any) => {
          accountsMap[account.platform.toLowerCase()] = account.id;
        });
        setSocialAccounts(accountsMap);

        // Load configurations for each platform
        const platforms = ['facebook', 'linkedin', 'instagram', 'twitter'];
        const configPromises = platforms.map(async (platform) => {
          try {
            const response = await fetch(`/api/social/business-accounts?platform=${platform}&userId=${userId}`);
            const data = await response.json();
            return { platform, config: data };
          } catch (error) {
            console.error(`Error loading ${platform} config:`, error);
            return { platform, config: null };
          }
        });

        const results = await Promise.all(configPromises);
        const configsMap: Record<string, BusinessAccountConfig> = {};
        
        results.forEach(({ platform, config }) => {
          if (config) {
            configsMap[platform] = config;
          }
        });

        setConfigurations(configsMap);
      }

    } catch (error) {
      console.error('Error loading configurations:', error);
      toast.error('فشل في تحميل إعدادات الحسابات');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAccountSelected = (platform: string, accountId: string) => {
    // Update the configuration for this platform
    setConfigurations(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        selectedAccount: prev[platform]?.businessAccounts.find(acc => acc.id === accountId)
      }
    }));
    
    toast.success(`تم اختيار حساب ${platform === 'facebook' ? 'فيسبوك' : 'لينكد إن'} بنجاح`);
  };

  const getConfigurationStatus = (platform: string) => {
    const config = configurations[platform];
    if (!config) return { status: 'loading', message: 'جاري التحميل...' };
    
    if (!config.isConfigured) {
      return { status: 'error', message: 'غير مكون' };
    }
    
    if (!config.selectedAccount) {
      return { status: 'warning', message: 'لم يتم اختيار حساب' };
    }
    
    return { status: 'success', message: 'مكون بنجاح' };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <RefreshCw className="w-4 h-4 animate-spin text-muted-foreground" />;
    }
  };

  const getPlatformLabel = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return 'فيسبوك';
      case 'linkedin':
        return 'لينكد إن';
      case 'instagram':
        return 'إنستغرام';
      case 'twitter':
        return 'تويتر';
      default:
        return platform;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>إعداد الحسابات التجارية</DialogTitle>
          <DialogDescription>
            اختر الصفحات والشركات التي تريد النشر عليها لكل منصة
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            {['facebook', 'linkedin', 'instagram', 'twitter'].map((platform) => {
              const status = getConfigurationStatus(platform);
              return (
                <TabsTrigger 
                  key={platform} 
                  value={platform}
                  className="flex items-center gap-2"
                >
                  {getStatusIcon(status.status)}
                  {getPlatformLabel(platform)}
                </TabsTrigger>
              );
            })}
          </TabsList>

          <TabsContent value="facebook" className="space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">صفحات فيسبوك</h3>
              <p className="text-sm text-muted-foreground mb-4">
                اختر صفحة فيسبوك التجارية التي تريد النشر عليها
              </p>
            </div>
            
            {socialAccounts.facebook ? (
              <FacebookPageSelector
                userId={userId}
                socialAccountId={socialAccounts.facebook}
                onPageSelected={(pageId) => handleAccountSelected('facebook', pageId)}
              />
            ) : (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                <p className="text-muted-foreground">
                  لم يتم العثور على حساب فيسبوك متصل
                </p>
                <Button variant="outline" className="mt-4" onClick={onClose}>
                  ربط حساب فيسبوك
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="linkedin" className="space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">شركات لينكد إن</h3>
              <p className="text-sm text-muted-foreground mb-4">
                اختر شركة لينكد إن التي تريد النشر باسمها
              </p>
            </div>
            
            {socialAccounts.linkedin ? (
              <LinkedInCompanySelector
                userId={userId}
                socialAccountId={socialAccounts.linkedin}
                onCompanySelected={(companyId) => handleAccountSelected('linkedin', companyId)}
              />
            ) : (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                <p className="text-muted-foreground">
                  لم يتم العثور على حساب لينكد إن متصل
                </p>
                <Button variant="outline" className="mt-4" onClick={onClose}>
                  ربط حساب لينكد إن
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="instagram" className="space-y-4">
            <div className="text-center py-8">
              <AlertCircle className="w-12 h-12 text-blue-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">إنستغرام</h3>
              <p className="text-muted-foreground mb-4">
                سيتم إضافة دعم الحسابات التجارية لإنستغرام قريباً
              </p>
              <p className="text-sm text-muted-foreground">
                حالياً يمكنك النشر على إنستغرام من خلال الحساب المتصل
              </p>
            </div>
          </TabsContent>

          <TabsContent value="twitter" className="space-y-4">
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">تويتر</h3>
              <p className="text-muted-foreground mb-4">
                تويتر يستخدم الحساب المتصل مباشرة للنشر
              </p>
              <p className="text-sm text-muted-foreground">
                لا حاجة لإعداد إضافي - يمكنك النشر مباشرة
              </p>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {Object.keys(configurations).length > 0 && (
              <>
                {Object.values(configurations).filter(c => c.selectedAccount).length} من{' '}
                {Object.keys(configurations).length} منصات مكونة
              </>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={loadConfigurations} disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
                  جاري التحديث...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 ml-2" />
                  تحديث
                </>
              )}
            </Button>
            <Button onClick={onClose}>
              إغلاق
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
