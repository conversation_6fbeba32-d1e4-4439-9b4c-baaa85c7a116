import { NextRequest, NextResponse } from 'next/server';
import JobQueueManager from '@/lib/queue/job-queue';
import RedisClient from '@/lib/queue/redis-client';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Test scheduler system without authentication
 * GET /api/scheduler/test
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing scheduler system...');

    const queueManager = JobQueueManager.getInstance();
    
    // Initialize queue if not already done
    const initialized = await queueManager.initialize();
    
    // Get queue statistics
    const queueStats = await queueManager.getQueueStats();
    
    // Get Redis information
    const redisInfo = await RedisClient.getRedisInfo();
    const redisConnected = await RedisClient.testConnection();

    // Test job scheduling
    let testJobResult = null;
    try {
      const testJobData = {
        postId: `test_${Date.now()}`,
        userId: 'test-user',
        content: 'Test post from scheduler system',
        platforms: ['twitter', 'facebook'],
        scheduledFor: new Date(Date.now() + 10000).toISOString(), // 10 seconds from now
      };

      const jobId = await queueManager.schedulePost(
        testJobData,
        new Date(Date.now() + 10000)
      );

      testJobResult = {
        success: !!jobId,
        jobId,
        scheduledFor: new Date(Date.now() + 10000).toISOString(),
        testData: testJobData
      };

    } catch (error) {
      testJobResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Calculate overall health
    const isHealthy = initialized && queueManager.isHealthy();
    
    const response = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      scheduler: {
        initialized,
        healthy: queueManager.isHealthy(),
        mode: redisConnected ? 'redis' : 'memory',
      },
      queue: queueStats || {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        total: 0,
      },
      redis: {
        connected: redisConnected,
        configured: !!process.env.REDIS_URL || !!process.env.REDIS_HOST,
        info: redisInfo,
      },
      testJob: testJobResult,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        redisUrl: process.env.REDIS_URL ? 'configured' : 'not configured',
        redisHost: process.env.REDIS_HOST || 'localhost',
        redisPort: process.env.REDIS_PORT || '6379',
      },
      recommendations: []
    };

    // Add recommendations
    if (!redisConnected && process.env.REDIS_URL) {
      response.recommendations.push('Redis is configured but not accessible. Check connection.');
    }
    
    if (!process.env.REDIS_URL && !process.env.REDIS_HOST) {
      response.recommendations.push('Consider configuring Redis for production job queue.');
    }

    if (queueStats && queueStats.failed > 0) {
      response.recommendations.push(`${queueStats.failed} failed jobs need attention.`);
    }

    if (!initialized) {
      response.recommendations.push('Scheduler failed to initialize. Check logs for errors.');
    }

    console.log('✅ Scheduler test completed:', {
      healthy: isHealthy,
      redis: redisConnected,
      queue: queueStats?.total || 0
    });

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Scheduler test failed:', error);
    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Scheduler test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Control scheduler test operations
 * POST /api/scheduler/test
 */
export async function POST(request: NextRequest) {
  try {
    const { action, delay } = await request.json();

    const queueManager = JobQueueManager.getInstance();

    switch (action) {
      case 'schedule-test-job':
        const delayMs = delay || 5000; // Default 5 seconds
        const testJobData = {
          postId: `test_${Date.now()}`,
          userId: 'test-user',
          content: `Test post scheduled at ${new Date().toISOString()}`,
          platforms: ['twitter', 'facebook'],
          scheduledFor: new Date(Date.now() + delayMs).toISOString(),
        };

        const jobId = await queueManager.schedulePost(
          testJobData,
          new Date(Date.now() + delayMs)
        );

        return NextResponse.json({
          success: !!jobId,
          message: jobId ? 'Test job scheduled successfully' : 'Failed to schedule test job',
          jobId,
          scheduledFor: new Date(Date.now() + delayMs).toISOString(),
          testData: testJobData
        });

      case 'get-job-status':
        const { jobId: statusJobId } = await request.json();
        if (!statusJobId) {
          return NextResponse.json(
            { error: 'jobId is required for get-job-status action' },
            { status: 400 }
          );
        }

        const jobStatus = await queueManager.getJobStatus(statusJobId);
        return NextResponse.json({
          success: !!jobStatus,
          jobStatus
        });

      case 'initialize':
        const initialized = await queueManager.initialize();
        return NextResponse.json({
          success: initialized,
          message: initialized ? 'Scheduler initialized successfully' : 'Failed to initialize scheduler'
        });

      case 'stats':
        const stats = await queueManager.getQueueStats();
        return NextResponse.json({
          success: true,
          stats
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: schedule-test-job, get-job-status, initialize, or stats' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Scheduler test control failed:', error);
    return NextResponse.json(
      {
        error: 'Scheduler test control failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
