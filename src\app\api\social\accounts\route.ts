import { NextRequest, NextResponse } from 'next/server';
import { createClient, createServiceRoleClient } from '@/lib/supabase/server';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    // Use service role client to bypass RLS policies
    const supabase = createServiceRoleClient();

    // Use demo user ID for testing (in production, get from authenticated session)
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';

    // Get social accounts for demo user
    const { data: accounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching social accounts:', error);
      return NextResponse.json(
        { error: 'Failed to fetch social accounts' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      accounts: accounts || [],
    });

  } catch (error) {
    console.error('Error in social accounts API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { platform, accountId, accountName, accessToken, refreshToken } = await request.json();

    if (!platform || !accountId || !accountName || !accessToken) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Add or update social account using authenticated user ID
    const { data, error } = await supabase
      .from('social_accounts')
      .upsert({
        user_id: user.id, // Use authenticated user ID
        platform,
        account_id: accountId,
        account_name: accountName,
        access_token: accessToken,
        refresh_token: refreshToken,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error saving social account:', error);
      return NextResponse.json(
        { error: 'Failed to save social account' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id, // Use authenticated user ID
        action: 'CONNECT_SOCIAL',
        details: `Connected ${platform} account: ${accountName}`,
      });

    return NextResponse.json({
      success: true,
      account: data,
    });

  } catch (error) {
    console.error('Error in social accounts POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
