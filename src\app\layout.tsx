import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as SonnerToaster } from "sonner";
import SupabaseProvider from "@/components/auth/supabase-provider";
import { ForceTailwindClasses } from "@/components/force-tailwind";
import "@/app/globals.css";
import "@/styles/emergency-styles.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "eWasl Social Scheduler",
  description: "منصة إدارة وسائل التواصل الاجتماعي مع دعم متميز للغة العربية",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body
        className={`${inter.variable} antialiased bg-background text-foreground`}
      >
        <SupabaseProvider>
          <ForceTailwindClasses />
          {children}
          <Toaster />
          <SonnerToaster position="top-right" />
        </SupabaseProvider>
      </body>
    </html>
  );
}
