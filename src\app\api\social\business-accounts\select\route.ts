import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { PageSelectionService } from '@/lib/social/business-accounts/page-selection-service';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

const pageSelectionService = new PageSelectionService();

// POST - Select a business account for posting
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Selecting business account...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { platform, userId, businessAccountId } = body;

    if (!platform || !businessAccountId) {
      return NextResponse.json(
        { error: 'Platform and businessAccountId are required' },
        { status: 400 }
      );
    }

    // Validate user access
    if (userId && userId !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    console.log(`Selecting business account ${businessAccountId} for platform: ${platform}`);

    // Select the business account
    await pageSelectionService.selectBusinessAccount(
      user.id,
      platform,
      businessAccountId
    );

    // Get updated configuration
    const config = await pageSelectionService.getBusinessAccountConfig(
      user.id,
      platform
    );

    return NextResponse.json({
      success: true,
      message: 'Business account selected successfully',
      platform: config.platform,
      selectedAccount: config.selectedAccount,
      businessAccounts: config.businessAccounts,
    });

  } catch (error) {
    console.error('Error selecting business account:', error);
    return NextResponse.json(
      { 
        error: 'Failed to select business account',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
