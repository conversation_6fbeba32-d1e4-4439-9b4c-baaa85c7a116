/**
 * Media Upload Service
 * Handles file uploads with progress tracking, validation, and CDN integration
 */

import { createServiceRoleClient } from '@/lib/supabase/server';
import CDNManager, { CDNConfig, MediaMetadata } from './cdn-config';
import ImageOptimizer from './image-optimizer';
import VideoProcessor from './video-processor';

export interface UploadProgress {
  uploadId: string;
  filename: string;
  totalSize: number;
  uploadedSize: number;
  percentage: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
  cdnUrl?: string;
  estimatedTimeRemaining?: number;
}

export interface UploadOptions {
  userId: string;
  folder?: string;
  isPublic?: boolean;
  generateThumbnail?: boolean;
  optimizeForPlatforms?: string[];
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
}

export interface UploadResult {
  success: boolean;
  fileId: string;
  cdnUrl: string;
  metadata: MediaMetadata;
  optimizedVersions?: OptimizedVersion[];
  error?: string;
}

export interface OptimizedVersion {
  platform: string;
  format: string;
  quality: string;
  url: string;
  dimensions: {
    width: number;
    height: number;
  };
  size: number;
}

export class MediaUploadService {
  private supabase = createServiceRoleClient();
  private cdnManager: CDNManager;
  private imageOptimizer: ImageOptimizer;
  private videoProcessor: VideoProcessor;
  private uploadProgress: Map<string, UploadProgress> = new Map();

  constructor() {
    // Initialize CDN manager with configuration
    const cdnConfig: CDNConfig = {
      provider: (process.env.CDN_PROVIDER as any) || 'vercel',
      bucket: process.env.CDN_BUCKET || 'ewasl-media',
      region: process.env.CDN_REGION || 'us-east-1',
      cdnDomain: process.env.CDN_DOMAIN || 'cdn.ewasl.com',
      accessKey: process.env.CDN_ACCESS_KEY,
      secretKey: process.env.CDN_SECRET_KEY,
      maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '50000000'), // 50MB
      allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi', 'webm'],
      cacheTTL: 31536000, // 1 year
    };

    this.cdnManager = new CDNManager(cdnConfig);
    this.imageOptimizer = new ImageOptimizer();
    this.videoProcessor = new VideoProcessor();
  }

  /**
   * Upload file with progress tracking
   */
  async uploadFile(
    file: File | Buffer,
    filename: string,
    options: UploadOptions
  ): Promise<UploadResult> {
    const uploadId = this.generateUploadId();
    
    try {
      console.log('Starting file upload:', {
        uploadId,
        filename,
        size: file instanceof File ? file.size : file.length,
        userId: options.userId
      });

      // Initialize progress tracking
      this.initializeProgress(uploadId, filename, file);

      // Validate file
      const validation = this.validateFile(file, filename);
      if (!validation.isValid) {
        throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
      }

      // Update progress
      this.updateProgress(uploadId, { status: 'uploading', percentage: 10 });

      // Upload to CDN
      const cdnResult = await this.cdnManager.uploadFile(file, filename, {
        contentType: this.getMimeType(filename),
        isPublic: options.isPublic,
        metadata: {
          userId: options.userId,
          folder: options.folder || 'general',
          uploadId,
        }
      });

      if (!cdnResult.success) {
        throw new Error(cdnResult.error || 'CDN upload failed');
      }

      // Update progress
      this.updateProgress(uploadId, { status: 'processing', percentage: 60 });

      // Store in database
      const { data: mediaFile, error: dbError } = await this.supabase
        .from('media_files')
        .insert({
          id: cdnResult.fileId,
          user_id: options.userId,
          original_filename: filename,
          file_type: this.getMimeType(filename),
          file_size: cdnResult.metadata.size,
          dimensions: cdnResult.metadata.dimensions,
          cdn_url: cdnResult.cdnUrl,
          metadata: cdnResult.metadata,
          processing_status: 'completed',
          folder: options.folder || 'general',
          is_public: options.isPublic || false,
        })
        .select()
        .single();

      if (dbError) {
        console.error('Database storage error:', dbError);
        // Don't fail the upload, just log the error
      }

      // Generate optimized versions if requested
      let optimizedVersions: OptimizedVersion[] = [];
      if (options.optimizeForPlatforms && options.optimizeForPlatforms.length > 0) {
        this.updateProgress(uploadId, { status: 'processing', percentage: 80 });
        optimizedVersions = await this.generateOptimizedVersionsV2(
          file,
          cdnResult.metadata,
          options.optimizeForPlatforms
        );
      }

      // Update progress - completed
      this.updateProgress(uploadId, { 
        status: 'completed', 
        percentage: 100,
        cdnUrl: cdnResult.cdnUrl
      });

      console.log('File upload completed successfully:', {
        uploadId,
        fileId: cdnResult.fileId,
        cdnUrl: cdnResult.cdnUrl
      });

      return {
        success: true,
        fileId: cdnResult.fileId,
        cdnUrl: cdnResult.cdnUrl,
        metadata: cdnResult.metadata,
        optimizedVersions,
      };

    } catch (error) {
      console.error('File upload error:', error);
      
      // Update progress - failed
      this.updateProgress(uploadId, { 
        status: 'failed', 
        error: error instanceof Error ? error.message : String(error) 
      });

      return {
        success: false,
        fileId: '',
        cdnUrl: '',
        metadata: {} as MediaMetadata,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Upload multiple files with batch processing
   */
  async uploadMultipleFiles(
    files: (File | Buffer)[],
    filenames: string[],
    options: UploadOptions
  ): Promise<UploadResult[]> {
    console.log(`Starting batch upload of ${files.length} files`);

    const results: UploadResult[] = [];
    const batchSize = 3; // Process 3 files at a time

    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      const batchFilenames = filenames.slice(i, i + batchSize);

      const batchPromises = batch.map((file, index) =>
        this.uploadFile(file, batchFilenames[index], options)
      );

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            fileId: '',
            cdnUrl: '',
            metadata: {} as MediaMetadata,
            error: result.reason?.message || 'Upload failed'
          });
        }
      });

      // Small delay between batches to avoid overwhelming the CDN
      if (i + batchSize < files.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`Batch upload completed: ${results.filter(r => r.success).length}/${files.length} successful`);
    return results;
  }

  /**
   * Get upload progress
   */
  getUploadProgress(uploadId: string): UploadProgress | null {
    return this.uploadProgress.get(uploadId) || null;
  }

  /**
   * Delete file from CDN and database
   */
  async deleteFile(fileId: string, userId: string): Promise<boolean> {
    try {
      console.log('Deleting file:', { fileId, userId });

      // Delete from CDN
      const cdnDeleted = await this.cdnManager.deleteFile(fileId);
      
      // Delete from database
      const { error: dbError } = await this.supabase
        .from('media_files')
        .delete()
        .eq('id', fileId)
        .eq('user_id', userId);

      if (dbError) {
        console.error('Database deletion error:', dbError);
      }

      console.log('File deletion result:', { 
        fileId, 
        cdnDeleted, 
        dbDeleted: !dbError 
      });

      return cdnDeleted && !dbError;

    } catch (error) {
      console.error('File deletion error:', error);
      return false;
    }
  }

  /**
   * Get user's media files
   */
  async getUserMediaFiles(
    userId: string,
    folder?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<any[]> {
    try {
      let query = this.supabase
        .from('media_files')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (folder) {
        query = query.eq('folder', folder);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch media files: ${error instanceof Error ? error.message : String(error)}`);
      }

      return data || [];

    } catch (error) {
      console.error('Get media files error:', error);
      return [];
    }
  }

  /**
   * Generate optimized versions for different platforms using V2 processors
   */
  private async generateOptimizedVersionsV2(
    file: File | Buffer,
    metadata: MediaMetadata,
    platforms: string[]
  ): Promise<OptimizedVersion[]> {
    const optimizedVersions: OptimizedVersion[] = [];
    const fileBuffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file;
    const isImage = metadata.mimeType?.startsWith('image/');
    const isVideo = metadata.mimeType?.startsWith('video/');

    console.log('Generating optimized versions V2:', {
      platforms,
      isImage,
      isVideo,
      originalSize: fileBuffer.length
    });

    for (const platform of platforms) {
      try {
        if (isImage) {
          // Use image optimizer
          const result = await this.imageOptimizer.optimizeForPlatform(fileBuffer, platform);

          if (result.success) {
            // Upload optimized version to CDN
            const optimizedCdnResult = await this.cdnManager.uploadFile(
              result.optimizedBuffer,
              `${platform}-optimized-${Date.now()}.${result.format}`
            );

            if (optimizedCdnResult.success) {
              optimizedVersions.push({
                platform,
                format: result.format,
                quality: 'optimized',
                url: optimizedCdnResult.cdnUrl,
                dimensions: result.dimensions,
                size: result.optimizedSize,
              });
            }
          }
        } else if (isVideo) {
          // Use video processor
          const result = await this.videoProcessor.processForPlatform(fileBuffer, platform);

          if (result.success) {
            // Upload processed version to CDN
            const processedCdnResult = await this.cdnManager.uploadFile(
              result.processedBuffer,
              `${platform}-processed-${Date.now()}.${result.format}`
            );

            if (processedCdnResult.success) {
              optimizedVersions.push({
                platform,
                format: result.format,
                quality: 'processed',
                url: processedCdnResult.cdnUrl,
                dimensions: result.dimensions,
                size: result.processedSize,
              });
            }
          }
        }

      } catch (error) {
        console.error(`Failed to optimize for ${platform}:`, error);
      }
    }

    console.log(`Generated ${optimizedVersions.length} optimized versions`);
    return optimizedVersions;
  }

  /**
   * Generate optimized versions for different platforms (legacy method)
   */
  private async generateOptimizedVersions(
    originalUrl: string,
    metadata: MediaMetadata,
    platforms: string[]
  ): Promise<OptimizedVersion[]> {
    const optimizedVersions: OptimizedVersion[] = [];

    // Platform-specific optimization settings
    const platformSettings = {
      instagram: { width: 1080, height: 1080, quality: 85 },
      facebook: { width: 1200, height: 630, quality: 80 },
      twitter: { width: 1024, height: 512, quality: 80 },
      linkedin: { width: 1200, height: 627, quality: 85 },
    };

    for (const platform of platforms) {
      const settings = platformSettings[platform.toLowerCase()];
      if (!settings) continue;

      try {
        // This would integrate with image processing service
        // For now, we'll create placeholder optimized versions
        const optimizedVersion: OptimizedVersion = {
          platform,
          format: 'webp',
          quality: 'high',
          url: `${originalUrl}?w=${settings.width}&h=${settings.height}&q=${settings.quality}`,
          dimensions: {
            width: settings.width,
            height: settings.height,
          },
          size: Math.floor(metadata.size * 0.7), // Estimated 30% reduction
        };

        optimizedVersions.push(optimizedVersion);

      } catch (error) {
        console.error(`Failed to optimize for ${platform}:`, error);
      }
    }

    return optimizedVersions;
  }

  /**
   * Initialize upload progress tracking
   */
  private initializeProgress(uploadId: string, filename: string, file: File | Buffer): void {
    const size = file instanceof File ? file.size : file.length;
    
    this.uploadProgress.set(uploadId, {
      uploadId,
      filename,
      totalSize: size,
      uploadedSize: 0,
      percentage: 0,
      status: 'pending',
    });
  }

  /**
   * Update upload progress
   */
  private updateProgress(uploadId: string, updates: Partial<UploadProgress>): void {
    const current = this.uploadProgress.get(uploadId);
    if (current) {
      this.uploadProgress.set(uploadId, { ...current, ...updates });
    }
  }

  /**
   * Generate unique upload ID
   */
  private generateUploadId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File | Buffer, filename: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const size = file instanceof File ? file.size : file.length;

    // Check file size (50MB limit)
    if (size > 50000000) {
      errors.push('File size exceeds 50MB limit');
    }

    // Check filename
    if (!filename || filename.length > 255) {
      errors.push('Invalid filename');
    }

    // Check file extension
    const extension = filename.split('.').pop()?.toLowerCase();
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi', 'webm'];
    
    if (!extension || !allowedExtensions.includes(extension)) {
      errors.push(`File type not supported: ${extension}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get MIME type from filename
   */
  private getMimeType(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'mp4': 'video/mp4',
      'mov': 'video/quicktime',
      'avi': 'video/x-msvideo',
      'webm': 'video/webm',
    };

    return mimeTypes[extension || ''] || 'application/octet-stream';
  }

  /**
   * Clean up old progress entries
   */
  private cleanupProgress(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [uploadId, progress] of this.uploadProgress.entries()) {
      if (now - new Date(progress.uploadId.split('_')[1]).getTime() > maxAge) {
        this.uploadProgress.delete(uploadId);
      }
    }
  }
}

export default MediaUploadService;
