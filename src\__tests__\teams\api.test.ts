import { NextRequest } from 'next/server';
import { createMocks } from 'node-mocks-http';

// Mock Supabase
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
      order: jest.fn().mockReturnThis(),
    })),
  })),
}));

describe('Team Collaboration API Tests', () => {
  let mockSupabase: any;

  beforeEach(() => {
    const { createClient } = require('@/lib/supabase/server');
    mockSupabase = createClient();
    jest.clearAllMocks();
  });

  describe('Organizations API', () => {
    describe('GET /api/teams/organizations', () => {
      test('should return user organizations', async () => {
        // Mock authenticated user
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock organizations query
        const mockOrganizations = [
          {
            id: 'org-123',
            name: 'Test Organization',
            slug: 'test-org',
            subscription_plan: 'free',
            organization_members: [{ role: 'owner', status: 'active' }],
          },
        ];

        mockSupabase.from().select().eq().eq().order.mockResolvedValue({
          data: mockOrganizations,
          error: null,
        });

        // Mock member and workspace counts
        mockSupabase.from().select().eq().eq.mockResolvedValue({
          count: 5,
          error: null,
        });

        const { GET } = await import('@/app/api/teams/organizations/route');
        const request = new NextRequest('http://localhost:3000/api/teams/organizations');
        const response = await GET(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.organizations).toHaveLength(1);
        expect(result.organizations[0].user_role).toBe('owner');
      });

      test('should return 401 for unauthenticated user', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: null },
          error: new Error('Not authenticated'),
        });

        const { GET } = await import('@/app/api/teams/organizations/route');
        const request = new NextRequest('http://localhost:3000/api/teams/organizations');
        const response = await GET(request);

        expect(response.status).toBe(401);
      });
    });

    describe('POST /api/teams/organizations', () => {
      test('should create new organization', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock slug availability check
        mockSupabase.from().select().eq().single.mockResolvedValue({
          data: null,
          error: null,
        });

        // Mock organization creation
        const mockOrganization = {
          id: 'org-123',
          name: 'New Organization',
          slug: 'new-org',
          subscription_plan: 'free',
          max_users: 3,
        };

        mockSupabase.from().insert().select().single.mockResolvedValue({
          data: mockOrganization,
          error: null,
        });

        // Mock member addition
        mockSupabase.from().insert.mockResolvedValue({
          error: null,
        });

        const { POST } = await import('@/app/api/teams/organizations/route');
        const request = new NextRequest('http://localhost:3000/api/teams/organizations', {
          method: 'POST',
          body: JSON.stringify({
            name: 'New Organization',
            slug: 'new-org',
            subscription_plan: 'free',
          }),
        });

        const response = await POST(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.organization.name).toBe('New Organization');
      });

      test('should return 409 for duplicate slug', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock existing organization with same slug
        mockSupabase.from().select().eq().single.mockResolvedValue({
          data: { id: 'existing-org' },
          error: null,
        });

        const { POST } = await import('@/app/api/teams/organizations/route');
        const request = new NextRequest('http://localhost:3000/api/teams/organizations', {
          method: 'POST',
          body: JSON.stringify({
            name: 'New Organization',
            slug: 'existing-slug',
            subscription_plan: 'free',
          }),
        });

        const response = await POST(request);
        const result = await response.json();

        expect(response.status).toBe(409);
        expect(result.error).toContain('slug already exists');
      });

      test('should validate input data', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        const { POST } = await import('@/app/api/teams/organizations/route');
        const request = new NextRequest('http://localhost:3000/api/teams/organizations', {
          method: 'POST',
          body: JSON.stringify({
            name: '', // Invalid: empty name
            slug: 'invalid slug!', // Invalid: contains special characters
          }),
        });

        const response = await POST(request);
        const result = await response.json();

        expect(response.status).toBe(400);
        expect(result.error).toBe('Invalid input');
        expect(result.details).toBeDefined();
      });
    });
  });

  describe('Workspaces API', () => {
    describe('GET /api/teams/workspaces', () => {
      test('should return organization workspaces', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock organization membership check
        mockSupabase.from().select().eq().eq().eq().single.mockResolvedValue({
          data: { role: 'owner', status: 'active' },
          error: null,
        });

        // Mock workspaces query
        const mockWorkspaces = [
          {
            id: 'workspace-123',
            name: 'Test Workspace',
            slug: 'test-workspace',
            color: '#3b82f6',
            workspace_members: [{ role: 'admin', status: 'active' }],
          },
        ];

        mockSupabase.from().select().eq().eq().eq().eq().order.mockResolvedValue({
          data: mockWorkspaces,
          error: null,
        });

        // Mock counts
        mockSupabase.from().select().eq().eq.mockResolvedValue({
          count: 3,
          error: null,
        });

        const { GET } = await import('@/app/api/teams/workspaces/route');
        const request = new NextRequest('http://localhost:3000/api/teams/workspaces?organization_id=org-123');
        const response = await GET(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.workspaces).toHaveLength(1);
        expect(result.workspaces[0].user_role).toBe('admin');
      });

      test('should return 400 without organization_id', async () => {
        const { GET } = await import('@/app/api/teams/workspaces/route');
        const request = new NextRequest('http://localhost:3000/api/teams/workspaces');
        const response = await GET(request);

        expect(response.status).toBe(400);
      });
    });

    describe('POST /api/teams/workspaces', () => {
      test('should create new workspace', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock permission check
        mockSupabase.from().select().eq().eq().eq().single.mockResolvedValue({
          data: { role: 'admin' },
          error: null,
        });

        // Mock slug availability check
        mockSupabase.from().select().eq().eq().single.mockResolvedValue({
          data: null,
          error: null,
        });

        // Mock workspace creation
        const mockWorkspace = {
          id: 'workspace-123',
          name: 'New Workspace',
          slug: 'new-workspace',
          color: '#3b82f6',
        };

        mockSupabase.from().insert().select().single.mockResolvedValue({
          data: mockWorkspace,
          error: null,
        });

        // Mock member addition and workflow creation
        mockSupabase.from().insert.mockResolvedValue({
          error: null,
        });

        const { POST } = await import('@/app/api/teams/workspaces/route');
        const request = new NextRequest('http://localhost:3000/api/teams/workspaces', {
          method: 'POST',
          body: JSON.stringify({
            organization_id: 'org-123',
            name: 'New Workspace',
            slug: 'new-workspace',
            color: '#3b82f6',
          }),
        });

        const response = await POST(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.workspace.name).toBe('New Workspace');
      });

      test('should return 403 for insufficient permissions', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock permission check - user is only a viewer
        mockSupabase.from().select().eq().eq().eq().single.mockResolvedValue({
          data: { role: 'viewer' },
          error: null,
        });

        const { POST } = await import('@/app/api/teams/workspaces/route');
        const request = new NextRequest('http://localhost:3000/api/teams/workspaces', {
          method: 'POST',
          body: JSON.stringify({
            organization_id: 'org-123',
            name: 'New Workspace',
            slug: 'new-workspace',
          }),
        });

        const response = await POST(request);

        expect(response.status).toBe(403);
      });
    });
  });

  describe('Members API', () => {
    describe('GET /api/teams/members', () => {
      test('should return workspace members', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        const mockMembers = [
          {
            id: 'member-123',
            user_id: 'user-123',
            role: 'admin',
            status: 'active',
            user: {
              email: '<EMAIL>',
              raw_user_meta_data: { full_name: 'Test User' },
            },
          },
        ];

        mockSupabase.from().select().eq().eq().order.mockResolvedValue({
          data: mockMembers,
          error: null,
        });

        const { GET } = await import('@/app/api/teams/members/route');
        const request = new NextRequest('http://localhost:3000/api/teams/members?workspace_id=workspace-123');
        const response = await GET(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.members).toHaveLength(1);
        expect(result.user_role).toBe('admin');
      });
    });

    describe('POST /api/teams/members', () => {
      test('should invite new member', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock permission check
        mockSupabase.from().select().eq().eq().eq().single.mockResolvedValue({
          data: { role: 'admin' },
          error: null,
        });

        // Mock existing user check
        mockSupabase.from().select().eq().single.mockResolvedValue({
          data: { id: 'existing-user-456' },
          error: null,
        });

        // Mock existing member check
        mockSupabase.from().select().eq().eq().single.mockResolvedValue({
          data: null,
          error: null,
        });

        // Mock member addition
        const mockMember = {
          id: 'member-456',
          workspace_id: 'workspace-123',
          user_id: 'existing-user-456',
          role: 'editor',
        };

        mockSupabase.from().insert().select().single.mockResolvedValue({
          data: mockMember,
          error: null,
        });

        const { POST } = await import('@/app/api/teams/members/route');
        const request = new NextRequest('http://localhost:3000/api/teams/members', {
          method: 'POST',
          body: JSON.stringify({
            workspace_id: 'workspace-123',
            email: '<EMAIL>',
            role: 'editor',
          }),
        });

        const response = await POST(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.member.role).toBe('editor');
      });
    });
  });

  describe('Approvals API', () => {
    describe('GET /api/teams/approvals', () => {
      test('should return pending approvals', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock workspace access check
        mockSupabase.from().select().eq().eq().eq().single.mockResolvedValue({
          data: { role: 'admin' },
          error: null,
        });

        const mockApprovals = [
          {
            id: 'approval-123',
            status: 'pending',
            current_step: 1,
            post: {
              id: 'post-123',
              content: 'Test post content',
              workspace_id: 'workspace-123',
            },
            workflow: {
              id: 'workflow-123',
              name: 'Default Approval',
              steps: [
                {
                  step: 1,
                  name: 'Content Review',
                  required_approvers: 1,
                  approver_roles: ['admin', 'editor'],
                },
              ],
            },
          },
        ];

        mockSupabase.from().select().eq().order.mockResolvedValue({
          data: mockApprovals,
          error: null,
        });

        const { GET } = await import('@/app/api/teams/approvals/route');
        const request = new NextRequest('http://localhost:3000/api/teams/approvals?workspace_id=workspace-123&status=pending');
        const response = await GET(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.approvals).toHaveLength(1);
        expect(result.approvals[0].can_approve).toBe(true);
      });
    });

    describe('POST /api/teams/approvals', () => {
      test('should submit post for approval', async () => {
        mockSupabase.auth.getUser.mockResolvedValue({
          data: { user: { id: 'user-123', email: '<EMAIL>' } },
          error: null,
        });

        // Mock post fetch
        mockSupabase.from().select().eq().single.mockResolvedValue({
          data: {
            id: 'post-123',
            content: 'Test post',
            workspace_id: 'workspace-123',
          },
          error: null,
        });

        // Mock workspace access
        mockSupabase.from().select().eq().eq().eq().single.mockResolvedValue({
          data: { role: 'editor' },
          error: null,
        });

        // Mock workflow fetch
        mockSupabase.from().select().eq().eq().eq().single.mockResolvedValue({
          data: {
            id: 'workflow-123',
            name: 'Default Approval',
            steps: [{ step: 1, approver_roles: ['admin'] }],
          },
          error: null,
        });

        // Mock existing approval check
        mockSupabase.from().select().eq().single.mockResolvedValue({
          data: null,
          error: null,
        });

        // Mock approval creation
        const mockApproval = {
          id: 'approval-123',
          post_id: 'post-123',
          workflow_id: 'workflow-123',
          status: 'pending',
        };

        mockSupabase.from().insert().select().single.mockResolvedValue({
          data: mockApproval,
          error: null,
        });

        // Mock post status update and notifications
        mockSupabase.from().update().eq.mockResolvedValue({
          error: null,
        });

        mockSupabase.from().insert.mockResolvedValue({
          error: null,
        });

        const { POST } = await import('@/app/api/teams/approvals/route');
        const request = new NextRequest('http://localhost:3000/api/teams/approvals', {
          method: 'POST',
          body: JSON.stringify({
            action: 'submit',
            post_id: 'post-123',
            message: 'Please review this post',
          }),
        });

        const response = await POST(request);
        const result = await response.json();

        expect(response.status).toBe(200);
        expect(result.success).toBe(true);
        expect(result.approval.status).toBe('pending');
      });
    });
  });
});
