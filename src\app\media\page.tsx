'use client';

import React, { useState, useEffect } from 'react';
import { Upload, Search, Filter, Grid, List, Trash2, Download, Eye, Plus } from 'lucide-react';
import { MediaUpload } from '@/components/media/media-upload';
import { MediaLibrary } from '@/components/media/media-library';
import { createClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface MediaFile {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  createdAt: string;
}

export default function MediaPage() {
  const [media, setMedia] = useState<MediaFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'image' | 'video'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showUpload, setShowUpload] = useState(false);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    checkAuthAndLoadMedia();
  }, [filterType]);

  const checkAuthAndLoadMedia = async () => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      router.push('/auth/signin');
      return;
    }

    setUser(user);
    await loadMedia();
  };

  const loadMedia = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (filterType !== 'all') {
        params.append('type', filterType);
      }

      const response = await fetch(`/api/media?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load media');
      }

      setMedia(data.media || []);
    } catch (error: any) {
      console.error('Error loading media:', error);
      toast.error('فشل في تحميل الملفات');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteMedia = async (mediaId: string) => {
    try {
      const response = await fetch('/api/media', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mediaId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete media');
      }

      setMedia(media.filter(m => m.id !== mediaId));
      toast.success('تم حذف الملف بنجاح');
    } catch (error: any) {
      console.error('Error deleting media:', error);
      toast.error('فشل في حذف الملف');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const filteredMedia = media.filter(file =>
    file.fileName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif'
    }}>
      {/* Header */}
      <div style={{
        background: 'white',
        borderBottom: '1px solid #e5e7eb',
        padding: '1rem 2rem'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h1 style={{
              fontSize: '1.875rem',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              margin: 0
            }}>
              📁 مكتبة الوسائط
            </h1>
            <p style={{ color: '#6b7280', margin: '0.25rem 0 0 0' }}>
              إدارة الصور والفيديوهات الخاصة بك
            </p>
          </div>

          <button
            onClick={() => setShowUpload(true)}
            style={{
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.5rem',
              border: 'none',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <Plus className="h-4 w-4" />
            رفع ملف جديد
          </button>
        </div>
      </div>

      {/* Controls */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem'
      }}>
        <div style={{
          background: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          marginBottom: '2rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'flex',
            gap: '1rem',
            alignItems: 'center',
            flexWrap: 'wrap'
          }}>
            {/* Search */}
            <div style={{ position: 'relative', flex: '1', minWidth: '200px' }}>
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في الملفات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem 2.5rem 0.75rem 1rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.5rem',
                  fontSize: '0.875rem'
                }}
              />
            </div>

            {/* Filter */}
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              {[
                { key: 'all', label: 'الكل', icon: '📁' },
                { key: 'image', label: 'الصور', icon: '🖼️' },
                { key: 'video', label: 'الفيديو', icon: '🎥' }
              ].map((filter) => (
                <button
                  key={filter.key}
                  onClick={() => setFilterType(filter.key as any)}
                  style={{
                    padding: '0.5rem 1rem',
                    borderRadius: '0.375rem',
                    border: '1px solid #d1d5db',
                    background: filterType === filter.key ? '#2563eb' : 'white',
                    color: filterType === filter.key ? 'white' : '#374151',
                    fontSize: '0.875rem',
                    cursor: 'pointer'
                  }}
                >
                  {filter.icon} {filter.label}
                </button>
              ))}
            </div>

            {/* View Mode */}
            <div style={{ display: 'flex', gap: '0.25rem' }}>
              <button
                onClick={() => setViewMode('grid')}
                style={{
                  padding: '0.5rem',
                  borderRadius: '0.375rem',
                  border: '1px solid #d1d5db',
                  background: viewMode === 'grid' ? '#2563eb' : 'white',
                  color: viewMode === 'grid' ? 'white' : '#374151',
                  cursor: 'pointer'
                }}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                style={{
                  padding: '0.5rem',
                  borderRadius: '0.375rem',
                  border: '1px solid #d1d5db',
                  background: viewMode === 'list' ? '#2563eb' : 'white',
                  color: viewMode === 'list' ? 'white' : '#374151',
                  cursor: 'pointer'
                }}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Media Grid/List */}
        <div style={{
          background: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        }}>
          {isLoading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>🔄</div>
                <p style={{ color: '#6b7280', margin: 0 }}>جاري التحميل...</p>
              </div>
            </div>
          ) : filteredMedia.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '3rem',
              color: '#6b7280'
            }}>
              <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>📁</div>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                لا توجد ملفات
              </h3>
              <p style={{ margin: 0 }}>ابدأ برفع الصور والفيديوهات</p>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: viewMode === 'grid' ? 'repeat(auto-fill, minmax(200px, 1fr))' : '1fr',
              gap: '1rem'
            }} className="responsive grid-layout">
              {filteredMedia.map((file) => {
                const isImage = file.fileType.startsWith('image');
                const isVideo = file.fileType.startsWith('video');

                return (
                  <div
                    key={file.id}
                    style={{
                      border: '1px solid #e5e7eb',
                      borderRadius: '0.5rem',
                      overflow: 'hidden',
                      background: 'white',
                      transition: 'all 0.2s',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    {viewMode === 'grid' ? (
                      <>
                        {/* Grid View */}
                        <div style={{
                          aspectRatio: '1',
                          background: '#f3f4f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          position: 'relative'
                        }}>
                          {isImage ? (
                            <img
                              src={file.publicUrl}
                              alt={file.fileName}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover'
                              }}
                            />
                          ) : (
                            <div style={{ textAlign: 'center', color: '#6b7280' }}>
                              {isVideo ? '🎥' : '📎'}
                              <div style={{ fontSize: '0.75rem', marginTop: '0.5rem' }}>
                                {isVideo ? 'فيديو' : 'ملف'}
                              </div>
                            </div>
                          )}

                          {/* Actions */}
                          <div style={{
                            position: 'absolute',
                            top: '0.5rem',
                            left: '0.5rem',
                            display: 'flex',
                            gap: '0.25rem'
                          }}>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                window.open(file.publicUrl, '_blank');
                              }}
                              style={{
                                padding: '0.25rem',
                                background: 'rgba(255, 255, 255, 0.9)',
                                borderRadius: '0.25rem',
                                border: 'none',
                                cursor: 'pointer'
                              }}
                            >
                              <Eye className="h-3 w-3" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteMedia(file.id);
                              }}
                              style={{
                                padding: '0.25rem',
                                background: 'rgba(255, 255, 255, 0.9)',
                                borderRadius: '0.25rem',
                                border: 'none',
                                cursor: 'pointer',
                                color: '#ef4444'
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          </div>
                        </div>

                        <div style={{ padding: '0.75rem' }}>
                          <p style={{
                            fontSize: '0.875rem',
                            fontWeight: '500',
                            margin: '0 0 0.25rem 0',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {file.fileName}
                          </p>
                          <p style={{
                            fontSize: '0.75rem',
                            color: '#6b7280',
                            margin: 0
                          }}>
                            {formatFileSize(file.fileSize)} • {formatDate(file.createdAt)}
                          </p>
                        </div>
                      </>
                    ) : (
                      /* List View */
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        padding: '1rem'
                      }}>
                        <div style={{
                          width: '3rem',
                          height: '3rem',
                          background: '#f3f4f6',
                          borderRadius: '0.375rem',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          {isImage ? (
                            <img
                              src={file.publicUrl}
                              alt={file.fileName}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                                borderRadius: '0.375rem'
                              }}
                            />
                          ) : (
                            <span style={{ fontSize: '1.5rem' }}>
                              {isVideo ? '🎥' : '📎'}
                            </span>
                          )}
                        </div>

                        <div style={{ flex: 1 }}>
                          <p style={{
                            fontSize: '0.875rem',
                            fontWeight: '500',
                            margin: '0 0 0.25rem 0'
                          }}>
                            {file.fileName}
                          </p>
                          <p style={{
                            fontSize: '0.75rem',
                            color: '#6b7280',
                            margin: 0
                          }}>
                            {formatFileSize(file.fileSize)} • {formatDate(file.createdAt)}
                          </p>
                        </div>

                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            onClick={() => window.open(file.publicUrl, '_blank')}
                            style={{
                              padding: '0.5rem',
                              background: '#f3f4f6',
                              borderRadius: '0.375rem',
                              border: 'none',
                              cursor: 'pointer'
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => deleteMedia(file.id)}
                            style={{
                              padding: '0.5rem',
                              background: '#fef2f2',
                              borderRadius: '0.375rem',
                              border: 'none',
                              cursor: 'pointer',
                              color: '#ef4444'
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Upload Modal */}
      {showUpload && (
        <div style={{
          position: 'fixed',
          inset: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 50
        }}>
          <div style={{
            background: 'white',
            borderRadius: '0.75rem',
            padding: '2rem',
            maxWidth: '500px',
            width: '90%'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                margin: 0
              }}>
                رفع ملف جديد
              </h2>
              <button
                onClick={() => setShowUpload(false)}
                style={{
                  padding: '0.5rem',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  borderRadius: '0.375rem'
                }}
              >
                ✕
              </button>
            </div>

            <MediaUpload
              onUploadComplete={(newMedia) => {
                setMedia([newMedia, ...media]);
                setShowUpload(false);
                toast.success('تم رفع الملف بنجاح!');
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
