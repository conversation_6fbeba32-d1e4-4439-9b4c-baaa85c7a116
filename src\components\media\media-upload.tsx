'use client';

import React, { useState, useRef } from 'react';
import { Upload, X, Image, Video, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface MediaFile {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  createdAt: string;
}

interface MediaUploadProps {
  onUploadComplete?: (media: MediaFile) => void;
  onUploadStart?: () => void;
  acceptedTypes?: string[];
  maxSize?: number; // in bytes
  className?: string;
}

export function MediaUpload({
  onUploadComplete,
  onUploadStart,
  acceptedTypes = ['image/*', 'video/*'],
  maxSize = 50 * 1024 * 1024, // 50MB
  className = ''
}: MediaUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSize) {
      return `حجم الملف كبير جداً. الحد الأقصى ${formatFileSize(maxSize)}`;
    }

    // Check file type
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    
    if (!isImage && !isVideo) {
      return 'نوع الملف غير مدعوم. يرجى اختيار صورة أو فيديو';
    }

    // Check specific image types
    if (isImage) {
      const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedImageTypes.includes(file.type)) {
        return 'نوع الصورة غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP';
      }
    }

    // Check specific video types
    if (isVideo) {
      const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/quicktime'];
      if (!allowedVideoTypes.includes(file.type)) {
        return 'نوع الفيديو غير مدعوم. الأنواع المدعومة: MP4, WebM, MOV';
      }
    }

    return null;
  };

  const uploadFile = async (file: File) => {
    setIsUploading(true);
    onUploadStart?.();

    try {
      // Validate file
      const validationError = validateFile(file);
      if (validationError) {
        toast.error(validationError);
        return;
      }

      console.log('Uploading file:', file.name);

      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Upload to API
      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'فشل في رفع الملف');
      }

      console.log('Upload successful:', data.media);
      toast.success('تم رفع الملف بنجاح!');
      onUploadComplete?.(data.media);

    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.message || 'حدث خطأ أثناء رفع الملف');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      uploadFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      uploadFile(e.target.files[0]);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        onChange={handleFileSelect}
        className="hidden"
        disabled={isUploading}
      />

      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all
          ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={!isUploading ? openFileDialog : undefined}
      >
        {isUploading ? (
          <div className="flex flex-col items-center gap-3">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <p className="text-sm text-gray-600">جاري رفع الملف...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-3">
            <div className="flex items-center gap-2">
              <Upload className="h-8 w-8 text-gray-400" />
              <Image className="h-6 w-6 text-gray-400" />
              <Video className="h-6 w-6 text-gray-400" />
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-700 mb-1">
                اسحب وأفلت الملفات هنا أو انقر للاختيار
              </p>
              <p className="text-xs text-gray-500">
                الصور: JPEG, PNG, GIF, WebP • الفيديو: MP4, WebM, MOV
              </p>
              <p className="text-xs text-gray-500 mt-1">
                الحد الأقصى: {formatFileSize(maxSize)}
              </p>
            </div>

            <div className="flex gap-2">
              <button
                type="button"
                className="px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  openFileDialog();
                }}
              >
                📷 اختيار صورة
              </button>
              <button
                type="button"
                className="px-4 py-2 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  openFileDialog();
                }}
              >
                🎥 اختيار فيديو
              </button>
            </div>
          </div>
        )}
      </div>

      {dragActive && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-10 border-2 border-blue-500 border-dashed rounded-lg flex items-center justify-center">
          <p className="text-blue-600 font-medium">أفلت الملف هنا</p>
        </div>
      )}
    </div>
  );
}

export default MediaUpload;
