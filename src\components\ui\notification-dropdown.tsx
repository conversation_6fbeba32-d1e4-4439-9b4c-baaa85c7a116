"use client";

import React from 'react';
import { <PERSON>, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuTrigger, 
  DropdownMenuItem,
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { Notification } from '@/hooks/useNotifications';

interface NotificationDropdownProps {
  notifications: Notification[];
  unreadCount: number;
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  language: 'ar' | 'en';
}

const NotificationDropdown = ({ 
  notifications, 
  unreadCount, 
  onMarkAsRead, 
  onMarkAllAsRead,
  language 
}: NotificationDropdownProps) => {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'info': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (hours < 1) {
      const minutes = Math.floor(diff / (1000 * 60));
      return language === 'ar' ? `منذ ${minutes} دقيقة` : `${minutes}m ago`;
    }
    return language === 'ar' ? `منذ ${hours} ساعة` : `${hours}h ago`;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="relative rounded-xl hover:bg-gray-100 h-9 w-9 sm:h-10 sm:w-10"
        >
          <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
          {unreadCount > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center font-medium">
              {unreadCount > 9 ? '9+' : unreadCount}
            </div>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align={language === 'ar' ? 'start' : 'end'} 
        className="w-80 max-h-96 overflow-y-auto"
      >
        <div className={cn("px-4 py-2 border-b", language === 'ar' ? 'text-right' : 'text-left')}>
          <div className={cn("flex items-center justify-between", language === 'ar' ? 'flex-row-reverse' : '')}>
            <h3 className="font-semibold text-sm">
              {language === 'ar' ? 'الإشعارات' : 'Notifications'}
            </h3>
            {unreadCount > 0 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onMarkAllAsRead}
                className="text-xs h-6 px-2"
              >
                <Check className="w-3 h-3 mr-1" />
                {language === 'ar' ? 'قراءة الكل' : 'Mark all read'}
              </Button>
            )}
          </div>
          {unreadCount > 0 && (
            <p className="text-xs text-gray-500 mt-1">
              {language === 'ar' ? `${unreadCount} إشعارات غير مقروءة` : `${unreadCount} unread notifications`}
            </p>
          )}
        </div>
        
        {notifications.length === 0 ? (
          <div className="px-4 py-8 text-center">
            <Bell className="w-8 h-8 text-gray-300 mx-auto mb-2" />
            <p className="text-sm text-gray-500">
              {language === 'ar' ? 'لا توجد إشعارات' : 'No notifications'}
            </p>
          </div>
        ) : (
          notifications.slice(0, 10).map((notification) => (
            <DropdownMenuItem
              key={notification.id}
              className={cn(
                "px-4 py-3 cursor-pointer focus:bg-gray-50",
                !notification.read && "bg-blue-50",
                language === 'ar' ? 'text-right' : 'text-left'
              )}
              onClick={() => !notification.read && onMarkAsRead(notification.id)}
            >
              <div className={cn("flex gap-3 w-full", language === 'ar' ? 'flex-row-reverse' : '')}>
                <div className={cn("w-2 h-2 rounded-full mt-2 flex-shrink-0", getTypeColor(notification.type))} />
                <div className="flex-1 min-w-0">
                  <div className={cn("flex items-start justify-between gap-2", language === 'ar' ? 'flex-row-reverse' : '')}>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm text-gray-900 line-clamp-1">
                        {notification.title}
                      </h4>
                      <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {formatTime(notification.timestamp)}
                      </p>
                    </div>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0" />
                    )}
                  </div>
                </div>
              </div>
            </DropdownMenuItem>
          ))
        )}
        
        {notifications.length > 10 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="justify-center py-2">
              <span className="text-sm text-blue-600 font-medium">
                {language === 'ar' ? 'عرض جميع الإشعارات' : 'View all notifications'}
              </span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationDropdown;
