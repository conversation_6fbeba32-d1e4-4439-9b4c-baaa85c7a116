/**
 * Facebook Pages Manager
 * Handles Facebook Business Pages integration and management
 */

import { createClient } from '@/lib/supabase/client';

export interface FacebookPage {
  id: string;
  name: string;
  access_token: string;
  category: string;
  tasks: string[];
  picture?: {
    data: {
      url: string;
    };
  };
  fan_count?: number;
  is_verified?: boolean;
  website?: string;
}

export interface FacebookPagesResponse {
  data: FacebookPage[];
  paging?: {
    cursors: {
      before: string;
      after: string;
    };
    next?: string;
  };
}

export class FacebookPagesManager {
  private supabase = createClient();

  /**
   * Fetch user's Facebook Pages using their access token
   */
  async fetchUserPages(userAccessToken: string): Promise<FacebookPage[]> {
    try {
      console.log('Fetching Facebook Pages for user...');

      const response = await fetch(
        `https://graph.facebook.com/v19.0/me/accounts?fields=id,name,access_token,category,tasks,picture,fan_count,is_verified,website&access_token=${userAccessToken}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Facebook API error: ${errorData.error?.message || 'Unknown error'}`);
      }

      const data: FacebookPagesResponse = await response.json();
      
      console.log(`Found ${data.data.length} Facebook Pages`);
      
      // Filter pages where user has manage_posts permission
      const manageablePages = data.data.filter(page => 
        page.tasks && page.tasks.includes('MANAGE')
      );

      console.log(`User can manage ${manageablePages.length} pages`);
      
      return manageablePages;

    } catch (error) {
      console.error('Error fetching Facebook Pages:', error);
      throw error;
    }
  }

  /**
   * Store Facebook Pages in database for a user
   */
  async storeUserPages(
    userId: string, 
    socialAccountId: string, 
    pages: FacebookPage[]
  ): Promise<void> {
    try {
      console.log(`Storing ${pages.length} Facebook Pages for user ${userId}`);

      // Prepare pages data for storage
      const pagesData = pages.map(page => ({
        social_account_id: socialAccountId,
        page_id: page.id,
        page_name: page.name,
        page_access_token: page.access_token,
        page_category: page.category,
        page_picture_url: page.picture?.data?.url,
        fan_count: page.fan_count || 0,
        is_verified: page.is_verified || false,
        website: page.website,
        permissions: page.tasks || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      // Delete existing pages for this social account
      const { error: deleteError } = await this.supabase
        .from('facebook_pages')
        .delete()
        .eq('social_account_id', socialAccountId);

      if (deleteError) {
        console.error('Error deleting existing pages:', deleteError);
        throw deleteError;
      }

      // Insert new pages
      const { error: insertError } = await this.supabase
        .from('facebook_pages')
        .insert(pagesData);

      if (insertError) {
        console.error('Error storing Facebook Pages:', insertError);
        throw insertError;
      }

      console.log('Successfully stored Facebook Pages');

    } catch (error) {
      console.error('Error storing Facebook Pages:', error);
      throw error;
    }
  }

  /**
   * Get stored Facebook Pages for a social account
   */
  async getStoredPages(socialAccountId: string): Promise<any[]> {
    try {
      const { data: pages, error } = await this.supabase
        .from('facebook_pages')
        .select('*')
        .eq('social_account_id', socialAccountId)
        .order('page_name');

      if (error) {
        console.error('Error fetching stored pages:', error);
        throw error;
      }

      return pages || [];

    } catch (error) {
      console.error('Error getting stored pages:', error);
      throw error;
    }
  }

  /**
   * Update selected page for posting
   */
  async updateSelectedPage(
    socialAccountId: string, 
    selectedPageId: string
  ): Promise<void> {
    try {
      console.log(`Updating selected page for account ${socialAccountId}: ${selectedPageId}`);

      // Update the social account with selected page
      const { error } = await this.supabase
        .from('social_accounts')
        .update({
          page_id: selectedPageId,
          updated_at: new Date().toISOString(),
        })
        .eq('id', socialAccountId);

      if (error) {
        console.error('Error updating selected page:', error);
        throw error;
      }

      console.log('Successfully updated selected page');

    } catch (error) {
      console.error('Error updating selected page:', error);
      throw error;
    }
  }

  /**
   * Get page access token for posting
   */
  async getPageAccessToken(
    socialAccountId: string, 
    pageId: string
  ): Promise<string | null> {
    try {
      const { data: page, error } = await this.supabase
        .from('facebook_pages')
        .select('page_access_token')
        .eq('social_account_id', socialAccountId)
        .eq('page_id', pageId)
        .single();

      if (error) {
        console.error('Error fetching page access token:', error);
        return null;
      }

      return page?.page_access_token || null;

    } catch (error) {
      console.error('Error getting page access token:', error);
      return null;
    }
  }

  /**
   * Validate page permissions for posting
   */
  async validatePagePermissions(
    pageAccessToken: string, 
    pageId: string
  ): Promise<boolean> {
    try {
      console.log(`Validating permissions for page ${pageId}`);

      const response = await fetch(
        `https://graph.facebook.com/v19.0/${pageId}?fields=tasks&access_token=${pageAccessToken}`
      );

      if (!response.ok) {
        console.error('Failed to validate page permissions');
        return false;
      }

      const data = await response.json();
      const tasks = data.tasks || [];

      // Check if page has required permissions
      const hasManagePermission = tasks.includes('MANAGE');
      const hasCreateContentPermission = tasks.includes('CREATE_CONTENT');

      console.log(`Page permissions - MANAGE: ${hasManagePermission}, CREATE_CONTENT: ${hasCreateContentPermission}`);

      return hasManagePermission || hasCreateContentPermission;

    } catch (error) {
      console.error('Error validating page permissions:', error);
      return false;
    }
  }

  /**
   * Refresh page access tokens
   */
  async refreshPageTokens(
    socialAccountId: string, 
    userAccessToken: string
  ): Promise<void> {
    try {
      console.log('Refreshing Facebook Page access tokens...');

      // Fetch fresh pages data
      const pages = await this.fetchUserPages(userAccessToken);
      
      // Update stored pages with new tokens
      await this.storeUserPages('', socialAccountId, pages);

      console.log('Successfully refreshed page tokens');

    } catch (error) {
      console.error('Error refreshing page tokens:', error);
      throw error;
    }
  }

  /**
   * Get page posting configuration
   */
  async getPagePostingConfig(socialAccountId: string): Promise<{
    selectedPageId: string | null;
    availablePages: any[];
    hasValidTokens: boolean;
  }> {
    try {
      // Get social account with selected page
      const { data: account, error: accountError } = await this.supabase
        .from('social_accounts')
        .select('page_id')
        .eq('id', socialAccountId)
        .single();

      if (accountError) {
        throw accountError;
      }

      // Get available pages
      const availablePages = await this.getStoredPages(socialAccountId);

      // Check if tokens are still valid
      let hasValidTokens = false;
      if (availablePages.length > 0) {
        const firstPage = availablePages[0];
        hasValidTokens = await this.validatePagePermissions(
          firstPage.page_access_token,
          firstPage.page_id
        );
      }

      return {
        selectedPageId: account?.page_id || null,
        availablePages,
        hasValidTokens,
      };

    } catch (error) {
      console.error('Error getting page posting config:', error);
      throw error;
    }
  }
}
