'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Play, 
  RefreshCw,
  Database,
  CreditCard,
  Share2,
  BarChart3,
  Settings,
  Zap,
  Shield,
  Globe
} from 'lucide-react';
import { toast } from 'sonner';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  duration?: number;
  details?: any;
}

interface TestSuite {
  name: string;
  icon: any;
  description: string;
  tests: TestResult[];
}

export default function ComprehensiveAPITestingPage() {
  const [testSuites, setTestSuites] = useState<TestSuite[]>([
    {
      name: 'Database & Authentication',
      icon: Database,
      description: 'Supabase connection, user authentication, and database operations',
      tests: [
        { name: 'Supabase Connection', status: 'pending' },
        { name: 'User Authentication', status: 'pending' },
        { name: 'Database Read/Write', status: 'pending' },
        { name: 'RLS Policies', status: 'pending' },
      ]
    },
    {
      name: 'Billing & Payments',
      icon: CreditCard,
      description: 'Stripe integration, subscription management, and payment processing',
      tests: [
        { name: 'Stripe Connection', status: 'pending' },
        { name: 'Create Checkout Session', status: 'pending' },
        { name: 'Customer Portal', status: 'pending' },
        { name: 'Webhook Processing', status: 'pending' },
      ]
    },
    {
      name: 'Social Media APIs',
      icon: Share2,
      description: 'LinkedIn, Facebook, Instagram, and Twitter integrations',
      tests: [
        { name: 'LinkedIn OAuth', status: 'pending' },
        { name: 'Facebook API', status: 'pending' },
        { name: 'Instagram Business', status: 'pending' },
        { name: 'Twitter API v2', status: 'pending' },
      ]
    },
    {
      name: 'Analytics & Insights',
      icon: BarChart3,
      description: 'Data collection, processing, and reporting systems',
      tests: [
        { name: 'Analytics Collection', status: 'pending' },
        { name: 'Real-time Data', status: 'pending' },
        { name: 'Report Generation', status: 'pending' },
        { name: 'Performance Metrics', status: 'pending' },
      ]
    },
    {
      name: 'Content Management',
      icon: Settings,
      description: 'Post creation, scheduling, and media processing',
      tests: [
        { name: 'Post Creation', status: 'pending' },
        { name: 'Media Upload', status: 'pending' },
        { name: 'Scheduler Service', status: 'pending' },
        { name: 'Template System', status: 'pending' },
      ]
    },
    {
      name: 'AI & Automation',
      icon: Zap,
      description: 'OpenAI integration, content generation, and optimization',
      tests: [
        { name: 'OpenAI Connection', status: 'pending' },
        { name: 'Content Generation', status: 'pending' },
        { name: 'Image Optimization', status: 'pending' },
        { name: 'Auto-scheduling', status: 'pending' },
      ]
    },
    {
      name: 'Security & Performance',
      icon: Shield,
      description: 'Rate limiting, security headers, and performance optimization',
      tests: [
        { name: 'Rate Limiting', status: 'pending' },
        { name: 'Security Headers', status: 'pending' },
        { name: 'API Response Times', status: 'pending' },
        { name: 'Error Handling', status: 'pending' },
      ]
    },
    {
      name: 'Production Readiness',
      icon: Globe,
      description: 'Deployment, monitoring, and production environment checks',
      tests: [
        { name: 'Environment Variables', status: 'pending' },
        { name: 'Health Endpoints', status: 'pending' },
        { name: 'Monitoring Setup', status: 'pending' },
        { name: 'Backup Systems', status: 'pending' },
      ]
    }
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string | null>(null);

  const runTest = async (suiteIndex: number, testIndex: number) => {
    const newTestSuites = [...testSuites];
    newTestSuites[suiteIndex].tests[testIndex].status = 'running';
    setTestSuites(newTestSuites);
    setCurrentTest(`${newTestSuites[suiteIndex].name} - ${newTestSuites[suiteIndex].tests[testIndex].name}`);

    const startTime = Date.now();

    try {
      // Simulate API call based on test type
      const testName = newTestSuites[suiteIndex].tests[testIndex].name;
      const response = await fetch(`/api/test/${testName.toLowerCase().replace(/\s+/g, '-')}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: testName })
      });

      const duration = Date.now() - startTime;
      const result = await response.json();

      newTestSuites[suiteIndex].tests[testIndex] = {
        ...newTestSuites[suiteIndex].tests[testIndex],
        status: response.ok ? 'success' : 'error',
        message: result.message || (response.ok ? 'Test passed' : 'Test failed'),
        duration,
        details: result.details
      };

      if (response.ok) {
        toast.success(`✅ ${testName} passed in ${duration}ms`);
      } else {
        toast.error(`❌ ${testName} failed: ${result.message}`);
      }
    } catch (error: any) {
      const duration = Date.now() - startTime;
      newTestSuites[suiteIndex].tests[testIndex] = {
        ...newTestSuites[suiteIndex].tests[testIndex],
        status: 'error',
        message: error.message || 'Network error',
        duration
      };
      toast.error(`❌ ${newTestSuites[suiteIndex].tests[testIndex].name} failed: ${error.message}`);
    }

    setTestSuites(newTestSuites);
    setCurrentTest(null);
  };

  const runAllTests = async () => {
    setIsRunning(true);
    toast.info('🚀 Starting comprehensive API testing...');

    for (let suiteIndex = 0; suiteIndex < testSuites.length; suiteIndex++) {
      for (let testIndex = 0; testIndex < testSuites[suiteIndex].tests.length; testIndex++) {
        await runTest(suiteIndex, testIndex);
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    setIsRunning(false);
    toast.success('🎉 All tests completed!');
  };

  const resetAllTests = () => {
    const resetSuites = testSuites.map(suite => ({
      ...suite,
      tests: suite.tests.map(test => ({
        ...test,
        status: 'pending' as const,
        message: undefined,
        duration: undefined,
        details: undefined
      }))
    }));
    setTestSuites(resetSuites);
    toast.info('🔄 All tests reset');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running': return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      running: 'secondary',
      pending: 'outline'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getOverallStats = () => {
    const allTests = testSuites.flatMap(suite => suite.tests);
    const total = allTests.length;
    const passed = allTests.filter(test => test.status === 'success').length;
    const failed = allTests.filter(test => test.status === 'error').length;
    const running = allTests.filter(test => test.status === 'running').length;
    const pending = allTests.filter(test => test.status === 'pending').length;

    return { total, passed, failed, running, pending };
  };

  const stats = getOverallStats();

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">اختبار شامل لواجهات برمجة التطبيقات</h1>
        <p className="text-muted-foreground">
          اختبار جميع المكونات والتكاملات في نظام eWasl
        </p>
      </div>

      {/* Overall Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-sm text-muted-foreground">إجمالي الاختبارات</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.passed}</div>
            <div className="text-sm text-muted-foreground">نجح</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <div className="text-sm text-muted-foreground">فشل</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.running}</div>
            <div className="text-sm text-muted-foreground">قيد التشغيل</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
            <div className="text-sm text-muted-foreground">في الانتظار</div>
          </CardContent>
        </Card>
      </div>

      {/* Control Buttons */}
      <div className="flex gap-4 mb-8">
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          {isRunning ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
          {isRunning ? 'جاري التشغيل...' : 'تشغيل جميع الاختبارات'}
        </Button>
        <Button 
          onClick={resetAllTests} 
          variant="outline"
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          إعادة تعيين
        </Button>
      </div>

      {/* Current Test Status */}
      {currentTest && (
        <Alert className="mb-8">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <AlertDescription>
            جاري تشغيل: {currentTest}
          </AlertDescription>
        </Alert>
      )}

      {/* Test Suites */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {testSuites.map((suite, suiteIndex) => {
          const Icon = suite.icon;
          const suiteStats = {
            total: suite.tests.length,
            passed: suite.tests.filter(test => test.status === 'success').length,
            failed: suite.tests.filter(test => test.status === 'error').length,
          };

          return (
            <Card key={suiteIndex}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Icon className="h-5 w-5" />
                  {suite.name}
                  <div className="mr-auto flex gap-2">
                    {suiteStats.passed > 0 && (
                      <Badge variant="default" className="text-xs">
                        {suiteStats.passed} نجح
                      </Badge>
                    )}
                    {suiteStats.failed > 0 && (
                      <Badge variant="destructive" className="text-xs">
                        {suiteStats.failed} فشل
                      </Badge>
                    )}
                  </div>
                </CardTitle>
                <CardDescription>{suite.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {suite.tests.map((test, testIndex) => (
                    <div 
                      key={testIndex}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {getStatusIcon(test.status)}
                        <div>
                          <div className="font-medium">{test.name}</div>
                          {test.message && (
                            <div className="text-sm text-muted-foreground">
                              {test.message}
                            </div>
                          )}
                          {test.duration && (
                            <div className="text-xs text-muted-foreground">
                              {test.duration}ms
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(test.status)}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => runTest(suiteIndex, testIndex)}
                          disabled={isRunning || test.status === 'running'}
                        >
                          {test.status === 'running' ? (
                            <RefreshCw className="h-3 w-3 animate-spin" />
                          ) : (
                            <Play className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
