'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Calendar, momentLocalizer, Views, View } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  Calendar as CalendarIcon,
  Clock,
  Repeat,
  Users,
  MoreHorizontal,
  Edit,
  Trash2,
  Copy
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const localizer = momentLocalizer(moment);

interface ScheduledPost {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED';
  platforms: string[];
  isRecurring?: boolean;
  parentRecurringId?: string;
  content: string;
  media_url?: string;
  engagement?: {
    likes: number;
    comments: number;
    shares: number;
  };
}

interface AdvancedCalendarProps {
  posts: ScheduledPost[];
  onPostMove: (postId: string, newDate: Date) => Promise<void>;
  onPostClick: (post: ScheduledPost) => void;
  onDateSelect: (date: Date) => void;
  onPostEdit: (post: ScheduledPost) => void;
  onPostDelete: (postId: string) => Promise<void>;
  onPostDuplicate: (post: ScheduledPost) => void;
  loading?: boolean;
}

export function AdvancedCalendar({
  posts,
  onPostMove,
  onPostClick,
  onDateSelect,
  onPostEdit,
  onPostDelete,
  onPostDuplicate,
  loading = false
}: AdvancedCalendarProps) {
  const [view, setView] = useState<View>(Views.MONTH);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedPost, setSelectedPost] = useState<ScheduledPost | null>(null);

  // Custom event component with enhanced features
  const EventComponent = ({ event }: { event: ScheduledPost }) => {
    const handleMenuAction = (action: string, e: React.MouseEvent) => {
      e.stopPropagation();

      switch (action) {
        case 'edit':
          onPostEdit(event);
          break;
        case 'delete':
          onPostDelete(event.id);
          break;
        case 'duplicate':
          onPostDuplicate(event);
          break;
      }
    };

    return (
      <div className="p-1 text-xs relative group">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate">{event.title}</div>
            <div className="flex gap-1 mt-1 flex-wrap">
              {event.platforms.slice(0, 3).map(platform => (
                <Badge key={platform} variant="secondary" className="text-xs px-1 py-0">
                  {platform.slice(0, 2)}
                </Badge>
              ))}
              {event.platforms.length > 3 && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  +{event.platforms.length - 3}
                </Badge>
              )}
              {event.isRecurring && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  <Repeat className="w-2 h-2" />
                </Badge>
              )}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
              <DropdownMenuItem onClick={(e) => handleMenuAction('edit', e)}>
                <Edit className="w-3 h-3 mr-2" />
                تعديل
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => handleMenuAction('duplicate', e)}>
                <Copy className="w-3 h-3 mr-2" />
                نسخ
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => handleMenuAction('delete', e)}
                className="text-red-600"
              >
                <Trash2 className="w-3 h-3 mr-2" />
                حذف
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Engagement indicators for published posts */}
        {event.status === 'PUBLISHED' && event.engagement && (
          <div className="flex gap-1 mt-1 text-xs text-gray-500">
            <span>👍 {event.engagement.likes}</span>
            <span>💬 {event.engagement.comments}</span>
            <span>🔄 {event.engagement.shares}</span>
          </div>
        )}
      </div>
    );
  };

  // Handle drag and drop
  const handleEventDrop = useCallback(async ({ event, start }: { event: any; start: Date }) => {
    try {
      await onPostMove(event.id, start);
      toast.success('تم تحديث موعد المنشور بنجاح');
    } catch (error) {
      toast.error('فشل في تحديث موعد المنشور');
    }
  }, [onPostMove]);

  // Custom event style getter
  const eventStyleGetter = useCallback((event: ScheduledPost) => {
    let backgroundColor = '#3b82f6';
    let borderColor = '#3b82f6';

    switch (event.status) {
      case 'PUBLISHED':
        backgroundColor = '#10b981';
        borderColor = '#10b981';
        break;
      case 'FAILED':
        backgroundColor = '#ef4444';
        borderColor = '#ef4444';
        break;
      case 'DRAFT':
        backgroundColor = '#6b7280';
        borderColor = '#6b7280';
        break;
      case 'SCHEDULED':
        backgroundColor = '#3b82f6';
        borderColor = '#3b82f6';
        break;
    }

    if (event.isRecurring) {
      backgroundColor = `${backgroundColor}dd`; // Add transparency
      borderColor = '#8b5cf6'; // Purple border for recurring
    }

    return {
      style: {
        backgroundColor,
        borderColor,
        borderWidth: '2px',
        borderStyle: 'solid',
        borderRadius: '6px',
        opacity: 0.9,
        color: 'white',
        fontSize: '11px',
        padding: '2px 4px',
      }
    };
  }, []);

  // Custom toolbar
  const CustomToolbar = ({ label, onNavigate, onView }: any) => (
    <div className="flex justify-between items-center mb-4 p-4 bg-gray-50 rounded-lg">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('PREV')}
        >
          السابق
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('TODAY')}
        >
          اليوم
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('NEXT')}
        >
          التالي
        </Button>
      </div>

      <h3 className="text-lg font-semibold">{label}</h3>

      <div className="flex gap-2">
        <Button
          variant={view === Views.MONTH ? 'default' : 'outline'}
          size="sm"
          onClick={() => { setView(Views.MONTH); onView(Views.MONTH); }}
        >
          شهر
        </Button>
        <Button
          variant={view === Views.WEEK ? 'default' : 'outline'}
          size="sm"
          onClick={() => { setView(Views.WEEK); onView(Views.WEEK); }}
        >
          أسبوع
        </Button>
        <Button
          variant={view === Views.DAY ? 'default' : 'outline'}
          size="sm"
          onClick={() => { setView(Views.DAY); onView(Views.DAY); }}
        >
          يوم
        </Button>
      </div>
    </div>
  );

  // Calendar statistics
  const stats = React.useMemo(() => {
    const total = posts.length;
    const published = posts.filter(p => p.status === 'PUBLISHED').length;
    const scheduled = posts.filter(p => p.status === 'SCHEDULED').length;
    const failed = posts.filter(p => p.status === 'FAILED').length;
    const recurring = posts.filter(p => p.isRecurring).length;

    return { total, published, scheduled, failed, recurring };
  }, [posts]);

  if (loading) {
    return (
      <Card className="w-full h-full">
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">جاري تحميل التقويم...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4" dir="rtl">
      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-sm text-gray-600">إجمالي المنشورات</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.published}</div>
            <p className="text-sm text-gray-600">منشورة</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">{stats.scheduled}</div>
            <p className="text-sm text-gray-600">مجدولة</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <p className="text-sm text-gray-600">فشلت</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">{stats.recurring}</div>
            <p className="text-sm text-gray-600">متكررة</p>
          </CardContent>
        </Card>
      </div>

      {/* Calendar */}
      <Card className="w-full">
        <CardContent className="p-0">
          <div style={{ height: '700px' }} className="p-4">
            <Calendar
              localizer={localizer}
              events={posts}
              startAccessor="start"
              endAccessor="end"
              view={view}
              onView={setView}
              date={selectedDate}
              onNavigate={setSelectedDate}
              onSelectEvent={onPostClick}
              onSelectSlot={({ start }) => onDateSelect(start)}
              eventPropGetter={eventStyleGetter}
              components={{
                event: EventComponent,
                toolbar: CustomToolbar,
              }}
              selectable
              popup
              step={60}
              timeslots={1}
              views={[Views.MONTH, Views.WEEK, Views.DAY]}
              messages={{
                next: 'التالي',
                previous: 'السابق',
                today: 'اليوم',
                month: 'شهر',
                week: 'أسبوع',
                day: 'يوم',
                agenda: 'جدول الأعمال',
                date: 'التاريخ',
                time: 'الوقت',
                event: 'المنشور',
                noEventsInRange: 'لا توجد منشورات في هذا النطاق',
                showMore: (total) => `+${total} المزيد`,
              }}
              formats={{
                monthHeaderFormat: 'MMMM YYYY',
                dayHeaderFormat: 'dddd, MMMM DD',
                dayRangeHeaderFormat: ({ start, end }) =>
                  `${moment(start).format('MMM DD')} - ${moment(end).format('MMM DD, YYYY')}`,
                timeGutterFormat: 'HH:mm',
                eventTimeRangeFormat: ({ start, end }) =>
                  `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`,
              }}
              style={{
                fontFamily: 'inherit',
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
