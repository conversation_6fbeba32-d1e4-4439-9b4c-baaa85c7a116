-- eWasl Subscription System Database Schema
-- Complete payment and subscription management

-- Subscription plans table with exact pricing tiers
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL,
  name_ar VARCHAR(50), -- Arabic name support
  description TEXT,
  description_ar TEXT, -- Arabic description support
  price_monthly DECIMAL(10,2),
  price_yearly DECIMAL(10,2),
  stripe_price_id_monthly VARCHAR(100),
  stripe_price_id_yearly VARCHAR(100),
  
  -- Feature limits
  max_social_accounts INTEGER NOT NULL,
  max_users INTEGER NOT NULL,
  max_posts_per_month INTEGER, -- NULL for unlimited
  
  -- Feature flags
  features JSONB NOT NULL DEFAULT '{}',
  
  -- Plan metadata
  plan_type VARCHAR(20) NOT NULL, -- 'free', 'pro', 'business', 'enterprise'
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions table
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  plan_id UUID REFERENCES subscription_plans(id),
  
  -- Stripe integration
  stripe_customer_id VARCHAR(100),
  stripe_subscription_id VARCHAR(100),
  stripe_payment_method_id VARCHAR(100),
  
  -- Subscription status
  status VARCHAR(20) DEFAULT 'active', -- active, canceled, past_due, incomplete, trialing
  billing_cycle VARCHAR(10) DEFAULT 'monthly', -- monthly, yearly
  
  -- Billing periods
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_start TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  
  -- Cancellation
  cancel_at_period_end BOOLEAN DEFAULT false,
  canceled_at TIMESTAMP WITH TIME ZONE,
  cancellation_reason TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id) -- One active subscription per user
);

-- Payment history table
CREATE TABLE payment_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES user_subscriptions(id),
  
  -- Stripe payment details
  stripe_payment_intent_id VARCHAR(100),
  stripe_invoice_id VARCHAR(100),
  
  -- Payment information
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  status VARCHAR(20) NOT NULL, -- succeeded, failed, pending, refunded
  payment_method VARCHAR(50),
  
  -- Billing details
  billing_reason VARCHAR(50), -- subscription_create, subscription_cycle, etc.
  invoice_pdf_url TEXT,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage tracking table for plan limits
CREATE TABLE usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Time period
  month_year VARCHAR(7) NOT NULL, -- Format: 2024-01
  
  -- Usage counters
  posts_created INTEGER DEFAULT 0,
  posts_scheduled INTEGER DEFAULT 0,
  api_calls INTEGER DEFAULT 0,
  storage_used_mb INTEGER DEFAULT 0,
  
  -- Feature usage
  ai_generations_used INTEGER DEFAULT 0,
  reports_generated INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, month_year)
);

-- Team members table for multi-user plans
CREATE TABLE team_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- Team owner
  member_user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- Team member
  
  -- Member details
  role VARCHAR(20) DEFAULT 'member', -- owner, admin, member, viewer
  permissions JSONB DEFAULT '{}',
  
  -- Status
  status VARCHAR(20) DEFAULT 'active', -- active, inactive, pending
  invited_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, member_user_id)
);

-- Billing addresses table
CREATE TABLE billing_addresses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Address details
  line1 VARCHAR(255),
  line2 VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(2), -- ISO country code
  
  -- Contact
  company_name VARCHAR(255),
  tax_id VARCHAR(50),
  
  is_default BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Modify existing users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS current_plan_id UUID REFERENCES subscription_plans(id);
ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(20) DEFAULT 'free';
ALTER TABLE users ADD COLUMN IF NOT EXISTS trial_ends_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(100);

-- Create indexes for performance
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_stripe_customer ON user_subscriptions(stripe_customer_id);
CREATE INDEX idx_payment_history_user_id ON payment_history(user_id);
CREATE INDEX idx_usage_tracking_user_month ON usage_tracking(user_id, month_year);
CREATE INDEX idx_team_members_user_id ON team_members(user_id);

-- Insert default subscription plans
INSERT INTO subscription_plans (
  name, name_ar, description, description_ar, 
  price_monthly, price_yearly,
  max_social_accounts, max_users, max_posts_per_month,
  features, plan_type, sort_order
) VALUES 
-- Free Plan
(
  'Free', 'مجاني',
  'Basic social media management for individuals', 
  'إدارة أساسية لوسائل التواصل الاجتماعي للأفراد',
  0.00, 0.00,
  2, 1, 10,
  '{
    "basic_scheduling": true,
    "basic_analytics": true,
    "arabic_support": true,
    "rtl_support": true,
    "image_upload": true,
    "basic_notifications": true
  }',
  'free', 1
),
-- Pro Plan (Basic) - $9/month
(
  'Pro', 'احترافي',
  'Perfect for small businesses and content creators',
  'مثالي للشركات الصغيرة ومنشئي المحتوى',
  9.00, 90.00,
  5, 2, NULL, -- Unlimited posts
  '{
    "unlimited_posts": true,
    "basic_analytics": true,
    "arabic_support": true,
    "rtl_support": true,
    "post_scheduling": true,
    "basic_ai_content": true,
    "calendar_view": true,
    "smart_scheduling": true,
    "image_video_upload": true,
    "comment_replies": true,
    "activity_monitoring": true,
    "basic_notifications": true
  }',
  'pro', 2
),
-- Business Plan (Standard) - $25/month  
(
  'Business', 'الأعمال',
  'Advanced features for growing businesses and teams',
  'ميزات متقدمة للشركات والفرق النامية',
  25.00, 250.00,
  10, 5, NULL, -- Unlimited posts
  '{
    "unlimited_posts": true,
    "advanced_analytics": true,
    "arabic_support": true,
    "rtl_support": true,
    "drag_drop_scheduling": true,
    "performance_analytics": true,
    "content_library": true,
    "content_templates": true,
    "advanced_ai_content": true,
    "realtime_notifications": true,
    "activity_analysis": true,
    "data_export": true,
    "priority_support": true,
    "automated_reports": true,
    "team_collaboration": true
  }',
  'business', 3
),
-- Enterprise Plan - Custom Pricing
(
  'Enterprise', 'المؤسسات',
  'Custom solutions for large organizations',
  'حلول مخصصة للمؤسسات الكبيرة',
  NULL, NULL, -- Custom pricing
  NULL, NULL, NULL, -- Unlimited everything
  '{
    "unlimited_everything": true,
    "custom_analytics": true,
    "comprehensive_reports": true,
    "crm_integrations": true,
    "zapier_integration": true,
    "whatsapp_api": true,
    "admin_dashboard": true,
    "direct_training": true,
    "priority_support": true,
    "ui_customization": true,
    "report_customization": true,
    "sso_support": true,
    "granular_permissions": true,
    "advanced_security": true
  }',
  'enterprise', 4
);
