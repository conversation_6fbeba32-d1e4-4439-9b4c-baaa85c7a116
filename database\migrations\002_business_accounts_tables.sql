-- Business Accounts Tables Migration
-- Creates tables for storing Facebook Pages, LinkedIn Companies, and other business accounts

-- Facebook Pages table
CREATE TABLE IF NOT EXISTS facebook_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  social_account_id TEXT NOT NULL,
  page_id TEXT NOT NULL,
  page_name TEXT NOT NULL,
  page_access_token TEXT NOT NULL,
  page_category TEXT,
  page_picture_url TEXT,
  fan_count INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT false,
  website TEXT,
  about TEXT,
  phone TEXT,
  email TEXT,
  permissions TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(social_account_id, page_id)
);

-- LinkedIn Companies table
CREATE TABLE IF NOT EXISTS linkedin_companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  social_account_id TEXT NOT NULL,
  organization_id TEXT NOT NULL,
  organization_name TEXT NOT NULL,
  organization_urn TEXT NOT NULL,
  logo_url TEXT,
  website TEXT,
  industry TEXT,
  description TEXT,
  employee_count TEXT,
  headquarters_country TEXT,
  headquarters_city TEXT,
  follower_count INTEGER DEFAULT 0,
  permissions TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(social_account_id, organization_id)
);

-- Instagram Business Accounts table
CREATE TABLE IF NOT EXISTS instagram_business_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  social_account_id TEXT NOT NULL,
  instagram_account_id TEXT NOT NULL,
  instagram_username TEXT NOT NULL,
  connected_facebook_page_id TEXT NOT NULL,
  profile_picture_url TEXT,
  biography TEXT,
  website TEXT,
  is_verified BOOLEAN DEFAULT false,
  account_type TEXT DEFAULT 'BUSINESS' CHECK (account_type IN ('BUSINESS', 'CREATOR')),
  media_count INTEGER DEFAULT 0,
  follower_count INTEGER DEFAULT 0,
  following_count INTEGER DEFAULT 0,
  permissions TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(social_account_id, instagram_account_id)
);

-- Business Account Selections table (tracks which business account is selected for each platform)
CREATE TABLE IF NOT EXISTS business_account_selections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('FACEBOOK', 'LINKEDIN', 'INSTAGRAM', 'TWITTER')),
  social_account_id TEXT NOT NULL,
  business_account_id TEXT NOT NULL,
  business_account_type TEXT NOT NULL CHECK (business_account_type IN ('PAGE', 'COMPANY', 'BUSINESS', 'PERSONAL')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(user_id, platform, social_account_id)
);

-- Add page_id column to social_accounts table for selected Facebook page
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS page_id TEXT,
ADD COLUMN IF NOT EXISTS business_account_type TEXT DEFAULT 'PERSONAL',
ADD COLUMN IF NOT EXISTS business_account_id TEXT,
ADD COLUMN IF NOT EXISTS business_account_name TEXT,
ADD COLUMN IF NOT EXISTS last_business_sync_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_facebook_pages_social_account_id ON facebook_pages(social_account_id);
CREATE INDEX IF NOT EXISTS idx_facebook_pages_page_id ON facebook_pages(page_id);

CREATE INDEX IF NOT EXISTS idx_linkedin_companies_social_account_id ON linkedin_companies(social_account_id);
CREATE INDEX IF NOT EXISTS idx_linkedin_companies_organization_id ON linkedin_companies(organization_id);

CREATE INDEX IF NOT EXISTS idx_instagram_business_accounts_social_account_id ON instagram_business_accounts(social_account_id);
CREATE INDEX IF NOT EXISTS idx_instagram_business_accounts_instagram_id ON instagram_business_accounts(instagram_account_id);

CREATE INDEX IF NOT EXISTS idx_business_account_selections_user_id ON business_account_selections(user_id);
CREATE INDEX IF NOT EXISTS idx_business_account_selections_platform ON business_account_selections(platform);
CREATE INDEX IF NOT EXISTS idx_business_account_selections_social_account_id ON business_account_selections(social_account_id);

CREATE INDEX IF NOT EXISTS idx_social_accounts_page_id ON social_accounts(page_id);
CREATE INDEX IF NOT EXISTS idx_social_accounts_business_account_id ON social_accounts(business_account_id);

-- Add status column to social_accounts if it doesn't exist
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED', 'ERROR'));

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_facebook_pages_updated_at
    BEFORE UPDATE ON facebook_pages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_linkedin_companies_updated_at
    BEFORE UPDATE ON linkedin_companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_instagram_business_accounts_updated_at
    BEFORE UPDATE ON instagram_business_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_account_selections_updated_at
    BEFORE UPDATE ON business_account_selections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS (Row Level Security) policies
ALTER TABLE facebook_pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE linkedin_companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE instagram_business_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_account_selections ENABLE ROW LEVEL SECURITY;

-- RLS policies for facebook_pages
CREATE POLICY "Users can view their own Facebook pages" ON facebook_pages
    FOR SELECT USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can insert their own Facebook pages" ON facebook_pages
    FOR INSERT WITH CHECK (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can update their own Facebook pages" ON facebook_pages
    FOR UPDATE USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can delete their own Facebook pages" ON facebook_pages
    FOR DELETE USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

-- RLS policies for linkedin_companies
CREATE POLICY "Users can view their own LinkedIn companies" ON linkedin_companies
    FOR SELECT USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can insert their own LinkedIn companies" ON linkedin_companies
    FOR INSERT WITH CHECK (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can update their own LinkedIn companies" ON linkedin_companies
    FOR UPDATE USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can delete their own LinkedIn companies" ON linkedin_companies
    FOR DELETE USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

-- RLS policies for instagram_business_accounts
CREATE POLICY "Users can view their own Instagram business accounts" ON instagram_business_accounts
    FOR SELECT USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can insert their own Instagram business accounts" ON instagram_business_accounts
    FOR INSERT WITH CHECK (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can update their own Instagram business accounts" ON instagram_business_accounts
    FOR UPDATE USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

CREATE POLICY "Users can delete their own Instagram business accounts" ON instagram_business_accounts
    FOR DELETE USING (
        social_account_id IN (
            SELECT id FROM social_accounts WHERE user_id = auth.uid()::text
        )
    );

-- RLS policies for business_account_selections
CREATE POLICY "Users can view their own business account selections" ON business_account_selections
    FOR SELECT USING (user_id = auth.uid()::text);

CREATE POLICY "Users can insert their own business account selections" ON business_account_selections
    FOR INSERT WITH CHECK (user_id = auth.uid()::text);

CREATE POLICY "Users can update their own business account selections" ON business_account_selections
    FOR UPDATE USING (user_id = auth.uid()::text);

CREATE POLICY "Users can delete their own business account selections" ON business_account_selections
    FOR DELETE USING (user_id = auth.uid()::text);

-- Comments for documentation
COMMENT ON TABLE facebook_pages IS 'Stores Facebook Business Pages that users can manage';
COMMENT ON TABLE linkedin_companies IS 'Stores LinkedIn Company Pages that users can manage';
COMMENT ON TABLE instagram_business_accounts IS 'Stores Instagram Business Accounts linked to Facebook Pages';
COMMENT ON TABLE business_account_selections IS 'Tracks which business account is selected for posting on each platform';

COMMENT ON COLUMN social_accounts.page_id IS 'Selected Facebook Page ID for posting';
COMMENT ON COLUMN social_accounts.business_account_type IS 'Type of business account (PAGE, COMPANY, BUSINESS, PERSONAL)';
COMMENT ON COLUMN social_accounts.business_account_id IS 'ID of the selected business account';
COMMENT ON COLUMN social_accounts.business_account_name IS 'Name of the selected business account';
COMMENT ON COLUMN social_accounts.last_business_sync_at IS 'Last time business accounts were synced from the platform';
