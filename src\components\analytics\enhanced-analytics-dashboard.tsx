'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  Heart,
  MessageCircle,
  Share,
  Users,
  Calendar,
  Download,
  RefreshCw,
  Filter,
  BarChart3,
  PieChart as PieChartIcon,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';

interface AnalyticsData {
  summary: {
    totalPosts: number;
    totalImpressions: number;
    totalEngagement: number;
    avgEngagementRate: number;
    totalReach: number;
    totalClicks: number;
  };
  platformPerformance: {
    [platform: string]: {
      posts: number;
      impressions: number;
      engagement: number;
      engagementRate: number;
      reach: number;
      clicks: number;
    };
  };
  engagementTrends: Array<{
    date: string;
    impressions: number;
    engagement: number;
    engagementRate: number;
    reach: number;
  }>;
  topPosts: Array<{
    id: string;
    content: string;
    platform: string;
    impressions: number;
    engagement: number;
    engagementRate: number;
    publishedAt: string;
  }>;
  contentTypePerformance: {
    [type: string]: {
      posts: number;
      avgEngagementRate: number;
      totalImpressions: number;
    };
  };
  audienceInsights: {
    demographics: {
      [key: string]: number;
    };
    locations: {
      [key: string]: number;
    };
    interests: {
      [key: string]: number;
    };
  };
}

interface EnhancedAnalyticsDashboardProps {
  userId: string;
}

const EnhancedAnalyticsDashboard: React.FC<EnhancedAnalyticsDashboardProps> = ({
  userId
}) => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('30d');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('overview');

  const text = {
    title: 'لوحة التحليلات المتقدمة',
    description: 'تحليلات شاملة لأداء منصات التواصل الاجتماعي',
    overview: 'نظرة عامة',
    trends: 'الاتجاهات',
    platforms: 'المنصات',
    content: 'المحتوى',
    audience: 'الجمهور',
    export: 'تصدير',
    refresh: 'تحديث',
    filter: 'تصفية',
    last7Days: 'آخر 7 أيام',
    last30Days: 'آخر 30 يوم',
    last90Days: 'آخر 90 يوم',
    thisYear: 'هذا العام',
    totalPosts: 'إجمالي المنشورات',
    totalImpressions: 'إجمالي المشاهدات',
    totalEngagement: 'إجمالي التفاعل',
    avgEngagementRate: 'متوسط معدل التفاعل',
    totalReach: 'إجمالي الوصول',
    totalClicks: 'إجمالي النقرات',
    platformPerformance: 'أداء المنصات',
    engagementTrends: 'اتجاهات التفاعل',
    topPerformingPosts: 'أفضل المنشورات أداءً',
    contentTypeAnalysis: 'تحليل نوع المحتوى',
    audienceInsights: 'رؤى الجمهور',
    demographics: 'التركيبة السكانية',
    locations: 'المواقع',
    interests: 'الاهتمامات',
    noData: 'لا توجد بيانات',
    loadingData: 'جاري تحميل البيانات...'
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  useEffect(() => {
    fetchAnalyticsData();
  }, [userId, period, selectedPlatforms]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const platformsQuery = selectedPlatforms.length > 0 
        ? `&platforms=${selectedPlatforms.join(',')}` 
        : '';
      
      const response = await fetch(
        `/api/analytics/advanced?userId=${userId}&period=${period}${platformsQuery}`
      );
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        toast.error('فشل في تحميل بيانات التحليلات');
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('خطأ في تحميل التحليلات');
    } finally {
      setLoading(false);
    }
  };

  const exportData = async () => {
    try {
      const response = await fetch(
        `/api/analytics/export?userId=${userId}&period=${period}&format=csv`
      );
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-${period}-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('تم تصدير بيانات التحليلات بنجاح');
    } catch (error) {
      toast.error('فشل في تصدير البيانات');
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>{text.loadingData}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">{text.noData}</h3>
        <p className="text-muted-foreground">ابدأ بالنشر لرؤية بيانات التحليلات</p>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{text.title}</h1>
          <p className="text-muted-foreground">{text.description}</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">{text.last7Days}</SelectItem>
              <SelectItem value="30d">{text.last30Days}</SelectItem>
              <SelectItem value="90d">{text.last90Days}</SelectItem>
              <SelectItem value="1y">{text.thisYear}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={exportData}>
            <Download className="w-4 h-4 mr-2" />
            {text.export}
          </Button>
          <Button variant="outline" onClick={fetchAnalyticsData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            {text.refresh}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.totalPosts}</p>
                <p className="text-2xl font-bold">{formatNumber(data.summary.totalPosts)}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.totalImpressions}</p>
                <p className="text-2xl font-bold">{formatNumber(data.summary.totalImpressions)}</p>
              </div>
              <Eye className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.totalEngagement}</p>
                <p className="text-2xl font-bold">{formatNumber(data.summary.totalEngagement)}</p>
              </div>
              <Heart className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.avgEngagementRate}</p>
                <p className="text-2xl font-bold">{formatPercentage(data.summary.avgEngagementRate)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.totalReach}</p>
                <p className="text-2xl font-bold">{formatNumber(data.summary.totalReach)}</p>
              </div>
              <Users className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{text.totalClicks}</p>
                <p className="text-2xl font-bold">{formatNumber(data.summary.totalClicks)}</p>
              </div>
              <Activity className="h-8 w-8 text-teal-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">{text.overview}</TabsTrigger>
          <TabsTrigger value="trends">{text.trends}</TabsTrigger>
          <TabsTrigger value="platforms">{text.platforms}</TabsTrigger>
          <TabsTrigger value="content">{text.content}</TabsTrigger>
          <TabsTrigger value="audience">{text.audience}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Engagement Trends */}
            <Card>
              <CardHeader>
                <CardTitle>{text.engagementTrends}</CardTitle>
                <CardDescription>تتبع التفاعل عبر الزمن</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={data.engagementTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="engagement" 
                      stackId="1"
                      stroke="#8884d8" 
                      fill="#8884d8"
                      name="التفاعل"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="impressions" 
                      stackId="2"
                      stroke="#82ca9d" 
                      fill="#82ca9d"
                      name="المشاهدات"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Platform Performance */}
            <Card>
              <CardHeader>
                <CardTitle>{text.platformPerformance}</CardTitle>
                <CardDescription>مقارنة الأداء عبر المنصات</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={Object.entries(data.platformPerformance).map(([platform, data]: [string, any]) => ({
                    platform: platform.charAt(0).toUpperCase() + platform.slice(1),
                    engagement: data.engagement,
                    impressions: data.impressions,
                    engagementRate: data.engagementRate
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="platform" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="engagement" fill="#8884d8" name="التفاعل" />
                    <Bar dataKey="impressions" fill="#82ca9d" name="المشاهدات" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Performing Posts */}
          <Card>
            <CardHeader>
              <CardTitle>{text.topPerformingPosts}</CardTitle>
              <CardDescription>أفضل محتوى لديك أداءً</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.topPosts.slice(0, 5).map((post, index) => (
                  <div key={post.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{post.platform}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {new Date(post.publishedAt).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm line-clamp-2">{post.content}</p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-lg font-bold">{formatNumber(post.engagement)}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatPercentage(post.engagementRate)} معدل
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Other tabs will be implemented in the next parts */}
        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{text.trends}</CardTitle>
              <CardDescription>تحليل الاتجاهات المفصل قريباً</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">ميزات تحليل الاتجاهات المتقدمة ستكون متاحة قريباً.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{text.platforms}</CardTitle>
              <CardDescription>تحليلات خاصة بالمنصات قريباً</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">تحليلات المنصات المفصلة ستكون متاحة قريباً.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{text.content}</CardTitle>
              <CardDescription>تحليل المحتوى قريباً</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">تحليل أداء المحتوى سيكون متاحاً قريباً.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audience" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{text.audience}</CardTitle>
              <CardDescription>رؤى الجمهور قريباً</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">التركيبة السكانية للجمهور والرؤى ستكون متاحة قريباً.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedAnalyticsDashboard;
