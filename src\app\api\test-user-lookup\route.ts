import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const supabase = createServiceRoleClient();
    
    // Test user lookup with service role client
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', demoUserId)
      .single();

    return NextResponse.json({
      success: !userError,
      userData,
      error: userError,
      message: userError ? 'User lookup failed' : 'User lookup successful'
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Test failed'
    }, { status: 500 });
  }
}
