import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import Link from "next/link";

interface SocialAccount {
  id: string;
  platform: "TWITTER" | "FACEBOOK" | "INSTAGRAM" | "LINKEDIN" | "TIKTOK";
  accountName: string;
}

interface SocialAccountsProps {
  accounts: SocialAccount[];
}

export function SocialAccounts({ accounts }: SocialAccountsProps) {
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "𝕏";
      case "FACEBOOK":
        return "f";
      case "INSTAGRAM":
        return "📷";
      case "LINKEDIN":
        return "in";
      case "TIKTOK":
        return "TT";
      default:
        return "?";
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "bg-black text-white";
      case "FACEBOOK":
        return "bg-blue-600 text-white";
      case "INSTAGRAM":
        return "bg-pink-600 text-white";
      case "LINKEDIN":
        return "bg-blue-700 text-white";
      case "TIKTOK":
        return "bg-black text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "تويتر";
      case "FACEBOOK":
        return "فيسبوك";
      case "INSTAGRAM":
        return "انستغرام";
      case "LINKEDIN":
        return "لينكد إن";
      case "TIKTOK":
        return "تيك توك";
      default:
        return platform;
    }
  };

  return (
    <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-white to-gray-50/50">
      <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-t-lg">
        <div className="flex items-center gap-3">
          <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full shadow-sm"></div>
          <CardTitle className="text-xl font-bold text-gray-800">الحسابات المتصلة</CardTitle>
        </div>
        <CardDescription className="text-gray-600 font-medium">حسابات وسائل التواصل الاجتماعي المتصلة</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {accounts.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-muted-foreground mb-4">
                لم تقم بربط أي حسابات حتى الآن
              </p>
              <Button asChild>
                <Link href="/social">
                  <PlusCircle className="ml-2 h-4 w-4" />
                  ربط حساب
                </Link>
              </Button>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {accounts.map((account) => (
                  <div
                    key={account.id}
                    className="group flex items-center p-4 border border-gray-100 rounded-xl hover:border-emerald-300 hover:bg-gradient-to-r hover:from-emerald-50/50 hover:to-teal-50/30 transition-all duration-300 cursor-pointer hover:shadow-md"
                  >
                    <div
                      className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 shadow-sm text-lg font-bold ${getPlatformColor(
                        account.platform
                      )}`}
                    >
                      {getPlatformIcon(account.platform)}
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold text-gray-900 group-hover:text-emerald-900">{account.accountName}</p>
                      <p className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full inline-block mt-1">
                        {getPlatformName(account.platform)}
                      </p>
                    </div>
                    <div className="w-3 h-3 bg-emerald-500 rounded-full shadow-sm"></div>
                  </div>
                ))}
              </div>
              <div className="text-center mt-4">
                <Button variant="outline" asChild>
                  <Link href="/social">
                    <PlusCircle className="ml-2 h-4 w-4" />
                    إضافة حساب آخر
                  </Link>
                </Button>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
