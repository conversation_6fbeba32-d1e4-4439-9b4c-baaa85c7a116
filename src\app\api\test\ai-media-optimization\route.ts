import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';
import ImageOptimizer from '@/lib/media/image-optimizer';
import ContentAnalyzer from '@/lib/media/content-analyzer';
import SmartFormatSelector from '@/lib/media/smart-format-selector';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    console.log('Running AI-Powered Media Optimization tests...');

    const supabase = createServiceRoleClient();
    const imageOptimizer = new ImageOptimizer();
    const contentAnalyzer = new ContentAnalyzer();
    const formatSelector = new SmartFormatSelector();

    const testResults = {
      contentAnalyzer: '⏳ Testing...',
      smartFormatSelector: '⏳ Testing...',
      aiImageOptimization: '⏳ Testing...',
      intelligentFormatSelection: '⏳ Testing...',
      platformOptimization: '⏳ Testing...',
      performanceMetrics: '⏳ Testing...',
    };

    // Test 1: Content Analyzer
    try {
      console.log('Testing AI content analyzer...');
      
      // Create test image buffer (1x1 pixel PNG)
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, // bit depth, color type, etc.
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
        0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00, // image data
        0x00, 0x01, 0x00, 0x01, 0xFA, 0x00, 0x37, 0xA3, // checksum
        0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, // IEND chunk
        0xAE, 0x42, 0x60, 0x82
      ]);

      // Test content analysis
      const contentAnalysis = await contentAnalyzer.analyzeImage(testImageBuffer);
      
      // Verify analysis results
      const hasValidAnalysis = [
        contentAnalysis.contentType,
        contentAnalysis.complexity,
        contentAnalysis.recommendedFormat,
        typeof contentAnalysis.recommendedQuality === 'number',
        typeof contentAnalysis.confidence === 'number'
      ].every(Boolean);

      if (hasValidAnalysis) {
        testResults.contentAnalyzer = `✅ Content analyzer working (${contentAnalysis.contentType}, ${contentAnalysis.complexity}, ${Math.round(contentAnalysis.confidence * 100)}% confidence)`;
      } else {
        testResults.contentAnalyzer = '⚠️ Content analyzer incomplete results';
      }
    } catch (err) {
      console.error('Content analyzer test error:', err);
      testResults.contentAnalyzer = '❌ Content analyzer test failed';
    }

    // Test 2: Smart Format Selector
    try {
      console.log('Testing smart format selector...');
      
      // Create mock content analysis
      const mockAnalysis = {
        contentType: 'photo' as const,
        complexity: 'medium' as const,
        hasTransparency: false,
        isAnimated: false,
        colorProfile: 'full' as const,
        textDensity: 'low' as const,
        noiseLevel: 'medium' as const,
        edgeComplexity: 'moderate' as const,
        recommendedFormat: 'webp' as const,
        recommendedQuality: 85,
        compressionStrategy: 'balanced' as const,
        confidence: 0.9
      };

      // Test format selection
      const formatRecommendation = await formatSelector.selectOptimalFormat(
        mockAnalysis,
        false, // isVideo
        'instagram',
        { webp: true, avif: true, heic: false, jxl: false, av1: false, vp9: false, h265: false },
        { strategy: 'balanced', targetSizeReduction: 30, qualityThreshold: 80, speedPriority: 'balanced', multiFormat: true }
      );

      // Verify recommendation
      const hasValidRecommendation = [
        formatRecommendation.primaryFormat,
        typeof formatRecommendation.quality === 'number',
        Array.isArray(formatRecommendation.reasoning),
        typeof formatRecommendation.expectedSizeReduction === 'number',
        typeof formatRecommendation.confidence === 'number'
      ].every(Boolean);

      if (hasValidRecommendation) {
        testResults.smartFormatSelector = `✅ Smart format selector working (${formatRecommendation.primaryFormat}, ${formatRecommendation.quality}% quality, ${formatRecommendation.expectedSizeReduction}% reduction)`;
      } else {
        testResults.smartFormatSelector = '⚠️ Smart format selector incomplete results';
      }
    } catch (err) {
      console.error('Smart format selector test error:', err);
      testResults.smartFormatSelector = '❌ Smart format selector test failed';
    }

    // Test 3: AI Image Optimization
    try {
      console.log('Testing AI-powered image optimization...');
      
      // Create test image buffer
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, // bit depth, color type, etc.
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
        0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00, // image data
        0x00, 0x01, 0x00, 0x01, 0xFA, 0x00, 0x37, 0xA3, // checksum
        0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, // IEND chunk
        0xAE, 0x42, 0x60, 0x82
      ]);

      // Test AI optimization
      const aiResult = await imageOptimizer.optimizeImageAI(
        testImageBuffer,
        { platform: 'instagram' },
        { webp: true, avif: true, heic: false, jxl: false, av1: false, vp9: false, h265: false },
        { strategy: 'balanced', targetSizeReduction: 30, qualityThreshold: 80, speedPriority: 'balanced', multiFormat: true }
      );

      // Verify AI optimization
      const hasValidAIResult = [
        aiResult.success,
        aiResult.format,
        typeof aiResult.compressionRatio === 'number',
        aiResult.metadata
      ].every(Boolean);

      if (hasValidAIResult) {
        testResults.aiImageOptimization = `✅ AI image optimization working (${aiResult.format}, ${Math.round(aiResult.compressionRatio * 100)}% compression)`;
      } else {
        testResults.aiImageOptimization = '⚠️ AI image optimization incomplete results';
      }
    } catch (err) {
      console.error('AI image optimization test error:', err);
      testResults.aiImageOptimization = '❌ AI image optimization test failed';
    }

    // Test 4: Intelligent Format Selection
    try {
      console.log('Testing intelligent format selection across content types...');
      
      const testCases = [
        { contentType: 'photo', expectedFormat: 'webp' },
        { contentType: 'graphics', expectedFormat: 'png' },
        { contentType: 'text', expectedFormat: 'png' }
      ];

      let successCount = 0;
      
      for (const testCase of testCases) {
        const mockAnalysis = {
          contentType: testCase.contentType as any,
          complexity: 'medium' as const,
          hasTransparency: false,
          isAnimated: false,
          colorProfile: 'full' as const,
          textDensity: 'low' as const,
          noiseLevel: 'medium' as const,
          edgeComplexity: 'moderate' as const,
          recommendedFormat: 'webp' as const,
          recommendedQuality: 85,
          compressionStrategy: 'balanced' as const,
          confidence: 0.9
        };

        const recommendation = await formatSelector.selectOptimalFormat(
          mockAnalysis,
          false,
          'web',
          { webp: true, avif: true, heic: false, jxl: false, av1: false, vp9: false, h265: false }
        );

        if (recommendation.primaryFormat) {
          successCount++;
        }
      }

      if (successCount === testCases.length) {
        testResults.intelligentFormatSelection = `✅ Intelligent format selection working (${successCount}/${testCases.length} test cases passed)`;
      } else {
        testResults.intelligentFormatSelection = `⚠️ Format selection issues (${successCount}/${testCases.length} test cases passed)`;
      }
    } catch (err) {
      console.error('Intelligent format selection test error:', err);
      testResults.intelligentFormatSelection = '❌ Intelligent format selection test failed';
    }

    // Test 5: Platform-Specific Optimization
    try {
      console.log('Testing platform-specific AI optimization...');
      
      const platforms = ['instagram', 'facebook', 'twitter', 'linkedin'];
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, // bit depth, color type, etc.
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
        0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00, // image data
        0x00, 0x01, 0x00, 0x01, 0xFA, 0x00, 0x37, 0xA3, // checksum
        0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, // IEND chunk
        0xAE, 0x42, 0x60, 0x82
      ]);

      let platformSuccessCount = 0;
      
      for (const platform of platforms) {
        try {
          const result = await imageOptimizer.optimizeImageAI(
            testImageBuffer,
            { platform },
            { webp: true, avif: false, heic: false, jxl: false, av1: false, vp9: false, h265: false }
          );
          
          if (result.success) {
            platformSuccessCount++;
          }
        } catch (platformError) {
          console.warn(`Platform ${platform} optimization failed:`, platformError);
        }
      }

      if (platformSuccessCount >= 3) {
        testResults.platformOptimization = `✅ Platform optimization working (${platformSuccessCount}/${platforms.length} platforms)`;
      } else {
        testResults.platformOptimization = `⚠️ Platform optimization issues (${platformSuccessCount}/${platforms.length} platforms)`;
      }
    } catch (err) {
      console.error('Platform optimization test error:', err);
      testResults.platformOptimization = '❌ Platform optimization test failed';
    }

    // Test 6: Performance Metrics
    try {
      console.log('Testing performance metrics and benchmarking...');
      
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, // bit depth, color type, etc.
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
        0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00, // image data
        0x00, 0x01, 0x00, 0x01, 0xFA, 0x00, 0x37, 0xA3, // checksum
        0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, // IEND chunk
        0xAE, 0x42, 0x60, 0x82
      ]);

      // Benchmark standard vs AI optimization
      const startTime = Date.now();
      
      const standardResult = await imageOptimizer.optimizeImage(testImageBuffer, { quality: 80 });
      const standardTime = Date.now() - startTime;
      
      const aiStartTime = Date.now();
      const aiResult = await imageOptimizer.optimizeImageAI(testImageBuffer, { quality: 80 });
      const aiTime = Date.now() - aiStartTime;

      const performanceData = {
        standardTime,
        aiTime,
        standardSuccess: standardResult.success,
        aiSuccess: aiResult.success,
        timeOverhead: aiTime - standardTime
      };

      if (performanceData.standardSuccess && performanceData.aiSuccess) {
        testResults.performanceMetrics = `✅ Performance metrics working (Standard: ${standardTime}ms, AI: ${aiTime}ms, Overhead: ${performanceData.timeOverhead}ms)`;
      } else {
        testResults.performanceMetrics = `⚠️ Performance metrics issues (Standard: ${performanceData.standardSuccess}, AI: ${performanceData.aiSuccess})`;
      }
    } catch (err) {
      console.error('Performance metrics test error:', err);
      testResults.performanceMetrics = '❌ Performance metrics test failed';
    }

    // Generate summary
    const successCount = Object.values(testResults).filter(result => result.includes('✅')).length;
    const warningCount = Object.values(testResults).filter(result => result.includes('⚠️')).length;
    const totalTests = Object.keys(testResults).length;
    const overallSuccess = successCount >= (totalTests - 1); // Allow 1 failure

    // Get system information
    const systemInfo = {
      aiFeatures: [
        'Content type detection',
        'Complexity analysis',
        'Smart format selection',
        'Quality optimization',
        'Platform-specific tuning',
        'Performance benchmarking'
      ],
      supportedAnalysis: [
        'Photo content detection',
        'Graphics optimization',
        'Text preservation',
        'Transparency handling',
        'Animation support',
        'Noise reduction'
      ],
      optimizationStrategies: ['Aggressive', 'Balanced', 'Conservative', 'Lossless'],
      platformSupport: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn', 'Web'],
      formatIntelligence: ['JPEG', 'PNG', 'WebP', 'AVIF'],
      timestamp: new Date().toISOString(),
    };

    console.log('AI-Powered Media Optimization tests completed:', {
      overallSuccess,
      successCount,
      warningCount,
      totalTests
    });

    return NextResponse.json({
      success: true,
      overallSuccess,
      testResults,
      summary: {
        total: totalTests,
        passed: successCount,
        warnings: warningCount,
        failed: totalTests - successCount - warningCount,
        successRate: Math.round(((successCount + warningCount) / totalTests) * 100)
      },
      systemInfo,
      recommendations: generateAIOptimizationRecommendations(testResults, systemInfo),
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('AI-Powered Media Optimization test error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'AI-Powered Media Optimization tests failed',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

function generateAIOptimizationRecommendations(testResults: any, systemInfo: any): string[] {
  const recommendations: string[] = [];

  if (testResults.contentAnalyzer.includes('❌')) {
    recommendations.push('Fix content analyzer implementation and Sharp integration');
  }

  if (testResults.smartFormatSelector.includes('❌')) {
    recommendations.push('Review smart format selector logic and platform requirements');
  }

  if (testResults.aiImageOptimization.includes('⚠️') || testResults.aiImageOptimization.includes('❌')) {
    recommendations.push('Optimize AI image optimization pipeline and error handling');
  }

  if (testResults.intelligentFormatSelection.includes('⚠️')) {
    recommendations.push('Improve format selection algorithms for different content types');
  }

  if (testResults.platformOptimization.includes('⚠️')) {
    recommendations.push('Enhance platform-specific optimization rules and constraints');
  }

  if (testResults.performanceMetrics.includes('⚠️')) {
    recommendations.push('Optimize AI processing performance and reduce overhead');
  }

  if (recommendations.length === 0) {
    recommendations.push('AI-Powered Media Optimization system is ready for production!');
    recommendations.push('Next: Implement machine learning model training for better predictions');
    recommendations.push('Consider adding real-time quality assessment and feedback loops');
    recommendations.push('Set up A/B testing for optimization strategies');
  }

  return recommendations;
}
