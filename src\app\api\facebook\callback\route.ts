import { NextRequest, NextResponse } from 'next/server';
import { getOAuthConfig } from '@/lib/social/oauth-config';
import { createServiceRoleClient } from '@/lib/supabase/server';
import crypto from 'crypto';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');
    const platform = searchParams.get('platform') || 'facebook'; // Default to facebook

    // Detect platform from state parameter if not explicitly provided
    const detectedPlatform = state?.includes('instagram') ? 'instagram' : platform;

    console.log('Facebook/Instagram OAuth callback received:', {
      code: code ? 'present' : 'missing',
      state,
      platform: detectedPlatform,
      error,
      errorDescription,
      timestamp: new Date().toISOString()
    });

    // Handle OAuth errors
    if (error) {
      console.error(`${detectedPlatform} OAuth error:`, { error, errorDescription });
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=oauth_error&message=${encodeURIComponent(errorDescription || error)}&platform=${detectedPlatform}`
      );
    }

    // Validate required parameters
    if (!code) {
      console.error(`${detectedPlatform} OAuth callback missing authorization code`);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=missing_code&platform=${detectedPlatform}`
      );
    }

    // Get OAuth configuration (both Facebook and Instagram use Facebook config)
    const config = getOAuthConfig('facebook');
    if (!config || !config.enabled) {
      console.error(`${detectedPlatform} OAuth not configured or disabled`);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=platform_not_configured&platform=${detectedPlatform}`
      );
    }

    console.log(`${detectedPlatform} OAuth config loaded:`, {
      clientId: config.clientId ? 'present' : 'missing',
      redirectUri: config.redirectUri,
      scopes: config.scope
    });

    // Exchange authorization code for access token
    console.log('Exchanging authorization code for access token...');
    const tokenParams = new URLSearchParams({
      client_id: config.clientId,
      client_secret: config.clientSecret,
      code: code,
      redirect_uri: config.redirectUri,
    });

    const tokenResponse = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Facebook token exchange failed:', {
        status: tokenResponse.status,
        statusText: tokenResponse.statusText,
        error: errorText
      });
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=token_exchange_failed&platform=facebook`
      );
    }

    const tokenData = await tokenResponse.json();
    console.log('Facebook token exchange successful:', {
      hasAccessToken: !!tokenData.access_token,
      tokenType: tokenData.token_type,
      expiresIn: tokenData.expires_in
    });

    // Fetch user profile information
    console.log('Fetching Facebook user profile...');

    // Generate appsecret_proof for secure API calls
    const appsecretProof = crypto
      .createHmac('sha256', config.clientSecret)
      .update(tokenData.access_token)
      .digest('hex');

    // Try with basic fields first (id, name are always available)
    // Email may require app review, so we'll make it optional
    const profileResponse = await fetch(
      `${config.userInfoUrl}?fields=id,name&access_token=${tokenData.access_token}&appsecret_proof=${appsecretProof}`
    );

    if (!profileResponse.ok) {
      const errorText = await profileResponse.text();
      console.error('Facebook profile fetch failed:', {
        status: profileResponse.status,
        statusText: profileResponse.statusText,
        error: errorText,
        url: `${config.userInfoUrl}?fields=id,name&access_token=${tokenData.access_token.substring(0, 10)}...&appsecret_proof=${appsecretProof.substring(0, 10)}...`,
        headers: Object.fromEntries(profileResponse.headers.entries())
      });

      // Try to parse error details
      let errorDetails = 'unknown_error';
      try {
        const errorJson = JSON.parse(errorText);
        errorDetails = errorJson.error?.message || errorJson.error?.type || 'api_error';
      } catch (e) {
        errorDetails = `http_${profileResponse.status}`;
      }

      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=profile_fetch_failed&platform=facebook&details=${encodeURIComponent(errorDetails)}`
      );
    }

    const profileData = await profileResponse.json();
    console.log('Facebook profile fetched successfully:', {
      id: profileData.id,
      name: profileData.name,
      email: profileData.email || 'not_available'
    });

    // Get Facebook pages the user manages
    console.log('Fetching Facebook pages...');
    const pagesResponse = await fetch(
      `https://graph.facebook.com/v19.0/me/accounts?fields=id,name,access_token,category,fan_count&access_token=${tokenData.access_token}`
    );

    let pages = [];
    if (pagesResponse.ok) {
      const pagesData = await pagesResponse.json();
      pages = pagesData.data || [];
      console.log(`Found ${pages.length} Facebook pages`);
    } else {
      console.log('No Facebook pages found or insufficient permissions');
    }

    // Store the Facebook account in database
    // Use service role client to bypass RLS policies
    const supabase = createServiceRoleClient();

    // Use an existing user ID for testing
    // In a full implementation, you'd get the actual user ID from session
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037'; // Demo User

    console.log('Looking up user in database...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', demoUserId)
      .single();

    if (userError || !userData) {
      console.error('User lookup failed:', userError);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=user_not_found&platform=facebook`
      );
    }

    console.log('User found:', userData);

    // Check if account already exists (use detected platform)
    const platformName = detectedPlatform.toUpperCase();
    const { data: existingAccount } = await supabase
      .from('social_accounts')
      .select('id')
      .eq('user_id', demoUserId)
      .eq('platform', platformName)
      .eq('account_id', profileData.id)
      .single();

    if (existingAccount) {
      console.log(`${detectedPlatform} account already exists, updating...`);
      const { error: updateError } = await supabase
        .from('social_accounts')
        .update({
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token || null,
          expires_at: tokenData.expires_in
            ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
            : null,
          account_name: profileData.name,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingAccount.id);

      if (updateError) {
        console.error(`Failed to update ${detectedPlatform} account:`, updateError);
        return NextResponse.redirect(
          `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=account_update_failed&platform=${detectedPlatform}`
        );
      }
    } else {
      console.log(`Creating new ${detectedPlatform} account...`);

      // If user has pages, use the first page as default
      const defaultPage = pages.length > 0 ? pages[0] : null;

      const { error: insertError } = await supabase
        .from('social_accounts')
        .insert({
          user_id: demoUserId,
          platform: platformName,
          account_id: profileData.id,
          account_name: profileData.name,
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token || null,
          expires_at: tokenData.expires_in
            ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
            : null,
          page_id: defaultPage?.id || null,
          page_name: defaultPage?.name || null,
          page_category: defaultPage?.category || null,
          fan_count: defaultPage?.fan_count || null
        });

      if (insertError) {
        console.error(`Failed to create ${detectedPlatform} account:`, insertError);
        return NextResponse.redirect(
          `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=account_creation_failed&platform=${detectedPlatform}`
        );
      }
    }

    console.log(`${detectedPlatform} OAuth flow completed successfully`);

    // Check if this is a popup request
    const isPopup = searchParams.get('popup') === 'true';
    if (isPopup) {
      return new NextResponse(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${detectedPlatform.charAt(0).toUpperCase() + detectedPlatform.slice(1)} Connected</title>
        </head>
        <body>
          <script>
            // Notify parent window and close popup
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth_success',
                platform: '${detectedPlatform}',
                account: '${profileData.name.replace(/'/g, "\\'")}',
                message: '${detectedPlatform.charAt(0).toUpperCase() + detectedPlatform.slice(1)} account connected successfully'
              }, '${process.env.NEXT_PUBLIC_APP_URL}');
            }
            window.close();
          </script>
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h2>✅ ${detectedPlatform.charAt(0).toUpperCase() + detectedPlatform.slice(1)} Connected Successfully!</h2>
            <p>Account: ${profileData.name}</p>
            <p>This window will close automatically...</p>
          </div>
        </body>
        </html>
      `, {
        headers: { 'Content-Type': 'text/html' }
      });
    }

    // Regular redirect for non-popup flows
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/social?success=${detectedPlatform}_connected&account=${encodeURIComponent(profileData.name)}`
    );

  } catch (error) {
    console.error('Facebook/Instagram OAuth callback error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=internal_error&platform=facebook&message=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    );
  }
}
