import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createTokenManager } from '@/lib/auth/token-manager';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for testing social media connections
const testConnectionSchema = z.object({
  platform: z.enum(['TWITTER', 'FACEBOOK', 'LINKEDIN', 'INSTAGRAM']),
  accountId: z.string().optional(), // Optional specific account ID to test
});

// POST - Test social media connection
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Testing social media connection...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log('Test connection request:', { platform: body.platform });

    // Validate request body
    const validation = testConnectionSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    const { platform, accountId } = validation.data;

    // Get real tokens from database using token manager
    const tokenManager = createTokenManager(true);
    const accountData = await tokenManager.getTokens(user.id, platform, accountId);

    if (!accountData) {
      return NextResponse.json({
        success: false,
        platform,
        error: `No ${platform} account connected. Please connect your account first.`,
        user: null
      }, { status: 404 });
    }

    // Check if tokens are expired and attempt refresh if needed
    const expiryInfo = await tokenManager.checkTokenExpiry(user.id, platform);

    if (expiryInfo.isExpired) {
      console.log(`${platform} tokens expired, attempting refresh...`);
      const refreshResult = await tokenManager.refreshTokens(user.id, platform);

      if (!refreshResult.success) {
        return NextResponse.json({
          success: false,
          platform,
          error: `${platform} tokens expired and refresh failed: ${refreshResult.error}`,
          user: null,
          needsReconnection: true
        }, { status: 401 });
      }

      // Get updated tokens after refresh
      const updatedAccountData = await tokenManager.getTokens(user.id, platform, accountId);
      if (updatedAccountData) {
        accountData.accessToken = updatedAccountData.accessToken;
        accountData.refreshToken = updatedAccountData.refreshToken;
      }
    }

    let testResult;

    try {
      switch (platform) {
        case 'TWITTER':
          testResult = await testTwitterConnection(accountData.accessToken, accountData.refreshToken);
          break;
        case 'FACEBOOK':
          testResult = await testFacebookConnection(accountData.accessToken);
          break;
        case 'LINKEDIN':
          testResult = await testLinkedInConnection(accountData.accessToken);
          break;
        case 'INSTAGRAM':
          testResult = await testInstagramConnection(accountData.accessToken, accountData.accountId);
          break;
        default:
          testResult = {
            success: false,
            error: `Platform ${platform} not supported`,
            user: null
          };
      }

      // Log the test result with enhanced metadata
      await supabase
        .from('activities')
        .insert({
          user_id: user.id,
          action: testResult.success ? 'SOCIAL_CONNECTION_TEST_SUCCESS' : 'SOCIAL_CONNECTION_TEST_FAILED',
          metadata: {
            platform,
            accountId: accountData.accountId,
            accountName: accountData.accountName,
            success: testResult.success,
            error: testResult.error,
            tokenExpired: expiryInfo.isExpired,
            tokenRefreshed: expiryInfo.isExpired,
            timestamp: new Date().toISOString(),
          },
        });

      return NextResponse.json({
        success: testResult.success,
        platform,
        accountId: accountData.accountId,
        accountName: accountData.accountName,
        user: testResult.user,
        error: testResult.error,
        tokenStatus: {
          isExpired: expiryInfo.isExpired,
          willExpireSoon: expiryInfo.willExpireSoon,
          daysUntilExpiry: expiryInfo.daysUntilExpiry,
        },
        message: testResult.success
          ? `${platform} connection verified successfully for @${accountData.accountName}`
          : `${platform} connection failed: ${testResult.error}`
      }, { status: testResult.success ? 200 : 400 });

    } catch (error: any) {
      console.error(`Error testing ${platform} connection:`, error);

      // Log the error
      await supabase
        .from('activities')
        .insert({
          user_id: user.id,
          action: 'SOCIAL_CONNECTION_TEST_ERROR',
          details: `${platform}: ${error.message}`,
          created_at: new Date().toISOString(),
        });

      return NextResponse.json({
        success: false,
        platform,
        error: error.message || 'Connection test failed',
        user: null
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Social connection test error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Platform-specific connection testing functions
async function testTwitterConnection(accessToken: string, accessSecret?: string) {
  try {
    console.log('Testing Twitter connection...');

    // Test Twitter connection using direct API call (OAuth 2.0)
    const response = await fetch('https://api.twitter.com/2/users/me', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const userData = await response.json();
      return {
        success: true,
        error: null,
        user: userData.data
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API error: ${response.status} - ${errorText}`,
        user: null
      };
    }
  } catch (error: any) {
    console.error('Twitter connection test error:', error);
    return {
      success: false,
      error: error.message || 'Twitter connection test failed',
      user: null
    };
  }
}

async function testFacebookConnection(accessToken: string) {
  try {
    console.log('Testing Facebook connection...');

    // Test Facebook connection using direct API call
    const response = await fetch(`https://graph.facebook.com/v19.0/me?fields=id,name,email&access_token=${accessToken}&appsecret_proof=${require('crypto').createHmac('sha256', process.env.FACEBOOK_APP_SECRET!).update(accessToken).digest('hex')}`);

    if (response.ok) {
      const userData = await response.json();
      return {
        success: true,
        error: null,
        user: userData
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API error: ${response.status} - ${errorText}`,
        user: null
      };
    }
  } catch (error: any) {
    console.error('Facebook connection test error:', error);
    return {
      success: false,
      error: error.message || 'Facebook connection test failed',
      user: null
    };
  }
}

async function testLinkedInConnection(accessToken: string) {
  try {
    console.log('Testing LinkedIn connection...');

    // Test LinkedIn connection using direct API call
    const response = await fetch(`https://api.linkedin.com/v2/userinfo`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const userData = await response.json();
      return {
        success: true,
        error: null,
        user: userData
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API error: ${response.status} - ${errorText}`,
        user: null
      };
    }
  } catch (error: any) {
    console.error('LinkedIn connection test error:', error);
    return {
      success: false,
      error: error.message || 'LinkedIn connection test failed',
      user: null
    };
  }
}

async function testInstagramConnection(accessToken: string, accountId?: string) {
  try {
    console.log('Testing Instagram connection...');

    // Instagram uses Facebook Graph API - test connection using direct API call
    const response = await fetch(`https://graph.facebook.com/v19.0/me?fields=id,name,email&access_token=${accessToken}&appsecret_proof=${require('crypto').createHmac('sha256', process.env.FACEBOOK_APP_SECRET!).update(accessToken).digest('hex')}`);

    if (response.ok) {
      const userData = await response.json();
      const result = {
        success: true,
        user: userData
      };
    } else {
      const errorText = await response.text();
      const result = {
        success: false,
        error: `API error: ${response.status} - ${errorText}`
      };
    }

    if (result.success && accountId) {
      // Additional check: verify Instagram business account access
      try {
        // Use secure URL building for Instagram account verification
        const url = `https://graph.facebook.com/v18.0/${accountId}?fields=id,username&access_token=${accessToken}&appsecret_proof=${require('crypto').createHmac('sha256', process.env.FACEBOOK_APP_SECRET!).update(accessToken).digest('hex')}`;
        const response = await fetch(url);
        const data = await response.json();

        if (data.error) {
          return {
            success: false,
            error: `Instagram account verification failed: ${data.error.message}`,
            user: null
          };
        }

        return {
          success: true,
          error: null,
          user: {
            id: data.id,
            username: data.username,
            name: data.username
          }
        };
      } catch (instagramError: any) {
        return {
          success: false,
          error: `Instagram account verification failed: ${instagramError.message}`,
          user: null
        };
      }
    }

    return {
      success: result.success,
      error: result.success ? null : result.error,
      user: result.success ? result.user : null
    };
  } catch (error: any) {
    console.error('Instagram connection test error:', error);
    return {
      success: false,
      error: error.message || 'Instagram connection test failed',
      user: null
    };
  }
}
