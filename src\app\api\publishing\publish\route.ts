import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { EnhancedPublishingService, PublishingRequest } from '@/lib/publishing/enhanced-publishing-service';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('📡 Publishing API endpoint called');

    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    console.log('📝 Publishing request received:', {
      platforms: body.platforms?.map((p: any) => p.platform),
      contentLength: body.content?.length,
      hasMedia: !!body.mediaUrls?.length,
      isScheduled: !!body.scheduledAt
    });

    // Validate required fields
    if (!body.content || !body.platforms || !Array.isArray(body.platforms)) {
      return NextResponse.json(
        { error: 'Content and platforms are required' },
        { status: 400 }
      );
    }

    if (body.platforms.length === 0) {
      return NextResponse.json(
        { error: 'At least one platform must be specified' },
        { status: 400 }
      );
    }

    // Validate platform configurations
    for (const platform of body.platforms) {
      if (!platform.platform || !platform.accountId) {
        return NextResponse.json(
          { error: 'Each platform must have platform and accountId specified' },
          { status: 400 }
        );
      }

      // Validate platform type
      const validPlatforms = ['facebook', 'linkedin', 'instagram', 'twitter'];
      if (!validPlatforms.includes(platform.platform)) {
        return NextResponse.json(
          { error: `Invalid platform: ${platform.platform}. Must be one of: ${validPlatforms.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Validate scheduled time if provided
    if (body.scheduledAt) {
      const scheduledTime = new Date(body.scheduledAt);
      const now = new Date();
      
      if (scheduledTime <= now) {
        return NextResponse.json(
          { error: 'Scheduled time must be in the future' },
          { status: 400 }
        );
      }

      // Don't allow scheduling more than 1 year in advance
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      
      if (scheduledTime > oneYearFromNow) {
        return NextResponse.json(
          { error: 'Cannot schedule posts more than 1 year in advance' },
          { status: 400 }
        );
      }
    }

    // Validate content length
    if (body.content.length > 10000) {
      return NextResponse.json(
        { error: 'Content is too long (maximum 10,000 characters)' },
        { status: 400 }
      );
    }

    // Validate media URLs if provided
    if (body.mediaUrls && Array.isArray(body.mediaUrls)) {
      if (body.mediaUrls.length > 10) {
        return NextResponse.json(
          { error: 'Too many media files (maximum 10)' },
          { status: 400 }
        );
      }

      // Basic URL validation
      for (const url of body.mediaUrls) {
        try {
          new URL(url);
        } catch {
          return NextResponse.json(
            { error: `Invalid media URL: ${url}` },
            { status: 400 }
          );
        }
      }
    }

    // Verify user owns the social accounts
    const accountIds = body.platforms.map((p: any) => p.accountId);
    const { data: userAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('id, platform, is_active')
      .eq('user_id', user.id)
      .in('id', accountIds);

    if (accountsError) {
      console.error('Error fetching user accounts:', accountsError);
      return NextResponse.json(
        { error: 'Failed to verify account ownership' },
        { status: 500 }
      );
    }

    // Check if all accounts exist and are active
    for (const platform of body.platforms) {
      const account = userAccounts?.find(acc => acc.id === platform.accountId);
      if (!account) {
        return NextResponse.json(
          { error: `Account not found or not owned by user: ${platform.accountId}` },
          { status: 403 }
        );
      }

      if (!account.is_active) {
        return NextResponse.json(
          { error: `Account is inactive: ${platform.accountId}` },
          { status: 400 }
        );
      }
    }

    // Create publishing request
    const publishingRequest: PublishingRequest = {
      userId: user.id,
      content: body.content,
      platforms: body.platforms,
      mediaUrls: body.mediaUrls,
      scheduledAt: body.scheduledAt,
      metadata: {
        hashtags: body.hashtags,
        mentions: body.mentions,
        location: body.location,
        customFields: body.customFields
      }
    };

    // Initialize publishing service and publish
    const publishingService = new EnhancedPublishingService();
    const result = await publishingService.publishContent(publishingRequest);

    console.log('✅ Publishing completed:', {
      publishingId: result.publishingId,
      successful: result.successfulPlatforms,
      failed: result.failedPlatforms,
      scheduled: !!result.scheduledAt
    });

    // Log activity
    try {
      await supabase
        .from('user_activities')
        .insert({
          user_id: user.id,
          activity_type: body.scheduledAt ? 'post_scheduled' : 'post_published',
          activity_data: {
            publishingId: result.publishingId,
            platforms: body.platforms.map((p: any) => p.platform),
            contentPreview: body.content.substring(0, 100),
            scheduledAt: body.scheduledAt
          },
          created_at: new Date().toISOString()
        });
    } catch (activityError) {
      console.error('Error logging activity:', activityError);
      // Don't fail the request for activity logging errors
    }

    return NextResponse.json({
      success: true,
      message: body.scheduledAt 
        ? `Post scheduled successfully for ${new Date(body.scheduledAt).toLocaleString()}`
        : 'Post published successfully',
      data: result
    });

  } catch (error: any) {
    console.error('❌ Publishing API error:', error);

    // Log error for debugging
    try {
      
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        await supabase
          .from('error_logs')
          .insert({
            user_id: user.id,
            error_type: 'publishing_error',
            error_message: error.message,
            error_stack: error.stack,
            request_data: await request.json().catch(() => ({})),
            created_at: new Date().toISOString()
          });
      }
    } catch (logError) {
      console.error('Error logging error:', logError);
    }

    return NextResponse.json(
      { 
        error: 'Publishing failed',
        message: error.message || 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const publishingId = searchParams.get('publishingId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (publishingId) {
      // Get specific publishing result
      const { data: publishingLog, error: logError } = await supabase
        .from('publishing_logs')
        .select(`
          *,
          publishing_results (*)
        `)
        .eq('id', publishingId)
        .eq('user_id', user.id)
        .single();

      if (logError) {
        return NextResponse.json(
          { error: 'Publishing record not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: publishingLog
      });

    } else {
      // Get user's publishing history
      const { data: publishingHistory, error: historyError } = await supabase
        .from('publishing_logs')
        .select(`
          *,
          publishing_results (*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (historyError) {
        console.error('Error fetching publishing history:', historyError);
        return NextResponse.json(
          { error: 'Failed to fetch publishing history' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: publishingHistory || [],
        pagination: {
          limit,
          offset,
          total: publishingHistory?.length || 0
        }
      });
    }

  } catch (error: any) {
    console.error('❌ Publishing API GET error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch publishing data' },
      { status: 500 }
    );
  }
}
