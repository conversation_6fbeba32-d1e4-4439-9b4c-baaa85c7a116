import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for approval request creation
const createApprovalRequestSchema = z.object({
  content_type: z.enum(['post', 'campaign', 'template']),
  content_id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  workflow_template_id: z.string().uuid().optional(),
  title: z.string().min(1).max(200),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  deadline_hours: z.number().min(1).max(168).optional(), // Max 1 week
  metadata: z.record(z.any()).default({}),
  attachments: z.array(z.string()).default([]),
});

// Validation schema for approval action
const approvalActionSchema = z.object({
  action: z.enum(['approved', 'rejected', 'delegated', 'commented']),
  comments: z.string().optional(),
  decision_reason: z.string().optional(),
  delegated_to: z.string().uuid().optional(),
});

// GET - Get approval requests
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching approval requests...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspace_id');
    const status = searchParams.get('status');
    const contentType = searchParams.get('content_type');
    const assignedToMe = searchParams.get('assigned_to_me') === 'true';

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    // Check if user has access to this workspace
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember) {
      return NextResponse.json(
        { error: 'Access denied to workspace' },
        { status: 403 }
      );
    }

    // Build query
    let query = supabase
      .from('content_approval_requests')
      .select(`
        id,
        content_type,
        content_id,
        workflow_template_id,
        title,
        description,
        priority,
        status,
        current_step,
        current_approvers,
        requested_by,
        requested_at,
        deadline_at,
        approved_by,
        approved_at,
        rejected_by,
        rejected_at,
        rejection_reason,
        metadata,
        attachments,
        created_at,
        updated_at
      `)
      .eq('workspace_id', workspaceId);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (contentType) {
      query = query.eq('content_type', contentType);
    }

    if (assignedToMe) {
      query = query.contains('current_approvers', [user.id]);
    }

    // Execute query with ordering
    const { data: requests, error: requestsError } = await query
      .order('deadline_at', { ascending: true, nullsFirst: false })
      .order('created_at', { ascending: false });

    if (requestsError) {
      console.error('Error fetching approval requests:', requestsError);
      return NextResponse.json(
        { error: 'Failed to fetch approval requests' },
        { status: 500 }
      );
    }

    // Get user information for requesters and approvers
    const userIds = new Set<string>();
    requests?.forEach(request => {
      if (request.requested_by) userIds.add(request.requested_by);
      if (request.approved_by) userIds.add(request.approved_by);
      if (request.rejected_by) userIds.add(request.rejected_by);
      request.current_approvers?.forEach((id: string) => userIds.add(id));
    });

    const { data: users } = await supabase.auth.admin.listUsers();
    const userProfiles = users?.users?.reduce((acc, profile) => {
      acc[profile.id] = {
        email: profile.email,
        user_metadata: profile.user_metadata
      };
      return acc;
    }, {} as Record<string, any>) || {};

    // Get workflow template information
    const templateIds = requests?.map(r => r.workflow_template_id).filter(Boolean) || [];
    const { data: templates } = await supabase
      .from('workflow_templates')
      .select('id, name')
      .in('id', templateIds);

    const templateMap = templates?.reduce((acc, template) => {
      acc[template.id] = template;
      return acc;
    }, {} as Record<string, any>) || {};

    // Enhance requests with user and template info
    const enhancedRequests = requests?.map(request => ({
      ...request,
      requester: userProfiles[request.requested_by] || null,
      approver: request.approved_by ? userProfiles[request.approved_by] : null,
      rejector: request.rejected_by ? userProfiles[request.rejected_by] : null,
      current_approvers_info: request.current_approvers?.map((id: string) => ({
        id,
        ...userProfiles[id]
      })) || [],
      workflow_template: request.workflow_template_id ? templateMap[request.workflow_template_id] : null,
      is_assigned_to_user: request.current_approvers?.includes(user.id) || false,
      can_approve: request.current_approvers?.includes(user.id) && request.status === 'pending'
    })) || [];

    console.log(`Found ${enhancedRequests.length} approval requests for workspace ${workspaceId}`);

    return NextResponse.json({
      success: true,
      requests: enhancedRequests,
      user_role: userMember.role
    }, { status: 200 });

  } catch (error) {
    console.error('Approval requests fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new approval request
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Creating new approval request...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createApprovalRequestSchema.parse(body);

    console.log('Creating approval request:', validatedData);

    // Check if user has access to this workspace
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', validatedData.workspace_id)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember) {
      return NextResponse.json(
        { error: 'Access denied to workspace' },
        { status: 403 }
      );
    }

    // Get workflow template or use default
    let workflowTemplate;
    if (validatedData.workflow_template_id) {
      const { data: template } = await supabase
        .from('workflow_templates')
        .select('*')
        .eq('id', validatedData.workflow_template_id)
        .eq('workspace_id', validatedData.workspace_id)
        .single();
      
      workflowTemplate = template;
    } else {
      // Get default workflow template
      const { data: defaultTemplate } = await supabase
        .from('workflow_templates')
        .select('*')
        .eq('workspace_id', validatedData.workspace_id)
        .eq('is_default', true)
        .eq('is_active', true)
        .single();
      
      workflowTemplate = defaultTemplate;
    }

    if (!workflowTemplate) {
      return NextResponse.json(
        { error: 'No workflow template found' },
        { status: 400 }
      );
    }

    // Calculate deadline
    const deadlineHours = validatedData.deadline_hours || workflowTemplate.settings?.deadline_hours || 24;
    const deadlineAt = new Date(Date.now() + deadlineHours * 60 * 60 * 1000);

    // Get first step approvers
    const firstStep = workflowTemplate.steps[0];
    if (!firstStep || !firstStep.approvers || firstStep.approvers.length === 0) {
      return NextResponse.json(
        { error: 'Workflow template has no approvers configured' },
        { status: 400 }
      );
    }

    // Create approval request
    const { data: approvalRequest, error: requestError } = await supabase
      .from('content_approval_requests')
      .insert({
        content_type: validatedData.content_type,
        content_id: validatedData.content_id,
        workspace_id: validatedData.workspace_id,
        workflow_template_id: workflowTemplate.id,
        workflow_steps: workflowTemplate.steps,
        title: validatedData.title,
        description: validatedData.description,
        priority: validatedData.priority,
        status: 'pending',
        current_step: 1,
        current_approvers: firstStep.approvers,
        requested_by: user.id,
        deadline_at: deadlineAt.toISOString(),
        metadata: validatedData.metadata,
        attachments: validatedData.attachments
      })
      .select()
      .single();

    if (requestError) {
      console.error('Error creating approval request:', requestError);
      return NextResponse.json(
        { error: 'Failed to create approval request' },
        { status: 500 }
      );
    }

    // Update workflow template usage
    await supabase
      .from('workflow_templates')
      .update({
        usage_count: workflowTemplate.usage_count + 1,
        last_used_at: new Date().toISOString()
      })
      .eq('id', workflowTemplate.id);

    // Create notifications for approvers
    const notifications = firstStep.approvers.map((approverId: string) => ({
      user_id: approverId,
      workspace_id: validatedData.workspace_id,
      type: 'approval_request',
      title: 'New Approval Request',
      message: `${validatedData.title} requires your approval`,
      approval_request_id: approvalRequest.id,
      action_url: `/workflows/approvals/${approvalRequest.id}`,
      expires_at: deadlineAt.toISOString()
    }));

    await supabase
      .from('approval_notifications')
      .insert(notifications);

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: validatedData.workspace_id,
        action: 'APPROVAL_REQUEST_CREATED',
        details: `Created approval request "${validatedData.title}"`,
        created_at: new Date().toISOString(),
      });

    console.log('Approval request created successfully:', approvalRequest.id);

    return NextResponse.json({
      success: true,
      request: approvalRequest,
      message: 'Approval request created successfully'
    }, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid approval request data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Approval request creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Take action on approval request
export async function PUT(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Taking action on approval request...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get request ID from query params
    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('id');

    if (!requestId) {
      return NextResponse.json(
        { error: 'Request ID is required' },
        { status: 400 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = approvalActionSchema.parse(body);

    console.log('Taking approval action:', requestId, validatedData);

    // Get approval request
    const { data: approvalRequest } = await supabase
      .from('content_approval_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (!approvalRequest) {
      return NextResponse.json(
        { error: 'Approval request not found' },
        { status: 404 }
      );
    }

    // Check if user has access to this workspace
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', approvalRequest.workspace_id)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember) {
      return NextResponse.json(
        { error: 'Access denied to workspace' },
        { status: 403 }
      );
    }

    // Use the database function to advance workflow
    const { data: result, error: actionError } = await supabase
      .rpc('advance_approval_workflow', {
        request_id: requestId,
        approver_uuid: user.id,
        action_type: validatedData.action,
        comments_text: validatedData.comments
      });

    if (actionError) {
      console.error('Error taking approval action:', actionError);
      return NextResponse.json(
        { error: 'Failed to process approval action' },
        { status: 500 }
      );
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: approvalRequest.workspace_id,
        action: `APPROVAL_${validatedData.action.toUpperCase()}`,
        details: `${validatedData.action} approval request "${approvalRequest.title}"`,
        created_at: new Date().toISOString(),
      });

    console.log('Approval action completed successfully:', requestId, result);

    return NextResponse.json({
      success: true,
      result,
      message: `Approval ${validatedData.action} successfully`
    }, { status: 200 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid approval action data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Approval action error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
