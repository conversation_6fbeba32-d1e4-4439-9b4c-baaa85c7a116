import { createClient } from '@/lib/supabase/server';
import { SchedulerLogger } from './scheduler-logger';

export interface TimeSlot {
  hour: number;
  minute: number;
  dayOfWeek: number; // 0 = Sunday, 6 = Saturday
  score: number; // 0-100
  reason: string;
  timezone: string;
}

export interface OptimalTimeAnalysis {
  bestTimes: TimeSlot[];
  platformSpecific: Record<string, TimeSlot[]>;
  audienceInsights: {
    peakHours: number[];
    peakDays: number[];
    timezone: string;
    activityPattern: string;
  };
  recommendations: string[];
}

/**
 * Service for analyzing and suggesting optimal posting times
 */
export class OptimalTimeService {
  private logger: SchedulerLogger;

  constructor() {
    this.logger = new SchedulerLogger('optimal-time-service');
  }

  /**
   * Get optimal posting times for a user
   */
  async getOptimalTimes(userId: string, platforms: string[] = []): Promise<OptimalTimeAnalysis> {
    try {
      this.logger.info('Analyzing optimal posting times', { userId, platforms });

      const supabase = createClient();

      // Get user's historical post performance
      const historicalData = await this.getHistoricalPerformance(supabase, userId, platforms);

      // Get user's audience insights
      const audienceInsights = await this.getAudienceInsights(supabase, userId, platforms);

      // Get platform-specific best practices
      const platformBestPractices = this.getPlatformBestPractices(platforms);

      // Analyze and score time slots
      const timeSlots = await this.analyzeTimeSlots(
        historicalData,
        audienceInsights,
        platformBestPractices
      );

      // Generate platform-specific recommendations
      const platformSpecific = this.generatePlatformSpecificTimes(timeSlots, platforms);

      // Generate recommendations
      const recommendations = this.generateRecommendations(timeSlots, audienceInsights, platforms);

      const analysis: OptimalTimeAnalysis = {
        bestTimes: timeSlots.slice(0, 10), // Top 10 overall
        platformSpecific,
        audienceInsights,
        recommendations,
      };

      this.logger.info('Optimal time analysis completed', {
        userId,
        bestTimesCount: analysis.bestTimes.length,
        platformsAnalyzed: Object.keys(platformSpecific).length,
      });

      return analysis;

    } catch (error) {
      this.logger.error('Failed to analyze optimal times', error, { userId, platforms });
      throw error;
    }
  }

  /**
   * Get historical post performance data
   */
  private async getHistoricalPerformance(
    supabase: any,
    userId: string,
    platforms: string[]
  ): Promise<any[]> {
    try {
      let query = supabase
        .from('post_publish_history')
        .select(`
          platform,
          started_at,
          status,
          posts (
            id,
            content,
            scheduled_at,
            user_id
          )
        `)
        .eq('posts.user_id', userId)
        .eq('status', 'published')
        .gte('started_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()) // Last 90 days
        .order('started_at', { ascending: false })
        .limit(1000);

      if (platforms.length > 0) {
        query = query.in('platform', platforms);
      }

      const { data, error } = await query;

      if (error) {
        this.logger.warn('Failed to fetch historical performance', error);
        return [];
      }

      return data || [];

    } catch (error) {
      this.logger.warn('Error fetching historical performance', error as Record<string, any>);
      return [];
    }
  }

  /**
   * Get audience insights (simulated for now)
   */
  private async getAudienceInsights(
    supabase: any,
    userId: string,
    platforms: string[]
  ): Promise<any> {
    // In a real implementation, this would analyze:
    // - User's followers' activity patterns
    // - Geographic distribution
    // - Engagement timing patterns
    // - Industry-specific data

    // For now, return MENA region defaults
    return {
      peakHours: [9, 12, 15, 18, 21], // 9 AM, 12 PM, 3 PM, 6 PM, 9 PM
      peakDays: [0, 1, 2, 3, 4], // Sunday to Thursday (MENA work week)
      timezone: 'Asia/Riyadh',
      activityPattern: 'mena_business',
    };
  }

  /**
   * Get platform-specific best practices
   */
  private getPlatformBestPractices(platforms: string[]): Record<string, any> {
    const bestPractices: Record<string, any> = {
      TWITTER: {
        peakHours: [9, 12, 15, 18],
        peakDays: [1, 2, 3, 4], // Monday to Thursday
        avoidHours: [2, 3, 4, 5, 6], // Late night/early morning
        frequency: 'high', // Can post multiple times per day
      },
      FACEBOOK: {
        peakHours: [13, 15, 18, 20],
        peakDays: [2, 3, 4], // Tuesday to Thursday
        avoidHours: [1, 2, 3, 4, 5, 6],
        frequency: 'medium', // 1-2 times per day
      },
      INSTAGRAM: {
        peakHours: [11, 13, 17, 19],
        peakDays: [1, 2, 3, 4, 5], // Monday to Friday
        avoidHours: [2, 3, 4, 5, 6, 7],
        frequency: 'medium', // 1-2 times per day
      },
      LINKEDIN: {
        peakHours: [8, 9, 12, 17, 18],
        peakDays: [1, 2, 3, 4], // Monday to Thursday
        avoidHours: [19, 20, 21, 22, 23, 0, 1, 2, 3, 4, 5, 6], // Evenings and nights
        frequency: 'low', // 1 time per day max
      },
    };

    return platforms.reduce((acc, platform) => {
      if (bestPractices[platform]) {
        acc[platform] = bestPractices[platform];
      }
      return acc;
    }, {} as Record<string, any>);
  }

  /**
   * Analyze and score time slots
   */
  private async analyzeTimeSlots(
    historicalData: any[],
    audienceInsights: any,
    platformBestPractices: Record<string, any>
  ): Promise<TimeSlot[]> {
    const timeSlots: TimeSlot[] = [];

    // Generate time slots for each day of week and hour
    for (let dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
      for (let hour = 6; hour < 24; hour++) { // 6 AM to 11 PM
        for (let minute of [0, 30]) { // Every 30 minutes
          const slot: TimeSlot = {
            hour,
            minute,
            dayOfWeek,
            score: 0,
            reason: '',
            timezone: audienceInsights.timezone,
          };

          // Calculate score based on multiple factors
          slot.score = this.calculateTimeSlotScore(
            slot,
            historicalData,
            audienceInsights,
            platformBestPractices
          );

          slot.reason = this.generateScoreReason(slot, audienceInsights, platformBestPractices);

          timeSlots.push(slot);
        }
      }
    }

    // Sort by score (highest first)
    return timeSlots.sort((a, b) => b.score - a.score);
  }

  /**
   * Calculate score for a time slot
   */
  private calculateTimeSlotScore(
    slot: TimeSlot,
    historicalData: any[],
    audienceInsights: any,
    platformBestPractices: Record<string, any>
  ): number {
    let score = 0;

    // Base score from audience insights
    if (audienceInsights.peakHours.includes(slot.hour)) {
      score += 30;
    }

    if (audienceInsights.peakDays.includes(slot.dayOfWeek)) {
      score += 20;
    }

    // Platform best practices score
    const platformScores = Object.values(platformBestPractices).map(practices => {
      let platformScore = 0;

      if (practices.peakHours.includes(slot.hour)) {
        platformScore += 25;
      }

      if (practices.peakDays.includes(slot.dayOfWeek)) {
        platformScore += 15;
      }

      if (practices.avoidHours.includes(slot.hour)) {
        platformScore -= 20;
      }

      return platformScore;
    });

    if (platformScores.length > 0) {
      score += platformScores.reduce((sum, s) => sum + s, 0) / platformScores.length;
    }

    // Historical performance boost
    const historicalPerformance = this.getHistoricalPerformanceForSlot(slot, historicalData);
    score += historicalPerformance * 20;

    // Time-specific adjustments
    if (slot.hour >= 2 && slot.hour <= 6) {
      score -= 30; // Penalize very early hours
    }

    if (slot.hour >= 22) {
      score -= 15; // Penalize very late hours
    }

    // Weekend adjustments for MENA region
    if (slot.dayOfWeek === 5 || slot.dayOfWeek === 6) { // Friday and Saturday
      score -= 10; // Slight penalty for weekends
    }

    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get historical performance for a specific time slot
   */
  private getHistoricalPerformanceForSlot(slot: TimeSlot, historicalData: any[]): number {
    const relevantPosts = historicalData.filter(post => {
      const postDate = new Date(post.started_at);
      return (
        postDate.getDay() === slot.dayOfWeek &&
        postDate.getHours() === slot.hour &&
        Math.abs(postDate.getMinutes() - slot.minute) <= 15 // Within 15 minutes
      );
    });

    if (relevantPosts.length === 0) {
      return 0;
    }

    // For now, just return a normalized count
    // In a real implementation, this would factor in engagement metrics
    return Math.min(1, relevantPosts.length / 10);
  }

  /**
   * Generate reason for the score
   */
  private generateScoreReason(
    slot: TimeSlot,
    audienceInsights: any,
    platformBestPractices: Record<string, any>
  ): string {
    const reasons = [];

    if (audienceInsights.peakHours.includes(slot.hour)) {
      reasons.push('ساعة ذروة للجمهور');
    }

    if (audienceInsights.peakDays.includes(slot.dayOfWeek)) {
      reasons.push('يوم نشط للجمهور');
    }

    const dayNames = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const timeStr = `${slot.hour.toString().padStart(2, '0')}:${slot.minute.toString().padStart(2, '0')}`;

    if (reasons.length === 0) {
      return `${dayNames[slot.dayOfWeek]} في ${timeStr}`;
    }

    return `${dayNames[slot.dayOfWeek]} في ${timeStr} - ${reasons.join('، ')}`;
  }

  /**
   * Generate platform-specific optimal times
   */
  private generatePlatformSpecificTimes(
    timeSlots: TimeSlot[],
    platforms: string[]
  ): Record<string, TimeSlot[]> {
    const platformSpecific: Record<string, TimeSlot[]> = {};

    for (const platform of platforms) {
      // Filter and re-score based on platform-specific criteria
      const platformSlots = timeSlots
        .map(slot => ({ ...slot })) // Clone
        .filter(slot => {
          // Platform-specific filtering logic
          switch (platform) {
            case 'LINKEDIN':
              // Avoid evenings and weekends for LinkedIn
              return !(slot.hour >= 19 || slot.dayOfWeek === 5 || slot.dayOfWeek === 6);
            case 'INSTAGRAM':
              // Instagram works well in evenings too
              return slot.hour >= 8 && slot.hour <= 22;
            default:
              return slot.hour >= 6 && slot.hour <= 23;
          }
        })
        .slice(0, 5); // Top 5 for each platform

      platformSpecific[platform] = platformSlots;
    }

    return platformSpecific;
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(
    timeSlots: TimeSlot[],
    audienceInsights: any,
    platforms: string[]
  ): string[] {
    const recommendations = [];

    // Best overall time
    if (timeSlots.length > 0) {
      const bestTime = timeSlots[0];
      const dayNames = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
      recommendations.push(
        `أفضل وقت للنشر هو ${dayNames[bestTime.dayOfWeek]} في ${bestTime.hour}:${bestTime.minute.toString().padStart(2, '0')}`
      );
    }

    // Peak hours recommendation
    if (audienceInsights.peakHours.length > 0) {
      const peakHoursStr = audienceInsights.peakHours.map((h: number) => `${h}:00`).join('، ');
      recommendations.push(`ساعات الذروة لجمهورك: ${peakHoursStr}`);
    }

    // Platform-specific recommendations
    if (platforms.includes('LINKEDIN')) {
      recommendations.push('لينكد إن: انشر في ساعات العمل (8 صباحاً - 6 مساءً) من الإثنين إلى الخميس');
    }

    if (platforms.includes('INSTAGRAM')) {
      recommendations.push('إنستغرام: أفضل الأوقات هي 11 صباحاً، 1 ظهراً، 5 مساءً، و 7 مساءً');
    }

    if (platforms.includes('TWITTER')) {
      recommendations.push('تويتر: يمكنك النشر عدة مرات يومياً في أوقات مختلفة');
    }

    // General recommendations
    recommendations.push('تجنب النشر في ساعات متأخرة من الليل (2-6 صباحاً)');
    recommendations.push('أيام الأسبوع (الأحد-الخميس) أفضل من عطلة نهاية الأسبوع في المنطقة العربية');

    return recommendations;
  }

  /**
   * Get next optimal time for immediate scheduling
   */
  async getNextOptimalTime(userId: string, platforms: string[]): Promise<Date> {
    try {
      const analysis = await this.getOptimalTimes(userId, platforms);

      if (analysis.bestTimes.length === 0) {
        // Fallback to default good time (9 AM tomorrow)
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(9, 0, 0, 0);
        return tomorrow;
      }

      const now = new Date();
      const bestTime = analysis.bestTimes[0];

      // Find next occurrence of this optimal time
      const nextTime = new Date();
      nextTime.setHours(bestTime.hour, bestTime.minute, 0, 0);

      // If the time has passed today, move to next occurrence
      if (nextTime <= now) {
        // Find next occurrence of this day of week
        const daysUntilTarget = (bestTime.dayOfWeek + 7 - nextTime.getDay()) % 7;
        if (daysUntilTarget === 0) {
          nextTime.setDate(nextTime.getDate() + 7); // Next week
        } else {
          nextTime.setDate(nextTime.getDate() + daysUntilTarget);
        }
      }

      return nextTime;

    } catch (error) {
      this.logger.error('Failed to get next optimal time', error);

      // Fallback to 9 AM tomorrow
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(9, 0, 0, 0);
      return tomorrow;
    }
  }
}
