-- Migration: Analytics System Tables
-- Description: Create comprehensive analytics and insights infrastructure

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Add analytics_collected column to posts table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'posts' AND column_name = 'analytics_collected'
    ) THEN
        ALTER TABLE posts ADD COLUMN analytics_collected BOOLEAN DEFAULT FALSE;
        CREATE INDEX IF NOT EXISTS idx_posts_analytics_collected ON posts(analytics_collected);
    END IF;
END $$;

-- Post analytics table for detailed performance metrics
CREATE TABLE IF NOT EXISTS post_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,
    social_account_id UUID REFERENCES social_accounts(id) ON DELETE SET NULL,
    platform_post_id TEXT,

    -- Engagement metrics
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    saves_count INTEGER DEFAULT 0,
    clicks_count INTEGER DEFAULT 0,

    -- Reach and impressions
    impressions INTEGER DEFAULT 0,
    reach INTEGER DEFAULT 0,
    unique_views INTEGER DEFAULT 0,

    -- Calculated metrics
    engagement_rate DECIMAL(5,2) DEFAULT 0,
    click_through_rate DECIMAL(5,2) DEFAULT 0,
    save_rate DECIMAL(5,2) DEFAULT 0,

    -- Time-based metrics
    collected_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    post_published_at TIMESTAMPTZ,

    -- Raw data from platform APIs
    raw_data JSONB DEFAULT '{}',

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Account analytics for overall performance tracking
CREATE TABLE IF NOT EXISTS account_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    social_account_id UUID NOT NULL REFERENCES social_accounts(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,

    -- Follower metrics
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,

    -- Engagement metrics
    total_likes INTEGER DEFAULT 0,
    total_comments INTEGER DEFAULT 0,
    total_shares INTEGER DEFAULT 0,
    avg_engagement_rate DECIMAL(5,2) DEFAULT 0,

    -- Growth metrics
    followers_growth INTEGER DEFAULT 0,
    followers_growth_rate DECIMAL(5,2) DEFAULT 0,

    -- Time period
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly')),

    -- Raw data from platform APIs
    raw_data JSONB DEFAULT '{}',

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(social_account_id, period_start, period_type)
);

-- Content insights for AI-powered recommendations
CREATE TABLE IF NOT EXISTS content_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Content analysis
    content_type VARCHAR(50) NOT NULL, -- 'text', 'image', 'video', 'carousel'
    content_category VARCHAR(100), -- 'business', 'personal', 'promotional', etc.
    content_sentiment VARCHAR(20), -- 'positive', 'negative', 'neutral'
    content_length INTEGER,

    -- Hashtag analysis
    hashtags TEXT[],
    hashtag_count INTEGER DEFAULT 0,
    trending_hashtags TEXT[],

    -- Performance correlation
    avg_engagement_rate DECIMAL(5,2) DEFAULT 0,
    avg_reach INTEGER DEFAULT 0,
    performance_score INTEGER DEFAULT 0, -- 0-100

    -- Time analysis
    best_posting_times JSONB DEFAULT '{}',
    best_posting_days INTEGER[],

    -- Platform-specific insights
    platform_performance JSONB DEFAULT '{}',

    -- Analysis period
    analysis_period_start TIMESTAMPTZ NOT NULL,
    analysis_period_end TIMESTAMPTZ NOT NULL,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Hashtag analytics for trend tracking
CREATE TABLE IF NOT EXISTS hashtag_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    hashtag VARCHAR(100) NOT NULL,
    platform VARCHAR(20) NOT NULL,

    -- Usage metrics
    usage_count INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    total_engagement INTEGER DEFAULT 0,

    -- Trend metrics
    trend_score INTEGER DEFAULT 0, -- 0-100
    growth_rate DECIMAL(5,2) DEFAULT 0,
    peak_usage_time TIMESTAMPTZ,

    -- Geographic and demographic data
    top_countries TEXT[],
    age_groups JSONB DEFAULT '{}',

    -- Time period
    period_date DATE NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly')),

    created_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(hashtag, platform, period_date, period_type)
);

-- Competitor analysis data
CREATE TABLE IF NOT EXISTS competitor_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    competitor_name VARCHAR(200) NOT NULL,
    competitor_handle VARCHAR(100) NOT NULL,
    platform VARCHAR(20) NOT NULL,

    -- Competitor metrics
    followers_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    avg_engagement_rate DECIMAL(5,2) DEFAULT 0,
    posting_frequency DECIMAL(3,1) DEFAULT 0, -- posts per day

    -- Content analysis
    top_content_types JSONB DEFAULT '{}',
    top_hashtags TEXT[],
    avg_post_length INTEGER DEFAULT 0,

    -- Performance comparison
    relative_performance DECIMAL(5,2) DEFAULT 0, -- vs user's performance
    market_share DECIMAL(5,2) DEFAULT 0,

    -- Analysis period
    analysis_date DATE NOT NULL,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_id, competitor_handle, platform, analysis_date)
);

-- Analytics reports for scheduled reporting
CREATE TABLE IF NOT EXISTS analytics_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    report_name VARCHAR(200) NOT NULL,
    report_type VARCHAR(50) NOT NULL, -- 'performance', 'growth', 'content', 'competitor'

    -- Report configuration
    platforms TEXT[],
    date_range_start TIMESTAMPTZ NOT NULL,
    date_range_end TIMESTAMPTZ NOT NULL,
    metrics TEXT[], -- which metrics to include

    -- Report data
    report_data JSONB NOT NULL DEFAULT '{}',
    summary JSONB DEFAULT '{}',
    insights TEXT[],
    recommendations TEXT[],

    -- Scheduling
    is_scheduled BOOLEAN DEFAULT FALSE,
    schedule_frequency VARCHAR(20), -- 'daily', 'weekly', 'monthly'
    next_generation_at TIMESTAMPTZ,

    -- Export settings
    export_formats TEXT[], -- 'pdf', 'excel', 'csv'
    email_recipients TEXT[],

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Real-time metrics for live monitoring
CREATE TABLE IF NOT EXISTS realtime_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    metric_value NUMERIC NOT NULL,
    metric_type VARCHAR(20) NOT NULL, -- 'counter', 'gauge', 'rate'

    -- Context
    platform VARCHAR(20),
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    social_account_id UUID REFERENCES social_accounts(id) ON DELETE CASCADE,

    -- Metadata
    labels JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Analytics collection jobs for automated data gathering
CREATE TABLE IF NOT EXISTS analytics_collection_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('post', 'account', 'hashtag', 'competitor')),
    target_id VARCHAR(100) NOT NULL, -- ID of the target (post_id, social_account_id, etc.)
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Job scheduling
    priority INTEGER DEFAULT 5,
    scheduled_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,

    -- Job status
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,

    -- Error handling
    last_error TEXT,
    error_details JSONB,

    -- Additional metadata
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_post_analytics_post_id ON post_analytics(post_id);
CREATE INDEX IF NOT EXISTS idx_post_analytics_platform ON post_analytics(platform);
CREATE INDEX IF NOT EXISTS idx_post_analytics_collected_at ON post_analytics(collected_at DESC);
CREATE INDEX IF NOT EXISTS idx_post_analytics_social_account_id ON post_analytics(social_account_id);

CREATE INDEX IF NOT EXISTS idx_account_analytics_social_account_id ON account_analytics(social_account_id);
CREATE INDEX IF NOT EXISTS idx_account_analytics_period ON account_analytics(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_account_analytics_platform ON account_analytics(platform);

CREATE INDEX IF NOT EXISTS idx_content_insights_user_id ON content_insights(user_id);
CREATE INDEX IF NOT EXISTS idx_content_insights_content_type ON content_insights(content_type);
CREATE INDEX IF NOT EXISTS idx_content_insights_period ON content_insights(analysis_period_start, analysis_period_end);

CREATE INDEX IF NOT EXISTS idx_hashtag_analytics_hashtag ON hashtag_analytics(hashtag);
CREATE INDEX IF NOT EXISTS idx_hashtag_analytics_platform ON hashtag_analytics(platform);
CREATE INDEX IF NOT EXISTS idx_hashtag_analytics_period_date ON hashtag_analytics(period_date DESC);
CREATE INDEX IF NOT EXISTS idx_hashtag_analytics_trend_score ON hashtag_analytics(trend_score DESC);

CREATE INDEX IF NOT EXISTS idx_competitor_analytics_user_id ON competitor_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_competitor_analytics_platform ON competitor_analytics(platform);
CREATE INDEX IF NOT EXISTS idx_competitor_analytics_analysis_date ON competitor_analytics(analysis_date DESC);

CREATE INDEX IF NOT EXISTS idx_analytics_reports_user_id ON analytics_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_reports_next_generation ON analytics_reports(next_generation_at);
CREATE INDEX IF NOT EXISTS idx_analytics_reports_created_at ON analytics_reports(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_realtime_metrics_user_id ON realtime_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_realtime_metrics_timestamp ON realtime_metrics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_realtime_metrics_metric_name ON realtime_metrics(metric_name);

CREATE INDEX IF NOT EXISTS idx_analytics_collection_jobs_user_id ON analytics_collection_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_collection_jobs_status ON analytics_collection_jobs(status);
CREATE INDEX IF NOT EXISTS idx_analytics_collection_jobs_type ON analytics_collection_jobs(type);
CREATE INDEX IF NOT EXISTS idx_analytics_collection_jobs_scheduled_at ON analytics_collection_jobs(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_analytics_collection_jobs_target_id ON analytics_collection_jobs(target_id);

-- Add RLS policies
ALTER TABLE post_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE hashtag_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE competitor_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE realtime_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_collection_jobs ENABLE ROW LEVEL SECURITY;

-- Post analytics policies
CREATE POLICY "Users can view analytics for their posts" ON post_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM posts
            WHERE posts.id = post_analytics.post_id
            AND posts.user_id = auth.uid()
        )
    );

CREATE POLICY "Service role can manage all post analytics" ON post_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- Account analytics policies
CREATE POLICY "Users can view their account analytics" ON account_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM social_accounts
            WHERE social_accounts.id = account_analytics.social_account_id
            AND social_accounts.user_id = auth.uid()
        )
    );

CREATE POLICY "Service role can manage all account analytics" ON account_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- Content insights policies
CREATE POLICY "Users can view their content insights" ON content_insights
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Service role can manage all content insights" ON content_insights
    FOR ALL USING (auth.role() = 'service_role');

-- Hashtag analytics policies (public read, service write)
CREATE POLICY "Anyone can view hashtag analytics" ON hashtag_analytics
    FOR SELECT USING (true);

CREATE POLICY "Service role can manage hashtag analytics" ON hashtag_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- Competitor analytics policies
CREATE POLICY "Users can view their competitor analytics" ON competitor_analytics
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their competitor analytics" ON competitor_analytics
    FOR ALL USING (user_id = auth.uid());

-- Analytics reports policies
CREATE POLICY "Users can manage their analytics reports" ON analytics_reports
    FOR ALL USING (user_id = auth.uid());

-- Realtime metrics policies
CREATE POLICY "Users can view their realtime metrics" ON realtime_metrics
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Service role can manage all realtime metrics" ON realtime_metrics
    FOR ALL USING (auth.role() = 'service_role');

-- Analytics collection jobs policies
CREATE POLICY "Users can manage their collection jobs" ON analytics_collection_jobs
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Service role can manage all collection jobs" ON analytics_collection_jobs
    FOR ALL USING (auth.role() = 'service_role');

-- Add updated_at triggers
CREATE TRIGGER update_post_analytics_updated_at
    BEFORE UPDATE ON post_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_account_analytics_updated_at
    BEFORE UPDATE ON account_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_insights_updated_at
    BEFORE UPDATE ON content_insights
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_competitor_analytics_updated_at
    BEFORE UPDATE ON competitor_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analytics_reports_updated_at
    BEFORE UPDATE ON analytics_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analytics_collection_jobs_updated_at
    BEFORE UPDATE ON analytics_collection_jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Analytics helper functions
CREATE OR REPLACE FUNCTION calculate_engagement_rate(
    likes INTEGER,
    comments INTEGER,
    shares INTEGER,
    impressions INTEGER
) RETURNS DECIMAL(5,2) AS $$
BEGIN
    IF impressions = 0 OR impressions IS NULL THEN
        RETURN 0;
    END IF;

    RETURN ROUND(((likes + comments + shares)::DECIMAL / impressions::DECIMAL) * 100, 2);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION get_user_analytics_summary(
    p_user_id UUID,
    p_start_date TIMESTAMPTZ DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMPTZ DEFAULT NOW()
) RETURNS TABLE (
    total_posts BIGINT,
    total_engagement BIGINT,
    avg_engagement_rate DECIMAL,
    total_reach BIGINT,
    total_impressions BIGINT,
    best_performing_platform TEXT,
    growth_rate DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(DISTINCT pa.post_id) as total_posts,
        SUM(pa.likes_count + pa.comments_count + pa.shares_count) as total_engagement,
        AVG(pa.engagement_rate) as avg_engagement_rate,
        SUM(pa.reach) as total_reach,
        SUM(pa.impressions) as total_impressions,
        (
            SELECT pa2.platform
            FROM post_analytics pa2
            JOIN posts p2 ON pa2.post_id = p2.id
            WHERE p2.user_id = p_user_id
            AND pa2.collected_at BETWEEN p_start_date AND p_end_date
            GROUP BY pa2.platform
            ORDER BY AVG(pa2.engagement_rate) DESC
            LIMIT 1
        ) as best_performing_platform,
        0::DECIMAL as growth_rate -- Placeholder for growth calculation
    FROM post_analytics pa
    JOIN posts p ON pa.post_id = p.id
    WHERE p.user_id = p_user_id
    AND pa.collected_at BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old analytics data
CREATE OR REPLACE FUNCTION cleanup_old_analytics(days_to_keep INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Cleanup old post analytics (keep 1 year by default)
    DELETE FROM post_analytics
    WHERE collected_at < NOW() - (days_to_keep || ' days')::INTERVAL;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    -- Cleanup old realtime metrics (keep 30 days)
    DELETE FROM realtime_metrics
    WHERE timestamp < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE post_analytics IS 'Detailed performance metrics for individual posts';
COMMENT ON TABLE account_analytics IS 'Account-level performance metrics and growth tracking';
COMMENT ON TABLE content_insights IS 'AI-powered content analysis and recommendations';
COMMENT ON TABLE hashtag_analytics IS 'Hashtag trend tracking and performance analysis';
COMMENT ON TABLE competitor_analytics IS 'Competitor benchmarking and market analysis';
COMMENT ON TABLE analytics_reports IS 'Scheduled and custom analytics reports';
COMMENT ON TABLE realtime_metrics IS 'Real-time performance monitoring metrics';

COMMENT ON FUNCTION calculate_engagement_rate(INTEGER, INTEGER, INTEGER, INTEGER) IS 'Calculate engagement rate from interaction metrics';
COMMENT ON FUNCTION get_user_analytics_summary(UUID, TIMESTAMPTZ, TIMESTAMPTZ) IS 'Get comprehensive analytics summary for a user';
COMMENT ON FUNCTION cleanup_old_analytics(INTEGER) IS 'Remove old analytics data to manage storage';
