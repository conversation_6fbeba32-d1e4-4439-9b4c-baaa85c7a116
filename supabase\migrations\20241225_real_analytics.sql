-- Real Analytics & Performance Tracking Migration
-- Creates comprehensive analytics infrastructure for social media performance tracking

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Analytics Events Table - Track all user interactions and post performance
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    social_account_id UUID REFERENCES social_accounts(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- 'post_published', 'engagement_received', 'click', 'impression', etc.
    platform VARCHAR(20) NOT NULL, -- 'TWITTER', 'FACEBOOK', 'LINKEDIN', 'INSTAGRAM', 'SNAPCHAT'
    event_data JSONB NOT NULL DEFAULT '{}', -- Flexible data storage for platform-specific metrics
    metrics JSONB NOT NULL DEFAULT '{}', -- Standardized metrics: likes, shares, comments, impressions, clicks
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes for performance
    INDEX idx_analytics_events_user_id ON analytics_events(user_id),
    INDEX idx_analytics_events_post_id ON analytics_events(post_id),
    INDEX idx_analytics_events_platform ON analytics_events(platform),
    INDEX idx_analytics_events_type ON analytics_events(event_type),
    INDEX idx_analytics_events_timestamp ON analytics_events(timestamp),
    INDEX idx_analytics_events_created_at ON analytics_events(created_at)
);

-- Post Performance Metrics - Aggregated performance data for posts
CREATE TABLE IF NOT EXISTS post_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    social_account_id UUID NOT NULL REFERENCES social_accounts(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,
    
    -- Core engagement metrics
    impressions INTEGER DEFAULT 0,
    reach INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    comments INTEGER DEFAULT 0,
    shares INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    saves INTEGER DEFAULT 0,
    
    -- Platform-specific metrics
    retweets INTEGER DEFAULT 0, -- Twitter
    quote_tweets INTEGER DEFAULT 0, -- Twitter
    replies INTEGER DEFAULT 0, -- Twitter
    reactions JSONB DEFAULT '{}', -- Facebook reactions breakdown
    video_views INTEGER DEFAULT 0, -- Video content
    video_completion_rate DECIMAL(5,2) DEFAULT 0, -- Video completion percentage
    
    -- Calculated metrics
    engagement_rate DECIMAL(5,2) DEFAULT 0, -- (likes + comments + shares) / impressions * 100
    click_through_rate DECIMAL(5,2) DEFAULT 0, -- clicks / impressions * 100
    cost_per_engagement DECIMAL(10,2) DEFAULT 0, -- For paid content
    
    -- Temporal data
    last_updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    data_freshness INTERVAL DEFAULT INTERVAL '1 hour', -- How fresh is this data
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(post_id, social_account_id, platform),
    
    -- Indexes
    INDEX idx_post_performance_post_id ON post_performance(post_id),
    INDEX idx_post_performance_platform ON post_performance(platform),
    INDEX idx_post_performance_engagement_rate ON post_performance(engagement_rate),
    INDEX idx_post_performance_last_updated ON post_performance(last_updated)
);

-- Analytics Insights - AI-generated insights and recommendations
CREATE TABLE IF NOT EXISTS analytics_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    insight_type VARCHAR(50) NOT NULL, -- 'optimal_time', 'content_recommendation', 'audience_insight', etc.
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    data JSONB NOT NULL DEFAULT '{}', -- Supporting data for the insight
    confidence_score DECIMAL(3,2) DEFAULT 0, -- 0.0 to 1.0 confidence in the insight
    priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'dismissed', 'implemented'
    expires_at TIMESTAMPTZ, -- When this insight becomes irrelevant
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_analytics_insights_user_id ON analytics_insights(user_id),
    INDEX idx_analytics_insights_type ON analytics_insights(insight_type),
    INDEX idx_analytics_insights_priority ON analytics_insights(priority),
    INDEX idx_analytics_insights_status ON analytics_insights(status),
    INDEX idx_analytics_insights_created_at ON analytics_insights(created_at)
);

-- Audience Demographics - Track audience insights across platforms
CREATE TABLE IF NOT EXISTS audience_demographics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    social_account_id UUID NOT NULL REFERENCES social_accounts(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,
    
    -- Demographic data
    age_groups JSONB DEFAULT '{}', -- {"18-24": 25, "25-34": 40, "35-44": 20, "45+": 15}
    gender_distribution JSONB DEFAULT '{}', -- {"male": 45, "female": 52, "other": 3}
    location_data JSONB DEFAULT '{}', -- Top cities/countries
    interests JSONB DEFAULT '{}', -- Top interests/categories
    languages JSONB DEFAULT '{}', -- Language distribution
    
    -- Engagement patterns
    active_hours JSONB DEFAULT '{}', -- When audience is most active
    device_types JSONB DEFAULT '{}', -- Mobile, desktop, tablet usage
    
    -- Metadata
    total_followers INTEGER DEFAULT 0,
    data_period_start TIMESTAMPTZ NOT NULL,
    data_period_end TIMESTAMPTZ NOT NULL,
    last_updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(social_account_id, platform, data_period_start),
    
    -- Indexes
    INDEX idx_audience_demographics_user_id ON audience_demographics(user_id),
    INDEX idx_audience_demographics_account ON audience_demographics(social_account_id),
    INDEX idx_audience_demographics_platform ON audience_demographics(platform),
    INDEX idx_audience_demographics_period ON audience_demographics(data_period_start, data_period_end)
);

-- Performance Benchmarks - Industry and competitor benchmarks
CREATE TABLE IF NOT EXISTS performance_benchmarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    industry VARCHAR(100) NOT NULL,
    platform VARCHAR(20) NOT NULL,
    content_type VARCHAR(50) NOT NULL, -- 'text', 'image', 'video', 'carousel', etc.
    
    -- Benchmark metrics
    avg_engagement_rate DECIMAL(5,2) DEFAULT 0,
    avg_reach_rate DECIMAL(5,2) DEFAULT 0,
    avg_click_rate DECIMAL(5,2) DEFAULT 0,
    top_quartile_engagement DECIMAL(5,2) DEFAULT 0,
    median_engagement DECIMAL(5,2) DEFAULT 0,
    
    -- Sample data
    sample_size INTEGER DEFAULT 0,
    data_period_start TIMESTAMPTZ NOT NULL,
    data_period_end TIMESTAMPTZ NOT NULL,
    
    -- Metadata
    source VARCHAR(100), -- Data source
    last_updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(industry, platform, content_type, data_period_start),
    
    -- Indexes
    INDEX idx_performance_benchmarks_industry ON performance_benchmarks(industry),
    INDEX idx_performance_benchmarks_platform ON performance_benchmarks(platform),
    INDEX idx_performance_benchmarks_content_type ON performance_benchmarks(content_type)
);

-- Analytics Reports - Saved reports and dashboards
CREATE TABLE IF NOT EXISTS analytics_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    report_type VARCHAR(50) NOT NULL, -- 'performance', 'audience', 'competitive', 'custom'
    config JSONB NOT NULL DEFAULT '{}', -- Report configuration and filters
    schedule JSONB DEFAULT '{}', -- Automated report schedule
    last_generated TIMESTAMPTZ,
    next_generation TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_analytics_reports_user_id ON analytics_reports(user_id),
    INDEX idx_analytics_reports_type ON analytics_reports(report_type),
    INDEX idx_analytics_reports_active ON analytics_reports(is_active),
    INDEX idx_analytics_reports_next_generation ON analytics_reports(next_generation)
);

-- Optimal Posting Times - AI-calculated best posting times
CREATE TABLE IF NOT EXISTS optimal_posting_times (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    social_account_id UUID REFERENCES social_accounts(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,
    
    -- Time recommendations
    optimal_times JSONB NOT NULL DEFAULT '{}', -- {"monday": ["09:00", "13:00", "17:00"], ...}
    time_zone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    
    -- Performance data
    confidence_score DECIMAL(3,2) DEFAULT 0,
    sample_size INTEGER DEFAULT 0,
    avg_engagement_improvement DECIMAL(5,2) DEFAULT 0, -- Expected improvement percentage
    
    -- Calculation metadata
    calculation_period_start TIMESTAMPTZ NOT NULL,
    calculation_period_end TIMESTAMPTZ NOT NULL,
    algorithm_version VARCHAR(20) DEFAULT 'v1.0',
    last_calculated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, social_account_id, platform, calculation_period_start),
    
    -- Indexes
    INDEX idx_optimal_posting_times_user_id ON optimal_posting_times(user_id),
    INDEX idx_optimal_posting_times_account ON optimal_posting_times(social_account_id),
    INDEX idx_optimal_posting_times_platform ON optimal_posting_times(platform),
    INDEX idx_optimal_posting_times_calculated ON optimal_posting_times(last_calculated)
);

-- Content Performance Predictions - ML predictions for content performance
CREATE TABLE IF NOT EXISTS content_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    
    -- Prediction data
    predicted_engagement_rate DECIMAL(5,2) DEFAULT 0,
    predicted_reach INTEGER DEFAULT 0,
    predicted_clicks INTEGER DEFAULT 0,
    confidence_interval JSONB DEFAULT '{}', -- {"min": 2.5, "max": 7.8}
    
    -- Model information
    model_version VARCHAR(20) DEFAULT 'v1.0',
    features_used JSONB DEFAULT '{}', -- Features used for prediction
    prediction_accuracy DECIMAL(5,2), -- Actual vs predicted accuracy (filled after post is published)
    
    -- Metadata
    predicted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    actual_performance JSONB, -- Filled after post performance is measured
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_content_predictions_user_id ON content_predictions(user_id),
    INDEX idx_content_predictions_post_id ON content_predictions(post_id),
    INDEX idx_content_predictions_predicted_at ON content_predictions(predicted_at)
);

-- Row Level Security Policies
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE audience_demographics ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE optimal_posting_times ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_predictions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for analytics_events
CREATE POLICY "Users can view their own analytics events" ON analytics_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own analytics events" ON analytics_events
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for post_performance
CREATE POLICY "Users can view their own post performance" ON post_performance
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM posts 
            WHERE posts.id = post_performance.post_id 
            AND posts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own post performance" ON post_performance
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM posts 
            WHERE posts.id = post_performance.post_id 
            AND posts.user_id = auth.uid()
        )
    );

-- RLS Policies for analytics_insights
CREATE POLICY "Users can manage their own insights" ON analytics_insights
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for audience_demographics
CREATE POLICY "Users can manage their own audience data" ON audience_demographics
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for analytics_reports
CREATE POLICY "Users can manage their own reports" ON analytics_reports
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for optimal_posting_times
CREATE POLICY "Users can manage their own optimal times" ON optimal_posting_times
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for content_predictions
CREATE POLICY "Users can manage their own predictions" ON content_predictions
    FOR ALL USING (auth.uid() = user_id);

-- Performance benchmarks are public read-only
CREATE POLICY "Anyone can view performance benchmarks" ON performance_benchmarks
    FOR SELECT USING (true);

-- Functions for analytics calculations
CREATE OR REPLACE FUNCTION calculate_engagement_rate(
    p_likes INTEGER,
    p_comments INTEGER,
    p_shares INTEGER,
    p_impressions INTEGER
) RETURNS DECIMAL(5,2) AS $$
BEGIN
    IF p_impressions = 0 THEN
        RETURN 0;
    END IF;
    
    RETURN ROUND(
        ((p_likes + p_comments + p_shares)::DECIMAL / p_impressions::DECIMAL) * 100,
        2
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to update post performance metrics
CREATE OR REPLACE FUNCTION update_post_performance_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update engagement rate when metrics change
    NEW.engagement_rate := calculate_engagement_rate(
        NEW.likes,
        NEW.comments,
        NEW.shares,
        NEW.impressions
    );
    
    -- Update click-through rate
    IF NEW.impressions > 0 THEN
        NEW.click_through_rate := ROUND(
            (NEW.clicks::DECIMAL / NEW.impressions::DECIMAL) * 100,
            2
        );
    END IF;
    
    NEW.last_updated := NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic metric calculations
CREATE TRIGGER update_post_performance_metrics_trigger
    BEFORE INSERT OR UPDATE ON post_performance
    FOR EACH ROW
    EXECUTE FUNCTION update_post_performance_metrics();

-- Create indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_events_composite 
    ON analytics_events(user_id, platform, event_type, timestamp);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_post_performance_composite 
    ON post_performance(post_id, platform, last_updated);

-- Comments for documentation
COMMENT ON TABLE analytics_events IS 'Stores all analytics events and interactions for comprehensive tracking';
COMMENT ON TABLE post_performance IS 'Aggregated performance metrics for social media posts';
COMMENT ON TABLE analytics_insights IS 'AI-generated insights and recommendations for users';
COMMENT ON TABLE audience_demographics IS 'Audience demographic and behavioral data';
COMMENT ON TABLE performance_benchmarks IS 'Industry benchmarks for performance comparison';
COMMENT ON TABLE analytics_reports IS 'Saved analytics reports and dashboard configurations';
COMMENT ON TABLE optimal_posting_times IS 'AI-calculated optimal posting times for maximum engagement';
COMMENT ON TABLE content_predictions IS 'ML-based predictions for content performance';
