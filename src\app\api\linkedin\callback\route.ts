import { NextRequest, NextResponse } from 'next/server';
import { getOAuthConfig } from '@/lib/social/oauth-config';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * LinkedIn OAuth callback handler
 * GET /api/linkedin/callback
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    console.log('LinkedIn OAuth callback received:', {
      code: code ? 'present' : 'missing',
      state,
      error,
      errorDescription
    });

    // Handle OAuth errors
    if (error) {
      console.error('LinkedIn OAuth error:', error, errorDescription);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=${encodeURIComponent(error)}&description=${encodeURIComponent(errorDescription || '')}`
      );
    }

    // Validate required parameters
    if (!code) {
      console.error('Missing authorization code in LinkedIn callback');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=missing_code`
      );
    }

    // Get LinkedIn OAuth configuration
    const linkedinConfig = getOAuthConfig('linkedin');
    if (!linkedinConfig || !linkedinConfig.enabled) {
      console.error('LinkedIn OAuth not configured or disabled');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=oauth_not_configured`
      );
    }

    // Exchange authorization code for access token
    console.log('Exchanging authorization code for access token...');
    
    const tokenResponse = await fetch(linkedinConfig.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: linkedinConfig.redirectUri,
        client_id: linkedinConfig.clientId,
        client_secret: linkedinConfig.clientSecret,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('LinkedIn token exchange failed:', tokenResponse.status, errorText);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=token_exchange_failed`
      );
    }

    const tokenData = await tokenResponse.json();
    console.log('LinkedIn token exchange successful');

    // Get user profile information
    console.log('Fetching LinkedIn user profile...');

    // Try multiple LinkedIn API endpoints to find which one works
    let profileData = null;
    let profileError = null;

    const endpoints = [
      {
        name: 'userinfo (v2)',
        url: 'https://api.linkedin.com/v2/userinfo'
      },
      {
        name: 'people/~ (v2)',
        url: 'https://api.linkedin.com/v2/people/~'
      },
      {
        name: 'people/~ with projection',
        url: 'https://api.linkedin.com/v2/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams))'
      }
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying LinkedIn endpoint: ${endpoint.name} - ${endpoint.url}`);

        const profileResponse = await fetch(endpoint.url, {
          headers: {
            'Authorization': `Bearer ${tokenData.access_token}`,
            'Accept': 'application/json',
          },
        });

        if (profileResponse.ok) {
          const data = await profileResponse.json();
          console.log(`✅ Success with ${endpoint.name}:`, data);

          // Normalize the data structure
          if (endpoint.name === 'userinfo (v2)') {
            profileData = {
              sub: data.sub,
              name: data.name,
              email: data.email,
              given_name: data.given_name,
              family_name: data.family_name,
              picture: data.picture
            };
          } else {
            // Handle v2 people endpoint format
            profileData = {
              sub: data.id,
              name: `${data.firstName?.localized?.en_US || ''} ${data.lastName?.localized?.en_US || ''}`.trim(),
              email: data.email || '',
              given_name: data.firstName?.localized?.en_US || '',
              family_name: data.lastName?.localized?.en_US || '',
              picture: data.profilePicture?.displayImage?.elements?.[0]?.identifiers?.[0]?.identifier || ''
            };
          }
          break;
        } else {
          const errorText = await profileResponse.text();
          console.log(`❌ Failed with ${endpoint.name} (${profileResponse.status}):`, errorText);
          profileError = `${endpoint.name}: ${profileResponse.status} ${errorText}`;
        }
      } catch (error) {
        console.log(`❌ Error with ${endpoint.name}:`, error);
        profileError = `${endpoint.name}: ${error}`;
      }
    }

    if (!profileData) {
      console.error('All LinkedIn profile endpoints failed:', profileError);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=profile_fetch_failed&details=${encodeURIComponent(profileError || 'All endpoints failed')}`
      );
    }
    console.log('LinkedIn profile fetched successfully:', {
      id: profileData.sub,
      name: profileData.name,
      email: profileData.email
    });

    // Get company pages/organizations the user can manage
    console.log('Fetching LinkedIn organizations...');
    const organizationsResponse = await fetch(
      'https://api.linkedin.com/v2/organizationAcls?q=roleAssignee&role=ADMINISTRATOR&projection=(elements*(organization~(id,name,logoV2(original~:playableStreams))))',
      {
        headers: {
          'Authorization': `Bearer ${tokenData.access_token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    let organizations = [];
    if (organizationsResponse.ok) {
      const orgData = await organizationsResponse.json();
      organizations = orgData.elements?.map((element: any) => ({
        id: element.organization?.id,
        name: element.organization?.name,
        logo: element.organization?.logoV2?.original
      })) || [];
      console.log(`Found ${organizations.length} LinkedIn organizations`);
    } else {
      console.log('No LinkedIn organizations found or insufficient permissions');
    }

    // Store the LinkedIn account in database
    // Use service role client to bypass RLS policies
    const supabase = createServiceRoleClient();

    // Use an existing user ID for testing
    // In a full implementation, you'd get the actual user ID from session
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037'; // Demo User

    // First check if the user exists
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', demoUserId)
      .single();

    if (userError) {
      console.error('Failed to find user:', userError);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=user_not_found`
      );
    }

    const { data: socialAccount, error: dbError } = await supabase
      .from('social_accounts')
      .upsert({
        user_id: demoUserId,
        platform: 'LINKEDIN',
        account_id: profileData.sub,
        account_name: profileData.name || profileData.email,
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token || null,
        expires_at: tokenData.expires_in
          ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
          : null,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id,platform,account_id'
      });

    if (dbError) {
      console.error('Failed to store LinkedIn account:', dbError);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=database_error`
      );
    }

    console.log('LinkedIn account stored successfully');

    // Check if this is a popup request
    const isPopup = searchParams.get('popup') === 'true';
    if (isPopup) {
      return new NextResponse(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>LinkedIn Connected</title>
        </head>
        <body>
          <script>
            // Notify parent window and close popup
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth_success',
                platform: 'linkedin',
                account: '${(profileData.name || profileData.email).replace(/'/g, "\\'")}',
                message: 'LinkedIn account connected successfully'
              }, '${process.env.NEXT_PUBLIC_APP_URL}');
            }
            window.close();
          </script>
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h2>✅ LinkedIn Connected Successfully!</h2>
            <p>Account: ${profileData.name || profileData.email}</p>
            <p>This window will close automatically...</p>
          </div>
        </body>
        </html>
      `, {
        headers: { 'Content-Type': 'text/html' }
      });
    }

    // Regular redirect for non-popup flows
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/auth/success?platform=linkedin&account=${encodeURIComponent(profileData.name || profileData.email)}`
    );

  } catch (error) {
    console.error('LinkedIn OAuth callback error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=callback_error&details=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    );
  }
}

/**
 * Handle POST requests (not typically used for OAuth callbacks)
 */
export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed for OAuth callback' },
    { status: 405 }
  );
}
