"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function BypassPage() {
  const router = useRouter();

  useEffect(() => {
    // Log that we're attempting to bypass
    console.log("Bypass page loaded, redirecting to dashboard...");
    
    // Redirect to dashboard after a short delay
    const timer = setTimeout(() => {
      router.push("/dashboard");
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <h1 className="text-2xl font-bold mb-4">جاري التوجيه...</h1>
        <p className="mb-4">سيتم توجيهك إلى لوحة التحكم خلال لحظات.</p>
        <div className="animate-pulse bg-blue-100 h-2 w-full rounded-full">
          <div className="bg-blue-500 h-2 w-1/3 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
