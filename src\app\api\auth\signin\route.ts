import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { z } from "zod";

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// Simple validation schema
const signinSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

async function signinHandler(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('Signin API called');
    
    // Get request body
    const body = await request.json();
    console.log('Request body received:', { ...body, password: '[HIDDEN]' });

    // Validate request body with Zod schema
    const validation = signinSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        { 
          error: 'Invalid input', 
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    const { email, password } = validation.data;
    console.log('Validation passed for email:', email);

    // Create Supabase client
    
    console.log('Supabase client created');

    // Use Supabase Auth to sign in user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError) {
      console.error('Supabase auth error:', authError);
      return NextResponse.json(
        { error: authError.message || 'Invalid credentials' },
        { status: 401 }
      );
    }

    if (!authData.user) {
      console.error('No user data returned from Supabase');
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    console.log('User signed in successfully:', authData.user.id);

    return NextResponse.json(
      {
        message: "تم تسجيل الدخول بنجاح",
        user: {
          id: authData.user.id,
          name: authData.user.user_metadata?.name || authData.user.email,
          email: authData.user.email,
          role: authData.user.user_metadata?.role || 'USER',
        },
        session: authData.session,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Signin error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export the handler
export const POST = signinHandler;
