/**
 * Component Tests for Advanced Content Creation System
 * Tests React components, user interactions, and UI behavior
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import { AdvancedEditor } from '@/components/content/advanced-editor';
import { TemplatesBrowser } from '@/components/content/templates-browser';

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock toast notifications
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
}));

describe('Advanced Content Creation Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  describe('AdvancedEditor Component', () => {
    const mockOnContentChange = jest.fn();
    const mockOnSave = jest.fn();

    const defaultProps = {
      initialContent: '',
      platform: 'facebook',
      onContentChange: mockOnContentChange,
      onSave: mockOnSave,
    };

    beforeEach(() => {
      mockOnContentChange.mockClear();
      mockOnSave.mockClear();
    });

    test('should render advanced editor with initial content', () => {
      render(
        <AdvancedEditor 
          {...defaultProps} 
          initialContent="Initial test content" 
        />
      );

      expect(screen.getByDisplayValue('Initial test content')).toBeInTheDocument();
      expect(screen.getByText('محرر المحتوى المتقدم')).toBeInTheDocument();
      expect(screen.getByText('إنشاء وتحرير المحتوى بمساعدة الذكاء الاصطناعي')).toBeInTheDocument();
    });

    test('should update content stats when typing', async () => {
      const user = userEvent.setup();
      
      render(<AdvancedEditor {...defaultProps} />);

      const textarea = screen.getByPlaceholderText(/اكتب محتواك هنا/);
      
      await user.type(textarea, 'Test content with #hashtag and emoji 📱');

      await waitFor(() => {
        expect(screen.getByText(/الأحرف: 39/)).toBeInTheDocument();
        expect(screen.getByText(/الكلمات: 6/)).toBeInTheDocument();
        expect(screen.getByText(/الهاشتاقات: 1/)).toBeInTheDocument();
        expect(screen.getByText(/الإيموجي: 1/)).toBeInTheDocument();
      });

      expect(mockOnContentChange).toHaveBeenCalledWith('Test content with #hashtag and emoji 📱');
    });

    test('should show character limit warning', async () => {
      const user = userEvent.setup();
      
      render(<AdvancedEditor {...defaultProps} platform="twitter" />);

      const textarea = screen.getByPlaceholderText(/اكتب محتواك هنا/);
      const longContent = 'A'.repeat(300); // Exceeds Twitter limit
      
      await user.type(textarea, longContent);

      await waitFor(() => {
        expect(screen.getByText(/المحتوى يتجاوز الحد المسموح/)).toBeInTheDocument();
      });
    });

    test('should generate content with AI', async () => {
      const user = userEvent.setup();

      // Mock AI generation API
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          result: {
            content: 'AI generated content',
            hashtags: ['ai', 'generated'],
            emojis: ['🤖'],
            suggestions: ['Alternative content']
          }
        })
      });

      render(<AdvancedEditor {...defaultProps} />);

      // Open AI generation dialog
      const generateButton = screen.getByText('إنشاء محتوى');
      await user.click(generateButton);

      // Enter prompt
      const promptTextarea = screen.getByPlaceholderText(/مثال: منشور عن فوائد/);
      await user.type(promptTextarea, 'Generate content about technology');

      // Click generate
      const aiGenerateButton = screen.getByText('إنشاء المحتوى');
      await user.click(aiGenerateButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/content/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('Generate content about technology')
        });
      });
    });

    test('should generate hashtags', async () => {
      const user = userEvent.setup();

      // Mock hashtag generation API
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          hashtags: [
            { tag: 'technology', popularity: 'high', relevance: 0.9 },
            { tag: 'innovation', popularity: 'medium', relevance: 0.8 }
          ]
        })
      });

      render(<AdvancedEditor {...defaultProps} initialContent="Content about technology" />);

      const hashtagButton = screen.getByText('هاشتاقات');
      await user.click(hashtagButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/content/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('hashtags')
        });
      });
    });

    test('should optimize content for different platforms', async () => {
      const user = userEvent.setup();

      // Mock content optimization API
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          optimizedContent: 'Optimized content for Twitter'
        })
      });

      render(<AdvancedEditor {...defaultProps} initialContent="Long content for Facebook" />);

      const twitterButton = screen.getByText('تويتر');
      await user.click(twitterButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/content/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('optimize')
        });
      });
    });

    test('should copy content to clipboard', async () => {
      const user = userEvent.setup();

      // Mock clipboard API
      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockResolvedValue(undefined),
        },
      });

      render(<AdvancedEditor {...defaultProps} initialContent="Content to copy" />);

      const copyButton = screen.getByText('نسخ');
      await user.click(copyButton);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Content to copy');
    });

    test('should save content with metadata', async () => {
      const user = userEvent.setup();

      render(<AdvancedEditor {...defaultProps} initialContent="Content to save" />);

      const saveButton = screen.getByText('حفظ المحتوى');
      await user.click(saveButton);

      expect(mockOnSave).toHaveBeenCalledWith(
        'Content to save',
        expect.objectContaining({
          hashtags: expect.any(Array),
          stats: expect.objectContaining({
            characterCount: expect.any(Number),
            wordCount: expect.any(Number)
          })
        })
      );
    });
  });

  describe('TemplatesBrowser Component', () => {
    const mockOnSelectTemplate = jest.fn();
    const mockOnUseTemplate = jest.fn();

    const defaultProps = {
      onSelectTemplate: mockOnSelectTemplate,
      onUseTemplate: mockOnUseTemplate,
    };

    beforeEach(() => {
      mockOnSelectTemplate.mockClear();
      mockOnUseTemplate.mockClear();

      // Mock templates API
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url.includes('action=search')) {
          return Promise.resolve({
            ok: true,
            json: async () => ({
              success: true,
              templates: [
                {
                  id: 'test-template-1',
                  name: 'Test Template 1',
                  description: 'Test description 1',
                  category: 'promotional',
                  platform: ['facebook', 'instagram'],
                  language: 'ar',
                  content: 'Test content {{variable1}}',
                  variables: [
                    {
                      name: 'variable1',
                      type: 'text',
                      label: 'Variable 1',
                      placeholder: 'Enter value',
                      required: true
                    }
                  ],
                  hashtags: ['test', 'template'],
                  tone: 'friendly',
                  industry: ['technology'],
                  isPublic: true,
                  usageCount: 10,
                  rating: 4.5,
                  tags: ['test'],
                  createdAt: new Date()
                }
              ],
              total: 1,
              hasMore: false
            })
          });
        }

        if (url.includes('action=categories')) {
          return Promise.resolve({
            ok: true,
            json: async () => ({
              success: true,
              categories: [
                { id: 'promotional', name: 'ترويجي', description: 'قوالب للترويج' },
                { id: 'educational', name: 'تعليمي', description: 'قوالب تعليمية' }
              ]
            })
          });
        }

        return Promise.reject(new Error('Unknown API call'));
      });
    });

    test('should render templates browser with templates', async () => {
      render(<TemplatesBrowser {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('مكتبة القوالب')).toBeInTheDocument();
        expect(screen.getByText('Test Template 1')).toBeInTheDocument();
        expect(screen.getByText('Test description 1')).toBeInTheDocument();
      });
    });

    test('should filter templates by category', async () => {
      const user = userEvent.setup();

      render(<TemplatesBrowser {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Test Template 1')).toBeInTheDocument();
      });

      // Select promotional category
      const categorySelect = screen.getByDisplayValue('جميع الفئات');
      await user.click(categorySelect);
      
      const promotionalOption = screen.getByText('ترويجي');
      await user.click(promotionalOption);

      // Should trigger new API call with category filter
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('category=promotional')
        );
      });
    });

    test('should search templates by query', async () => {
      const user = userEvent.setup();

      render(<TemplatesBrowser {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Test Template 1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('ابحث في القوالب...');
      await user.type(searchInput, 'promotional');

      // Should filter templates locally
      await waitFor(() => {
        // The search should work on the loaded templates
        expect(screen.getByText('Test Template 1')).toBeInTheDocument();
      });
    });

    test('should use template without variables', async () => {
      const user = userEvent.setup();

      // Mock template processing API
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url.includes('/api/content/templates') && url.includes('POST')) {
          return Promise.resolve({
            ok: true,
            json: async () => ({
              success: true,
              content: 'Processed template content'
            })
          });
        }
        return (global.fetch as jest.Mock).mockImplementation();
      });

      render(<TemplatesBrowser {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Test Template 1')).toBeInTheDocument();
      });

      const useButton = screen.getByText('استخدام');
      await user.click(useButton);

      // Should open variables dialog for template with variables
      await waitFor(() => {
        expect(screen.getByText('Variable 1')).toBeInTheDocument();
      });
    });

    test('should copy template content', async () => {
      const user = userEvent.setup();

      // Mock clipboard API
      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockResolvedValue(undefined),
        },
      });

      render(<TemplatesBrowser {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Test Template 1')).toBeInTheDocument();
      });

      const copyButton = screen.getByRole('button', { name: '' }); // Copy button with icon
      await user.click(copyButton);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Test content {{variable1}}');
    });

    test('should show empty state when no templates found', async () => {
      // Mock empty response
      (global.fetch as jest.Mock).mockImplementation((url) => {
        if (url.includes('action=search')) {
          return Promise.resolve({
            ok: true,
            json: async () => ({
              success: true,
              templates: [],
              total: 0,
              hasMore: false
            })
          });
        }
        return Promise.resolve({
          ok: true,
          json: async () => ({ success: true, categories: [] })
        });
      });

      render(<TemplatesBrowser {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('لا توجد قوالب')).toBeInTheDocument();
        expect(screen.getByText('لم يتم العثور على قوالب تطابق معايير البحث')).toBeInTheDocument();
      });
    });

    test('should handle API errors gracefully', async () => {
      // Mock API error
      (global.fetch as jest.Mock).mockRejectedValue(new Error('API Error'));

      render(<TemplatesBrowser {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('جاري تحميل القوالب...')).toBeInTheDocument();
      });

      // Should show loading state and handle error gracefully
      // The component should not crash
    });
  });

  describe('Integration Tests', () => {
    test('should integrate advanced editor with templates browser', async () => {
      const user = userEvent.setup();
      let selectedTemplate: any = null;

      const handleSelectTemplate = (template: any) => {
        selectedTemplate = template;
      };

      render(
        <div>
          <AdvancedEditor 
            initialContent=""
            platform="facebook"
            onContentChange={() => {}}
            onSave={() => {}}
          />
          <TemplatesBrowser 
            onSelectTemplate={handleSelectTemplate}
            onUseTemplate={() => {}}
          />
        </div>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Template 1')).toBeInTheDocument();
      });

      // Click preview button
      const previewButton = screen.getByRole('button', { name: '' }); // Eye icon button
      await user.click(previewButton);

      expect(selectedTemplate).toBeDefined();
      expect(selectedTemplate.id).toBe('test-template-1');
    });
  });
});
