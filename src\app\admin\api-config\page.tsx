'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

interface APIValidationResult {
  platform: string;
  isConfigured: boolean;
  isValid: boolean;
  error?: string;
  details?: any;
}

interface APIReport {
  summary: {
    totalPlatforms: number;
    configured: number;
    valid: number;
    issues: number;
  };
  results: APIValidationResult[];
  recommendations: string[];
}

export default function APIConfigPage() {
  const [report, setReport] = useState<APIReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [testing, setTesting] = useState<string | null>(null);

  const loadAPIReport = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/test-apis');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setReport(data.report);
      
      if (data.report.summary.issues > 0) {
        toast.warning(`Found ${data.report.summary.issues} API configuration issues`);
      } else {
        toast.success('All APIs are properly configured!');
      }
    } catch (error) {
      console.error('Failed to load API report:', error);
      toast.error('Failed to load API configuration report');
    } finally {
      setLoading(false);
    }
  };

  const testSpecificAPI = async (platform: string) => {
    try {
      setTesting(platform);
      const response = await fetch('/api/admin/test-apis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ platform }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Update the specific result in the report
      if (report) {
        const updatedResults = report.results.map(result => 
          result.platform === platform ? data.result : result
        );
        setReport({
          ...report,
          results: updatedResults,
        });
      }
      
      if (data.result.isValid) {
        toast.success(`${platform} API test successful!`);
      } else {
        toast.error(`${platform} API test failed: ${data.result.error}`);
      }
    } catch (error) {
      console.error(`Failed to test ${platform} API:`, error);
      toast.error(`Failed to test ${platform} API`);
    } finally {
      setTesting(null);
    }
  };

  useEffect(() => {
    loadAPIReport();
  }, []);

  const getStatusIcon = (result: APIValidationResult) => {
    if (!result.isConfigured) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
    if (result.isValid) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
  };

  const getStatusBadge = (result: APIValidationResult) => {
    if (!result.isConfigured) {
      return <Badge variant="destructive">Not Configured</Badge>;
    }
    if (result.isValid) {
      return <Badge variant="default" className="bg-green-500">Working</Badge>;
    }
    return <Badge variant="secondary">Configuration Error</Badge>;
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading API configuration...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">إعدادات API المنصات الاجتماعية</h1>
          <p className="text-gray-600 mt-2">
            اختبار وإدارة اتصالات API للمنصات الاجتماعية
          </p>
        </div>
        <Button onClick={loadAPIReport} disabled={loading}>
          <RefreshCw className="h-4 w-4 mr-2" />
          تحديث
        </Button>
      </div>

      {/* Summary Cards */}
      {report && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المنصات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{report.summary.totalPlatforms}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">مُعدة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{report.summary.configured}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">تعمل بشكل صحيح</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{report.summary.valid}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">مشاكل</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{report.summary.issues}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recommendations */}
      {report && report.recommendations.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>توصيات</AlertTitle>
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1 mt-2">
              {report.recommendations.map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* API Status Cards */}
      {report && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {report.results.map((result) => (
            <Card key={result.platform}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {getStatusIcon(result)}
                    {result.platform}
                  </CardTitle>
                  {getStatusBadge(result)}
                </div>
                <CardDescription>
                  API configuration and connection status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Configuration Details */}
                <div className="space-y-2">
                  <h4 className="font-medium">Configuration Status:</h4>
                  {result.details && (
                    <div className="text-sm space-y-1">
                      {Object.entries(result.details).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <span className="text-gray-600">{key}:</span>
                          <span className={typeof value === 'boolean' ? 
                            (value ? 'text-green-600' : 'text-red-600') : 
                            'text-gray-900'
                          }>
                            {typeof value === 'boolean' ? (value ? '✓' : '✗') : String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Error Message */}
                {result.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-800">{result.error}</p>
                  </div>
                )}

                {/* Test Button */}
                <Button
                  onClick={() => testSpecificAPI(result.platform)}
                  disabled={testing === result.platform}
                  variant="outline"
                  className="w-full"
                >
                  {testing === result.platform ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    'Test API Connection'
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Configuration Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>تعليمات الإعداد</CardTitle>
          <CardDescription>
            كيفية إعداد مفاتيح API للمنصات الاجتماعية
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <h4 className="font-medium">Twitter/X API:</h4>
              <p className="text-sm text-gray-600">
                1. Go to https://developer.twitter.com/en/portal/dashboard<br/>
                2. Create a new app or use existing app<br/>
                3. Generate API Key, API Secret, and Bearer Token<br/>
                4. Set environment variables: TWITTER_API_SECRET, TWITTER_BEARER_TOKEN
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">Facebook API:</h4>
              <p className="text-sm text-gray-600">
                1. Go to https://developers.facebook.com/apps/<br/>
                2. Use app ID: 1366325774493759 or create new app<br/>
                3. Get App Secret from app settings<br/>
                4. Set environment variable: FACEBOOK_APP_SECRET
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">LinkedIn API:</h4>
              <p className="text-sm text-gray-600">
                1. Go to https://developer.linkedin.com/apps<br/>
                2. Use app ID: 787coegnsdocvq or create new app<br/>
                3. Get Client Secret from app credentials<br/>
                4. Set environment variable: LINKEDIN_CLIENT_SECRET
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
