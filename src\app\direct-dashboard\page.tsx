"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";

export default function DirectDashboardPage() {
  const router = useRouter();

  useEffect(() => {
    // Automatically redirect to dashboard after 2 seconds
    const timer = setTimeout(() => {
      router.push("/dashboard");
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  const goToDashboard = () => {
    router.push("/dashboard");
  };

  const goToAllPages = () => {
    // Open multiple dashboard pages for comprehensive testing
    window.open("/dashboard", "_blank");
    window.open("/posts", "_blank");
    window.open("/social", "_blank");
    window.open("/schedule", "_blank");
    window.open("/settings", "_blank");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <span className="text-3xl font-bold text-white">🔓</span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-3">
            Direct Dashboard Access
          </h1>
          <p className="text-gray-600 text-lg">وصول مباشر للوحة التحكم - للاختبار فقط</p>
          <p className="text-sm text-gray-500 mt-2">Authentication Bypass Enabled</p>
        </div>

        {/* Access Panel */}
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-10 border border-white/50">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">🚨 Testing Mode Active</h2>
            <p className="text-gray-600 text-lg">Authentication middleware has been temporarily disabled</p>
          </div>

          <div className="space-y-6">
            {/* Auto Redirect Notice */}
            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-6 border border-emerald-100">
              <h4 className="font-bold text-emerald-900 mb-4 text-lg">⏱️ Auto Redirect</h4>
              <p className="text-emerald-800">You will be automatically redirected to the dashboard in 2 seconds...</p>
            </div>

            {/* Manual Access Buttons */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={goToDashboard}
                size="lg"
                className="h-14 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
              >
                🎯 Go to Dashboard
              </Button>

              <Button
                onClick={goToAllPages}
                size="lg"
                variant="outline"
                className="h-14 border-2 border-emerald-200 hover:border-emerald-300 text-emerald-700 font-semibold rounded-xl hover:bg-emerald-50 transition-all duration-200"
              >
                🔍 Test All Pages
              </Button>
            </div>

            {/* Testing Information */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100">
              <h4 className="font-bold text-blue-900 mb-4 text-lg">📋 Testing Information</h4>
              <div className="space-y-2 text-sm text-blue-800">
                <p>• <strong>Authentication:</strong> Temporarily disabled</p>
                <p>• <strong>Dashboard Access:</strong> Direct access enabled</p>
                <p>• <strong>All Routes:</strong> Accessible without login</p>
                <p>• <strong>Purpose:</strong> Core functionality testing</p>
              </div>
            </div>

            {/* Available Routes */}
            <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-6 border border-gray-100">
              <h4 className="font-bold text-gray-900 mb-4 text-lg">🌐 Available Test Routes</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <p>• <a href="/dashboard" className="text-blue-600 hover:underline">/dashboard</a> - Main Dashboard</p>
                  <p>• <a href="/posts" className="text-blue-600 hover:underline">/posts</a> - Posts Management</p>
                  <p>• <a href="/social" className="text-blue-600 hover:underline">/social</a> - Social Accounts</p>
                </div>
                <div className="space-y-1">
                  <p>• <a href="/schedule" className="text-blue-600 hover:underline">/schedule</a> - Content Scheduler</p>
                  <p>• <a href="/settings" className="text-blue-600 hover:underline">/settings</a> - Settings</p>
                  <p>• <a href="/bypass" className="text-blue-600 hover:underline">/bypass</a> - Original Bypass</p>
                </div>
              </div>
            </div>

            {/* Warning */}
            <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-2xl p-6 border border-red-100">
              <h4 className="font-bold text-red-900 mb-2 text-lg">⚠️ Important Notice</h4>
              <p className="text-red-800 text-sm">
                This is a temporary testing configuration. Authentication will be re-enabled once deployment issues are resolved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
