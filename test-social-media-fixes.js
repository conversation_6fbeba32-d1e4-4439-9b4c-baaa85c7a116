/**
 * Test Script for Social Media Integration Fixes
 * Tests the comprehensive authentication and publishing fixes
 */

const API_BASE = 'https://app.ewasl.com/api';

async function testSocialMediaFixes() {
  console.log('🎯 Testing Comprehensive Social Media Integration Fixes...\n');

  // Test 1: Health Check
  console.log('1️⃣ Testing Health API...');
  try {
    const healthResponse = await fetch(`${API_BASE}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health API:', healthData.status);
  } catch (error) {
    console.log('❌ Health API Error:', error.message);
  }

  // Test 2: Test Publish with Real Accounts
  console.log('\n2️⃣ Testing Fixed Publishing (Facebook)...');
  try {
    const publishResponse = await fetch(`${API_BASE}/posts/test-publish`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform: 'FACEBOOK',
        content: '[TEST] Comprehensive social media integration fixes deployed! 🚀',
        isTest: true
      })
    });
    
    const publishData = await publishResponse.json();
    console.log('📤 Facebook Publishing Result:');
    console.log('  Success:', publishData.success);
    console.log('  Platform:', publishData.platform);
    console.log('  Message:', publishData.message);
    console.log('  Account:', publishData.account?.name || 'N/A');
    console.log('  Error:', publishData.error || 'None');
  } catch (error) {
    console.log('❌ Publishing Error:', error.message);
  }

  // Test 3: Test Publish with LinkedIn
  console.log('\n3️⃣ Testing Fixed Publishing (LinkedIn)...');
  try {
    const linkedinResponse = await fetch(`${API_BASE}/posts/test-publish`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform: 'LINKEDIN',
        content: '[TEST] Business account integration working perfectly! 💼',
        isTest: true
      })
    });
    
    const linkedinData = await linkedinResponse.json();
    console.log('💼 LinkedIn Publishing Result:');
    console.log('  Success:', linkedinData.success);
    console.log('  Platform:', linkedinData.platform);
    console.log('  Message:', linkedinData.message);
    console.log('  Account:', linkedinData.account?.name || 'N/A');
    console.log('  Error:', linkedinData.error || 'None');
  } catch (error) {
    console.log('❌ LinkedIn Error:', error.message);
  }

  // Test 4: Account Selection API
  console.log('\n4️⃣ Testing Account Selection API...');
  try {
    const accountsResponse = await fetch(`${API_BASE}/social/account-selection?platform=FACEBOOK`);
    const accountsData = await accountsResponse.json();
    console.log('🔗 Account Selection Result:');
    console.log('  Success:', accountsData.success);
    console.log('  Platform:', accountsData.platform);
    console.log('  Total Accounts:', accountsData.totalAccounts);
    console.log('  Account Options:', accountsData.accountOptions?.length || 0);
  } catch (error) {
    console.log('❌ Account Selection Error:', error.message);
  }

  console.log('\n🎉 Social Media Integration Testing Complete!');
}

// Run the tests
testSocialMediaFixes();
