import { SocialAccount, PostContent } from '../social-media-publisher';
import { SchedulerLogger } from '../scheduler-logger';
import FacebookPublisherV2 from '@/lib/social/publishers/facebook-publisher-v2';
import ContentFormatter from '@/lib/social/content-formatter';

export interface FacebookPublishResult {
  postId: string;
  url: string;
}

/**
 * Enhanced Facebook publisher using Facebook Graph API V2
 * Integrates with the new FacebookPublisherV2 service
 */
export class FacebookPublisher {
  private logger: SchedulerLogger;
  private publisherV2: FacebookPublisherV2;
  private contentFormatter: ContentFormatter;
  private readonly API_BASE = 'https://graph.facebook.com/v19.0';

  constructor() {
    this.logger = new SchedulerLogger('facebook-publisher');
    this.publisherV2 = new FacebookPublisherV2();
    this.contentFormatter = new ContentFormatter();
  }

  /**
   * Publish a post to Facebook using enhanced V2 publisher
   */
  async publish(account: SocialAccount, content: PostContent): Promise<FacebookPublishResult> {
    try {
      this.logger.info('Publishing to Facebook with V2 publisher', {
        accountId: account.account_id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
      });

      // Format content for Facebook
      const formattedContent = this.contentFormatter.formatForPlatform(content.content, 'facebook');

      if (!formattedContent.isValid) {
        throw new Error(`Content validation failed: ${formattedContent.warnings.join(', ')}`);
      }

      if (formattedContent.warnings.length > 0) {
        this.logger.warn('Content formatting warnings', { warnings: formattedContent.warnings });
      }

      // Prepare content for V2 publisher
      const publishContent = {
        content: formattedContent.text,
        mediaUrl: content.mediaUrl,
        mediaType: content.mediaType || (content.mediaUrl ? this.detectMediaType(content.mediaUrl) : undefined) as "IMAGE" | "VIDEO" | undefined
      };

      // Use V2 publisher for real API calls
      const result = await this.publisherV2.publishPost(account, publishContent);

      if (!result.success) {
        throw new Error(result.error || 'Facebook publishing failed');
      }

      this.logger.info('Facebook post published successfully with V2', {
        postId: result.postId,
        postUrl: result.url,
      });

      return {
        postId: result.postId!,
        url: result.url!,
      };

    } catch (error) {
      this.logger.error('Failed to publish Facebook post with V2', error);
      throw error;
    }
  }

  /**
   * Publish video to Facebook
   */
  private async publishVideo(account: SocialAccount, content: PostContent): Promise<FacebookPublishResult> {
    try {
      this.logger.info('Publishing video to Facebook', {
        mediaUrl: content.mediaUrl,
      });

      // Download video
      const videoResponse = await fetch(content.mediaUrl!);
      if (!videoResponse.ok) {
        throw new Error(`Failed to download video: ${videoResponse.statusText}`);
      }

      const videoBuffer = await videoResponse.arrayBuffer();

      // Upload video
      const formData = new FormData();
      formData.append('source', new Blob([videoBuffer]));
      formData.append('description', content.content);
      formData.append('access_token', account.access_token);

      const response = await fetch(`${this.API_BASE}/${account.account_id}/videos`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Facebook video upload failed: ${errorData.error?.message || response.statusText}`);
      }

      const result = await response.json();
      const postUrl = `https://www.facebook.com/${result.id}`;

      this.logger.info('Facebook video published successfully', {
        postId: result.id,
        postUrl,
      });

      return {
        postId: result.id,
        url: postUrl,
      };

    } catch (error) {
      this.logger.error('Failed to publish Facebook video', error);
      throw error;
    }
  }

  /**
   * Check if URL is a video
   */
  private isVideoUrl(url: string): boolean {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm'];
    return videoExtensions.some(ext => url.toLowerCase().includes(ext));
  }

  /**
   * Make authenticated request to Facebook API
   */
  private async makeFacebookRequest(
    account: SocialAccount,
    method: string,
    endpoint: string,
    data?: any
  ): Promise<any> {
    const url = `${this.API_BASE}${endpoint}`;

    const options: RequestInit = {
      method,
    };

    if (method === 'GET') {
      const params = new URLSearchParams(data || {});
      const fullUrl = `${url}?${params.toString()}`;
      const response = await fetch(fullUrl, options);
      return await this.handleResponse(response);
    } else {
      if (data) {
        const formData = new FormData();
        Object.keys(data).forEach(key => {
          formData.append(key, data[key]);
        });
        options.body = formData;
      }

      const response = await fetch(url, options);
      return await this.handleResponse(response);
    }
  }

  /**
   * Handle Facebook API response
   */
  private async handleResponse(response: Response): Promise<any> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Handle specific Facebook errors
      if (response.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }

      if (response.status === 401) {
        throw new Error('Facebook authentication failed. Token may be expired or invalid.');
      }

      if (response.status === 403) {
        throw new Error('Facebook API access forbidden. Check page permissions.');
      }

      const errorMessage = errorData.error?.message || `Facebook API error: ${response.status}`;
      throw new Error(errorMessage);
    }

    return await response.json();
  }

  /**
   * Test connection to Facebook account using V2 publisher
   */
  async testConnection(account: SocialAccount): Promise<any> {
    try {
      this.logger.info('Testing Facebook connection with V2', {
        accountId: account.account_id,
      });

      // Use V2 publisher for connection testing
      const result = await this.publisherV2.testConnection(account);

      this.logger.info('Facebook connection test successful with V2', {
        accountInfo: result.accountInfo || result.pageInfo
      });

      return result;

    } catch (error) {
      this.logger.error('Facebook connection test failed with V2', error);
      throw error;
    }
  }

  /**
   * Detect media type from URL
   */
  private detectMediaType(url: string): 'IMAGE' | 'VIDEO' {
    const urlLower = url.toLowerCase();

    if (urlLower.includes('.mp4') || urlLower.includes('.mov') || urlLower.includes('.avi') ||
        urlLower.includes('.wmv') || urlLower.includes('.flv') || urlLower.includes('.webm')) {
      return 'VIDEO';
    } else {
      return 'IMAGE';
    }
  }

  /**
   * Get account information
   */
  async getAccountInfo(account: SocialAccount): Promise<any> {
    try {
      const response = await this.makeFacebookRequest(
        account,
        'GET',
        `/${account.account_id}`,
        {
          fields: 'id,name,category,followers_count,fan_count,about,website',
          access_token: account.access_token,
        }
      );

      return {
        id: response.id,
        name: response.name,
        category: response.category,
        followers_count: response.followers_count || response.fan_count,
        about: response.about,
        website: response.website,
      };

    } catch (error) {
      this.logger.error('Failed to get Facebook account info', error);
      throw error;
    }
  }

  /**
   * Delete a Facebook post
   */
  async deletePost(account: SocialAccount, postId: string): Promise<boolean> {
    try {
      this.logger.info('Deleting Facebook post', { postId });

      await this.makeFacebookRequest(
        account,
        'DELETE',
        `/${postId}`,
        {
          access_token: account.access_token,
        }
      );

      this.logger.info('Facebook post deleted successfully', { postId });
      return true;

    } catch (error) {
      this.logger.error('Failed to delete Facebook post', error, { postId });
      throw error;
    }
  }

  /**
   * Get post analytics
   */
  async getPostAnalytics(account: SocialAccount, postId: string): Promise<any> {
    try {
      const response = await this.makeFacebookRequest(
        account,
        'GET',
        `/${postId}`,
        {
          fields: 'id,message,created_time,likes.summary(true),comments.summary(true),shares',
          access_token: account.access_token,
        }
      );

      return {
        id: response.id,
        message: response.message,
        created_time: response.created_time,
        metrics: {
          likes: response.likes?.summary?.total_count || 0,
          comments: response.comments?.summary?.total_count || 0,
          shares: response.shares?.count || 0,
        },
      };

    } catch (error) {
      this.logger.error('Failed to get Facebook post analytics', error);
      throw error;
    }
  }

  /**
   * Get page insights
   */
  async getPageInsights(account: SocialAccount, since?: Date, until?: Date): Promise<any> {
    try {
      const params: any = {
        metric: 'page_impressions,page_reach,page_engaged_users',
        access_token: account.access_token,
      };

      if (since) {
        params.since = Math.floor(since.getTime() / 1000);
      }

      if (until) {
        params.until = Math.floor(until.getTime() / 1000);
      }

      const response = await this.makeFacebookRequest(
        account,
        'GET',
        `/${account.account_id}/insights`,
        params
      );

      return response.data;

    } catch (error) {
      this.logger.error('Failed to get Facebook page insights', error);
      throw error;
    }
  }

  /**
   * Validate Facebook credentials
   */
  async validateCredentials(accessToken: string, pageId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/${pageId}?access_token=${accessToken}`);
      return response.ok;

    } catch (error) {
      this.logger.error('Failed to validate Facebook credentials', error);
      return false;
    }
  }

  /**
   * Get user's pages
   */
  async getUserPages(accessToken: string): Promise<any[]> {
    try {
      const response = await fetch(
        `${this.API_BASE}/me/accounts?access_token=${accessToken}&fields=id,name,category,access_token`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch user pages');
      }

      const data = await response.json();
      return data.data || [];

    } catch (error) {
      this.logger.error('Failed to get user pages', error);
      throw error;
    }
  }
}
