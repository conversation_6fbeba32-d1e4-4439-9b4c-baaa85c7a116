/**
 * Analytics Data Collector
 * Collects real-time analytics data from social media platforms
 */

import { createClient, createServiceRoleClient } from '@/lib/supabase/server';
import LinkedInPublisherV2 from '@/lib/social/publishers/linkedin-publisher-v2';
import TwitterPublisherV2 from '@/lib/social/publishers/twitter-publisher-v2';
import FacebookPublisherV2 from '@/lib/social/publishers/facebook-publisher-v2';
import { InstagramGraphService } from '@/lib/social/services/instagram-graph-service';

export interface AnalyticsEvent {
  user_id: string;
  post_id?: string;
  social_account_id?: string;
  event_type: string;
  platform: string;
  event_data: Record<string, any>;
  metrics: Record<string, number>;
  timestamp?: string;
}

export interface PostMetrics {
  impressions: number;
  reach: number;
  likes: number;
  comments: number;
  shares: number;
  clicks: number;
  saves?: number;
  retweets?: number;
  quote_tweets?: number;
  replies?: number;
  reactions?: Record<string, number>;
  video_views?: number;
  video_completion_rate?: number;
}

export interface PlatformMetricsCollector {
  platform: string;
  collectPostMetrics(postId: string, platformPostId: string): Promise<PostMetrics>;
  collectAccountMetrics(accountId: string): Promise<any>;
  collectAudienceInsights(accountId: string): Promise<any>;
}

export class AnalyticsDataCollector {
  private supabase;
  private serviceSupabase;
  private collectors: Map<string, PlatformMetricsCollector> = new Map();
  private linkedinPublisher: LinkedInPublisherV2;
  private twitterPublisher: TwitterPublisherV2;
  private facebookPublisher: FacebookPublisherV2;
  private instagramService: InstagramGraphService;

  constructor() {
    this.supabase = createServiceRoleClient(); // Use service role for analytics operations
    this.serviceSupabase = createServiceRoleClient();
    this.linkedinPublisher = new LinkedInPublisherV2();
    this.twitterPublisher = new TwitterPublisherV2();
    this.facebookPublisher = new FacebookPublisherV2();
    this.instagramService = new InstagramGraphService();
  }

  /**
   * Register a platform-specific metrics collector
   */
  registerCollector(platform: string, collector: PlatformMetricsCollector) {
    this.collectors.set(platform.toUpperCase(), collector);
  }

  /**
   * Collect comprehensive post analytics using V2 publishers
   */
  async collectPostAnalyticsV2(postId: string): Promise<any[]> {
    try {
      console.log('Collecting post analytics V2:', postId);

      // Get post details and associated social accounts
      const { data: post, error: postError } = await this.serviceSupabase
        .from('posts')
        .select(`
          id,
          content,
          user_id,
          created_at,
          social_accounts!inner(
            id,
            platform,
            account_id,
            access_token,
            page_id
          )
        `)
        .eq('id', postId)
        .single();

      if (postError || !post) {
        throw new Error(`Failed to fetch post: ${postError?.message}`);
      }

      const analytics: any[] = [];

      // Collect analytics from each platform using V2 publishers
      for (const account of post.social_accounts) {
        try {
          const platformAnalytics = await this.collectPlatformAnalyticsV2(
            account.platform,
            postId,
            account
          );

          if (platformAnalytics) {
            analytics.push(platformAnalytics);

            // Store in new analytics tables
            await this.storePostAnalyticsV2(platformAnalytics);
          }
        } catch (error) {
          console.error(`Failed to collect V2 analytics for ${account.platform}:`, error);
        }
      }

      console.log(`Collected V2 analytics for ${analytics.length} platforms`);
      return analytics;

    } catch (error) {
      console.error('Post analytics V2 collection error:', error);
      throw error;
    }
  }

  /**
   * Collect analytics for a specific platform using V2 publishers
   */
  private async collectPlatformAnalyticsV2(
    platform: string,
    postId: string,
    account: any
  ): Promise<any | null> {
    try {
      let analyticsData: any = null;

      switch (platform.toUpperCase()) {
        case 'LINKEDIN':
          analyticsData = await this.linkedinPublisher.getPostAnalytics(postId, account.access_token);
          break;
        case 'TWITTER':
        case 'X':
          analyticsData = await this.twitterPublisher.getPostAnalytics(postId, account.access_token);
          break;
        case 'FACEBOOK':
          analyticsData = await this.facebookPublisher.getPostAnalytics(postId, account.access_token);
          break;
        case 'INSTAGRAM':
          analyticsData = await this.instagramService.getPostAnalytics(postId, account.access_token);
          break;
        default:
          console.warn(`Unsupported platform for V2 analytics: ${platform}`);
          return null;
      }

      if (!analyticsData) {
        return null;
      }

      // Normalize analytics data across platforms
      return this.normalizeAnalyticsDataV2(platform, postId, analyticsData);

    } catch (error) {
      console.error(`Platform V2 analytics collection error for ${platform}:`, error);
      return null;
    }
  }

  /**
   * Normalize analytics data from different platforms into a common format
   */
  private normalizeAnalyticsDataV2(platform: string, postId: string, rawData: any): any {
    const normalized = {
      platform: platform.toUpperCase(),
      postId,
      metrics: {
        impressions: 0,
        reach: 0,
        likes: 0,
        comments: 0,
        shares: 0,
        clicks: 0,
      },
      collectedAt: new Date(),
      rawData,
    };

    switch (platform.toUpperCase()) {
      case 'LINKEDIN':
        // LinkedIn analytics normalization
        if (rawData.data) {
          const data = rawData.data;
          normalized.metrics.impressions = this.extractMetricValueV2(data, 'impressions');
          normalized.metrics.reach = this.extractMetricValueV2(data, 'reach');
          normalized.metrics.likes = this.extractMetricValueV2(data, 'likes');
          normalized.metrics.comments = this.extractMetricValueV2(data, 'comments');
          normalized.metrics.shares = this.extractMetricValueV2(data, 'shares');
          normalized.metrics.clicks = this.extractMetricValueV2(data, 'clicks');
        }
        break;

      case 'TWITTER':
      case 'X':
        // Twitter analytics normalization
        if (rawData.data && rawData.data.public_metrics) {
          const metrics = rawData.data.public_metrics;
          normalized.metrics.impressions = metrics.impression_count || 0;
          normalized.metrics.likes = metrics.like_count || 0;
          normalized.metrics.comments = metrics.reply_count || 0;
          normalized.metrics.shares = metrics.retweet_count || 0;
          normalized.metrics.clicks = metrics.url_link_clicks || 0;
        }
        break;

      case 'FACEBOOK':
        // Facebook analytics normalization
        if (rawData.data) {
          const data = rawData.data;
          normalized.metrics.impressions = this.extractMetricValueV2(data, 'post_impressions');
          normalized.metrics.reach = this.extractMetricValueV2(data, 'post_reach');
          normalized.metrics.likes = this.extractMetricValueV2(data, 'post_reactions_like_total');
          normalized.metrics.comments = this.extractMetricValueV2(data, 'post_comments');
          normalized.metrics.shares = this.extractMetricValueV2(data, 'post_shares');
          normalized.metrics.clicks = this.extractMetricValueV2(data, 'post_clicks');
        }
        break;

      case 'INSTAGRAM':
        // Instagram analytics normalization
        if (rawData.data) {
          const data = rawData.data;
          normalized.metrics.impressions = this.extractMetricValueV2(data, 'impressions');
          normalized.metrics.reach = this.extractMetricValueV2(data, 'reach');
          normalized.metrics.likes = this.extractMetricValueV2(data, 'likes');
          normalized.metrics.comments = this.extractMetricValueV2(data, 'comments');
          normalized.metrics.shares = this.extractMetricValueV2(data, 'shares');
        }
        break;
    }

    return normalized;
  }

  /**
   * Extract metric value from platform-specific data structure
   */
  private extractMetricValueV2(data: any[], metricName: string): number {
    if (!Array.isArray(data)) return 0;

    const metric = data.find(item => item.name === metricName);
    return metric?.values?.[0]?.value || 0;
  }

  /**
   * Store post analytics in new analytics tables
   */
  private async storePostAnalyticsV2(analytics: any): Promise<void> {
    try {
      const engagementRate = this.calculateEngagementRateV2(analytics.metrics);

      const { error } = await this.serviceSupabase
        .from('post_analytics')
        .insert({
          post_id: analytics.postId,
          platform: analytics.platform,
          collected_at: analytics.collectedAt.toISOString(),
          impressions: analytics.metrics.impressions,
          reach: analytics.metrics.reach,
          likes: analytics.metrics.likes,
          comments: analytics.metrics.comments,
          shares: analytics.metrics.shares,
          clicks: analytics.metrics.clicks,
          engagement_rate: engagementRate,
          raw_data: analytics.rawData,
        });

      if (error) {
        throw new Error(`Failed to store V2 analytics: ${error.message}`);
      }

      console.log(`Stored V2 analytics for ${analytics.platform} post ${analytics.postId}`);

    } catch (error) {
      console.error('Store V2 analytics error:', error);
      throw error;
    }
  }

  /**
   * Calculate engagement rate from metrics
   */
  private calculateEngagementRateV2(metrics: any): number {
    const totalEngagement =
      metrics.likes +
      metrics.comments +
      metrics.shares +
      metrics.clicks;

    if (metrics.impressions === 0) return 0;

    return totalEngagement / metrics.impressions;
  }

  /**
   * Track an analytics event
   */
  async trackEvent(event: AnalyticsEvent): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('analytics_events')
        .insert({
          user_id: event.user_id,
          post_id: event.post_id,
          social_account_id: event.social_account_id,
          event_type: event.event_type,
          platform: event.platform.toUpperCase(),
          event_data: event.event_data,
          metrics: event.metrics,
          timestamp: event.timestamp || new Date().toISOString(),
        });

      if (error) {
        console.error('Error tracking analytics event:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to track analytics event:', error);
      throw error;
    }
  }

  /**
   * Collect and store post performance metrics
   */
  async collectPostMetrics(
    userId: string,
    postId: string,
    socialAccountId: string,
    platform: string,
    platformPostId: string
  ): Promise<void> {
    try {
      const collector = this.collectors.get(platform.toUpperCase());
      if (!collector) {
        console.warn(`No collector registered for platform: ${platform}`);
        return;
      }

      const metrics = await collector.collectPostMetrics(postId, platformPostId);

      // Store metrics in post_performance table
      const { error } = await this.supabase
        .from('post_performance')
        .upsert({
          post_id: postId,
          social_account_id: socialAccountId,
          platform: platform.toUpperCase(),
          ...metrics,
          last_updated: new Date().toISOString(),
        });

      if (error) {
        console.error('Error storing post metrics:', error);
        throw error;
      }

      // Track the metrics collection event
      await this.trackEvent({
        user_id: userId,
        post_id: postId,
        social_account_id: socialAccountId,
        event_type: 'metrics_collected',
        platform: platform,
        event_data: { platform_post_id: platformPostId },
        metrics: metrics as unknown as Record<string, number>,
      });

    } catch (error) {
      console.error('Failed to collect post metrics:', error);
      throw error;
    }
  }

  /**
   * Collect audience demographics for a social account
   */
  async collectAudienceInsights(
    userId: string,
    socialAccountId: string,
    platform: string
  ): Promise<void> {
    try {
      const collector = this.collectors.get(platform.toUpperCase());
      if (!collector) {
        console.warn(`No collector registered for platform: ${platform}`);
        return;
      }

      const insights = await collector.collectAudienceInsights(socialAccountId);

      // Store audience demographics
      const { error } = await this.supabase
        .from('audience_demographics')
        .upsert({
          user_id: userId,
          social_account_id: socialAccountId,
          platform: platform.toUpperCase(),
          age_groups: insights.age_groups || {},
          gender_distribution: insights.gender_distribution || {},
          location_data: insights.location_data || {},
          interests: insights.interests || {},
          languages: insights.languages || {},
          active_hours: insights.active_hours || {},
          device_types: insights.device_types || {},
          total_followers: insights.total_followers || 0,
          data_period_start: insights.period_start || new Date().toISOString(),
          data_period_end: insights.period_end || new Date().toISOString(),
          last_updated: new Date().toISOString(),
        });

      if (error) {
        console.error('Error storing audience insights:', error);
        throw error;
      }

    } catch (error) {
      console.error('Failed to collect audience insights:', error);
      throw error;
    }
  }

  /**
   * Batch collect metrics for multiple posts
   */
  async batchCollectMetrics(
    userId: string,
    posts: Array<{
      postId: string;
      socialAccountId: string;
      platform: string;
      platformPostId: string;
    }>
  ): Promise<void> {
    const batchSize = 10;

    for (let i = 0; i < posts.length; i += batchSize) {
      const batch = posts.slice(i, i + batchSize);

      await Promise.allSettled(
        batch.map(post =>
          this.collectPostMetrics(
            userId,
            post.postId,
            post.socialAccountId,
            post.platform,
            post.platformPostId
          )
        )
      );

      // Add delay between batches to respect rate limits
      if (i + batchSize < posts.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  /**
   * Get analytics summary for a user
   */
  async getAnalyticsSummary(
    userId: string,
    timeframe: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<any> {
    try {
      const timeframeMap = {
        day: '1 day',
        week: '7 days',
        month: '30 days',
        year: '365 days',
      };

      const { data, error } = await this.supabase
        .from('post_performance')
        .select(`
          *,
          posts!inner(user_id, created_at, status)
        `)
        .eq('posts.user_id', userId)
        .gte('posts.created_at', `now() - interval '${timeframeMap[timeframe]}'`);

      if (error) {
        throw error;
      }

      // Calculate summary metrics
      const summary = {
        total_posts: data.length,
        total_impressions: data.reduce((sum, post) => sum + (post.impressions || 0), 0),
        total_engagement: data.reduce((sum, post) =>
          sum + (post.likes || 0) + (post.comments || 0) + (post.shares || 0), 0),
        avg_engagement_rate: 0,
        top_performing_platform: '',
        engagement_trend: [],
      };

      if (data.length > 0) {
        summary.avg_engagement_rate = data.reduce((sum, post) =>
          sum + (post.engagement_rate || 0), 0) / data.length;

        // Find top performing platform
        const platformPerformance = data.reduce((acc, post) => {
          acc[post.platform] = (acc[post.platform] || 0) + (post.engagement_rate || 0);
          return acc;
        }, {} as Record<string, number>);

        summary.top_performing_platform = Object.entries(platformPerformance)
          .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0] || '';
      }

      return summary;

    } catch (error) {
      console.error('Failed to get analytics summary:', error);
      throw error;
    }
  }

  /**
   * Generate insights based on performance data
   */
  async generateInsights(userId: string): Promise<void> {
    try {
      // Get recent performance data
      const { data: recentPosts, error } = await this.supabase
        .from('post_performance')
        .select(`
          *,
          posts!inner(user_id, created_at, scheduled_at, content)
        `)
        .eq('posts.user_id', userId)
        .gte('posts.created_at', `now() - interval '30 days'`)
        .order('posts.created_at', { ascending: false });

      if (error || !recentPosts?.length) {
        return;
      }

      const insights = [];

      // Insight 1: Best performing content type
      const contentTypePerformance = this.analyzeContentTypes(recentPosts);
      if (contentTypePerformance.insight) {
        insights.push({
          user_id: userId,
          insight_type: 'content_recommendation',
          title: 'Best Performing Content Type',
          description: contentTypePerformance.insight,
          data: contentTypePerformance.data,
          confidence_score: contentTypePerformance.confidence,
          priority: 'high',
        });
      }

      // Insight 2: Optimal posting times
      const timingInsight = this.analyzePostingTimes(recentPosts);
      if (timingInsight.insight) {
        insights.push({
          user_id: userId,
          insight_type: 'optimal_time',
          title: 'Optimal Posting Times',
          description: timingInsight.insight,
          data: timingInsight.data,
          confidence_score: timingInsight.confidence,
          priority: 'medium',
        });
      }

      // Insight 3: Platform performance comparison
      const platformInsight = this.analyzePlatformPerformance(recentPosts);
      if (platformInsight.insight) {
        insights.push({
          user_id: userId,
          insight_type: 'platform_optimization',
          title: 'Platform Performance Analysis',
          description: platformInsight.insight,
          data: platformInsight.data,
          confidence_score: platformInsight.confidence,
          priority: 'medium',
        });
      }

      // Store insights
      if (insights.length > 0) {
        const { error: insertError } = await this.supabase
          .from('analytics_insights')
          .insert(insights);

        if (insertError) {
          console.error('Error storing insights:', insertError);
        }
      }

    } catch (error) {
      console.error('Failed to generate insights:', error);
    }
  }

  private analyzeContentTypes(posts: any[]): any {
    // Analyze content types and their performance
    const contentTypes = posts.reduce((acc, post) => {
      const hasImage = post.posts.content.includes('http') || post.media_url;
      const hasVideo = post.posts.content.includes('video') || post.video_views > 0;
      const type = hasVideo ? 'video' : hasImage ? 'image' : 'text';

      if (!acc[type]) {
        acc[type] = { count: 0, totalEngagement: 0 };
      }

      acc[type].count++;
      acc[type].totalEngagement += post.engagement_rate || 0;

      return acc;
    }, {} as Record<string, any>);

    const bestType = Object.entries(contentTypes)
      .map(([type, data]: [string, any]) => ({
        type,
        avgEngagement: data.totalEngagement / data.count,
        count: data.count,
      }))
      .sort((a, b) => b.avgEngagement - a.avgEngagement)[0];

    if (bestType && bestType.count >= 3) {
      return {
        insight: `Your ${bestType.type} content performs ${Math.round(bestType.avgEngagement)}% better on average. Consider creating more ${bestType.type} content.`,
        data: contentTypes,
        confidence: Math.min(bestType.count / 10, 0.9),
      };
    }

    return { insight: null };
  }

  private analyzePostingTimes(posts: any[]): any {
    // Analyze posting times and their performance
    const timePerformance = posts.reduce((acc, post) => {
      const hour = new Date(post.posts.scheduled_at || post.posts.created_at).getHours();

      if (!acc[hour]) {
        acc[hour] = { count: 0, totalEngagement: 0 };
      }

      acc[hour].count++;
      acc[hour].totalEngagement += post.engagement_rate || 0;

      return acc;
    }, {} as Record<number, any>);

    const bestHours = Object.entries(timePerformance)
      .map(([hour, data]: [string, any]) => ({
        hour: parseInt(hour),
        avgEngagement: data.totalEngagement / data.count,
        count: data.count,
      }))
      .filter(item => item.count >= 2)
      .sort((a, b) => b.avgEngagement - a.avgEngagement)
      .slice(0, 3);

    if (bestHours.length > 0) {
      const topHour = bestHours[0];
      return {
        insight: `Your posts perform best around ${topHour.hour}:00 with ${Math.round(topHour.avgEngagement)}% average engagement rate.`,
        data: { bestHours, timePerformance },
        confidence: Math.min(topHour.count / 5, 0.8),
      };
    }

    return { insight: null };
  }

  private analyzePlatformPerformance(posts: any[]): any {
    // Analyze platform performance
    const platformPerformance = posts.reduce((acc, post) => {
      if (!acc[post.platform]) {
        acc[post.platform] = { count: 0, totalEngagement: 0, totalReach: 0 };
      }

      acc[post.platform].count++;
      acc[post.platform].totalEngagement += post.engagement_rate || 0;
      acc[post.platform].totalReach += post.reach || 0;

      return acc;
    }, {} as Record<string, any>);

    const platforms = Object.entries(platformPerformance)
      .map(([platform, data]: [string, any]) => ({
        platform,
        avgEngagement: data.totalEngagement / data.count,
        avgReach: data.totalReach / data.count,
        count: data.count,
      }))
      .filter(item => item.count >= 2)
      .sort((a, b) => b.avgEngagement - a.avgEngagement);

    if (platforms.length > 1) {
      const best = platforms[0];
      const worst = platforms[platforms.length - 1];
      const improvement = ((best.avgEngagement - worst.avgEngagement) / worst.avgEngagement * 100);

      return {
        insight: `${best.platform} outperforms ${worst.platform} by ${Math.round(improvement)}%. Consider focusing more content on ${best.platform}.`,
        data: { platforms, platformPerformance },
        confidence: Math.min(best.count / 10, 0.8),
      };
    }

    return { insight: null };
  }
}

// Export singleton instance
export const analyticsCollector = new AnalyticsDataCollector();
