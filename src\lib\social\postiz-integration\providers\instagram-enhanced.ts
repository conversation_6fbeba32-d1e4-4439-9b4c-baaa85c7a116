/**
 * Enhanced Instagram Provider
 * Uses Facebook Graph API for Instagram Business accounts
 */

import { 
  AuthTokenDetails, 
  PostDetails, 
  PostResponse, 
  SocialProvider,
  GenerateAuthUrlResponse,
  MediaContent
} from '../interfaces';
import { SocialAbstract } from '../social-abstract';

export class InstagramEnhancedProvider extends SocialAbstract implements SocialProvider {
  identifier = 'instagram';
  name = 'Instagram Business';
  isBetweenSteps = true; // Requires business account selection
  scopes = [
    'instagram_basic',
    'instagram_content_publish',
    'pages_show_list',
    'pages_read_engagement',
    'business_management',
  ];

  async refreshToken(refresh_token: string): Promise<AuthTokenDetails> {
    // Instagram uses Facebook's token system
    return {
      refreshToken: '',
      expiresIn: 0,
      accessToken: '',
      id: '',
      name: '',
      picture: '',
      username: '',
    };
  }

  async generateAuthUrl(): Promise<GenerateAuthUrlResponse> {
    const state = `instagram_${this.makeId(6)}`;
    const codeVerifier = this.makeId(10);

    const url = 'https://www.facebook.com/v20.0/dialog/oauth' +
      `?client_id=${process.env.FACEBOOK_APP_ID}` +
      `&redirect_uri=${encodeURIComponent(
        `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback`
      )}` +
      `&state=${state}` +
      `&scope=${this.scopes.join(',')}`;

    return {
      url,
      codeVerifier,
      state,
    };
  }

  async authenticate(params: {
    code: string;
    codeVerifier: string;
    refresh?: string;
  }): Promise<AuthTokenDetails> {
    try {
      // Step 1: Exchange code for access token (same as Facebook)
      const tokenResponse = await this.fetch(
        'https://graph.facebook.com/v20.0/oauth/access_token' +
        `?client_id=${process.env.FACEBOOK_APP_ID}` +
        `&redirect_uri=${encodeURIComponent(
          `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback${
            params.refresh ? `?refresh=${params.refresh}` : ''
          }`
        )}` +
        `&client_secret=${process.env.FACEBOOK_APP_SECRET}` +
        `&code=${params.code}`
      );

      const { access_token } = await tokenResponse.json();

      // Step 2: Get user information
      const userResponse = await this.fetch(
        `https://graph.facebook.com/v20.0/me?fields=id,name,picture&access_token=${access_token}`
      );

      const {
        id,
        name,
        picture: {
          data: { url },
        },
      } = await userResponse.json();

      return {
        id,
        name,
        accessToken: access_token,
        refreshToken: access_token,
        expiresIn: 5184000, // 60 days
        picture: url,
        username: '',
      };
    } catch (error) {
      console.error('[InstagramEnhanced] Authentication error:', error);
      throw new Error(`Instagram authentication failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  async getInstagramBusinessAccounts(accessToken: string): Promise<Array<{
    id: string;
    name: string;
    username: string;
    picture?: string;
    type: 'business';
  }>> {
    try {
      // Get Facebook pages first
      const pagesResponse = await this.fetch(
        `https://graph.facebook.com/v20.0/me/accounts?fields=id,name,instagram_business_account&access_token=${accessToken}`
      );

      const { data: pages } = await pagesResponse.json();
      
      const instagramAccounts = [];
      
      for (const page of pages) {
        if (page.instagram_business_account) {
          try {
            // Get Instagram account details
            const igResponse = await this.fetch(
              `https://graph.facebook.com/v20.0/${page.instagram_business_account.id}?fields=id,username,name,profile_picture_url&access_token=${page.access_token}`
            );

            const igAccount = await igResponse.json();
            
            instagramAccounts.push({
              id: igAccount.id,
              name: igAccount.name || igAccount.username,
              username: igAccount.username,
              picture: igAccount.profile_picture_url,
              type: 'business' as const,
              pageId: page.id,
              pageAccessToken: page.access_token,
            });
          } catch (error) {
            console.error('[InstagramEnhanced] Error fetching IG account details:', error);
          }
        }
      }

      return instagramAccounts;
    } catch (error) {
      console.error('[InstagramEnhanced] Get business accounts error:', error);
      return [];
    }
  }

  async post(
    id: string,
    accessToken: string,
    postDetails: PostDetails[],
    integration: any
  ): Promise<PostResponse[]> {
    try {
      const [firstPost] = postDetails;
      
      if (!firstPost.media || firstPost.media.length === 0) {
        throw new Error('Instagram posts require at least one media item');
      }

      const results: PostResponse[] = [];

      // Handle single media post
      if (firstPost.media.length === 1) {
        const media = firstPost.media[0];
        const containerId = await this.createMediaContainer(
          id,
          accessToken,
          media,
          firstPost.message
        );

        const publishedPost = await this.publishMedia(id, accessToken, containerId);
        
        results.push({
          id: firstPost.id,
          postId: publishedPost.id,
          releaseURL: `https://www.instagram.com/p/${publishedPost.id}/`,
          status: 'posted',
        });
      } else {
        // Handle carousel post (multiple media)
        const containerIds = await Promise.all(
          firstPost.media.map(media => 
            this.createMediaContainer(id, accessToken, media)
          )
        );

        const carouselId = await this.createCarouselContainer(
          id,
          accessToken,
          containerIds,
          firstPost.message
        );

        const publishedPost = await this.publishMedia(id, accessToken, carouselId);
        
        results.push({
          id: firstPost.id,
          postId: publishedPost.id,
          releaseURL: `https://www.instagram.com/p/${publishedPost.id}/`,
          status: 'posted',
        });
      }

      return results;
    } catch (error) {
      console.error('[InstagramEnhanced] Post error:', error);
      throw new Error(`Instagram posting failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  private async createMediaContainer(
    igAccountId: string,
    accessToken: string,
    media: MediaContent,
    caption?: string
  ): Promise<string> {
    try {
      const isVideo = media.type === 'video' || media.url.includes('.mp4');
      
      const body: any = {
        image_url: !isVideo ? media.url : undefined,
        video_url: isVideo ? media.url : undefined,
        media_type: isVideo ? 'VIDEO' : 'IMAGE',
      };

      if (caption) {
        body.caption = caption;
      }

      const response = await this.fetch(
        `https://graph.facebook.com/v20.0/${igAccountId}/media`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(body),
        }
      );

      const { id } = await response.json();
      return id;
    } catch (error) {
      console.error('[InstagramEnhanced] Create media container error:', error);
      throw error;
    }
  }

  private async createCarouselContainer(
    igAccountId: string,
    accessToken: string,
    containerIds: string[],
    caption: string
  ): Promise<string> {
    try {
      const response = await this.fetch(
        `https://graph.facebook.com/v20.0/${igAccountId}/media`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            media_type: 'CAROUSEL',
            children: containerIds.join(','),
            caption,
          }),
        }
      );

      const { id } = await response.json();
      return id;
    } catch (error) {
      console.error('[InstagramEnhanced] Create carousel container error:', error);
      throw error;
    }
  }

  private async publishMedia(
    igAccountId: string,
    accessToken: string,
    containerId: string
  ): Promise<{ id: string }> {
    try {
      const response = await this.fetch(
        `https://graph.facebook.com/v20.0/${igAccountId}/media_publish`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            creation_id: containerId,
          }),
        }
      );

      return await response.json();
    } catch (error) {
      console.error('[InstagramEnhanced] Publish media error:', error);
      throw error;
    }
  }

  async getMediaInsights(
    mediaId: string,
    accessToken: string
  ): Promise<any> {
    try {
      const response = await this.fetch(
        `https://graph.facebook.com/v20.0/${mediaId}/insights?metric=impressions,reach,engagement&access_token=${accessToken}`
      );

      return await response.json();
    } catch (error) {
      console.error('[InstagramEnhanced] Get media insights error:', error);
      return null;
    }
  }

  async getAccountInsights(
    igAccountId: string,
    accessToken: string,
    period: 'day' | 'week' | 'days_28' = 'day',
    since?: string,
    until?: string
  ): Promise<any> {
    try {
      let url = `https://graph.facebook.com/v20.0/${igAccountId}/insights?metric=impressions,reach,profile_views&period=${period}&access_token=${accessToken}`;
      
      if (since) url += `&since=${since}`;
      if (until) url += `&until=${until}`;

      const response = await this.fetch(url);
      return await response.json();
    } catch (error) {
      console.error('[InstagramEnhanced] Get account insights error:', error);
      return null;
    }
  }
}
