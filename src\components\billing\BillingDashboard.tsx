'use client';

import { useState, useEffect } from 'react';
import { 
  CreditCardIcon, 
  DocumentTextIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface SubscriptionData {
  user: {
    id: string;
    email: string;
    name: string;
    subscriptionStatus: string;
    trialEndsAt: string | null;
  };
  plan: {
    name: string;
    name_ar: string;
    description: string;
    price_monthly: number;
    price_yearly: number;
    plan_type: string;
  };
  subscription: {
    id: string;
    status: string;
    billing_cycle: string;
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    trial_end: string | null;
  } | null;
  usage: {
    current: {
      postsCreated: number;
      postsScheduled: number;
      aiGenerationsUsed: number;
      reportsGenerated: number;
    };
    limits: {
      postsPerMonth: number | null;
      aiGenerations: number | null;
      reportsPerMonth: number | null;
    };
    socialAccounts: number;
    teamMembers: number;
    limitStatus: {
      exceeded: boolean;
      limits: Array<{
        type: string;
        current: number;
        limit: number;
        percentage: number;
      }>;
    };
  };
  features: Record<string, boolean>;
}

interface BillingDashboardProps {
  isRTL?: boolean;
}

export default function BillingDashboard({ isRTL = false }: BillingDashboardProps) {
  const [data, setData] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [canceling, setCanceling] = useState(false);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/stripe/subscription-status');
      const result = await response.json();
      
      if (result.success) {
        setData(result);
      } else {
        setError('Failed to load subscription data');
      }
    } catch (err) {
      setError('Failed to load subscription data');
      console.error('Error fetching subscription data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!data?.subscription) return;
    
    setCanceling(true);
    try {
      const response = await fetch('/api/stripe/cancel-subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          subscriptionId: data.subscription.id,
          cancelAtPeriodEnd: true 
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        await fetchSubscriptionData(); // Refresh data
      } else {
        setError('Failed to cancel subscription');
      }
    } catch (err) {
      setError('Failed to cancel subscription');
      console.error('Error canceling subscription:', err);
    } finally {
      setCanceling(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getUsagePercentage = (current: number, limit: number | null) => {
    if (limit === null) return 0; // Unlimited
    return Math.min((current / limit) * 100, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'trialing': return 'text-blue-600 bg-blue-100';
      case 'past_due': return 'text-red-600 bg-red-100';
      case 'canceled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, { en: string; ar: string }> = {
      active: { en: 'Active', ar: 'نشط' },
      trialing: { en: 'Trial', ar: 'تجريبي' },
      past_due: { en: 'Past Due', ar: 'متأخر' },
      canceled: { en: 'Canceled', ar: 'ملغي' },
      free: { en: 'Free', ar: 'مجاني' },
    };
    
    return isRTL ? statusMap[status]?.ar || status : statusMap[status]?.en || status;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-700">{error}</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">
          {isRTL ? 'لا توجد بيانات اشتراك' : 'No subscription data available'}
        </p>
      </div>
    );
  }

  return (
    <div className={`max-w-4xl mx-auto space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Current Plan Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            {isRTL ? 'الخطة الحالية' : 'Current Plan'}
          </h2>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(data.user.subscriptionStatus)}`}>
            {getStatusText(data.user.subscriptionStatus)}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {isRTL ? data.plan.name_ar : data.plan.name}
            </h3>
            <p className="text-gray-600 mb-4">{data.plan.description}</p>
            
            {data.plan.plan_type !== 'free' && (
              <div className="text-2xl font-bold text-gray-900">
                {data.subscription?.billing_cycle === 'yearly' 
                  ? formatPrice(data.plan.price_yearly)
                  : formatPrice(data.plan.price_monthly)
                }
                <span className="text-sm font-normal text-gray-600 ml-1">
                  /{isRTL 
                    ? (data.subscription?.billing_cycle === 'yearly' ? 'سنة' : 'شهر')
                    : (data.subscription?.billing_cycle === 'yearly' ? 'year' : 'month')
                  }
                </span>
              </div>
            )}
          </div>

          <div className="space-y-3">
            {data.subscription && (
              <>
                <div>
                  <span className="text-sm text-gray-500">
                    {isRTL ? 'تاريخ التجديد التالي:' : 'Next billing date:'}
                  </span>
                  <div className="font-medium">
                    {formatDate(data.subscription.current_period_end)}
                  </div>
                </div>

                {data.subscription.trial_end && (
                  <div>
                    <span className="text-sm text-gray-500">
                      {isRTL ? 'انتهاء الفترة التجريبية:' : 'Trial ends:'}
                    </span>
                    <div className="font-medium text-blue-600">
                      {formatDate(data.subscription.trial_end)}
                    </div>
                  </div>
                )}

                {data.subscription.cancel_at_period_end && (
                  <div className="flex items-center text-orange-600">
                    <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                    <span className="text-sm">
                      {isRTL ? 'سيتم إلغاء الاشتراك في نهاية الفترة الحالية' : 'Subscription will cancel at period end'}
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Usage Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {isRTL ? 'استخدام الخطة' : 'Plan Usage'}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Posts Usage */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                {isRTL ? 'المنشورات' : 'Posts'}
              </span>
              <DocumentTextIcon className="h-4 w-4 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {data.usage.current.postsCreated + data.usage.current.postsScheduled}
            </div>
            {data.usage.limits.postsPerMonth && (
              <>
                <div className="text-sm text-gray-500">
                  {isRTL ? `من ${data.usage.limits.postsPerMonth}` : `of ${data.usage.limits.postsPerMonth}`}
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${getUsagePercentage(
                        data.usage.current.postsCreated + data.usage.current.postsScheduled, 
                        data.usage.limits.postsPerMonth
                      )}%` 
                    }}
                  ></div>
                </div>
              </>
            )}
            {data.usage.limits.postsPerMonth === null && (
              <div className="text-sm text-green-600">
                {isRTL ? 'غير محدود' : 'Unlimited'}
              </div>
            )}
          </div>

          {/* Social Accounts */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                {isRTL ? 'الحسابات الاجتماعية' : 'Social Accounts'}
              </span>
              <CreditCardIcon className="h-4 w-4 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {data.usage.socialAccounts}
            </div>
            <div className="text-sm text-gray-500">
              {data.plan.plan_type === 'free' 
                ? (isRTL ? 'من 2' : 'of 2')
                : data.plan.plan_type === 'pro'
                ? (isRTL ? 'من 5' : 'of 5')
                : data.plan.plan_type === 'business'
                ? (isRTL ? 'من 10' : 'of 10')
                : (isRTL ? 'غير محدود' : 'Unlimited')
              }
            </div>
          </div>

          {/* Team Members */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                {isRTL ? 'أعضاء الفريق' : 'Team Members'}
              </span>
              <CheckCircleIcon className="h-4 w-4 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {data.usage.teamMembers}
            </div>
            <div className="text-sm text-gray-500">
              {data.plan.plan_type === 'free' 
                ? (isRTL ? 'من 1' : 'of 1')
                : data.plan.plan_type === 'pro'
                ? (isRTL ? 'من 2' : 'of 2')
                : data.plan.plan_type === 'business'
                ? (isRTL ? 'من 5' : 'of 5')
                : (isRTL ? 'غير محدود' : 'Unlimited')
              }
            </div>
          </div>

          {/* AI Generations */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                {isRTL ? 'المحتوى الذكي' : 'AI Content'}
              </span>
              <ClockIcon className="h-4 w-4 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {data.usage.current.aiGenerationsUsed}
            </div>
            {data.usage.limits.aiGenerations && (
              <div className="text-sm text-gray-500">
                {isRTL ? `من ${data.usage.limits.aiGenerations}` : `of ${data.usage.limits.aiGenerations}`}
              </div>
            )}
            {data.usage.limits.aiGenerations === null && (
              <div className="text-sm text-green-600">
                {isRTL ? 'غير محدود' : 'Unlimited'}
              </div>
            )}
          </div>
        </div>

        {/* Usage Warnings */}
        {data.usage.limitStatus.exceeded && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
              <span className="text-red-700 font-medium">
                {isRTL ? 'تم تجاوز حدود الاستخدام' : 'Usage limits exceeded'}
              </span>
            </div>
            <p className="text-red-600 text-sm mt-1">
              {isRTL 
                ? 'يرجى ترقية خطتك للاستمرار في استخدام جميع الميزات.'
                : 'Please upgrade your plan to continue using all features.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {isRTL ? 'إجراءات الاشتراك' : 'Subscription Actions'}
        </h2>

        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => window.location.href = '/billing/plans'}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            {isRTL ? 'ترقية الخطة' : 'Upgrade Plan'}
          </button>

          {data.subscription && !data.subscription.cancel_at_period_end && (
            <button
              onClick={handleCancelSubscription}
              disabled={canceling}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {canceling 
                ? (isRTL ? 'جاري الإلغاء...' : 'Canceling...')
                : (isRTL ? 'إلغاء الاشتراك' : 'Cancel Subscription')
              }
            </button>
          )}

          <button
            onClick={() => window.location.href = '/billing/history'}
            className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
          >
            {isRTL ? 'سجل الفواتير' : 'Billing History'}
          </button>
        </div>
      </div>
    </div>
  );
}
