import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { PageSelectionService } from '@/lib/social/business-accounts/page-selection-service';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

const pageSelectionService = new PageSelectionService();

// GET - Get business accounts for a platform
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Getting business accounts...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');
    const userId = searchParams.get('userId');

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform parameter is required' },
        { status: 400 }
      );
    }

    // Validate user access
    if (userId && userId !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    console.log(`Getting business accounts for platform: ${platform}`);

    // Get business account configuration
    const config = await pageSelectionService.getBusinessAccountConfig(
      user.id,
      platform
    );

    return NextResponse.json({
      success: true,
      platform: config.platform,
      isConfigured: config.isConfigured,
      hasBusinessAccounts: config.hasBusinessAccounts,
      businessAccounts: config.businessAccounts,
      selectedAccount: config.selectedAccount,
      requiresReconnection: config.requiresReconnection,
      missingPermissions: config.missingPermissions,
      lastUpdated: config.lastUpdated,
    });

  } catch (error) {
    console.error('Error getting business accounts:', error);
    return NextResponse.json(
      {
        error: 'Failed to get business accounts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST - Refresh business accounts for a platform
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Refreshing business accounts...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { platform, userId } = body;

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform is required' },
        { status: 400 }
      );
    }

    // Validate user access
    if (userId && userId !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    console.log(`Refreshing business accounts for platform: ${platform}`);

    // Refresh business accounts
    const config = await pageSelectionService.refreshBusinessAccounts(
      user.id,
      platform
    );

    // Log the refresh activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'BUSINESS_ACCOUNTS_REFRESHED',
        metadata: {
          platform,
          accountCount: config.businessAccounts.length,
          timestamp: new Date().toISOString(),
        },
      });

    return NextResponse.json({
      success: true,
      message: 'Business accounts refreshed successfully',
      platform: config.platform,
      isConfigured: config.isConfigured,
      hasBusinessAccounts: config.hasBusinessAccounts,
      businessAccounts: config.businessAccounts,
      selectedAccount: config.selectedAccount,
      requiresReconnection: config.requiresReconnection,
      missingPermissions: config.missingPermissions,
      lastUpdated: config.lastUpdated,
    });

  } catch (error) {
    console.error('Error refreshing business accounts:', error);
    return NextResponse.json(
      {
        error: 'Failed to refresh business accounts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}


