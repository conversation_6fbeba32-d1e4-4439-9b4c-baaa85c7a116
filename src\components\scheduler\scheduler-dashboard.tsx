'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RefreshCw, 
  Activity, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Play,
  Pause,
  BarChart3,
  Settings,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

interface SchedulerStatus {
  system: {
    status: string;
    isHealthy: boolean;
    lastActivity: string | null;
    activeSchedulers: number;
  };
  jobs: {
    total: number;
    pending: number;
    processing: number;
    failed: number;
    completed: number;
  };
  schedulerInstances: any[];
  recentJobs: any[];
  publishHistory: any[];
  platformMetrics: Record<string, any>;
}

export function SchedulerDashboard() {
  const [status, setStatus] = useState<SchedulerStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchStatus();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchStatus = async () => {
    try {
      setIsRefreshing(true);
      const response = await fetch('/api/scheduler/status');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في جلب حالة المجدول');
      }

      setStatus(result.data);
    } catch (error: any) {
      console.error('Error fetching scheduler status:', error);
      toast.error(error.message);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const getStatusIcon = (systemStatus: string, isHealthy: boolean) => {
    if (systemStatus === 'running' && isHealthy) {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    } else if (systemStatus === 'running' && !isHealthy) {
      return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-600" />;
    }
  };

  const getStatusBadge = (systemStatus: string, isHealthy: boolean) => {
    if (systemStatus === 'running' && isHealthy) {
      return <Badge className="bg-green-100 text-green-800">نشط وصحي</Badge>;
    } else if (systemStatus === 'running' && !isHealthy) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">نشط مع تحذيرات</Badge>;
    } else {
      return <Badge variant="destructive">متوقف</Badge>;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'غير محدد';
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ar 
      });
    } catch {
      return 'تاريخ غير صالح';
    }
  };

  const getJobStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'processing': return 'text-blue-600';
      case 'pending': return 'text-yellow-600';
      case 'cancelled': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getJobStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      pending: 'في الانتظار',
      processing: 'قيد المعالجة',
      completed: 'مكتمل',
      failed: 'فشل',
      cancelled: 'ملغي',
    };
    return labels[status] || status;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        جاري تحميل لوحة تحكم المجدول...
      </div>
    );
  }

  if (!status) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">فشل في تحميل البيانات</h3>
        <Button onClick={fetchStatus}>
          <RefreshCw className="h-4 w-4 mr-2" />
          إعادة المحاولة
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">لوحة تحكم المجدول</h2>
          <p className="text-muted-foreground">
            مراقبة وإدارة نظام جدولة المنشورات
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={fetchStatus} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            إعدادات
          </Button>
        </div>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">حالة النظام</p>
                {getStatusBadge(status.system.status, status.system.isHealthy)}
              </div>
              {getStatusIcon(status.system.status, status.system.isHealthy)}
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              آخر نشاط: {formatDate(status.system.lastActivity)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">المهام النشطة</p>
                <p className="text-2xl font-bold text-blue-600">{status.jobs.processing}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              {status.jobs.pending} في الانتظار
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">المهام المكتملة</p>
                <p className="text-2xl font-bold text-green-600">{status.jobs.completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              {status.jobs.failed} فشل
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">المجدولات النشطة</p>
                <p className="text-2xl font-bold text-purple-600">{status.system.activeSchedulers}</p>
              </div>
              <Zap className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              {status.schedulerInstances.length} إجمالي
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="jobs">المهام</TabsTrigger>
          <TabsTrigger value="platforms">المنصات</TabsTrigger>
          <TabsTrigger value="history">السجل</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Job Queue Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  حالة طابور المهام
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">إجمالي المهام</span>
                    <span className="font-semibold">{status.jobs.total}</span>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>مكتمل</span>
                      <span className="text-green-600">{status.jobs.completed}</span>
                    </div>
                    <Progress 
                      value={status.jobs.total > 0 ? (status.jobs.completed / status.jobs.total) * 100 : 0} 
                      className="h-2"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between">
                      <span>في الانتظار:</span>
                      <span className="text-yellow-600">{status.jobs.pending}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>فشل:</span>
                      <span className="text-red-600">{status.jobs.failed}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Scheduler Instances */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  مثيلات المجدول
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {status.schedulerInstances.length === 0 ? (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      لا توجد مثيلات مجدول نشطة
                    </p>
                  ) : (
                    status.schedulerInstances.slice(0, 5).map((instance) => (
                      <div key={instance.id} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium text-sm">{instance.instance_id}</div>
                          <div className="text-xs text-muted-foreground">
                            {formatDate(instance.last_heartbeat)}
                          </div>
                        </div>
                        <Badge 
                          variant={instance.status === 'running' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {instance.status === 'running' ? 'نشط' : 'متوقف'}
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Jobs Tab */}
        <TabsContent value="jobs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>المهام الحديثة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {status.recentJobs.length === 0 ? (
                  <p className="text-center text-muted-foreground py-4">
                    لا توجد مهام حديثة
                  </p>
                ) : (
                  status.recentJobs.map((job) => (
                    <div key={job.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{job.job_type}</span>
                          <Badge variant="outline" className="text-xs">
                            أولوية {job.priority}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatDate(job.created_at)}
                          {job.attempts > 0 && ` • ${job.attempts} محاولة`}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getJobStatusColor(job.status)}`}
                        >
                          {getJobStatusLabel(job.status)}
                        </Badge>
                        {job.started_at && job.completed_at && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {Math.round((new Date(job.completed_at).getTime() - new Date(job.started_at).getTime()) / 1000)}ث
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Platforms Tab */}
        <TabsContent value="platforms" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(status.platformMetrics).map(([platform, metrics]) => (
              <Card key={platform}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{platform}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>الحسابات:</span>
                      <span>{metrics.accounts}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>نشط:</span>
                      <span className="text-green-600">{metrics.activeAccounts}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>المنشورات الحديثة:</span>
                      <span>{metrics.recentPosts}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>معدل النجاح:</span>
                      <span className={metrics.successRate >= 90 ? 'text-green-600' : metrics.successRate >= 70 ? 'text-yellow-600' : 'text-red-600'}>
                        {metrics.successRate}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>سجل النشر</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {status.publishHistory.length === 0 ? (
                  <p className="text-center text-muted-foreground py-4">
                    لا يوجد سجل نشر
                  </p>
                ) : (
                  status.publishHistory.map((entry) => (
                    <div key={entry.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <div className="font-medium">
                          {entry.posts?.content?.substring(0, 50)}...
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {entry.platform} • {formatDate(entry.started_at)}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge 
                          variant={entry.status === 'published' ? 'default' : 'destructive'}
                          className="text-xs"
                        >
                          {entry.status === 'published' ? 'منشور' : 'فشل'}
                        </Badge>
                        {entry.attempt_number > 1 && (
                          <div className="text-xs text-muted-foreground mt-1">
                            محاولة {entry.attempt_number}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
