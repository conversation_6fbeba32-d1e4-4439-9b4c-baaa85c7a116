'use client';

import { useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export default function DemoLoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  const router = useRouter();

  const signInAsDemo = async () => {
    setIsLoading(true);
    setResult('Signing in as Demo User...');

    try {
      const supabase = createClient();

      // Sign in as Demo User
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'demo123456',
      });

      if (error) {
        console.error('Demo sign in error:', error);
        setResult(`❌ Demo Sign In Failed: ${error.message}`);
        toast.error('Demo sign in failed');
        return;
      }

      if (data.user) {
        setResult(`✅ Demo Sign In Successful!\nUser ID: ${data.user.id}\nEmail: ${data.user.email}`);
        toast.success('Signed in as Demo User successfully!');
        
        // Redirect to social accounts page after successful login
        setTimeout(() => {
          router.push('/social');
        }, 2000);
      }
    } catch (error: any) {
      console.error('Demo sign in error:', error);
      setResult(`❌ Demo sign in failed: ${error.message}`);
      toast.error('Demo sign in failed');
    } finally {
      setIsLoading(false);
    }
  };

  const createDemoUser = async () => {
    setIsLoading(true);
    setResult('Creating Demo User...');

    try {
      const supabase = createClient();

      // Create Demo User
      const { data, error } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'demo123456',
        options: {
          data: {
            name: 'Demo User',
            role: 'USER'
          }
        }
      });

      if (error) {
        console.error('Demo user creation error:', error);
        setResult(`❌ Demo User Creation Failed: ${error.message}`);
        toast.error('Demo user creation failed');
        return;
      }

      if (data.user) {
        setResult(`✅ Demo User Created Successfully!\nUser ID: ${data.user.id}\nEmail: ${data.user.email}\n\nNow try signing in...`);
        toast.success('Demo user created successfully!');
      }
    } catch (error: any) {
      console.error('Demo user creation error:', error);
      setResult(`❌ Demo user creation failed: ${error.message}`);
      toast.error('Demo user creation failed');
    } finally {
      setIsLoading(false);
    }
  };

  const checkCurrentUser = async () => {
    setIsLoading(true);
    setResult('Checking current user...');

    try {
      const supabase = createClient();
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error) {
        setResult(`❌ Error checking user: ${error.message}`);
        return;
      }

      if (user) {
        setResult(`✅ Current User:\nID: ${user.id}\nEmail: ${user.email}\nCreated: ${user.created_at}`);
      } else {
        setResult('❌ No user currently authenticated');
      }
    } catch (error: any) {
      setResult(`❌ Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setIsLoading(true);
    setResult('Signing out...');

    try {
      const supabase = createClient();
      const { error } = await supabase.auth.signOut();

      if (error) {
        setResult(`❌ Sign out failed: ${error.message}`);
        return;
      }

      setResult('✅ Signed out successfully');
      toast.success('Signed out successfully');
    } catch (error: any) {
      setResult(`❌ Sign out error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Demo User Authentication
            </h1>
            <p className="text-gray-600">
              Sign in as Demo User to access LinkedIn account
            </p>
          </div>

          <div className="space-y-4">
            <button
              onClick={checkCurrentUser}
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Checking...' : 'Check Current User'}
            </button>

            <button
              onClick={signOut}
              disabled={isLoading}
              className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {isLoading ? 'Signing Out...' : 'Sign Out Current User'}
            </button>

            <button
              onClick={signInAsDemo}
              disabled={isLoading}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isLoading ? 'Signing In...' : 'Sign In as Demo User'}
            </button>

            <button
              onClick={createDemoUser}
              disabled={isLoading}
              className="w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 disabled:opacity-50"
            >
              {isLoading ? 'Creating...' : 'Create Demo User (if needed)'}
            </button>
          </div>

          {result && (
            <div className="mt-6 p-4 bg-gray-100 rounded-md">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Result:</h3>
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">{result}</pre>
            </div>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 mb-2">Demo User Credentials:</p>
            <p className="text-xs text-gray-500">
              Email: <EMAIL><br />
              Password: demo123456<br />
              ID: 3ddaeb03-2d95-4fff-abad-2a2c7dd25037
            </p>
          </div>

          <div className="mt-4 text-center">
            <a
              href="/social"
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              → Go to Social Accounts Page
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
