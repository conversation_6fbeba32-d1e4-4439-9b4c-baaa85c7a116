import { NextRequest, NextResponse } from 'next/server';
import { getOAuthConfig } from '@/lib/social/oauth-config';
import { createServiceRoleClient } from '@/lib/supabase/service-role';
import { XOAuthService } from '@/lib/social/x-oauth';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    console.log('X OAuth callback received:', {
      code: code ? 'present' : 'missing',
      state,
      error,
      errorDescription,
      timestamp: new Date().toISOString()
    });

    // Handle OAuth errors
    if (error) {
      console.error('X OAuth error:', { error, errorDescription });
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=oauth_error&message=${encodeURIComponent(errorDescription || error)}&platform=x`
      );
    }

    // Validate required parameters
    if (!code) {
      console.error('X OAuth callback missing authorization code');
      return NextResponse.redirect(
        `http://127.0.0.1:3001/auth/error?error=missing_code&platform=x`
      );
    }

    // Get X OAuth configuration
    const config = getOAuthConfig('x');
    if (!config || !config.enabled) {
      console.error('X OAuth not configured or disabled');
      return NextResponse.redirect(
        `http://127.0.0.1:3001/auth/error?error=platform_not_configured&platform=x`
      );
    }

    console.log('X OAuth config loaded:', {
      clientId: config.clientId ? 'present' : 'missing',
      redirectUri: config.redirectUri,
      scopes: config.scope
    });

    // Initialize X OAuth service
    const xOAuth = new XOAuthService({
      clientId: config.clientId,
      clientSecret: config.clientSecret,
      redirectUri: config.redirectUri,
      scopes: config.scope,
    });

    // Get code verifier from session/storage (in production, this would be stored securely)
    // For now, we'll use a placeholder - in real implementation, store this during auth initiation
    const codeVerifier = 'demo_code_verifier_for_testing'; // This should be retrieved from secure storage

    // Exchange authorization code for access token
    console.log('Exchanging X authorization code for access token...');
    let tokenData;
    try {
      tokenData = await xOAuth.exchangeCodeForToken(code, codeVerifier);
      console.log('X token exchange successful:', {
        hasAccessToken: !!tokenData.access_token,
        tokenType: tokenData.token_type,
        expiresIn: tokenData.expires_in,
        hasRefreshToken: !!tokenData.refresh_token,
        scope: tokenData.scope
      });
    } catch (tokenError) {
      console.error('X token exchange failed:', tokenError);
      return NextResponse.redirect(
        `http://127.0.0.1:3001/auth/error?error=token_exchange_failed&platform=x&message=${encodeURIComponent(tokenError.message)}`
      );
    }

    // Fetch user profile information
    console.log('Fetching X user profile...');
    let profileData;
    try {
      profileData = await xOAuth.getUserProfile(tokenData.access_token);
      console.log('X profile fetched:', {
        id: profileData.id,
        name: profileData.name,
        username: profileData.username,
        verified: profileData.verified,
        followers: profileData.public_metrics?.followers_count
      });
    } catch (profileError) {
      console.error('X profile fetch failed:', profileError);
      return NextResponse.redirect(
        `http://127.0.0.1:3001/auth/error?error=profile_fetch_failed&platform=x&message=${encodeURIComponent(profileError.message)}`
      );
    }

    // Store the X account in database
    // Use service role client to bypass RLS policies
    const supabase = createServiceRoleClient();

    // Use an existing user ID for testing
    // In a full implementation, you'd get the actual user ID from session
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037'; // Demo User

    console.log('Looking up user in database...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', demoUserId)
      .single();

    if (userError || !userData) {
      console.error('User lookup failed:', userError);
      return NextResponse.redirect(
        `http://127.0.0.1:3001/auth/error?error=user_not_found&platform=x`
      );
    }

    console.log('User found:', userData);

    // Check if X account already exists
    const { data: existingAccount } = await supabase
      .from('social_accounts')
      .select('id')
      .eq('user_id', demoUserId)
      .eq('platform', 'x')
      .eq('platform_user_id', profileData.id)
      .single();

    if (existingAccount) {
      console.log('X account already exists, updating...');
      const { error: updateError } = await supabase
        .from('social_accounts')
        .update({
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token || null,
          expires_at: tokenData.expires_in 
            ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
            : null,
          account_data: {
            name: profileData.name,
            username: profileData.username,
            profile: profileData,
            scope: tokenData.scope
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', existingAccount.id);

      if (updateError) {
        console.error('Failed to update X account:', updateError);
        return NextResponse.redirect(
          `http://127.0.0.1:3001/auth/error?error=account_update_failed&platform=x`
        );
      }
    } else {
      console.log('Creating new X account...');
      const { error: insertError } = await supabase
        .from('social_accounts')
        .insert({
          user_id: demoUserId,
          platform: 'x',
          platform_user_id: profileData.id,
          platform_username: profileData.username,
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token || null,
          expires_at: tokenData.expires_in 
            ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
            : null,
          account_data: {
            name: profileData.name,
            username: profileData.username,
            profile: profileData,
            scope: tokenData.scope
          }
        });

      if (insertError) {
        console.error('Failed to create X account:', insertError);
        return NextResponse.redirect(
          `http://127.0.0.1:3001/auth/error?error=account_creation_failed&platform=x`
        );
      }
    }

    console.log('X OAuth flow completed successfully');

    // Redirect back to social accounts page with success message
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/social?success=x_connected&account=${encodeURIComponent(profileData.username)}&name=${encodeURIComponent(profileData.name)}`
    );

  } catch (error) {
    console.error('X OAuth callback error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=internal_error&platform=x&message=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    );
  }
}
