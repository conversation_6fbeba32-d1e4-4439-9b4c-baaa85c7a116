'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  PlusCircle, 
  RefreshCw, 
  Upload, 
  Calendar,
  BarChart3,
  Clock,
  FileText,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import { createClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';

// Import our new components
import { RecurringPostForm } from '@/components/posts/recurring-post-form';
import { RecurringPostsList } from '@/components/posts/recurring-posts-list';
import { BulkImportModal } from '@/components/posts/bulk-import-modal';
import { BulkOperationsDashboard } from '@/components/posts/bulk-operations-dashboard';

interface Post {
  id: string;
  content: string;
  status: string;
  media_url?: string;
  scheduled_at?: string;
  published_at?: string;
  created_at: string;
  updated_at: string;
  platforms?: string[];
  is_recurring?: boolean;
  parent_post_id?: string;
}

export default function EnhancedPostsPage() {
  const [activeTab, setActiveTab] = useState('all-posts');
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [showRecurringForm, setShowRecurringForm] = useState(false);
  const [showBulkImport, setShowBulkImport] = useState(false);
  const [editingRecurringPost, setEditingRecurringPost] = useState<any>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const router = useRouter();

  useEffect(() => {
    checkAuthAndLoadData();
  }, []);

  const checkAuthAndLoadData = async () => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      router.push('/auth/signin');
      return;
    }

    setUser(user);
    await loadPosts();
  };

  const loadPosts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/posts');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load posts');
      }

      setPosts(data.posts || []);
    } catch (error: any) {
      console.error('Error loading posts:', error);
      toast.error('فشل في تحميل المنشورات');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecurringPostSuccess = (data: any) => {
    setShowRecurringForm(false);
    setEditingRecurringPost(null);
    setRefreshTrigger(prev => prev + 1);
    loadPosts(); // Refresh regular posts too
  };

  const handleBulkImportSuccess = (data: any) => {
    setShowBulkImport(false);
    setRefreshTrigger(prev => prev + 1);
    toast.success('تم بدء الاستيراد المجمع بنجاح');
  };

  const handleEditRecurringPost = (post: any) => {
    setEditingRecurringPost(post);
    setShowRecurringForm(true);
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      DRAFT: 'secondary',
      SCHEDULED: 'default',
      PUBLISHED: 'default',
      FAILED: 'destructive',
    };

    const labels: Record<string, string> = {
      DRAFT: 'مسودة',
      SCHEDULED: 'مجدول',
      PUBLISHED: 'منشور',
      FAILED: 'فشل',
    };

    return (
      <Badge variant={variants[status] || 'secondary'}>
        {labels[status] || status}
      </Badge>
    );
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'غير محدد';
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return 'غير محدد';
    }
  };

  // Statistics
  const stats = {
    total: posts.length,
    scheduled: posts.filter(p => p.status === 'SCHEDULED').length,
    published: posts.filter(p => p.status === 'PUBLISHED').length,
    recurring: posts.filter(p => p.is_recurring).length,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              إدارة المنشورات المتقدمة
            </h1>
            <p className="text-muted-foreground mt-1">
              إدارة شاملة للمنشورات مع الجدولة المتكررة والاستيراد المجمع
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={() => setShowBulkImport(true)}>
              <Upload className="h-4 w-4 mr-2" />
              استيراد مجمع
            </Button>
            <Button onClick={() => setShowRecurringForm(true)}>
              <RefreshCw className="h-4 w-4 mr-2" />
              منشور متكرر
            </Button>
            <Button onClick={() => router.push('/posts/new')}>
              <PlusCircle className="h-4 w-4 mr-2" />
              منشور جديد
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">إجمالي المنشورات</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">مجدولة</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.scheduled}</p>
                </div>
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">منشورة</p>
                  <p className="text-2xl font-bold text-green-600">{stats.published}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">متكررة</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.recurring}</p>
                </div>
                <RefreshCw className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all-posts" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              جميع المنشورات
            </TabsTrigger>
            <TabsTrigger value="recurring" className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              المنشورات المتكررة
            </TabsTrigger>
            <TabsTrigger value="bulk-operations" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              العمليات المجمعة
            </TabsTrigger>
            <TabsTrigger value="calendar" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              التقويم
            </TabsTrigger>
          </TabsList>

          {/* All Posts Tab */}
          <TabsContent value="all-posts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  جميع المنشورات
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                    جاري تحميل المنشورات...
                  </div>
                ) : posts.length === 0 ? (
                  <div className="text-center py-8">
                    <PlusCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">لا توجد منشورات</h3>
                    <p className="text-muted-foreground mb-4">ابدأ بإنشاء منشورك الأول</p>
                    <Button onClick={() => router.push('/posts/new')}>
                      <PlusCircle className="h-4 w-4 mr-2" />
                      إنشاء منشور جديد
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {posts.map((post) => (
                      <Card key={post.id} className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h3 className="font-semibold line-clamp-1 mb-1">
                                {post.content.length > 80 ? post.content.substring(0, 80) + "..." : post.content}
                              </h3>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <span>ID: {post.id.substring(0, 8)}...</span>
                                {post.is_recurring && (
                                  <Badge variant="outline" className="text-xs">
                                    <RefreshCw className="h-3 w-3 mr-1" />
                                    متكرر
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(post.status)}
                              <Button variant="ghost" size="sm">
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {post.platforms?.map(platform => (
                                <Badge key={platform} variant="secondary" className="text-xs">
                                  {platform}
                                </Badge>
                              )) || (
                                <Badge variant="secondary" className="text-xs">
                                  عام
                                </Badge>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {post.status === "SCHEDULED"
                                ? formatDate(post.scheduled_at)
                                : post.status === "PUBLISHED"
                                ? formatDate(post.published_at)
                                : formatDate(post.created_at)}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Recurring Posts Tab */}
          <TabsContent value="recurring" className="space-y-4">
            <RecurringPostsList 
              onEdit={handleEditRecurringPost}
              onCreateNew={() => setShowRecurringForm(true)}
            />
          </TabsContent>

          {/* Bulk Operations Tab */}
          <TabsContent value="bulk-operations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  العمليات المجمعة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <BulkOperationsDashboard refreshTrigger={refreshTrigger} />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Calendar Tab */}
          <TabsContent value="calendar" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  تقويم المنشورات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">تقويم المنشورات</h3>
                  <p className="text-muted-foreground">
                    سيتم إضافة عرض التقويم المتقدم قريباً
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Modals */}
        {showRecurringForm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <RecurringPostForm
                onSuccess={handleRecurringPostSuccess}
                onCancel={() => {
                  setShowRecurringForm(false);
                  setEditingRecurringPost(null);
                }}
                initialData={editingRecurringPost}
                isEditing={!!editingRecurringPost}
              />
            </div>
          </div>
        )}

        <BulkImportModal
          open={showBulkImport}
          onOpenChange={setShowBulkImport}
          onSuccess={handleBulkImportSuccess}
        />
      </div>
    </div>
  );
}
