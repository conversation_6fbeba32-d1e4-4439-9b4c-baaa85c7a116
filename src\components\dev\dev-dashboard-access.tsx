"use client";

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";

/**
 * Development component that provides direct access to the dashboard
 * This component should only be used during development
 */
export function DevDashboardAccess() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const accessDashboard = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log("Dev dashboard access: redirecting to dashboard...");
      
      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Redirect to dashboard
      router.push("/dashboard");
    } catch (error) {
      console.error("Dev dashboard access error:", error);
      setError("حدث خطأ أثناء الوصول إلى لوحة التحكم");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Button 
        variant="destructive" 
        onClick={accessDashboard}
        disabled={isLoading}
      >
        {isLoading ? "جاري الوصول..." : "الوصول المباشر للوحة التحكم"}
      </Button>
      {error && (
        <div className="mt-2 p-2 bg-red-100 text-red-800 rounded text-sm">
          {error}
        </div>
      )}
    </div>
  );
}
