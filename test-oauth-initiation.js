#!/usr/bin/env node

/**
 * OAuth Initiation Flow Testing
 * Tests OAuth URL generation and callback URL consistency
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-OAuth-Tester/1.0',
        ...options.headers
      },
      timeout: 10000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function testOAuthInitiation() {
  console.log('🔐 TESTING OAUTH INITIATION FLOWS\n');
  
  const platforms = ['twitter', 'facebook', 'instagram', 'linkedin'];
  const results = [];
  
  for (const platform of platforms) {
    console.log(`Testing ${platform.toUpperCase()} OAuth initiation...`);
    
    try {
      // Test OAuth connect endpoint
      const connectUrl = `${BASE_URL}/api/social/connect`;
      const response = await makeRequest(connectUrl, {
        method: 'POST',
        body: { platform }
      });
      
      console.log(`  Status: ${response.status}`);
      
      if (response.data) {
        if (response.data.authUrl) {
          console.log(`  ✅ Auth URL generated: ${response.data.authUrl.substring(0, 100)}...`);
          
          // Check if callback URL is consistent
          const expectedCallback = `${BASE_URL}/api/social/callback/${platform}`;
          if (response.data.authUrl.includes(encodeURIComponent(expectedCallback))) {
            console.log(`  ✅ Callback URL consistent: /api/social/callback/${platform}`);
            results.push({ platform, status: 'PASS', authUrl: true, callbackConsistent: true });
          } else {
            console.log(`  ⚠️  Callback URL may be inconsistent`);
            results.push({ platform, status: 'WARN', authUrl: true, callbackConsistent: false });
          }
        } else if (response.data.error) {
          console.log(`  ⚠️  Error: ${response.data.error}`);
          results.push({ platform, status: 'WARN', error: response.data.error });
        } else {
          console.log(`  ❌ No auth URL in response`);
          results.push({ platform, status: 'FAIL', authUrl: false });
        }
      } else {
        console.log(`  ❌ No data in response`);
        results.push({ platform, status: 'FAIL', noData: true });
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      results.push({ platform, status: 'FAIL', error: error.message });
    }
    
    console.log('');
  }
  
  // Summary
  console.log('📊 OAUTH INITIATION SUMMARY:');
  console.log('=' .repeat(50));
  
  const passed = results.filter(r => r.status === 'PASS').length;
  const warned = results.filter(r => r.status === 'WARN').length;
  const failed = results.filter(r => r.status === 'FAIL').length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`⚠️  Warnings: ${warned}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
  
  return results;
}

async function testSocialAccountsEndpoint() {
  console.log('\n📱 TESTING SOCIAL ACCOUNTS ENDPOINT\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/accounts`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Social accounts endpoint working');
      
      if (response.data.accounts) {
        console.log(`📊 Found ${response.data.accounts.length} connected accounts`);
        
        response.data.accounts.forEach(account => {
          console.log(`  - ${account.platform}: ${account.account_name} (${account.status})`);
        });
      } else {
        console.log('📝 No accounts found (expected for fresh setup)');
      }
      
      return { status: 'PASS', accountCount: response.data.accounts?.length || 0 };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testEnhancedProvidersConfig() {
  console.log('\n🚀 TESTING ENHANCED PROVIDERS CONFIGURATION\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/config/validate`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Enhanced providers config endpoint working');
      
      if (response.data.platforms) {
        console.log('📋 Platform configurations:');
        
        Object.entries(response.data.platforms).forEach(([platform, config]) => {
          const status = config.configured ? '✅' : '❌';
          console.log(`  ${status} ${platform.toUpperCase()}: ${config.configured ? 'Configured' : 'Not configured'}`);
          
          if (config.missingEnvVars && config.missingEnvVars.length > 0) {
            console.log(`    Missing: ${config.missingEnvVars.join(', ')}`);
          }
        });
      }
      
      return { status: 'PASS', config: response.data };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function runOAuthTests() {
  console.log('🧪 COMPREHENSIVE OAUTH FLOW TESTING');
  console.log('=' .repeat(60));
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  const oauthResults = await testOAuthInitiation();
  const accountsResult = await testSocialAccountsEndpoint();
  const configResult = await testEnhancedProvidersConfig();
  
  console.log('\n🏁 TESTING COMPLETE');
  console.log(`Finished at: ${new Date().toISOString()}`);
  
  // Determine overall success
  const oauthPassed = oauthResults.filter(r => r.status === 'PASS').length;
  const totalTests = oauthResults.length + 2; // +2 for accounts and config tests
  const totalPassed = oauthPassed + 
    (accountsResult.status === 'PASS' ? 1 : 0) + 
    (configResult.status === 'PASS' ? 1 : 0);
  
  console.log(`\n📈 OVERALL SUCCESS RATE: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);
  
  return {
    oauth: oauthResults,
    accounts: accountsResult,
    config: configResult,
    summary: {
      total: totalTests,
      passed: totalPassed,
      successRate: (totalPassed / totalTests) * 100
    }
  };
}

if (require.main === module) {
  runOAuthTests().then(results => {
    process.exit(results.summary.successRate >= 75 ? 0 : 1);
  }).catch(error => {
    console.error('❌ CRITICAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = { runOAuthTests };
