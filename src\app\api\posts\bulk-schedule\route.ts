import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const bulkScheduleSchema = z.object({
  posts: z.array(z.object({
    content: z.string().min(1).max(2800),
    media_url: z.string().url().optional(),
    scheduled_at: z.string().datetime(),
    social_account_ids: z.array(z.string()).min(1),
    time_zone: z.string().default('UTC'),
  })).min(1).max(100), // Limit to 100 posts per batch
});

const csvImportSchema = z.object({
  csv_data: z.string().min(1),
  social_account_ids: z.array(z.string()).min(1),
  time_zone: z.string().default('UTC'),
  start_date: z.string().datetime(),
  interval_hours: z.number().min(1).max(168).default(24), // 1 hour to 1 week
});

/**
 * Bulk schedule posts
 * POST /api/posts/bulk-schedule
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'csv_import') {

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
      return await handleCSVImport(request, supabase, user, body);
    } else {
      return await handleBulkSchedule(request, supabase, user, body);
    }

  } catch (error) {
    console.error('Bulk schedule error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function handleBulkSchedule(request: NextRequest, supabase: any, user: any, body: any) {
  const validation = bulkScheduleSchema.safeParse(body);
  
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { posts } = validation.data;

  // Create bulk operation record
  const { data: bulkOperation, error: bulkError } = await supabase
    .from('bulk_operations')
    .insert({
      user_id: user.id,
      operation_type: 'bulk_schedule',
      total_items: posts.length,
      status: 'processing',
      created_at: new Date().toISOString(),
    })
    .select()
    .single();

  if (bulkError) {
    console.error('Error creating bulk operation:', bulkError);
    return NextResponse.json({ error: 'Failed to create bulk operation' }, { status: 500 });
  }

  let completedItems = 0;
  let failedItems = 0;
  const errors = [];

  // Process posts in batches
  const batchSize = 10;
  for (let i = 0; i < posts.length; i += batchSize) {
    const batch = posts.slice(i, i + batchSize);
    
    for (const postData of batch) {
      try {
        // Verify user owns the social accounts
        const { data: userAccounts, error: accountsError } = await supabase
          .from('social_accounts')
          .select('id')
          .eq('user_id', user.id)
          .in('id', postData.social_account_ids);

        if (accountsError || userAccounts.length !== postData.social_account_ids.length) {
          failedItems++;
          errors.push(`Post ${i + 1}: Invalid social accounts`);
          continue;
        }

        // Create post
        const { data: newPost, error: postError } = await supabase
          .from('posts')
          .insert({
            user_id: user.id,
            content: postData.content,
            media_url: postData.media_url,
            status: 'SCHEDULED',
            scheduled_at: new Date(postData.scheduled_at).toISOString(),
            time_zone: postData.time_zone,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (postError) {
          failedItems++;
          errors.push(`Post ${i + 1}: ${postError.message}`);
          continue;
        }

        // Create social account associations
        const associations = postData.social_account_ids.map(accountId => ({
          post_id: newPost.id,
          social_account_id: accountId,
        }));

        const { error: associationError } = await supabase
          .from('post_social_accounts')
          .insert(associations);

        if (associationError) {
          failedItems++;
          errors.push(`Post ${i + 1}: Failed to associate social accounts`);
          continue;
        }

        completedItems++;

      } catch (error) {
        failedItems++;
        errors.push(`Post ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Update bulk operation progress
    await supabase
      .from('bulk_operations')
      .update({
        completed_items: completedItems,
        failed_items: failedItems,
        error_details: errors.length > 0 ? { errors } : null,
      })
      .eq('id', bulkOperation.id);
  }

  // Mark bulk operation as completed
  await supabase
    .from('bulk_operations')
    .update({
      status: failedItems === 0 ? 'completed' : 'completed_with_errors',
      completed_at: new Date().toISOString(),
    })
    .eq('id', bulkOperation.id);

  // Log activity
  await supabase
    .from('activities')
    .insert({
      user_id: user.id,
      action: 'BULK_SCHEDULE_COMPLETED',
      details: `Bulk scheduled ${completedItems} posts, ${failedItems} failed`,
      created_at: new Date().toISOString(),
    });

  return NextResponse.json({
    success: true,
    bulkOperationId: bulkOperation.id,
    totalPosts: posts.length,
    completedItems,
    failedItems,
    errors: errors.slice(0, 10), // Return first 10 errors
  });
}

async function handleCSVImport(request: NextRequest, supabase: any, user: any, body: any) {
  const validation = csvImportSchema.safeParse(body);
  
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { csv_data, social_account_ids, time_zone, start_date, interval_hours } = validation.data;

  // Parse CSV data
  const lines = csv_data.trim().split('\n');
  const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
  
  if (!headers.includes('content')) {
    return NextResponse.json({ error: 'CSV must contain a "content" column' }, { status: 400 });
  }

  const posts = [];
  let currentDate = new Date(start_date);

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim());
    const row: any = {};
    
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });

    if (row.content) {
      posts.push({
        content: row.content,
        media_url: row.media_url || undefined,
        scheduled_at: currentDate.toISOString(),
        social_account_ids,
        time_zone,
      });

      // Advance to next scheduled time
      currentDate = new Date(currentDate.getTime() + (interval_hours * 60 * 60 * 1000));
    }
  }

  if (posts.length === 0) {
    return NextResponse.json({ error: 'No valid posts found in CSV' }, { status: 400 });
  }

  // Use the bulk schedule logic
  return await handleBulkSchedule(request, supabase, user, { posts });
}

/**
 * Get bulk operation status
 * GET /api/posts/bulk-schedule?operation_id=xxx
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const operationId = searchParams.get('operation_id');

    if (!operationId) {
      // Get all bulk operations for user
      const { data: operations, error: operationsError } = await supabase
        .from('bulk_operations')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (operationsError) {
        return NextResponse.json({ error: 'Failed to fetch operations' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        operations,
      });
    }

    // Get specific operation
    const { data: operation, error: operationError } = await supabase
      .from('bulk_operations')
      .select('*')
      .eq('id', operationId)
      .eq('user_id', user.id)
      .single();

    if (operationError) {
      return NextResponse.json({ error: 'Operation not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      operation,
    });

  } catch (error) {
    console.error('Error fetching bulk operation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
