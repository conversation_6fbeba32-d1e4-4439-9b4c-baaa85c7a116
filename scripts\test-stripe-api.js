#!/usr/bin/env node

/**
 * Stripe API Connection Test
 * Tests the provided Stripe API key for connectivity and permissions
 */

const https = require('https');

// Stripe API Configuration
const STRIPE_SECRET_KEY = '***********************************************************************************************************';
const STRIPE_API_BASE = 'https://api.stripe.com/v1';

/**
 * Make authenticated request to Stripe API
 */
function makeStripeRequest(endpoint, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(endpoint, STRIPE_API_BASE);
    
    const auth = Buffer.from(`${STRIPE_SECRET_KEY}:`).toString('base64');
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'eWasl-API-Test/1.0',
        'Stripe-Version': '2023-10-16'
      }
    };

    if (data && method !== 'GET') {
      const postData = new URLSearchParams(data).toString();
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data && method !== 'GET') {
      req.write(new URLSearchParams(data).toString());
    }

    req.end();
  });
}

/**
 * Test Stripe API connectivity
 */
async function testStripeAPI() {
  console.log('💳 Testing Stripe API Connection');
  console.log('=' * 50);
  
  const tests = [
    {
      name: 'Account Information',
      endpoint: '/account',
      description: 'Get account details and verify API key'
    },
    {
      name: 'Balance Information',
      endpoint: '/balance',
      description: 'Get account balance'
    },
    {
      name: 'Payment Methods',
      endpoint: '/payment_methods?type=card&limit=3',
      description: 'List payment methods'
    },
    {
      name: 'Products List',
      endpoint: '/products?limit=5',
      description: 'List products'
    },
    {
      name: 'Prices List',
      endpoint: '/prices?limit=5',
      description: 'List prices'
    },
    {
      name: 'Customers List',
      endpoint: '/customers?limit=3',
      description: 'List customers'
    },
    {
      name: 'Subscriptions List',
      endpoint: '/subscriptions?limit=3',
      description: 'List subscriptions'
    }
  ];

  const results = [];

  for (const test of tests) {
    console.log(`\n🔍 Testing: ${test.name}`);
    console.log(`📍 Endpoint: ${test.endpoint}`);
    console.log(`📝 Description: ${test.description}`);
    
    try {
      const response = await makeStripeRequest(test.endpoint);
      
      if (response.status === 200) {
        console.log(`✅ SUCCESS: ${test.name}`);
        console.log(`📊 Status: ${response.status}`);
        
        if (test.endpoint === '/account') {
          const account = response.data;
          console.log(`🏢 Business Name: ${account.business_profile?.name || account.display_name || 'N/A'}`);
          console.log(`📧 Email: ${account.email || 'N/A'}`);
          console.log(`🌍 Country: ${account.country || 'N/A'}`);
          console.log(`💰 Currency: ${account.default_currency?.toUpperCase() || 'N/A'}`);
          console.log(`✅ Charges Enabled: ${account.charges_enabled ? 'Yes' : 'No'}`);
          console.log(`💸 Payouts Enabled: ${account.payouts_enabled ? 'Yes' : 'No'}`);
        } else if (test.endpoint === '/balance') {
          const balance = response.data;
          console.log(`💰 Available Balance:`);
          balance.available?.forEach(bal => {
            console.log(`   ${bal.currency.toUpperCase()}: ${(bal.amount / 100).toFixed(2)}`);
          });
          console.log(`🔒 Pending Balance:`);
          balance.pending?.forEach(bal => {
            console.log(`   ${bal.currency.toUpperCase()}: ${(bal.amount / 100).toFixed(2)}`);
          });
        } else if (test.endpoint.includes('/products')) {
          const products = response.data.data || [];
          console.log(`📦 Total Products: ${products.length}`);
          products.forEach((product, index) => {
            console.log(`   ${index + 1}. ${product.name} (${product.active ? 'Active' : 'Inactive'})`);
          });
        } else if (test.endpoint.includes('/prices')) {
          const prices = response.data.data || [];
          console.log(`💲 Total Prices: ${prices.length}`);
          prices.forEach((price, index) => {
            const amount = price.unit_amount ? (price.unit_amount / 100).toFixed(2) : 'N/A';
            console.log(`   ${index + 1}. ${amount} ${price.currency?.toUpperCase()} (${price.active ? 'Active' : 'Inactive'})`);
          });
        } else if (test.endpoint.includes('/customers')) {
          const customers = response.data.data || [];
          console.log(`👥 Total Customers: ${customers.length}`);
        } else if (test.endpoint.includes('/subscriptions')) {
          const subscriptions = response.data.data || [];
          console.log(`📋 Total Subscriptions: ${subscriptions.length}`);
          subscriptions.forEach((sub, index) => {
            console.log(`   ${index + 1}. Status: ${sub.status} (${sub.currency?.toUpperCase()})`);
          });
        } else {
          const dataArray = response.data.data;
          if (Array.isArray(dataArray)) {
            console.log(`📊 Total Items: ${dataArray.length}`);
          }
        }
        
        results.push({ test: test.name, status: 'PASSED', response });
      } else {
        console.log(`❌ FAILED: ${test.name}`);
        console.log(`📊 Status: ${response.status}`);
        console.log(`❗ Error: ${response.data.error?.message || 'Unknown error'}`);
        results.push({ test: test.name, status: 'FAILED', error: response.data });
      }
    } catch (error) {
      console.log(`💥 ERROR: ${test.name}`);
      console.log(`❗ Error: ${error.message}`);
      results.push({ test: test.name, status: 'ERROR', error: error.message });
    }
  }

  // Summary
  console.log('\n' + '=' * 50);
  console.log('📋 STRIPE API TEST SUMMARY');
  console.log('=' * 50);
  
  const passed = results.filter(r => r.status === 'PASSED').length;
  const failed = results.filter(r => r.status === 'FAILED').length;
  const errors = results.filter(r => r.status === 'ERROR').length;
  
  console.log(`✅ Passed: ${passed}/${tests.length}`);
  console.log(`❌ Failed: ${failed}/${tests.length}`);
  console.log(`💥 Errors: ${errors}/${tests.length}`);
  
  if (passed === tests.length) {
    console.log('\n🎉 ALL TESTS PASSED! Stripe API is working perfectly.');
  } else if (passed > 0) {
    console.log('\n⚠️  PARTIAL SUCCESS: Some tests passed, check failed tests above.');
  } else {
    console.log('\n🚨 ALL TESTS FAILED: Check your API key and permissions.');
  }
  
  return results;
}

// Run the tests
if (require.main === module) {
  testStripeAPI()
    .then((results) => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { testStripeAPI, makeStripeRequest };
