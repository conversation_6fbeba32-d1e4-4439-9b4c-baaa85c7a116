import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createTokenManager } from '@/lib/auth/token-manager';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Disconnect schema
const disconnectSchema = z.object({
  accountId: z.string().min(1, 'Account ID is required'),
});

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('🔌 Starting social media account disconnection...');

    // Parse request body first
    const body = await request.json();
    console.log('📝 Request body:', { accountId: body.accountId, hasUserId: !!body.userId });

    const validation = disconnectSchema.safeParse(body);

    if (!validation.success) {
      console.error('❌ Invalid request data:', validation.error.errors);
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validation.error.errors.map(err => err.message).join(', ')
        },
        { status: 400 }
      );
    }

    const { accountId } = validation.data;

    // ENHANCED: Better user handling for both authenticated and demo users
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';

    // Try to get authenticated user, but fall back to Demo User for testing
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    // Use Demo User ID for testing, or authenticated user ID in production
    const effectiveUserId = user?.id || demoUserId;

    console.log('👤 User context:', {
      authenticatedUser: user?.id || 'none',
      effectiveUser: effectiveUserId,
      isDemoUser: effectiveUserId === demoUserId,
      accountToDisconnect: accountId
    });

    // Use service role client to bypass RLS for Demo User accounts
    const { createServiceRoleClient } = await import('@/lib/supabase/server');

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
    const serviceSupabase = createServiceRoleClient();

    // ENHANCED: Get account details with better error handling
    console.log('🔍 Fetching account details for:', accountId);
    const { data: account, error: fetchError } = await serviceSupabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching account:', fetchError);
      if (fetchError.code === 'PGRST116') {
        console.log('📭 Account not found in database');
        return NextResponse.json(
          {
            error: 'Account not found',
            details: 'The specified account does not exist or has already been removed'
          },
          { status: 404 }
        );
      }
      console.error('💥 Database fetch error:', fetchError.message);
      return NextResponse.json(
        {
          error: 'Failed to fetch account details',
          details: fetchError.message
        },
        { status: 500 }
      );
    }

    console.log('✅ Account found:', {
      platform: account.platform,
      accountName: account.account_name,
      accountId: account.account_id,
      userId: account.user_id
    });

    // ENHANCED: Verify account belongs to effective user (Demo User or authenticated user)
    // For testing purposes, allow disconnection of Demo User accounts
    const DEMO_USER_ID = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    const isValidUser = account.user_id === effectiveUserId || account.user_id === DEMO_USER_ID;

    if (!isValidUser) {
      console.error('🚫 Account ownership mismatch:', {
        accountUserId: account.user_id,
        effectiveUserId: effectiveUserId,
        demoUserId: DEMO_USER_ID
      });
      return NextResponse.json(
        {
          error: 'Account access denied',
          details: 'You do not have permission to disconnect this account'
        },
        { status: 403 }
      );
    }

    console.log('✅ Account ownership verified:', {
      accountUserId: account.user_id,
      effectiveUserId: effectiveUserId,
      isDemoAccount: account.user_id === DEMO_USER_ID
    });

    // ENHANCED: Use token manager with service role to properly revoke tokens and disconnect account
    console.log('🔐 Starting token revocation process...');
    const tokenManager = createTokenManager(true); // Use service role

    try {
      // Use the account's actual user_id for token revocation
      const result = await tokenManager.revokeTokens(account.user_id, account.platform, account.account_id);

      if (!result.success) {
        console.error('❌ Token revocation failed:', result.error);
        return NextResponse.json(
          {
            error: result.error || 'Failed to disconnect account',
            details: 'Token revocation process failed'
          },
          { status: 500 }
        );
      }

      console.log('✅ Account disconnected successfully:', {
        accountId,
        platform: account.platform,
        accountName: account.account_name
      });
    } catch (tokenError: any) {
      console.error('💥 Token manager error:', tokenError);
      return NextResponse.json(
        {
          error: 'Token revocation failed',
          details: tokenError.message || 'Unknown token manager error'
        },
        { status: 500 }
      );
    }

    // ENHANCED: Log activity with enhanced metadata using service role client
    try {
      await serviceSupabase
        .from('activities')
        .insert({
          user_id: account.user_id,
          action: 'SOCIAL_ACCOUNT_DISCONNECTED',
          metadata: {
            platform: account.platform,
            accountId: account.account_id,
            accountName: account.account_name,
            timestamp: new Date().toISOString(),
            disconnectionMethod: 'manual_disconnect_api'
          },
        });
      console.log('📝 Activity logged successfully');
    } catch (activityError) {
      console.warn('⚠️ Failed to log activity:', activityError);
      // Don't fail the disconnect for logging issues
    }

    console.log('🎉 Disconnect process completed successfully');
    return NextResponse.json({
      success: true,
      message: `${account.platform} account disconnected successfully`,
      disconnectedAccount: {
        id: accountId,
        platform: account.platform,
        accountName: account.account_name,
        accountId: account.account_id
      }
    }, { status: 200 });

  } catch (error: any) {
    console.error('💥 Social disconnect error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message || 'Unknown error occurred during disconnection'
      },
      { status: 500 }
    );
  }
}
