/**
 * Enhanced LinkedIn Provider
 * Adapted from Postiz with eWasl-specific enhancements
 */

import { 
  AuthTokenDetails, 
  PostDetails, 
  PostResponse, 
  SocialProvider,
  GenerateAuthUrlResponse,
  MediaContent
} from '../interfaces';
import { SocialAbstract } from '../social-abstract';
// import * as sharp from 'sharp';
// import { lookup } from 'mime-types';

export class LinkedInEnhancedProvider extends SocialAbstract implements SocialProvider {
  identifier = 'linkedin';
  name = 'LinkedIn';
  oneTimeToken = true;
  isBetweenSteps = false;
  scopes = [
    'openid',
    'profile',
    'w_member_social',
    'email',
  ];
  refreshWait = true;

  async refreshToken(refresh_token: string): Promise<AuthTokenDetails> {
    try {
      const response = await this.fetch('https://www.linkedin.com/oauth/v2/accessToken', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token,
          client_id: process.env.LINKEDIN_CLIENT_ID!,
          client_secret: process.env.LINKEDIN_CLIENT_SECRET!,
        }),
      });

      const {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in,
      } = await response.json();

      // Get user profile information
      const profileResponse = await this.fetch('https://api.linkedin.com/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const { name, sub: id, picture } = await profileResponse.json();

      // Get vanity name
      const meResponse = await this.fetch('https://api.linkedin.com/v2/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const { vanityName } = await meResponse.json();

      return {
        id,
        accessToken,
        refreshToken,
        expiresIn: expires_in,
        name,
        picture,
        username: vanityName,
      };
    } catch (error) {
      console.error('[LinkedInEnhanced] Refresh token error:', error);
      throw new Error(`Failed to refresh LinkedIn token: ${error instanceof Error ? error.message : error}`);
    }
  }

  async generateAuthUrl(): Promise<GenerateAuthUrlResponse> {
    const state = this.makeId(6);
    const codeVerifier = this.makeId(30);
    
    const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${
      process.env.LINKEDIN_CLIENT_ID
    }&prompt=none&redirect_uri=${encodeURIComponent(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/linkedin/callback`
    )}&state=${state}&scope=${encodeURIComponent(this.scopes.join(' '))}`;

    return {
      url,
      codeVerifier,
      state,
    };
  }

  async authenticate(params: {
    code: string;
    codeVerifier: string;
    refresh?: string;
  }): Promise<AuthTokenDetails> {
    try {
      const body = new URLSearchParams();
      body.append('grant_type', 'authorization_code');
      body.append('code', params.code);
      body.append(
        'redirect_uri',
        `${process.env.NEXT_PUBLIC_APP_URL}/api/linkedin/callback${
          params.refresh ? `?refresh=${params.refresh}` : ''
        }`
      );
      body.append('client_id', process.env.LINKEDIN_CLIENT_ID!);
      body.append('client_secret', process.env.LINKEDIN_CLIENT_SECRET!);

      const tokenResponse = await this.fetch('https://www.linkedin.com/oauth/v2/accessToken', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body,
      });

      const {
        access_token: accessToken,
        expires_in: expiresIn,
        refresh_token: refreshToken,
        scope,
      } = await tokenResponse.json();

      this.checkScopes(this.scopes, scope);

      // Get user profile
      const profileResponse = await this.fetch('https://api.linkedin.com/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const { name, sub: id, picture } = await profileResponse.json();

      // Get vanity name
      const meResponse = await this.fetch('https://api.linkedin.com/v2/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const { vanityName } = await meResponse.json();

      return {
        id,
        accessToken,
        refreshToken,
        expiresIn,
        name,
        picture,
        username: vanityName,
      };
    } catch (error) {
      console.error('[LinkedInEnhanced] Authentication error:', error);
      throw new Error(`LinkedIn authentication failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  async getCompanyPages(accessToken: string): Promise<Array<{
    id: string;
    name: string;
    vanityName: string;
    logoUrl?: string;
  }>> {
    try {
      const response = await this.fetch(
        'https://api.linkedin.com/v2/organizationAcls?q=roleAssignee&role=ADMINISTRATOR&projection=(elements*(organization~(id,name,vanityName,logoV2(original~:playableStreams))))',
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'X-Restli-Protocol-Version': '2.0.0',
            'LinkedIn-Version': '202501',
          },
        }
      );

      const data = await response.json();
      
      return data.elements?.map((element: any) => ({
        id: element.organization.id,
        name: element.organization.name,
        vanityName: element.organization.vanityName,
        logoUrl: element.organization.logoV2?.original?.elements?.[0]?.identifiers?.[0]?.identifier,
      })) || [];
    } catch (error) {
      console.error('[LinkedInEnhanced] Get company pages error:', error);
      return [];
    }
  }

  protected fixText(text: string): string {
    const pattern = /@\[.+?\]\(urn:li:organization.+?\)/g;
    const matches = text.match(pattern) || [];
    const splitAll = text.split(pattern);
    
    const splitTextReformat = splitAll.map((p) => {
      return p
        .replace(/\\/g, '\\\\')
        .replace(/</g, '\\<')
        .replace(/>/g, '\\>')
        .replace(/#/g, '\\#')
        .replace(/~/g, '\\~')
        .replace(/_/g, '\\_')
        .replace(/\|/g, '\\|')
        .replace(/\[/g, '\\[')
        .replace(/]/g, '\\]')
        .replace(/\*/g, '\\*')
        .replace(/\(/g, '\\(')
        .replace(/\)/g, '\\)')
        .replace(/\{/g, '\\{')
        .replace(/}/g, '\\}')
        .replace(/@/g, '\\@');
    });

    const connectAll = splitTextReformat.reduce((all, current) => {
      const match = matches.shift();
      all.push(current);
      if (match) {
        all.push(match);
      }
      return all;
    }, [] as string[]);

    return connectAll.join('');
  }

  async post(
    id: string,
    accessToken: string,
    postDetails: PostDetails[],
    integration: any,
    type: 'personal' | 'company' = 'personal'
  ): Promise<PostResponse[]> {
    try {
      const [firstPost, ...restPosts] = postDetails;
      
      // Handle media uploads if present
      const mediaIds: string[] = [];
      if (firstPost.media?.length) {
        for (const media of firstPost.media) {
          const mediaId = await this.uploadMedia(media, accessToken, id, type);
          if (mediaId) {
            mediaIds.push(mediaId);
          }
        }
      }

      // Create the main post
      const postData = {
        author: type === 'personal' ? `urn:li:person:${id}` : `urn:li:organization:${id}`,
        commentary: this.fixText(firstPost.message),
        visibility: 'PUBLIC',
        distribution: {
          feedDistribution: 'MAIN_FEED',
          targetEntities: [],
          thirdPartyDistributionChannels: [],
        },
        ...(mediaIds.length > 0 ? {
          content: mediaIds.length === 1 ? {
            media: { id: mediaIds[0] },
          } : {
            multiImage: {
              images: mediaIds.map((id) => ({ id })),
            },
          },
        } : {}),
        lifecycleState: 'PUBLISHED',
        isReshareDisabledByAuthor: false,
      };

      const response = await this.fetch('https://api.linkedin.com/v2/posts', {
        method: 'POST',
        headers: {
          'X-Restli-Protocol-Version': '2.0.0',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(postData),
      });

      if (response.status !== 201 && response.status !== 200) {
        throw new Error('Error posting to LinkedIn');
      }

      const topPostId = response.headers.get('x-restli-id')!;
      
      const results: PostResponse[] = [{
        status: 'posted',
        postId: topPostId,
        id: firstPost.id,
        releaseURL: `https://www.linkedin.com/feed/update/${topPostId}`,
      }];

      // Handle additional posts as comments
      for (const post of restPosts) {
        try {
          const commentResponse = await this.fetch(
            `https://api.linkedin.com/v2/socialActions/${decodeURIComponent(topPostId)}/comments`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken}`,
              },
              body: JSON.stringify({
                actor: type === 'personal' ? `urn:li:person:${id}` : `urn:li:organization:${id}`,
                object: topPostId,
                message: {
                  text: this.fixText(post.message),
                },
              }),
            }
          );

          const { object } = await commentResponse.json();
          
          results.push({
            status: 'posted',
            postId: object,
            id: post.id,
            releaseURL: `https://www.linkedin.com/embed/feed/update/${object}`,
          });
        } catch (error) {
          console.error('[LinkedInEnhanced] Comment posting error:', error);
          results.push({
            status: 'failed',
            postId: '',
            id: post.id,
            releaseURL: '',
          });
        }
      }

      return results;
    } catch (error) {
      console.error('[LinkedInEnhanced] Post error:', error);
      throw new Error(`LinkedIn posting failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  private async uploadMedia(
    media: MediaContent,
    accessToken: string,
    personId: string,
    type: 'personal' | 'company' = 'personal'
  ): Promise<string | null> {
    try {
      // This is a simplified version - full implementation would handle file reading and processing
      console.log('[LinkedInEnhanced] Media upload not fully implemented yet');
      return null;
    } catch (error) {
      console.error('[LinkedInEnhanced] Media upload error:', error);
      return null;
    }
  }
}
