import { NextResponse } from 'next/server';

// Error types that are safe to expose to users
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT = 'RATE_LIMIT_ERROR',
  CONFLICT = 'CONFLICT_ERROR',
  INTERNAL = 'INTERNAL_ERROR'
}

export interface ApiError {
  type: ErrorType;
  message: string;
  statusCode: number;
  details?: Record<string, any>;
}

// Safe error messages that don't expose internal details
const SAFE_ERROR_MESSAGES = {
  [ErrorType.VALIDATION]: 'Invalid input provided',
  [ErrorType.AUTHENTICATION]: 'Authentication required',
  [ErrorType.AUTHORIZATION]: 'Access denied',
  [ErrorType.NOT_FOUND]: 'Resource not found',
  [ErrorType.RATE_LIMIT]: 'Too many requests',
  [ErrorType.CONFLICT]: 'Resource conflict',
  [ErrorType.INTERNAL]: 'Internal server error'
};

const ERROR_STATUS_CODES = {
  [ErrorType.VALIDATION]: 400,
  [ErrorType.AUTHENTICATION]: 401,
  [ErrorType.AUTHORIZATION]: 403,
  [ErrorType.NOT_FOUND]: 404,
  [ErrorType.RATE_LIMIT]: 429,
  [ErrorType.CONFLICT]: 409,
  [ErrorType.INTERNAL]: 500
};

export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly details?: Record<string, any>;
  public readonly isOperational: boolean;

  constructor(
    type: ErrorType,
    message?: string,
    details?: Record<string, any>,
    isOperational = true
  ) {
    super(message || SAFE_ERROR_MESSAGES[type]);
    
    this.type = type;
    this.statusCode = ERROR_STATUS_CODES[type];
    this.details = details;
    this.isOperational = isOperational;
    
    // Maintain proper stack trace
    Error.captureStackTrace(this, AppError);
  }
}

// Predefined error creators
export const createValidationError = (message?: string, details?: Record<string, any>) =>
  new AppError(ErrorType.VALIDATION, message, details);

export const createAuthenticationError = (message?: string) =>
  new AppError(ErrorType.AUTHENTICATION, message);

export const createAuthorizationError = (message?: string) =>
  new AppError(ErrorType.AUTHORIZATION, message);

export const createNotFoundError = (resource?: string) =>
  new AppError(ErrorType.NOT_FOUND, resource ? `${resource} not found` : undefined);

export const createConflictError = (message?: string) =>
  new AppError(ErrorType.CONFLICT, message);

export const createInternalError = (message?: string) =>
  new AppError(ErrorType.INTERNAL, message);

// Error handler for API routes
export function handleApiError(error: unknown): NextResponse {
  // Log the full error for debugging (in development)
  if (process.env.NODE_ENV === 'development') {
    console.error('API Error:', error);
  }

  // Handle known AppError instances
  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: error.message,
        type: error.type,
        ...(error.details && { details: error.details })
      },
      { status: error.statusCode }
    );
  }

  // Handle Zod validation errors
  if (error && typeof error === 'object' && 'issues' in error) {
    const validationError = error as any;
    const message = validationError.issues
      ?.map((issue: any) => `${issue.path.join('.')}: ${issue.message}`)
      .join(', ') || 'Validation failed';
    
    return NextResponse.json(
      {
        error: message,
        type: ErrorType.VALIDATION
      },
      { status: 400 }
    );
  }

  // Handle Prisma/Database errors
  if (error && typeof error === 'object' && 'code' in error) {
    const dbError = error as any;
    
    // Common Prisma error codes
    switch (dbError.code) {
      case 'P2002':
        return NextResponse.json(
          {
            error: 'Resource already exists',
            type: ErrorType.CONFLICT
          },
          { status: 409 }
        );
      case 'P2025':
        return NextResponse.json(
          {
            error: 'Resource not found',
            type: ErrorType.NOT_FOUND
          },
          { status: 404 }
        );
      default:
        // Log database errors but don't expose details
        console.error('Database error:', dbError);
        break;
    }
  }

  // Handle Supabase errors
  if (error && typeof error === 'object' && 'message' in error) {
    const supabaseError = error as any;
    
    // Don't expose internal Supabase error details
    if (supabaseError.message?.includes('JWT')) {
      return NextResponse.json(
        {
          error: 'Authentication token invalid',
          type: ErrorType.AUTHENTICATION
        },
        { status: 401 }
      );
    }
  }

  // Handle network/fetch errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return NextResponse.json(
      {
        error: 'External service unavailable',
        type: ErrorType.INTERNAL
      },
      { status: 503 }
    );
  }

  // Log unexpected errors for debugging
  console.error('Unexpected error:', error);

  // Default to internal server error with no details exposed
  return NextResponse.json(
    {
      error: SAFE_ERROR_MESSAGES[ErrorType.INTERNAL],
      type: ErrorType.INTERNAL
    },
    { status: 500 }
  );
}

// Wrapper for API route handlers with error handling
export function withErrorHandler(
  handler: (request: Request) => Promise<NextResponse>
) {
  return async function(request: Request): Promise<NextResponse> {
    try {
      return await handler(request);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Helper to sanitize error messages for logging
export function sanitizeErrorForLogging(error: unknown): Record<string, any> {
  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      timestamp: new Date().toISOString()
    };
  }

  if (typeof error === 'object' && error !== null) {
    const sanitized: Record<string, any> = {};
    
    // Only include safe properties
    const safeProps = ['message', 'code', 'status', 'type'];
    for (const prop of safeProps) {
      if (prop in error) {
        sanitized[prop] = (error as any)[prop];
      }
    }
    
    return {
      ...sanitized,
      timestamp: new Date().toISOString()
    };
  }

  return {
    error: String(error),
    timestamp: new Date().toISOString()
  };
}
