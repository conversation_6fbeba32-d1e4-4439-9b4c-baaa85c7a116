import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for creating jobs
const createJobSchema = z.object({
  jobType: z.enum(['publish-post', 'generate-recurring-posts', 'process-bulk-import', 'cleanup-old-data']),
  jobData: z.object({}).passthrough(),
  priority: z.number().min(0).max(10).optional(),
  scheduledAt: z.string().datetime().optional(),
  maxAttempts: z.number().min(1).max(10).optional(),
});

// GET /api/scheduler/jobs - List jobs with filtering
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching scheduler jobs...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const jobType = searchParams.get('jobType');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabase
      .from('job_queue')
      .select('*')
      .eq('created_by', user.id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (jobType) {
      query = query.eq('job_type', jobType);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: jobs, error } = await query;

    if (error) {
      console.error('Error fetching jobs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch jobs' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('job_queue')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', user.id);

    if (status) {
      countQuery = countQuery.eq('status', status);
    }

    if (jobType) {
      countQuery = countQuery.eq('job_type', jobType);
    }

    const { count: totalCount } = await countQuery;

    // Calculate job statistics
    const { data: statsData } = await supabase
      .from('job_queue')
      .select('status')
      .eq('created_by', user.id);

    const stats = {
      total: statsData?.length || 0,
      pending: statsData?.filter(j => j.status === 'pending').length || 0,
      processing: statsData?.filter(j => j.status === 'processing').length || 0,
      completed: statsData?.filter(j => j.status === 'completed').length || 0,
      failed: statsData?.filter(j => j.status === 'failed').length || 0,
      cancelled: statsData?.filter(j => j.status === 'cancelled').length || 0,
    };

    console.log(`Found ${jobs?.length || 0} jobs for user`);

    return NextResponse.json({
      success: true,
      data: {
        jobs: jobs || [],
        pagination: {
          total: totalCount || 0,
          limit,
          offset,
          hasMore: (offset + limit) < (totalCount || 0),
        },
        statistics: stats,
        filters: {
          status,
          jobType,
        },
      },
    });

  } catch (error: any) {
    console.error('Error fetching jobs:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch jobs',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/scheduler/jobs - Create a new job
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Creating new scheduler job...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = createJobSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validation.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const { jobType, jobData, priority, scheduledAt, maxAttempts } = validation.data;

    // Validate job data based on job type
    const validationResult = validateJobData(jobType, jobData);
    if (!validationResult.valid) {
      return NextResponse.json(
        { error: validationResult.error },
        { status: 400 }
      );
    }

    // Create job
    const { data: job, error } = await supabase
      .from('job_queue')
      .insert({
        job_type: jobType,
        job_data: jobData,
        priority: priority || 0,
        max_attempts: maxAttempts || 3,
        scheduled_at: scheduledAt ? new Date(scheduledAt).toISOString() : new Date().toISOString(),
        created_by: user.id,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating job:', error);
      return NextResponse.json(
        { error: 'Failed to create job' },
        { status: 500 }
      );
    }

    console.log(`Job created successfully: ${job.id} (${jobType})`);

    return NextResponse.json({
      success: true,
      data: job,
      message: 'تم إنشاء المهمة بنجاح',
    });

  } catch (error: any) {
    console.error('Error creating job:', error);
    return NextResponse.json(
      {
        error: 'Failed to create job',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper function to validate job data based on job type
function validateJobData(jobType: string, jobData: any): { valid: boolean; error?: string } {
  switch (jobType) {
    case 'publish-post':
      if (!jobData.postId) {
        return { valid: false, error: 'postId is required for publish-post jobs' };
      }
      if (!jobData.platforms || !Array.isArray(jobData.platforms) || jobData.platforms.length === 0) {
        return { valid: false, error: 'platforms array is required for publish-post jobs' };
      }
      break;

    case 'generate-recurring-posts':
      if (!jobData.recurringPostId) {
        return { valid: false, error: 'recurringPostId is required for generate-recurring-posts jobs' };
      }
      if (jobData.days && (typeof jobData.days !== 'number' || jobData.days < 1 || jobData.days > 365)) {
        return { valid: false, error: 'days must be a number between 1 and 365' };
      }
      break;

    case 'process-bulk-import':
      if (!jobData.operationId) {
        return { valid: false, error: 'operationId is required for process-bulk-import jobs' };

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
      }
      break;

    case 'cleanup-old-data':
      if (jobData.daysToKeep && (typeof jobData.daysToKeep !== 'number' || jobData.daysToKeep < 1)) {
        return { valid: false, error: 'daysToKeep must be a positive number' };
      }
      break;

    default:
      return { valid: false, error: `Unknown job type: ${jobType}` };
  }

  return { valid: true };
}

// DELETE /api/scheduler/jobs - Bulk delete jobs
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Bulk deleting scheduler jobs...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { jobIds, status } = body;

    if (!jobIds && !status) {
      return NextResponse.json(
        { error: 'Either jobIds array or status filter is required' },
        { status: 400 }
      );
    }

    let query = supabase
      .from('job_queue')
      .delete()
      .eq('created_by', user.id);

    if (jobIds && Array.isArray(jobIds)) {
      query = query.in('id', jobIds);
    }

    if (status) {
      // Only allow deletion of completed, failed, or cancelled jobs
      const allowedStatuses = ['completed', 'failed', 'cancelled'];
      if (!allowedStatuses.includes(status)) {
        return NextResponse.json(
          { error: 'Can only delete completed, failed, or cancelled jobs' },
          { status: 400 }
        );
      }
      query = query.eq('status', status);
    }

    const { count, error } = await query;

    if (error) {
      console.error('Error deleting jobs:', error);
      return NextResponse.json(
        { error: 'Failed to delete jobs' },
        { status: 500 }
      );
    }

    const deletedCount = count || 0;
    console.log(`Deleted ${deletedCount} jobs`);

    return NextResponse.json({
      success: true,
      data: {
        deletedCount,
      },
      message: `تم حذف ${deletedCount} مهمة`,
    });

  } catch (error: any) {
    console.error('Error deleting jobs:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete jobs',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
