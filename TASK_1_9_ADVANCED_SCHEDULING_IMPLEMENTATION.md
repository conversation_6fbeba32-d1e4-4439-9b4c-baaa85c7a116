# 🗓️ TASK 1.9: Advanced Content Scheduling System - Implementation Guide

## 📋 **TASK OVERVIEW**

**Priority**: 🔴 CRITICAL
**Estimated Time**: 5-7 days
**Dependencies**: Completed Tasks 1.7, 1.8
**Goal**: Transform basic scheduling into enterprise-grade scheduling system

---

## 🎯 **CURRENT STATE vs TARGET STATE**

### **Current Implementation (60% Complete)**
- ✅ Basic post scheduling with date/time picker
- ✅ Single post scheduling
- ✅ Calendar view (basic)
- ⚠️ Limited calendar functionality
- ❌ No recurring posts
- ❌ No bulk operations
- ❌ No optimal time suggestions

### **Target Implementation (95% Complete)**
- ✅ Advanced recurring post patterns
- ✅ Bulk scheduling with CSV import
- ✅ Drag-and-drop calendar interface
- ✅ AI-powered optimal posting times
- ✅ Queue management system
- ✅ Time zone optimization
- ✅ Performance optimized for 1000+ posts

---

## 🗄️ **DATABASE SCHEMA IMPLEMENTATION**

### **Step 1: Create Migration Files**

```sql
-- Migration: 001_add_advanced_scheduling.sql
-- Add recurring post support
ALTER TABLE posts ADD COLUMN IF NOT EXISTS recurring_pattern JSONB;
<PERSON><PERSON><PERSON> TABLE posts ADD COLUMN IF NOT EXISTS recurring_end_date TIMESTAMPTZ;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS parent_recurring_id UUID REFERENCES posts(id);
ALTER TABLE posts ADD COLUMN IF NOT EXISTS is_recurring_parent BOOLEAN DEFAULT false;

-- Add scheduling metadata
ALTER TABLE posts ADD COLUMN IF NOT EXISTS optimal_time_used BOOLEAN DEFAULT false;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS time_zone TEXT DEFAULT 'UTC';
ALTER TABLE posts ADD COLUMN IF NOT EXISTS scheduling_notes TEXT;

-- Create optimal posting times table
CREATE TABLE IF NOT EXISTS optimal_posting_times (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  platform platform_type NOT NULL,
  day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6),
  hour_of_day INTEGER CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
  engagement_score DECIMAL(5,2) DEFAULT 0,
  sample_size INTEGER DEFAULT 0,
  last_calculated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, platform, day_of_week, hour_of_day)
);

-- Create bulk operations tracking
CREATE TABLE IF NOT EXISTS bulk_operations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  operation_type TEXT NOT NULL CHECK (operation_type IN ('bulk_schedule', 'bulk_import', 'bulk_delete')),
  total_items INTEGER NOT NULL DEFAULT 0,
  completed_items INTEGER DEFAULT 0,
  failed_items INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  error_details JSONB,
  file_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);

-- Create scheduling queue for performance
CREATE TABLE IF NOT EXISTS scheduling_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
  scheduled_for TIMESTAMPTZ NOT NULL,
  status TEXT DEFAULT 'queued' CHECK (status IN ('queued', 'processing', 'published', 'failed')),
  retry_count INTEGER DEFAULT 0,
  last_attempt TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  INDEX idx_scheduling_queue_scheduled_for (scheduled_for),
  INDEX idx_scheduling_queue_status (status)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON posts(scheduled_at) WHERE scheduled_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_posts_recurring_parent ON posts(parent_recurring_id) WHERE parent_recurring_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_optimal_times_user_platform ON optimal_posting_times(user_id, platform);
```

---

## 🔧 **API ENDPOINTS IMPLEMENTATION**

### **Step 2: Create Recurring Posts API**

```typescript
// File: src/app/api/posts/recurring/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

const recurringPostSchema = z.object({
  content: z.string().min(1).max(2800),
  media_url: z.string().url().optional(),
  social_account_ids: z.array(z.string()).min(1),
  recurring_pattern: z.object({
    frequency: z.enum(['daily', 'weekly', 'monthly']),
    interval: z.number().min(1).max(30), // Every N days/weeks/months
    days_of_week: z.array(z.number().min(0).max(6)).optional(), // For weekly
    day_of_month: z.number().min(1).max(31).optional(), // For monthly
    time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:MM format
  }),
  start_date: z.string().datetime(),
  end_date: z.string().datetime().optional(),
  time_zone: z.string().default('UTC'),
});

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validation = recurringPostSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { content, media_url, social_account_ids, recurring_pattern, start_date, end_date, time_zone } = validation.data;

    // Create parent recurring post
    const { data: parentPost, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content,
        media_url,
        status: 'SCHEDULED',
        is_recurring_parent: true,
        recurring_pattern,
        recurring_end_date: end_date ? new Date(end_date).toISOString() : null,
        time_zone,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating recurring post:', createError);
      return NextResponse.json({ error: 'Failed to create recurring post' }, { status: 500 });
    }

    // Generate individual post instances
    const instances = generateRecurringInstances(
      parentPost,
      recurring_pattern,
      new Date(start_date),
      end_date ? new Date(end_date) : null
    );

    // Insert post instances
    const { data: createdInstances, error: instancesError } = await supabase
      .from('posts')
      .insert(instances.map(instance => ({
        ...instance,
        user_id: user.id,
        parent_recurring_id: parentPost.id,
      })))
      .select();

    if (instancesError) {
      console.error('Error creating post instances:', instancesError);
      return NextResponse.json({ error: 'Failed to create post instances' }, { status: 500 });
    }

    // Create social account associations
    for (const instance of createdInstances) {
      await supabase
        .from('post_social_accounts')
        .insert(
          social_account_ids.map(accountId => ({
            post_id: instance.id,
            social_account_id: accountId,
          }))
        );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'RECURRING_POST_CREATED',
        details: `Created recurring post with ${createdInstances.length} instances`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      parentPost,
      instancesCreated: createdInstances.length,
      instances: createdInstances,
    });

  } catch (error) {
    console.error('Recurring post creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to generate recurring instances
function generateRecurringInstances(
  parentPost: any,
  pattern: any,
  startDate: Date,
  endDate: Date | null
): any[] {
  const instances = [];
  const maxInstances = 100; // Prevent infinite loops
  let currentDate = new Date(startDate);
  
  // Set time from pattern
  const [hours, minutes] = pattern.time.split(':').map(Number);
  currentDate.setHours(hours, minutes, 0, 0);

  for (let i = 0; i < maxInstances; i++) {
    if (endDate && currentDate > endDate) break;

    // Check if this date matches the pattern
    if (shouldCreateInstance(currentDate, pattern)) {
      instances.push({
        content: parentPost.content,
        media_url: parentPost.media_url,
        status: 'SCHEDULED',
        scheduled_at: currentDate.toISOString(),
        time_zone: parentPost.time_zone,
        is_recurring_parent: false,
      });
    }

    // Advance to next date based on frequency
    advanceDate(currentDate, pattern);
  }

  return instances;
}

function shouldCreateInstance(date: Date, pattern: any): boolean {
  switch (pattern.frequency) {
    case 'daily':
      return true;
    case 'weekly':
      return pattern.days_of_week?.includes(date.getDay()) ?? true;
    case 'monthly':
      return pattern.day_of_month ? date.getDate() === pattern.day_of_month : date.getDate() === 1;
    default:
      return false;
  }
}

function advanceDate(date: Date, pattern: any): void {
  switch (pattern.frequency) {
    case 'daily':
      date.setDate(date.getDate() + pattern.interval);
      break;
    case 'weekly':
      date.setDate(date.getDate() + (7 * pattern.interval));
      break;
    case 'monthly':
      date.setMonth(date.getMonth() + pattern.interval);
      break;
  }
}
```

---

## 🎨 **UI COMPONENTS IMPLEMENTATION**

### **Step 3: Create Advanced Calendar Component**

```typescript
// File: src/components/scheduling/advanced-calendar.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

const localizer = momentLocalizer(moment);

interface ScheduledPost {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED';
  platforms: string[];
  isRecurring?: boolean;
}

interface AdvancedCalendarProps {
  posts: ScheduledPost[];
  onPostMove: (postId: string, newDate: Date) => Promise<void>;
  onPostClick: (post: ScheduledPost) => void;
  onDateSelect: (date: Date) => void;
}

export function AdvancedCalendar({ 
  posts, 
  onPostMove, 
  onPostClick, 
  onDateSelect 
}: AdvancedCalendarProps) {
  const [view, setView] = useState<'month' | 'week' | 'day'>('month');
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Custom event component
  const EventComponent = ({ event }: { event: ScheduledPost }) => (
    <div className="p-1 text-xs">
      <div className="font-medium truncate">{event.title}</div>
      <div className="flex gap-1 mt-1">
        {event.platforms.map(platform => (
          <Badge key={platform} variant="secondary" className="text-xs px-1">
            {platform.slice(0, 2)}
          </Badge>
        ))}
        {event.isRecurring && (
          <Badge variant="outline" className="text-xs px-1">🔄</Badge>
        )}
      </div>
    </div>
  );

  // Handle drag and drop
  const handleEventDrop = async ({ event, start }: { event: ScheduledPost; start: Date }) => {
    try {
      await onPostMove(event.id, start);
      toast.success('تم تحديث موعد المنشور بنجاح');
    } catch (error) {
      toast.error('فشل في تحديث موعد المنشور');
    }
  };

  // Custom event style getter
  const eventStyleGetter = (event: ScheduledPost) => {
    let backgroundColor = '#3174ad';
    
    switch (event.status) {
      case 'PUBLISHED':
        backgroundColor = '#10b981';
        break;
      case 'FAILED':
        backgroundColor = '#ef4444';
        break;
      case 'DRAFT':
        backgroundColor = '#6b7280';
        break;
      default:
        backgroundColor = '#3b82f6';
    }

    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block'
      }
    };
  };

  return (
    <Card className="w-full h-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>تقويم المحتوى</CardTitle>
          <div className="flex gap-2">
            <Button
              variant={view === 'month' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('month')}
            >
              شهر
            </Button>
            <Button
              variant={view === 'week' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('week')}
            >
              أسبوع
            </Button>
            <Button
              variant={view === 'day' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setView('day')}
            >
              يوم
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div style={{ height: '600px' }}>
          <Calendar
            localizer={localizer}
            events={posts}
            startAccessor="start"
            endAccessor="end"
            view={view}
            onView={setView}
            date={selectedDate}
            onNavigate={setSelectedDate}
            onSelectEvent={onPostClick}
            onSelectSlot={({ start }) => onDateSelect(start)}
            onEventDrop={handleEventDrop}
            eventPropGetter={eventStyleGetter}
            components={{
              event: EventComponent,
            }}
            draggableAccessor={() => true}
            resizable={false}
            selectable
            popup
            messages={{
              next: 'التالي',
              previous: 'السابق',
              today: 'اليوم',
              month: 'شهر',
              week: 'أسبوع',
              day: 'يوم',
              agenda: 'جدول الأعمال',
              date: 'التاريخ',
              time: 'الوقت',
              event: 'المنشور',
              noEventsInRange: 'لا توجد منشورات في هذا النطاق',
            }}
            formats={{
              monthHeaderFormat: 'MMMM YYYY',
              dayHeaderFormat: 'dddd, MMMM DD',
              dayRangeHeaderFormat: ({ start, end }) =>
                `${moment(start).format('MMM DD')} - ${moment(end).format('MMM DD, YYYY')}`,
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
```

---

## 🧪 **TESTING PROTOCOL**

### **Step 4: Create Comprehensive Tests**

```typescript
// File: src/__tests__/scheduling/recurring-posts.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { generateRecurringInstances } from '@/lib/scheduling/recurring';

describe('Recurring Posts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should generate daily recurring posts correctly', () => {
    const pattern = {
      frequency: 'daily',
      interval: 1,
      time: '09:00'
    };
    
    const startDate = new Date('2024-01-01T09:00:00Z');
    const endDate = new Date('2024-01-05T09:00:00Z');
    
    const instances = generateRecurringInstances(
      { content: 'Test post', media_url: null },
      pattern,
      startDate,
      endDate
    );
    
    expect(instances).toHaveLength(5);
    expect(instances[0].scheduled_at).toBe('2024-01-01T09:00:00.000Z');
    expect(instances[4].scheduled_at).toBe('2024-01-05T09:00:00.000Z');
  });

  it('should generate weekly recurring posts correctly', () => {
    const pattern = {
      frequency: 'weekly',
      interval: 1,
      days_of_week: [1, 3, 5], // Monday, Wednesday, Friday
      time: '14:30'
    };
    
    const startDate = new Date('2024-01-01T14:30:00Z'); // Monday
    const endDate = new Date('2024-01-15T14:30:00Z');
    
    const instances = generateRecurringInstances(
      { content: 'Test post', media_url: null },
      pattern,
      startDate,
      endDate
    );
    
    // Should have posts for Mon, Wed, Fri of each week
    expect(instances.length).toBeGreaterThan(0);
    
    // Verify days of week
    instances.forEach(instance => {
      const date = new Date(instance.scheduled_at);
      expect([1, 3, 5]).toContain(date.getDay());
    });
  });
});
```

---

## ✅ **ACCEPTANCE CRITERIA CHECKLIST**

### **Functional Requirements**
- [ ] Users can create daily/weekly/monthly recurring posts
- [ ] Recurring patterns support custom intervals (every N days/weeks/months)
- [ ] Weekly posts can specify specific days of the week
- [ ] Monthly posts can specify day of the month
- [ ] Users can set start and end dates for recurring series
- [ ] Individual instances can be edited without affecting the series
- [ ] Users can delete entire recurring series or individual instances

### **Performance Requirements**
- [ ] Calendar loads in <2 seconds with 500+ scheduled posts
- [ ] Drag-and-drop rescheduling responds in <500ms
- [ ] Recurring post generation completes in <5 seconds
- [ ] Database queries are optimized with proper indexing

### **User Experience Requirements**
- [ ] Intuitive recurring post creation form
- [ ] Visual indicators for recurring posts in calendar
- [ ] Drag-and-drop functionality works smoothly
- [ ] Mobile-responsive calendar interface
- [ ] Arabic RTL support maintained throughout

### **Integration Requirements**
- [ ] Integrates with existing post creation system
- [ ] Works with all connected social media platforms
- [ ] Maintains compatibility with publishing system
- [ ] Preserves user authentication and permissions

---

## 🚀 **DEPLOYMENT STEPS**

1. **Database Migration**: Run schema updates on production database
2. **API Testing**: Test all new endpoints with Postman/automated tests
3. **UI Testing**: Verify calendar functionality across browsers
4. **Performance Testing**: Load test with 1000+ posts
5. **User Acceptance Testing**: Test with real users and feedback
6. **Production Deployment**: Deploy to DigitalOcean with monitoring
7. **Post-Deployment Monitoring**: Monitor for errors and performance issues

**Estimated Implementation Time**: 5-7 days for full-stack developer
**Dependencies**: Requires Tasks 1.7 and 1.8 to be completed
**Risk Level**: Medium - Complex calendar interactions and recurring logic
