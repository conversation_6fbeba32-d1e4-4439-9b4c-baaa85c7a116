import { createClient } from '@supabase/supabase-js';

// Test database schema and RLS policies
describe('Team Collaboration Database Tests', () => {
  let supabase: any;
  let testUserId: string;
  let testOrgId: string;
  let testWorkspaceId: string;

  beforeAll(async () => {
    // Initialize Supabase client for testing
    supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Create test user
    const { data: user, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'testpassword123',
      email_confirm: true,
    });

    if (error) throw error;
    testUserId = user.user.id;
  });

  afterAll(async () => {
    // Cleanup test data
    if (testOrgId) {
      await supabase.from('organizations').delete().eq('id', testOrgId);
    }
    if (testUserId) {
      await supabase.auth.admin.deleteUser(testUserId);
    }
  });

  describe('Organizations Table', () => {
    test('should create organization with valid data', async () => {
      const orgData = {
        name: 'Test Organization',
        slug: 'test-org',
        description: 'Test organization for unit tests',
        subscription_plan: 'free',
        created_by: testUserId,
      };

      const { data, error } = await supabase
        .from('organizations')
        .insert(orgData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.name).toBe(orgData.name);
      expect(data.slug).toBe(orgData.slug);
      expect(data.max_users).toBe(3); // Free plan default
      
      testOrgId = data.id;
    });

    test('should enforce unique slug constraint', async () => {
      const duplicateOrgData = {
        name: 'Another Test Organization',
        slug: 'test-org', // Same slug as above
        created_by: testUserId,
      };

      const { error } = await supabase
        .from('organizations')
        .insert(duplicateOrgData);

      expect(error).toBeDefined();
      expect(error.code).toBe('23505'); // Unique violation
    });

    test('should set correct limits based on subscription plan', async () => {
      const proOrgData = {
        name: 'Pro Test Organization',
        slug: 'pro-test-org',
        subscription_plan: 'pro',
        created_by: testUserId,
      };

      const { data, error } = await supabase
        .from('organizations')
        .insert(proOrgData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data.max_users).toBe(25); // Pro plan limit
      expect(data.max_social_accounts).toBe(50);
      expect(data.max_posts_per_month).toBe(1000);

      // Cleanup
      await supabase.from('organizations').delete().eq('id', data.id);
    });
  });

  describe('Organization Members Table', () => {
    test('should add organization owner', async () => {
      const memberData = {
        organization_id: testOrgId,
        user_id: testUserId,
        role: 'owner',
        status: 'active',
      };

      const { data, error } = await supabase
        .from('organization_members')
        .insert(memberData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data.role).toBe('owner');
      expect(data.status).toBe('active');
    });

    test('should enforce unique user per organization', async () => {
      const duplicateMemberData = {
        organization_id: testOrgId,
        user_id: testUserId,
        role: 'admin',
        status: 'active',
      };

      const { error } = await supabase
        .from('organization_members')
        .insert(duplicateMemberData);

      expect(error).toBeDefined();
      expect(error.code).toBe('23505'); // Unique violation
    });
  });

  describe('Workspaces Table', () => {
    test('should create workspace within organization', async () => {
      const workspaceData = {
        organization_id: testOrgId,
        name: 'Test Workspace',
        slug: 'test-workspace',
        description: 'Test workspace for unit tests',
        color: '#3b82f6',
        created_by: testUserId,
      };

      const { data, error } = await supabase
        .from('workspaces')
        .insert(workspaceData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data.name).toBe(workspaceData.name);
      expect(data.slug).toBe(workspaceData.slug);
      expect(data.is_active).toBe(true);
      
      testWorkspaceId = data.id;
    });

    test('should enforce unique slug within organization', async () => {
      const duplicateWorkspaceData = {
        organization_id: testOrgId,
        name: 'Another Test Workspace',
        slug: 'test-workspace', // Same slug as above
        created_by: testUserId,
      };

      const { error } = await supabase
        .from('workspaces')
        .insert(duplicateWorkspaceData);

      expect(error).toBeDefined();
      expect(error.code).toBe('23505'); // Unique violation
    });
  });

  describe('Workspace Members Table', () => {
    test('should add workspace admin', async () => {
      const memberData = {
        workspace_id: testWorkspaceId,
        user_id: testUserId,
        role: 'admin',
        status: 'active',
        added_by: testUserId,
      };

      const { data, error } = await supabase
        .from('workspace_members')
        .insert(memberData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data.role).toBe('admin');
      expect(data.status).toBe('active');
    });
  });

  describe('Approval Workflows Table', () => {
    test('should create default approval workflow', async () => {
      const workflowData = {
        workspace_id: testWorkspaceId,
        name: 'Default Approval',
        description: 'Default approval workflow',
        steps: [
          {
            step: 1,
            name: 'Content Review',
            required_approvers: 1,
            approver_roles: ['admin', 'editor'],
            auto_approve: false,
          }
        ],
        is_default: true,
        created_by: testUserId,
      };

      const { data, error } = await supabase
        .from('approval_workflows')
        .insert(workflowData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data.name).toBe(workflowData.name);
      expect(data.is_default).toBe(true);
      expect(data.steps).toHaveLength(1);
    });
  });

  describe('RLS Policies', () => {
    test('should enforce organization access control', async () => {
      // Create another user
      const { data: anotherUser } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'testpassword123',
        email_confirm: true,
      });

      // Try to access organization as unauthorized user
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', testOrgId);

      // Should return empty result due to RLS
      expect(data).toEqual([]);

      // Cleanup
      await supabase.auth.admin.deleteUser(anotherUser.user.id);
    });

    test('should allow organization member access', async () => {
      // Set auth context to test user
      await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword123',
      });

      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', testOrgId);

      expect(error).toBeNull();
      expect(data).toHaveLength(1);
      expect(data[0].id).toBe(testOrgId);
    });
  });

  describe('Database Functions', () => {
    test('should get user organizations', async () => {
      const { data, error } = await supabase
        .rpc('get_user_organizations', { user_uuid: testUserId });

      expect(error).toBeNull();
      expect(data).toHaveLength(1);
      expect(data[0].organization_id).toBe(testOrgId);
      expect(data[0].user_role).toBe('owner');
    });

    test('should check workspace permissions', async () => {
      const { data, error } = await supabase
        .rpc('check_workspace_permission', {
          user_uuid: testUserId,
          workspace_uuid: testWorkspaceId,
          required_permission: 'write'
        });

      expect(error).toBeNull();
      expect(data).toBe(true); // Admin should have write permission
    });
  });

  describe('Triggers and Constraints', () => {
    test('should update updated_at timestamp on organization update', async () => {
      const originalData = await supabase
        .from('organizations')
        .select('updated_at')
        .eq('id', testOrgId)
        .single();

      // Wait a moment to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 1000));

      const { data: updatedData } = await supabase
        .from('organizations')
        .update({ description: 'Updated description' })
        .eq('id', testOrgId)
        .select('updated_at')
        .single();

      expect(new Date(updatedData.data.updated_at).getTime())
        .toBeGreaterThan(new Date(originalData.data.updated_at).getTime());
    });

    test('should cascade delete organization members when organization is deleted', async () => {
      // Create temporary organization
      const { data: tempOrg } = await supabase
        .from('organizations')
        .insert({
          name: 'Temp Org',
          slug: 'temp-org',
          created_by: testUserId,
        })
        .select()
        .single();

      // Add member
      await supabase
        .from('organization_members')
        .insert({
          organization_id: tempOrg.id,
          user_id: testUserId,
          role: 'owner',
          status: 'active',
        });

      // Delete organization
      await supabase
        .from('organizations')
        .delete()
        .eq('id', tempOrg.id);

      // Check that member was also deleted
      const { data: members } = await supabase
        .from('organization_members')
        .select('*')
        .eq('organization_id', tempOrg.id);

      expect(members).toHaveLength(0);
    });
  });
});
