/**
 * Facebook Graph API Service V2
 * Complete implementation for Facebook Graph API with page management,
 * media upload, and comprehensive error handling
 */

export interface FacebookPage {
  id: string;
  name: string;
  category: string;
  accessToken: string;
  permissions: string[];
  fanCount?: number;
  profilePictureUrl?: string;
  about?: string;
  website?: string;
  canPost?: boolean;
}

export interface FacebookUser {
  id: string;
  name: string;
  email?: string;
  profilePictureUrl?: string;
}

export interface FacebookPublishResult {
  success: boolean;
  postId?: string;
  url?: string;
  error?: string;
  platformResponse?: any;
}

export interface FacebookMediaUpload {
  mediaType: 'photo' | 'video';
  uploadSessionId?: string;
  mediaId?: string;
  processingStatus: 'uploading' | 'processing' | 'ready' | 'error';
}

export interface PostContent {
  content: string;
  mediaUrl?: string;
  mediaType?: 'IMAGE' | 'VIDEO';
  link?: string;
  linkPreview?: {
    title?: string;
    description?: string;
    imageUrl?: string;
  };
}

export class FacebookGraphService {
  private readonly baseUrl = 'https://graph.facebook.com/v19.0';

  /**
   * Get user information from Facebook
   */
  async getUserInfo(accessToken: string): Promise<FacebookUser> {
    try {
      console.log('Fetching Facebook user info...');

      const response = await fetch(
        `${this.baseUrl}/me?fields=id,name,email,picture.width(200).height(200)&access_token=${accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch user info: ${response.statusText}`);
      }

      const userData = await response.json();

      if (userData.error) {
        throw new Error(`Facebook API error: ${userData.error.message}`);
      }

      console.log('Facebook user info fetched successfully:', userData.name);

      return {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        profilePictureUrl: userData.picture?.data?.url,
      };

    } catch (error) {
      console.error('Facebook user info fetch error:', error);
      throw error;
    }
  }

  /**
   * Get Facebook pages managed by the user
   */
  async getUserPages(accessToken: string): Promise<FacebookPage[]> {
    try {
      console.log('Fetching Facebook pages...');

      const response = await fetch(
        `${this.baseUrl}/me/accounts?fields=id,name,category,access_token,fan_count,picture.width(200).height(200),about,website,perms&access_token=${accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch pages: ${response.statusText}`);
      }

      const pagesData = await response.json();

      if (pagesData.error) {
        throw new Error(`Facebook API error: ${pagesData.error.message}`);
      }

      const pages: FacebookPage[] = pagesData.data.map((page: any) => ({
        id: page.id,
        name: page.name,
        category: page.category,
        accessToken: page.access_token,
        permissions: page.perms || [],
        fanCount: page.fan_count,
        profilePictureUrl: page.picture?.data?.url,
        about: page.about,
        website: page.website,
        canPost: page.perms?.includes('CREATE_CONTENT') || page.perms?.includes('MODERATE'),
      }));

      console.log(`Found ${pages.length} Facebook pages`);
      return pages;

    } catch (error) {
      console.error('Facebook pages fetch error:', error);
      throw error;
    }
  }

  /**
   * Publish a post to Facebook page
   */
  async publishPost(page: FacebookPage, content: PostContent): Promise<FacebookPublishResult> {
    try {
      console.log('Publishing to Facebook page:', {
        pageId: page.id,
        pageName: page.name,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
        hasLink: !!content.link
      });

      // Check if page can post
      if (!page.canPost) {
        throw new Error('Insufficient permissions to post to this page');
      }

      let postData: any = {
        message: content.content,
        access_token: page.accessToken
      };

      // Handle media upload if present
      if (content.mediaUrl) {
        if (content.mediaType === 'VIDEO') {
          const mediaId = await this.uploadVideo(page, content.mediaUrl);
          postData = {
            ...postData,
            source: content.mediaUrl, // For video posts
          };
        } else {
          // For images, we can use the URL directly or upload
          postData.url = content.mediaUrl;
        }
      }

      // Handle link sharing
      if (content.link) {
        postData.link = content.link;
        if (content.linkPreview) {
          postData.name = content.linkPreview.title;
          postData.description = content.linkPreview.description;
          if (content.linkPreview.imageUrl) {
            postData.picture = content.linkPreview.imageUrl;
          }
        }
      }

      // Determine the correct endpoint based on content type
      let endpoint = `${this.baseUrl}/${page.id}/feed`;
      if (content.mediaUrl && content.mediaType === 'IMAGE') {
        endpoint = `${this.baseUrl}/${page.id}/photos`;
      } else if (content.mediaUrl && content.mediaType === 'VIDEO') {
        endpoint = `${this.baseUrl}/${page.id}/videos`;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Facebook API error: ${errorData.error?.message || response.statusText}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(`Facebook posting error: ${result.error.message}`);
      }

      const postUrl = `https://www.facebook.com/${result.id}`;

      console.log('Facebook post published successfully:', {
        postId: result.id,
        postUrl
      });

      return {
        success: true,
        postId: result.id,
        url: postUrl,
        platformResponse: result
      };

    } catch (error) {
      console.error('Facebook publishing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Upload video to Facebook
   */
  private async uploadVideo(page: FacebookPage, videoUrl: string): Promise<string> {
    try {
      console.log('Uploading video to Facebook:', { videoUrl });

      // For now, we'll use the direct URL approach
      // In production, you might want to implement resumable uploads for large videos
      const response = await fetch(
        `${this.baseUrl}/${page.id}/videos`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            file_url: videoUrl,
            access_token: page.accessToken
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Video upload failed: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(`Facebook video upload error: ${result.error.message}`);
      }

      console.log('Facebook video uploaded successfully:', result.id);
      return result.id;

    } catch (error) {
      console.error('Facebook video upload error:', error);
      throw error;
    }
  }

  /**
   * Get page insights and analytics
   */
  async getPageInsights(page: FacebookPage, since?: Date, until?: Date): Promise<any> {
    try {
      console.log('Fetching Facebook page insights:', page.name);

      const sinceParam = since ? `&since=${Math.floor(since.getTime() / 1000)}` : '';
      const untilParam = until ? `&until=${Math.floor(until.getTime() / 1000)}` : '';

      const metrics = [
        'page_impressions',
        'page_reach',
        'page_fan_adds',
        'page_fan_removes',
        'page_views_total',
        'page_posts_impressions',
        'page_engaged_users'
      ].join(',');

      const response = await fetch(
        `${this.baseUrl}/${page.id}/insights?metric=${metrics}${sinceParam}${untilParam}&access_token=${page.accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch insights: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook insights error: ${data.error.message}`);
      }

      return data;

    } catch (error) {
      console.error('Facebook insights fetch error:', error);
      throw error;
    }
  }

  /**
   * Get post insights and analytics
   */
  async getPostInsights(postId: string, accessToken: string): Promise<any> {
    try {
      console.log('Fetching Facebook post insights:', postId);

      const metrics = [
        'post_impressions',
        'post_reach',
        'post_reactions_like_total',
        'post_reactions_love_total',
        'post_reactions_wow_total',
        'post_reactions_haha_total',
        'post_reactions_sorry_total',
        'post_reactions_anger_total',
        'post_clicks',
        'post_shares'
      ].join(',');

      const response = await fetch(
        `${this.baseUrl}/${postId}/insights?metric=${metrics}&access_token=${accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch post insights: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook post insights error: ${data.error.message}`);
      }

      return data;

    } catch (error) {
      console.error('Facebook post insights fetch error:', error);
      throw error;
    }
  }

  /**
   * Test Facebook page connection
   */
  async testConnection(page: FacebookPage): Promise<any> {
    try {
      console.log('Testing Facebook page connection:', {
        pageId: page.id,
        pageName: page.name
      });

      const response = await fetch(
        `${this.baseUrl}/${page.id}?fields=id,name,category,fan_count,about&access_token=${page.accessToken}`
      );

      if (!response.ok) {
        throw new Error(`Connection test failed: ${response.statusText}`);
      }

      const pageData = await response.json();

      if (pageData.error) {
        throw new Error(`Facebook API error: ${pageData.error.message}`);
      }

      console.log('Facebook page connection test successful:', {
        name: pageData.name,
        category: pageData.category,
        fanCount: pageData.fan_count
      });

      return {
        success: true,
        pageInfo: {
          id: pageData.id,
          name: pageData.name,
          category: pageData.category,
          fanCount: pageData.fan_count,
          about: pageData.about
        }
      };

    } catch (error) {
      console.error('Facebook connection test failed:', error);
      throw error;
    }
  }

  /**
   * Validate Facebook access token
   */
  async validateToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.baseUrl}/me?access_token=${accessToken}`
      );

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      return !data.error;

    } catch (error) {
      console.warn('Facebook token validation failed:', error);
      return false;
    }
  }

  /**
   * Get Facebook rate limit status
   */
  async getRateLimitStatus(accessToken: string): Promise<any> {
    try {
      // Facebook doesn't provide explicit rate limit headers like Twitter
      // But we can check the app usage through the Graph API
      const response = await fetch(
        `${this.baseUrl}/me?fields=id&access_token=${accessToken}`,
        { method: 'HEAD' }
      );

      return {
        status: response.status,
        remaining: response.headers.get('X-App-Usage'),
        businessUseCase: response.headers.get('X-Business-Use-Case-Usage'),
        adAccount: response.headers.get('X-Ad-Account-Usage')
      };

    } catch (error) {
      console.error('Error checking Facebook rate limits:', error);
      return null;
    }
  }
}

export default FacebookGraphService;
