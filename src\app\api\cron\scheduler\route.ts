import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Cron job endpoint for processing scheduled posts
 * This endpoint can be called by external cron services like:
 * - Vercel Cron Jobs
 * - GitHub Actions
 * - External monitoring services
 * - Uptime monitoring tools
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Cron job: Processing scheduled posts...');

    // Verify cron job authentication
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'default-cron-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      console.warn('Unauthorized cron job attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use service role client for admin operations
    

    // Get current time
    const now = new Date();
    const nowISO = now.toISOString();

    // Find posts that are due for publishing
    const { data: duePosts, error: postsError } = await supabase
      .from('posts')
      .select(`
        id,
        user_id,
        content,
        media_url,
        platforms,
        scheduled_at,
        status
      `)
      .eq('status', 'SCHEDULED')
      .lte('scheduled_at', nowISO)
      .order('scheduled_at', { ascending: true })
      .limit(100); // Process up to 100 posts at a time

    if (postsError) {
      console.error('Error fetching due posts:', postsError);
      return NextResponse.json(
        { error: 'Failed to fetch due posts' },
        { status: 500 }
      );
    }

    if (!duePosts || duePosts.length === 0) {
      console.log('No posts due for publishing');
      return NextResponse.json({
        success: true,
        message: 'No posts due for publishing',
        processed: 0,
        timestamp: nowISO,
      });
    }

    console.log(`Found ${duePosts.length} posts due for publishing`);

    // Process each due post
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const post of duePosts) {
      try {
        // Update post status to PROCESSING
        await supabase
          .from('posts')
          .update({
            status: 'PROCESSING',
            updated_at: nowISO,
          })
          .eq('id', post.id);

        // Get user's social accounts for the platforms
        const { data: socialAccounts, error: accountsError } = await supabase
          .from('social_accounts')
          .select('id, platform, access_token, is_active')
          .eq('user_id', post.user_id)
          .in('platform', post.platforms)
          .eq('is_active', true);

        if (accountsError || !socialAccounts || socialAccounts.length === 0) {
          throw new Error('No active social accounts found for platforms: ' + post.platforms.join(', '));
        }

        // Create jobs for each platform
        const jobPromises = post.platforms.map(async (platform) => {
          const socialAccount = socialAccounts.find(acc => acc.platform === platform);
          
          if (!socialAccount) {
            return {
              platform,
              success: false,
              error: 'No active social account found',
            };
          }

          try {
            const { data: job, error: jobError } = await supabase
              .from('job_queue')
              .insert({
                job_type: 'publish-post',
                job_data: {
                  postId: post.id,
                  userId: post.user_id,
                  content: post.content,
                  mediaUrl: post.media_url,
                  platform,
                  socialAccountId: socialAccount.id,
                },
                priority: 10, // High priority for cron jobs
                max_attempts: 3,
                scheduled_at: nowISO,
                created_by: post.user_id,
              })
              .select()
              .single();

            if (jobError) {
              throw new Error(`Failed to create job: ${jobError.message}`);
            }

            return {
              platform,
              success: true,
              jobId: job.id,
            };

          } catch (platformError) {
            console.error(`Error creating job for platform ${platform}:`, platformError);
            return {
              platform,
              success: false,
              error: platformError.message,
            };
          }
        });

        const platformResults = await Promise.all(jobPromises);
        const anySuccessful = platformResults.some(r => r.success);

        if (!anySuccessful) {
          // All platforms failed - mark as FAILED
          await supabase
            .from('posts')
            .update({
              status: 'FAILED',
              updated_at: nowISO,
            })
            .eq('id', post.id);
          
          errorCount++;
        } else {
          successCount++;
        }

        results.push({
          postId: post.id,
          scheduledAt: post.scheduled_at,
          platforms: platformResults,
          success: anySuccessful,
        });

      } catch (postError) {
        console.error(`Error processing post ${post.id}:`, postError);
        
        // Mark post as failed
        await supabase
          .from('posts')
          .update({
            status: 'FAILED',
            updated_at: nowISO,
          })
          .eq('id', post.id);

        results.push({
          postId: post.id,
          scheduledAt: post.scheduled_at,
          success: false,
          error: postError.message,
        });

        errorCount++;
      }
    }

    // Log cron job completion
    console.log(`Cron job completed: ${duePosts.length} posts processed, ${successCount} successful, ${errorCount} failed`);

    // Record cron job execution
    await supabase
      .from('scheduler_logs')
      .insert({
        level: 'info',
        message: 'Cron job executed',
        metadata: {
          processed: duePosts.length,
          successful: successCount,
          failed: errorCount,
          timestamp: nowISO,
        },
      });

    return NextResponse.json({
      success: true,
      message: `Cron job completed: ${duePosts.length} posts processed`,
      processed: duePosts.length,
      successful: successCount,
      failed: errorCount,
      results,
      timestamp: nowISO,
    });

  } catch (error) {
    console.error('Cron job error:', error);
    
    // Log error
    try {
      
      await supabase
        .from('scheduler_logs')
        .insert({
          level: 'error',
          message: 'Cron job failed',
          metadata: {
            error: error.message,
            timestamp: new Date().toISOString(),
          },
        });
    } catch (logError) {
      console.error('Failed to log cron job error:', logError);
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Cron job failed',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST method for manual triggering
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  // Same logic as GET but with different authentication
  return GET(request);
}
