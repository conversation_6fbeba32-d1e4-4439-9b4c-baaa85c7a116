"use client";

import { useState } from "react";
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle, User, Mail, Lock, RefreshCw } from "lucide-react";

export default function TestAuthSystemPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [testEmail, setTestEmail] = useState("<EMAIL>");
  const [testPassword, setTestPassword] = useState("test123456");
  const [currentUser, setCurrentUser] = useState<any>(null);

  const addResult = (test: string, status: 'success' | 'error' | 'warning', message: string, details?: any) => {
    const result = {
      id: Date.now(),
      test,
      status,
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev]);
    
    if (status === 'success') {
      toast.success(`✅ ${test}: ${message}`);
    } else if (status === 'error') {
      toast.error(`❌ ${test}: ${message}`);
    } else {
      toast.warning(`⚠️ ${test}: ${message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    toast.info("تم مسح نتائج الاختبار");
  };

  const checkCurrentUser = async () => {
    try {
      const supabase = createClient();
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        addResult("Current User Check", "error", error.message);
        setCurrentUser(null);
      } else if (user) {
        addResult("Current User Check", "success", `User logged in: ${user.email}`);
        setCurrentUser(user);
      } else {
        addResult("Current User Check", "warning", "No user currently logged in");
        setCurrentUser(null);
      }
    } catch (error: any) {
      addResult("Current User Check", "error", error.message);
      setCurrentUser(null);
    }
  };

  const testSignUp = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();
      
      // First, try to sign up
      const { data, error } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
        options: {
          data: {
            name: "Test User",
            role: 'USER'
          }
        }
      });

      if (error) {
        addResult("Sign Up Test", "error", error.message, { error });
      } else if (data.user) {
        addResult("Sign Up Test", "success", `User created: ${data.user.email}`, { 
          user: data.user,
          session: data.session ? "Session created" : "Email confirmation required"
        });
      } else {
        addResult("Sign Up Test", "warning", "No user data returned");
      }
    } catch (error: any) {
      addResult("Sign Up Test", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testSignIn = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword,
      });

      if (error) {
        addResult("Sign In Test", "error", error.message, { error });
      } else if (data.user && data.session) {
        addResult("Sign In Test", "success", `Signed in: ${data.user.email}`, { 
          user: data.user,
          session: data.session
        });
        setCurrentUser(data.user);
      } else {
        addResult("Sign In Test", "warning", "Sign in returned no user/session");
      }
    } catch (error: any) {
      addResult("Sign In Test", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testPasswordReset = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();
      
      const { error } = await supabase.auth.resetPasswordForEmail(testEmail, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        addResult("Password Reset Test", "error", error.message, { error });
      } else {
        addResult("Password Reset Test", "success", `Reset email sent to: ${testEmail}`);
      }
    } catch (error: any) {
      addResult("Password Reset Test", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testSignOut = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();
      
      const { error } = await supabase.auth.signOut();

      if (error) {
        addResult("Sign Out Test", "error", error.message, { error });
      } else {
        addResult("Sign Out Test", "success", "Successfully signed out");
        setCurrentUser(null);
      }
    } catch (error: any) {
      addResult("Sign Out Test", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const runAllTests = async () => {
    clearResults();
    addResult("Test Suite", "warning", "Starting comprehensive authentication tests...");
    
    await checkCurrentUser();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testSignUp();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testSignIn();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testPasswordReset();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testSignOut();
    
    addResult("Test Suite", "success", "All tests completed!");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <span className="text-3xl font-bold text-white">🔐</span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
            Authentication System Test
          </h1>
          <p className="text-gray-600 text-lg">اختبار شامل لنظام المصادقة</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="space-y-6">
            {/* Current User Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Current User Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                {currentUser ? (
                  <div className="space-y-2">
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      Logged In
                    </Badge>
                    <p className="text-sm text-gray-600">
                      <strong>Email:</strong> {currentUser.email}
                    </p>
                    <p className="text-sm text-gray-600">
                      <strong>ID:</strong> {currentUser.id}
                    </p>
                  </div>
                ) : (
                  <Badge variant="secondary">Not Logged In</Badge>
                )}
                <Button 
                  onClick={checkCurrentUser} 
                  variant="outline" 
                  size="sm" 
                  className="mt-3"
                  disabled={isLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Status
                </Button>
              </CardContent>
            </Card>

            {/* Test Configuration */}
            <Card>
              <CardHeader>
                <CardTitle>Test Configuration</CardTitle>
                <CardDescription>Configure test credentials</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Test Email</label>
                  <Input
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Test Password</label>
                  <Input
                    type="password"
                    value={testPassword}
                    onChange={(e) => setTestPassword(e.target.value)}
                    placeholder="test123456"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Test Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Test Actions</CardTitle>
                <CardDescription>Run individual or comprehensive tests</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={runAllTests} 
                  className="w-full"
                  disabled={isLoading}
                >
                  🚀 Run All Tests
                </Button>
                
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    onClick={testSignUp} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Sign Up
                  </Button>
                  <Button 
                    onClick={testSignIn} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Sign In
                  </Button>
                  <Button 
                    onClick={testPasswordReset} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Reset Password
                  </Button>
                  <Button 
                    onClick={testSignOut} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Sign Out
                  </Button>
                </div>
                
                <Button 
                  onClick={clearResults} 
                  variant="ghost" 
                  className="w-full"
                >
                  Clear Results
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Test Results */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
                <CardDescription>
                  {testResults.length} test results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {testResults.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">
                      No test results yet. Run some tests to see results here.
                    </p>
                  ) : (
                    testResults.map((result) => (
                      <div
                        key={result.id}
                        className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                      >
                        <div className="flex items-start gap-3">
                          {getStatusIcon(result.status)}
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">{result.test}</h4>
                              <span className="text-xs text-gray-500">
                                {result.timestamp}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {result.message}
                            </p>
                            {result.details && (
                              <details className="mt-2">
                                <summary className="text-xs text-gray-500 cursor-pointer">
                                  View Details
                                </summary>
                                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                                  {JSON.stringify(result.details, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
