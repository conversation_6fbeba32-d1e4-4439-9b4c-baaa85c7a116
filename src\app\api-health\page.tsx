"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  details?: any;
  error?: string;
}

interface SystemHealthResponse {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: HealthCheckResult[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
}

export default function APIHealthPage() {
  const [healthData, setHealthData] = useState<SystemHealthResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<string | null>(null);

  const runHealthCheck = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/system/health');
      const data = await response.json();
      setHealthData(data);
      setLastChecked(new Date().toLocaleString());
    } catch (error) {
      console.error('Health check failed:', error);
      setHealthData({
        overall: 'unhealthy',
        timestamp: new Date().toISOString(),
        services: [],
        summary: { total: 0, healthy: 0, unhealthy: 0, degraded: 0 }
      });
    } finally {
      setLoading(false);
    }
  };

  const testSpecificService = async (service: string) => {
    try {
      const response = await fetch('/api/system/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ service })
      });
      const result = await response.json();
      console.log(`${service} test result:`, result);
      
      // Update the specific service in the health data
      if (healthData) {
        const updatedServices = healthData.services.map(s => 
          s.service.toLowerCase().includes(service) ? result : s
        );
        setHealthData({
          ...healthData,
          services: updatedServices
        });
      }
    } catch (error) {
      console.error(`${service} test failed:`, error);
    }
  };

  useEffect(() => {
    runHealthCheck();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'degraded':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'unhealthy':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'healthy' ? 'default' : 
                   status === 'degraded' ? 'secondary' : 'destructive';
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>;
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">API Health Dashboard</h1>
        <p className="text-gray-600">Monitor the health and connectivity of all integrated APIs</p>
      </div>

      {/* Overall Status */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {healthData && getStatusIcon(healthData.overall)}
              <CardTitle>System Status</CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              {healthData && getStatusBadge(healthData.overall)}
              <Button 
                onClick={runHealthCheck} 
                disabled={loading}
                size="sm"
                variant="outline"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
            </div>
          </div>
          <CardDescription>
            {lastChecked && `Last checked: ${lastChecked}`}
          </CardDescription>
        </CardHeader>
        {healthData && (
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{healthData.summary.healthy}</div>
                <div className="text-sm text-gray-500">Healthy</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{healthData.summary.degraded}</div>
                <div className="text-sm text-gray-500">Degraded</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{healthData.summary.unhealthy}</div>
                <div className="text-sm text-gray-500">Unhealthy</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{healthData.summary.total}</div>
                <div className="text-sm text-gray-500">Total</div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Individual Services */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {healthData?.services.map((service, index) => (
          <Card key={index}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(service.status)}
                  <CardTitle className="text-lg">{service.service}</CardTitle>
                </div>
                {getStatusBadge(service.status)}
              </div>
              <CardDescription>
                Response time: {service.responseTime}ms
              </CardDescription>
            </CardHeader>
            <CardContent>
              {service.status === 'healthy' && service.details && (
                <div className="space-y-2">
                  {Object.entries(service.details).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="text-gray-500 capitalize">{key.replace(/_/g, ' ')}:</span>
                      <span className="font-medium">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              )}
              
              {service.error && (
                <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
                  {service.error}
                </div>
              )}

              <div className="mt-4">
                <Button 
                  onClick={() => testSpecificService(service.service.toLowerCase().split(' ')[0])}
                  size="sm"
                  variant="outline"
                  className="w-full"
                >
                  Test {service.service}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {loading && !healthData && (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Running health checks...</span>
        </div>
      )}

      {/* API Integration Status */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>API Integration Status</CardTitle>
          <CardDescription>Current status of API integrations and next steps</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium">DigitalOcean API</span>
              </div>
              <Badge variant="default">CONNECTED</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium">Stripe API</span>
              </div>
              <Badge variant="default">CONNECTED</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-blue-500" />
                <span className="font-medium">Database (Supabase)</span>
              </div>
              <Badge variant="secondary">READY</Badge>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">✅ Ready for Implementation</h4>
            <p className="text-sm text-gray-600">
              Both DigitalOcean and Stripe APIs are successfully connected and ready for integration. 
              You can now proceed with implementing the payment system and deployment automation features.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
