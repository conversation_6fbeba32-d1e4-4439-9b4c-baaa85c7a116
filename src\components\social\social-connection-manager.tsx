'use client';

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Trash2,
  Refresh<PERSON><PERSON>,
  CheckCircle,
  AlertCircle,
  Clock,
  ExternalLink,
  Settings,
  TestTube,
  Send,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

interface SocialAccount {
  id: string;
  platform: string;
  account_id: string;
  account_name: string;
  status: 'connected' | 'expired' | 'error';
  isExpired: boolean;
  daysUntilExpiry: number | null;
  created_at: string;
  updated_at: string;
}

interface SocialConnectionManagerProps {
  className?: string;
}

export function SocialConnectionManager({ className = '' }: SocialConnectionManagerProps) {
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);
  const [availablePlatforms, setAvailablePlatforms] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [testing, setTesting] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [showTesting, setShowTesting] = useState(false);

  useEffect(() => {
    loadConnectionStatus();
  }, []);

  const loadConnectionStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/social/connect');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load connection status');
      }

      setAccounts(data.connectedAccounts || []);
      setAvailablePlatforms(data.availablePlatforms || []);
    } catch (error: any) {
      console.error('Error loading connection status:', error);
      toast.error('فشل في تحميل حالة الاتصال');
    } finally {
      setIsLoading(false);
    }
  };

  const connectPlatform = async (platform: string) => {
    setConnecting(platform);
    try {
      const response = await fetch('/api/social/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ platform }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to initiate connection');
      }

      // Add popup parameter to auth URL
      const authUrlWithPopup = data.authUrl + (data.authUrl.includes('?') ? '&' : '?') + 'popup=true';

      // Open OAuth popup
      const popup = window.open(
        authUrlWithPopup,
        'social-auth',
        'width=600,height=600,scrollbars=yes,resizable=yes'
      );

      // Listen for postMessage from popup
      const handleMessage = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'oauth_success') {
          clearInterval(checkClosed);
          setConnecting(null);
          window.removeEventListener('message', handleMessage);

          // Show success message
          toast.success(`✅ ${event.data.platform} connected successfully!`);

          // Reload connection status
          loadConnectionStatus();
        }
      };

      window.addEventListener('message', handleMessage);

      // Monitor popup for completion (fallback)
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          setConnecting(null);
          window.removeEventListener('message', handleMessage);
          // Reload connection status after a short delay
          setTimeout(() => {
            loadConnectionStatus();
          }, 1000);
        }
      }, 1000);

      // Auto-close after 5 minutes
      setTimeout(() => {
        if (popup && !popup.closed) {
          popup.close();
          clearInterval(checkClosed);
          setConnecting(null);
          toast.error('انتهت مهلة الاتصال');
        }
      }, 300000);

    } catch (error: any) {
      console.error('Connection error:', error);
      toast.error(error.message || 'فشل في بدء عملية الاتصال');
      setConnecting(null);
    }
  };

  const disconnectAccount = async (accountId: string, platform: string) => {
    try {
      console.log('🔌 Frontend: Starting disconnect process', { accountId, platform });

      // Show loading state
      toast.loading('جاري قطع الاتصال...', { id: 'disconnect-loading' });

      const response = await fetch('/api/social/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId }),
      });

      // Dismiss loading toast
      toast.dismiss('disconnect-loading');

      const data = await response.json();
      console.log('📡 Frontend: Disconnect response', {
        status: response.status,
        success: data.success,
        error: data.error
      });

      if (!response.ok) {
        console.error('❌ Frontend: Disconnect failed', data);
        throw new Error(data.error || data.details || 'Failed to disconnect account');
      }

      if (!data.success) {
        console.error('❌ Frontend: Disconnect unsuccessful', data);
        throw new Error(data.error || 'Disconnect was not successful');
      }

      // ENHANCED: Update UI state with better error handling
      console.log('✅ Frontend: Disconnect successful, updating UI');
      setAccounts(prevAccounts => {
        const updatedAccounts = prevAccounts.filter(acc => acc.id !== accountId);
        console.log('📊 Frontend: Updated accounts count', {
          before: prevAccounts.length,
          after: updatedAccounts.length
        });
        return updatedAccounts;
      });

      setAvailablePlatforms(prevPlatforms => {
        const updatedPlatforms = [...prevPlatforms, platform];
        console.log('📋 Frontend: Updated available platforms', updatedPlatforms);
        return updatedPlatforms;
      });

      toast.success(`تم قطع الاتصال مع ${getPlatformName(platform)} بنجاح`);
      console.log('🎉 Frontend: Disconnect process completed successfully');

    } catch (error: any) {
      console.error('💥 Frontend: Disconnect error:', error);
      toast.dismiss('disconnect-loading');
      toast.error(`فشل في قطع الاتصال: ${error.message || 'خطأ غير معروف'}`);
    }
  };

  const refreshConnection = async (accountId: string, platform: string) => {
    try {
      // This would trigger a token refresh
      toast.info('جاري تحديث الاتصال...');
      // Implementation would depend on platform-specific refresh logic
      await loadConnectionStatus();
      toast.success('تم تحديث الاتصال بنجاح');
    } catch (error: any) {
      console.error('Refresh error:', error);
      toast.error('فشل في تحديث الاتصال');
    }
  };

  const testConnection = async (account: SocialAccount) => {
    setTesting(account.id);
    try {
      const response = await fetch('/api/social-accounts/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: account.platform,
          accountId: account.account_id, // Optional specific account ID
        }),
      });

      const result = await response.json();
      setTestResults(prev => ({
        ...prev,
        [`${account.id}_connection`]: result
      }));

      if (result.success) {
        toast.success(`✅ اختبار الاتصال مع ${getPlatformName(account.platform)} نجح`);
      } else {
        toast.error(`❌ اختبار الاتصال مع ${getPlatformName(account.platform)} فشل: ${result.error}`);
      }
    } catch (error: any) {
      toast.error(`خطأ في اختبار الاتصال: ${error.message}`);
    } finally {
      setTesting(null);
    }
  };

  const testPublish = async (account: SocialAccount) => {
    setTesting(`${account.id}_publish`);
    try {
      const testContent = `[اختبار] مرحباً من منصة eWasl! 🚀 هذا منشور تجريبي لاختبار التكامل مع ${getPlatformName(account.platform)}. #eWasl #اختبار`;

      const response = await fetch('/api/posts/test-publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: account.platform,
          content: testContent,
          accountId: account.account_id,
          isTest: true,
        }),
      });

      const result = await response.json();
      setTestResults(prev => ({
        ...prev,
        [`${account.id}_publish`]: result
      }));

      if (result.success) {
        toast.success(`✅ اختبار النشر على ${getPlatformName(account.platform)} نجح!`);
        if (result.url) {
          toast.info(`🔗 رابط المنشور: ${result.url}`);
        }
      } else {
        toast.error(`❌ اختبار النشر على ${getPlatformName(account.platform)} فشل: ${result.error}`);
      }
    } catch (error: any) {
      toast.error(`خطأ في اختبار النشر: ${error.message}`);
    } finally {
      setTesting(null);
    }
  };

  const getPlatformIcon = (platform: string): string => {
    const icons: Record<string, string> = {
      TWITTER: '🐦',
      FACEBOOK: '📘',
      INSTAGRAM: '📷',
      LINKEDIN: '💼',
      TIKTOK: '🎵',
      SNAPCHAT: '👻',
    };
    return icons[platform] || '🔗';
  };

  const getPlatformName = (platform: string): string => {
    const names: Record<string, string> = {
      TWITTER: 'تويتر',
      FACEBOOK: 'فيسبوك',
      INSTAGRAM: 'إنستغرام',
      LINKEDIN: 'لينكد إن',
      TIKTOK: 'تيك توك',
      SNAPCHAT: 'سناب شات',
    };
    return names[platform] || platform;
  };

  const getPlatformColor = (platform: string): string => {
    const colors: Record<string, string> = {
      TWITTER: '#1DA1F2',
      FACEBOOK: '#1877F2',
      INSTAGRAM: '#E4405F',
      LINKEDIN: '#0A66C2',
      TIKTOK: '#000000',
      SNAPCHAT: '#FFFC00',
    };
    return colors[platform] || '#6B7280';
  };

  const getStatusIcon = (account: SocialAccount) => {
    if (account.isExpired) {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    if (account.status === 'connected') {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    return <Clock className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusText = (account: SocialAccount): string => {
    if (account.isExpired) {
      return 'منتهي الصلاحية';
    }
    if (account.status === 'connected') {
      return account.daysUntilExpiry
        ? `متصل (${account.daysUntilExpiry} يوم متبقي)`
        : 'متصل';
    }
    return 'غير متصل';
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-2" />
          <p className="text-gray-600">جاري تحميل الحسابات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Connected Accounts */}
      {accounts.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            الحسابات المتصلة ({accounts.length})
          </h3>
          <div className="grid gap-4">
            {accounts.map((account) => (
              <div
                key={account.id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-white hover:shadow-md transition-shadow"
              >
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-full flex items-center justify-center text-white text-lg font-bold"
                    style={{ backgroundColor: getPlatformColor(account.platform) }}
                  >
                    {getPlatformIcon(account.platform)}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">@{account.account_name}</h4>
                      {getStatusIcon(account)}
                    </div>
                    <p className="text-sm text-gray-600">
                      {getPlatformName(account.platform)} • {getStatusText(account)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => testConnection(account)}
                    disabled={testing === account.id}
                    className="px-3 py-1 text-sm bg-green-50 text-green-700 border border-green-200 rounded-md hover:bg-green-100 transition-colors disabled:opacity-50"
                  >
                    {testing === account.id ? (
                      <>
                        <Loader2 className="h-3 w-3 animate-spin inline mr-1" />
                        جاري الاختبار...
                      </>
                    ) : (
                      'اختبار الاتصال'
                    )}
                  </button>
                  <button
                    onClick={() => testPublish(account)}
                    disabled={testing === `${account.id}_publish`}
                    className="px-3 py-1 text-sm bg-purple-50 text-purple-700 border border-purple-200 rounded-md hover:bg-purple-100 transition-colors disabled:opacity-50"
                  >
                    {testing === `${account.id}_publish` ? (
                      <>
                        <Loader2 className="h-3 w-3 animate-spin inline mr-1" />
                        جاري النشر...
                      </>
                    ) : (
                      'اختبار النشر'
                    )}
                  </button>
                  {account.isExpired && (
                    <button
                      onClick={() => refreshConnection(account.id, account.platform)}
                      className="px-3 py-1 text-sm bg-blue-50 text-blue-700 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
                    >
                      <RefreshCw className="h-3 w-3 inline mr-1" />
                      تحديث الاتصال
                    </button>
                  )}
                  <button
                    onClick={() => disconnectAccount(account.id, account.platform)}
                    className="px-3 py-1 text-sm bg-red-50 text-red-700 border border-red-200 rounded-md hover:bg-red-100 transition-colors"
                  >
                    <Trash2 className="h-3 w-3 inline mr-1" />
                    قطع الاتصال
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Testing Results */}
      {Object.keys(testResults).length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <TestTube className="h-5 w-5 text-purple-500" />
            نتائج الاختبارات
          </h3>
          <div className="space-y-2">
            {Object.entries(testResults).map(([key, result]) => (
              <div
                key={key}
                className={`p-3 rounded-lg border ${
                  result.success
                    ? 'bg-green-50 border-green-200 text-green-800'
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}
              >
                <div className="flex items-center gap-2">
                  {result.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <span className="font-medium">
                    {key.includes('_publish') ? 'اختبار النشر' : 'اختبار الاتصال'}
                  </span>
                </div>
                <p className="text-sm mt-1">{result.message}</p>
                {result.data?.url && (
                  <a
                    href={result.data.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm underline hover:no-underline"
                  >
                    عرض المنشور
                  </a>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Platforms */}
      {availablePlatforms.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Plus className="h-5 w-5 text-blue-500" />
            منصات متاحة للربط ({availablePlatforms.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availablePlatforms.map((platform) => (
              <button
                key={platform}
                onClick={() => connectPlatform(platform)}
                disabled={connecting === platform}
                className="flex items-center gap-3 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center text-white text-lg"
                  style={{ backgroundColor: getPlatformColor(platform) }}
                >
                  {connecting === platform ? (
                    <RefreshCw className="h-5 w-5 animate-spin" />
                  ) : (
                    getPlatformIcon(platform)
                  )}
                </div>
                <div className="text-right">
                  <h4 className="font-medium">{getPlatformName(platform)}</h4>
                  <p className="text-sm text-gray-600">
                    {connecting === platform ? 'جاري الربط...' : 'ربط الحساب التجاري'}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {accounts.length === 0 && availablePlatforms.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔗</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            لا توجد حسابات متصلة
          </h3>
          <p className="text-gray-600 mb-4">
            ابدأ بربط حساباتك الاجتماعية لبدء النشر والجدولة
          </p>
          <button
            onClick={loadConnectionStatus}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            إعادة تحميل
          </button>
        </div>
      )}
    </div>
  );
}

export default SocialConnectionManager;
