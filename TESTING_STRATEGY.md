# 🧪 **SOCIAL MEDIA INTEGRATIONS - COMPREHENSIVE TESTING STRATEGY**

## **🎯 TESTING OVERVIEW**

This document outlines the comprehensive testing strategy for enhancing eWasl's social media integrations, ensuring reliability, security, and performance across all platforms.

## **📊 TESTING PYRAMID**

```
                    🔺 E2E Tests (10%)
                   /                 \
                  /   Integration     \
                 /     Tests (30%)     \
                /                       \
               /_________________________\
                    Unit Tests (60%)
```

## **🔬 UNIT TESTING STRATEGY**

### **OAuth Services Testing**

```typescript
// tests/unit/oauth/linkedin-oauth.test.ts
describe('LinkedInOAuthService', () => {
  let service: LinkedInOAuthService;
  let mockFetch: jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    service = new LinkedInOAuthService();
    mockFetch = jest.fn();
    global.fetch = mockFetch;
  });

  describe('token refresh', () => {
    it('should successfully refresh valid token', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          access_token: 'new_token',
          expires_in: 3600,
          refresh_token: 'new_refresh_token'
        })
      } as Response);

      const result = await service.refreshToken('old_refresh_token');
      
      expect(result.success).toBe(true);
      expect(result.accessToken).toBe('new_token');
      expect(mockFetch).toHaveBeenCalledWith(
        'https://www.linkedin.com/oauth/v2/accessToken',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded'
          })
        })
      );
    });

    it('should handle expired refresh token', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({
          error: 'invalid_grant',
          error_description: 'refresh token expired'
        })
      } as Response);

      const result = await service.refreshToken('expired_token');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('expired');
    });
  });

  describe('scope validation', () => {
    it('should validate required scopes', () => {
      const requiredScopes = ['w_member_social', 'r_basicprofile'];
      const userScopes = ['w_member_social', 'r_basicprofile', 'r_emailaddress'];
      
      const isValid = service.validateScopes(requiredScopes, userScopes);
      expect(isValid).toBe(true);
    });

    it('should reject insufficient scopes', () => {
      const requiredScopes = ['w_member_social', 'r_basicprofile'];
      const userScopes = ['r_basicprofile'];
      
      const isValid = service.validateScopes(requiredScopes, userScopes);
      expect(isValid).toBe(false);
    });
  });
});
```

### **Publisher Services Testing**

```typescript
// tests/unit/publishers/linkedin-publisher.test.ts
describe('LinkedInPublisherV2', () => {
  let publisher: LinkedInPublisherV2;
  let mockAccount: SocialAccount;
  let mockContent: PostContent;

  beforeEach(() => {
    publisher = new LinkedInPublisherV2();
    mockAccount = {
      id: 'account-1',
      platform: 'linkedin',
      access_token: 'valid_token',
      account_id: 'linkedin_user_id'
    };
    mockContent = {
      content: 'Test post content',
      mediaUrl: 'https://example.com/image.jpg'
    };
  });

  describe('publishPost', () => {
    it('should successfully publish text-only post', async () => {
      const mockResponse = {
        id: 'urn:li:ugcPost:*********',
        lifecycleState: 'PUBLISHED'
      };

      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      } as Response);

      const result = await publisher.publishPost(mockAccount, {
        content: 'Test post without media'
      });

      expect(result.success).toBe(true);
      expect(result.postId).toBe('*********');
      expect(result.url).toContain('linkedin.com/feed/update');
    });

    it('should handle API rate limiting', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: () => Promise.resolve({
          message: 'Rate limit exceeded'
        })
      } as Response);

      const result = await publisher.publishPost(mockAccount, mockContent);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit');
    });

    it('should handle invalid token', async () => {
      jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({
          message: 'Invalid access token'
        })
      } as Response);

      const result = await publisher.publishPost(mockAccount, mockContent);

      expect(result.success).toBe(false);
      expect(result.error).toContain('token');
    });
  });

  describe('media upload', () => {
    it('should upload media successfully', async () => {
      // Mock register upload
      jest.spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            value: {
              asset: 'urn:li:digitalmedia:asset:123',
              uploadMechanism: {
                'com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest': {
                  uploadUrl: 'https://upload.linkedin.com/123'
                }
              }
            }
          })
        } as Response)
        // Mock media download
        .mockResolvedValueOnce({
          ok: true,
          arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024))
        } as Response)
        // Mock upload
        .mockResolvedValueOnce({
          ok: true
        } as Response);

      const mediaUrn = await publisher['uploadMedia'](mockAccount, mockContent.mediaUrl);
      
      expect(mediaUrn).toBe('urn:li:digitalmedia:asset:123');
    });
  });
});
```

### **Content Formatter Testing**

```typescript
// tests/unit/content/content-formatter.test.ts
describe('ContentFormatter', () => {
  let formatter: ContentFormatter;

  beforeEach(() => {
    formatter = new ContentFormatter();
  });

  describe('platform-specific formatting', () => {
    it('should format content for LinkedIn', () => {
      const content = 'Check out this amazing product! #innovation #tech @company';
      const result = formatter.formatForPlatform(content, 'linkedin');

      expect(result.text).toContain('Check out this amazing product!');
      expect(result.hashtags).toEqual(['#innovation', '#tech']);
      expect(result.mentions).toEqual(['@company']);
      expect(result.characterCount).toBeLessThanOrEqual(3000);
      expect(result.isValid).toBe(true);
    });

    it('should truncate content for Twitter', () => {
      const longContent = 'A'.repeat(300);
      const result = formatter.formatForPlatform(longContent, 'twitter');

      expect(result.characterCount).toBeLessThanOrEqual(280);
      expect(result.text).toMatch(/\.\.\.$/); // Should end with ellipsis
      expect(result.warnings).toContain('Content was truncated');
    });

    it('should optimize hashtags for Instagram', () => {
      const content = 'Beautiful sunset! #nature #photography #beautiful #amazing #sunset #landscape #travel #adventure #outdoor #peaceful #serene #golden #hour #magic #moment #breathtaking #scenic #view #wanderlust #explore #discover #journey #memories #happiness #joy #life #love #blessed #grateful #thankful #inspiration #motivation #dreams #goals #success';
      const result = formatter.formatForPlatform(content, 'instagram');

      expect(result.hashtags.length).toBeLessThanOrEqual(30);
      expect(result.warnings).toContain('Hashtags were optimized');
    });
  });

  describe('Arabic content handling', () => {
    it('should handle RTL text correctly', () => {
      const arabicContent = 'مرحبا بكم في منصة إيواصل للتواصل الاجتماعي #تقنية #ابتكار';
      const result = formatter.formatForPlatform(arabicContent, 'linkedin');

      expect(result.isValid).toBe(true);
      expect(result.hashtags).toEqual(['#تقنية', '#ابتكار']);
      expect(result.text).toContain('مرحبا بكم');
    });

    it('should handle mixed Arabic-English content', () => {
      const mixedContent = 'Welcome مرحبا to eWasl! #tech #تقنية';
      const result = formatter.formatForPlatform(mixedContent, 'twitter');

      expect(result.isValid).toBe(true);
      expect(result.hashtags).toEqual(['#tech', '#تقنية']);
    });
  });
});
```

## **🔗 INTEGRATION TESTING STRATEGY**

### **OAuth Flow Integration Tests**

```typescript
// tests/integration/oauth-flow.test.ts
describe('OAuth Integration Flow', () => {
  let testServer: TestServer;
  let browser: Browser;

  beforeAll(async () => {
    testServer = await createTestServer();
    browser = await puppeteer.launch({ headless: false });
  });

  afterAll(async () => {
    await browser.close();
    await testServer.close();
  });

  describe('LinkedIn OAuth Flow', () => {
    it('should complete full OAuth authorization', async () => {
      const page = await browser.newPage();
      
      // Start OAuth flow
      await page.goto(`${testServer.url}/api/auth/linkedin`);
      
      // Should redirect to LinkedIn
      await page.waitForNavigation();
      expect(page.url()).toContain('linkedin.com/oauth');
      
      // Mock LinkedIn login (in test environment)
      await mockLinkedInLogin(page);
      
      // Should redirect back to callback
      await page.waitForNavigation();
      expect(page.url()).toContain('/api/auth/linkedin/callback');
      
      // Verify token storage
      const tokens = await testServer.getStoredTokens('linkedin');
      expect(tokens.access_token).toBeDefined();
      expect(tokens.refresh_token).toBeDefined();
      expect(tokens.expires_at).toBeGreaterThan(Date.now());
    });

    it('should handle OAuth errors gracefully', async () => {
      const page = await browser.newPage();
      
      // Start OAuth with invalid state
      await page.goto(`${testServer.url}/api/auth/linkedin?error=access_denied`);
      
      // Should show error page
      const errorText = await page.textContent('.error-message');
      expect(errorText).toContain('Authorization was denied');
    });
  });
});
```

### **Publishing Integration Tests**

```typescript
// tests/integration/publishing.test.ts
describe('Publishing Integration', () => {
  let testUser: TestUser;
  let socialAccount: SocialAccount;

  beforeEach(async () => {
    testUser = await createTestUser();
    socialAccount = await createTestSocialAccount(testUser.id, 'linkedin');
  });

  describe('Cross-platform publishing', () => {
    it('should publish to multiple platforms simultaneously', async () => {
      const postData = {
        content: 'Test post from eWasl integration test',
        platforms: ['linkedin', 'twitter'],
        scheduledAt: new Date(Date.now() + 60000).toISOString()
      };

      const response = await request(app)
        .post('/api/posts/schedule')
        .set('Authorization', `Bearer ${testUser.token}`)
        .send(postData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.jobs).toHaveLength(2);

      // Wait for jobs to process
      await waitForJobCompletion(response.body.data.jobs);

      // Verify posts were published
      const publishedPosts = await getPublishedPosts(testUser.id);
      expect(publishedPosts).toHaveLength(2);
      expect(publishedPosts.map(p => p.platform)).toEqual(['linkedin', 'twitter']);
    });

    it('should handle partial failures gracefully', async () => {
      // Mock one platform to fail
      mockPlatformFailure('twitter');

      const postData = {
        content: 'Test post with partial failure',
        platforms: ['linkedin', 'twitter'],
        scheduledAt: new Date(Date.now() + 60000).toISOString()
      };

      const response = await request(app)
        .post('/api/posts/schedule')
        .set('Authorization', `Bearer ${testUser.token}`)
        .send(postData)
        .expect(200);

      await waitForJobCompletion(response.body.data.jobs);

      const publishedPosts = await getPublishedPosts(testUser.id);
      const failedJobs = await getFailedJobs(testUser.id);

      expect(publishedPosts).toHaveLength(1);
      expect(publishedPosts[0].platform).toBe('linkedin');
      expect(failedJobs).toHaveLength(1);
      expect(failedJobs[0].platform).toBe('twitter');
    });
  });
});
```

## **🌐 END-TO-END TESTING STRATEGY**

### **User Journey Tests**

```typescript
// tests/e2e/user-journey.test.ts
describe('Complete User Journey', () => {
  let page: Page;

  beforeEach(async () => {
    page = await browser.newPage();
    await page.goto(process.env.TEST_BASE_URL);
  });

  it('should complete full social media management workflow', async () => {
    // 1. User registration and login
    await registerAndLogin(page, {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });

    // 2. Connect social accounts
    await connectSocialAccount(page, 'linkedin');
    await connectSocialAccount(page, 'twitter');

    // 3. Create and schedule post
    await page.click('[data-testid="create-post-button"]');
    await page.fill('[data-testid="post-content"]', 'My first scheduled post!');
    await page.selectOption('[data-testid="platforms"]', ['linkedin', 'twitter']);
    await page.fill('[data-testid="schedule-time"]', '2024-12-31T10:00');
    await page.click('[data-testid="schedule-button"]');

    // 4. Verify post was scheduled
    await page.waitForSelector('[data-testid="success-message"]');
    const successMessage = await page.textContent('[data-testid="success-message"]');
    expect(successMessage).toContain('Post scheduled successfully');

    // 5. Check dashboard
    await page.click('[data-testid="dashboard-link"]');
    const scheduledPosts = await page.$$('[data-testid="scheduled-post"]');
    expect(scheduledPosts).toHaveLength(1);

    // 6. View analytics (after post is published)
    await triggerScheduledPosts(); // Helper to process scheduled posts
    await page.reload();
    await page.click('[data-testid="analytics-tab"]');
    
    const analyticsData = await page.textContent('[data-testid="analytics-summary"]');
    expect(analyticsData).toContain('2 posts published');
  });
});
```

## **⚡ PERFORMANCE TESTING**

### **Load Testing Configuration**

```typescript
// tests/performance/load-test.ts
import { check } from 'k6';
import http from 'k6/http';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.05'],    // Error rate under 5%
  },
};

export default function () {
  // Test OAuth token refresh under load
  let response = http.post(`${__ENV.BASE_URL}/api/auth/refresh`, {
    refresh_token: __ENV.TEST_REFRESH_TOKEN,
  });
  
  check(response, {
    'token refresh successful': (r) => r.status === 200,
    'response time < 1s': (r) => r.timings.duration < 1000,
  });

  // Test post publishing under load
  response = http.post(`${__ENV.BASE_URL}/api/posts/schedule`, {
    content: 'Load test post',
    platforms: ['linkedin'],
    scheduledAt: new Date(Date.now() + 60000).toISOString(),
  }, {
    headers: {
      'Authorization': `Bearer ${__ENV.TEST_ACCESS_TOKEN}`,
      'Content-Type': 'application/json',
    },
  });

  check(response, {
    'post scheduling successful': (r) => r.status === 200,
    'response time < 5s': (r) => r.timings.duration < 5000,
  });
}
```

## **🔒 SECURITY TESTING**

### **Security Test Suite**

```typescript
// tests/security/security.test.ts
describe('Security Tests', () => {
  describe('Token Security', () => {
    it('should not expose tokens in logs', async () => {
      const response = await request(app)
        .post('/api/auth/linkedin/callback')
        .send({ code: 'test_code', state: 'test_state' });

      const logs = await getApplicationLogs();
      const tokenPattern = /access_token["\s]*[:=]["\s]*([^"\s,}]+)/gi;
      
      logs.forEach(log => {
        expect(log).not.toMatch(tokenPattern);
      });
    });

    it('should encrypt tokens at rest', async () => {
      const testUser = await createTestUser();
      const socialAccount = await createTestSocialAccount(testUser.id, 'linkedin');
      
      const rawDbRecord = await getRawDatabaseRecord('social_accounts', socialAccount.id);
      
      // Token should be encrypted, not plain text
      expect(rawDbRecord.access_token).not.toBe(socialAccount.access_token);
      expect(rawDbRecord.access_token).toMatch(/^[a-f0-9]{64,}$/); // Encrypted format
    });
  });

  describe('API Security', () => {
    it('should prevent SQL injection', async () => {
      const maliciousInput = "'; DROP TABLE social_accounts; --";
      
      const response = await request(app)
        .get(`/api/social/accounts?platform=${maliciousInput}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(400);

      // Verify table still exists
      const tableExists = await checkTableExists('social_accounts');
      expect(tableExists).toBe(true);
    });

    it('should prevent XSS attacks', async () => {
      const xssPayload = '<script>alert("xss")</script>';
      
      const response = await request(app)
        .post('/api/posts/schedule')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          content: xssPayload,
          platforms: ['linkedin']
        })
        .expect(400);

      expect(response.body.error).toContain('Invalid content');
    });
  });
});
```

## **📊 TEST REPORTING**

### **Coverage Requirements**

```json
{
  "jest": {
    "coverageThreshold": {
      "global": {
        "branches": 80,
        "functions": 85,
        "lines": 90,
        "statements": 90
      },
      "./src/lib/social/": {
        "branches": 90,
        "functions": 95,
        "lines": 95,
        "statements": 95
      }
    }
  }
}
```

### **Automated Testing Pipeline**

```yaml
# .github/workflows/test.yml
name: Comprehensive Testing

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:coverage

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run build
      - run: npm run test:e2e

  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm audit
      - run: npm run test:security
```

---

This comprehensive testing strategy ensures that all aspects of the social media integrations are thoroughly tested, from individual components to complete user workflows, with a focus on reliability, security, and performance.
