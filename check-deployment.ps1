# Check if the app is deployed successfully
Write-Host "Checking if app.ewasl.com is accessible..." -ForegroundColor Cyan

try {
    $response = Invoke-WebRequest -Uri "https://app.ewasl.com" -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "Success! The app is deployed and accessible at https://app.ewasl.com" -ForegroundColor Green
    } else {
        Write-Host "The app returned status code: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error accessing the app: $_" -ForegroundColor Red
    Write-Host "This could be due to:" -ForegroundColor Yellow
    Write-Host "1. The app is still deploying" -ForegroundColor Yellow
    Write-Host "2. DNS has not propagated yet" -ForegroundColor Yellow
    Write-Host "3. There's an issue with the deployment" -ForegroundColor Yellow
    
    Write-Host "`nTrying the DigitalOcean app URL instead..." -ForegroundColor Cyan
    try {
        $appUrl = "https://ewasl-social-scheduler-abcd1234.ondigitalocean.app"
        $response = Invoke-WebRequest -Uri $appUrl -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "Success! The app is deployed and accessible at $appUrl" -ForegroundColor Green
            Write-Host "DNS for app.ewasl.com may still be propagating" -ForegroundColor Yellow
        } else {
            Write-Host "The app returned status code: $($response.StatusCode) from $appUrl" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Error accessing the DigitalOcean app URL: $_" -ForegroundColor Red
        Write-Host "Please check the deployment status in the DigitalOcean App Platform dashboard" -ForegroundColor Yellow
    }
}