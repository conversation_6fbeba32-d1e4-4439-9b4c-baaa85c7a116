#!/usr/bin/env node

/**
 * Security Testing Script for eWasl Application
 * Tests all implemented security fixes and enhancements
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 eWasl Security Testing Suite');
console.log('================================\n');

let testsPassed = 0;
let testsFailed = 0;

function runTest(testName, testFunction) {
  try {
    console.log(`🧪 Testing: ${testName}`);
    const result = testFunction();
    if (result) {
      console.log(`✅ PASS: ${testName}\n`);
      testsPassed++;
    } else {
      console.log(`❌ FAIL: ${testName}\n`);
      testsFailed++;
    }
  } catch (error) {
    console.log(`❌ ERROR: ${testName} - ${error.message}\n`);
    testsFailed++;
  }
}

// Test 1: Authentication Middleware Re-enabled
runTest('Authentication Middleware Re-enabled', () => {
  const middlewarePath = path.join(__dirname, '..', 'src', 'middleware.ts');
  const content = fs.readFileSync(middlewarePath, 'utf8');
  
  // Check that authentication is no longer disabled
  const hasDisabledAuth = content.includes('AUTHENTICATION COMPLETELY DISABLED');
  const hasGetToken = content.includes('getToken');
  const hasAuthLogic = content.includes('isAuthenticated');
  
  if (hasDisabledAuth) {
    console.log('   ❌ Authentication still disabled');
    return false;
  }
  
  if (!hasGetToken || !hasAuthLogic) {
    console.log('   ❌ Authentication logic not properly implemented');
    return false;
  }
  
  console.log('   ✅ Authentication middleware properly re-enabled');
  return true;
});

// Test 2: Hardcoded Admin Credentials Removed
runTest('Hardcoded Admin Credentials Removed', () => {
  const authOptionsPath = path.join(__dirname, '..', 'src', 'lib', 'auth', 'auth-options.ts');
  const content = fs.readFileSync(authOptionsPath, 'utf8');
  
  const hasHardcodedCredentials = content.includes('<EMAIL>') && content.includes('admin123');
  
  if (hasHardcodedCredentials) {
    console.log('   ❌ Hardcoded credentials still present');
    return false;
  }
  
  console.log('   ✅ Hardcoded credentials successfully removed');
  return true;
});

// Test 3: Admin Emails Moved to Environment Variables
runTest('Admin Emails Configuration', () => {
  const setupDbPath = path.join(__dirname, '..', 'src', 'app', 'api', 'setup-db', 'route.ts');
  const content = fs.readFileSync(setupDbPath, 'utf8');
  
  const hasHardcodedEmails = content.includes("['<EMAIL>', '<EMAIL>']");
  const hasEnvConfig = content.includes('process.env.ADMIN_EMAILS');
  
  if (hasHardcodedEmails) {
    console.log('   ❌ Admin emails still hardcoded');
    return false;
  }
  
  if (!hasEnvConfig) {
    console.log('   ❌ Environment variable configuration not found');
    return false;
  }
  
  console.log('   ✅ Admin emails properly configured via environment variables');
  return true;
});

// Test 4: NextAuth Secret Updated
runTest('NextAuth Secret Security', () => {
  const appYamlPath = path.join(__dirname, '..', '.do', 'app.yaml');
  
  if (!fs.existsSync(appYamlPath)) {
    console.log('   ⚠️  Deployment config not found - manual verification needed');
    return true;
  }
  
  const content = fs.readFileSync(appYamlPath, 'utf8');
  const hasWeakSecret = content.includes('eWasl2025SecureNextAuthSecret123456789');
  
  if (hasWeakSecret) {
    console.log('   ❌ Weak NextAuth secret still in use');
    return false;
  }
  
  console.log('   ✅ NextAuth secret has been updated');
  return true;
});

// Test 5: Rate Limiting Implementation
runTest('Rate Limiting Implementation', () => {
  const rateLimitPath = path.join(__dirname, '..', 'src', 'lib', 'rate-limit.ts');
  
  if (!fs.existsSync(rateLimitPath)) {
    console.log('   ❌ Rate limiting module not found');
    return false;
  }
  
  const content = fs.readFileSync(rateLimitPath, 'utf8');
  const hasRateLimitConfigs = content.includes('rateLimitConfigs');
  const hasWithRateLimit = content.includes('withRateLimit');
  
  if (!hasRateLimitConfigs || !hasWithRateLimit) {
    console.log('   ❌ Rate limiting implementation incomplete');
    return false;
  }
  
  console.log('   ✅ Rate limiting properly implemented');
  return true;
});

// Test 6: Input Validation Schemas
runTest('Input Validation Schemas', () => {
  const schemasPath = path.join(__dirname, '..', 'src', 'lib', 'validation', 'schemas.ts');
  
  if (!fs.existsSync(schemasPath)) {
    console.log('   ❌ Validation schemas not found');
    return false;
  }
  
  const content = fs.readFileSync(schemasPath, 'utf8');
  const hasZodImport = content.includes("from 'zod'");
  const hasValidationSchemas = content.includes('registerSchema') && content.includes('publishPostSchema');
  
  if (!hasZodImport || !hasValidationSchemas) {
    console.log('   ❌ Validation schemas incomplete');
    return false;
  }
  
  console.log('   ✅ Input validation schemas properly implemented');
  return true;
});

// Test 7: Error Handling Implementation
runTest('Error Handling and Sanitization', () => {
  const errorHandlerPath = path.join(__dirname, '..', 'src', 'lib', 'error-handler.ts');
  
  if (!fs.existsSync(errorHandlerPath)) {
    console.log('   ❌ Error handler module not found');
    return false;
  }
  
  const content = fs.readFileSync(errorHandlerPath, 'utf8');
  const hasErrorTypes = content.includes('ErrorType');
  const hasHandleApiError = content.includes('handleApiError');
  const hasSafeMessages = content.includes('SAFE_ERROR_MESSAGES');
  
  if (!hasErrorTypes || !hasHandleApiError || !hasSafeMessages) {
    console.log('   ❌ Error handling implementation incomplete');
    return false;
  }
  
  console.log('   ✅ Error handling and sanitization properly implemented');
  return true;
});

// Test 8: Billing API IDOR Fix
runTest('Billing API IDOR Vulnerability Fix', () => {
  const billingPath = path.join(__dirname, '..', 'src', 'app', 'api', 'stripe', 'manage-billing', 'route.ts');
  const content = fs.readFileSync(billingPath, 'utf8');
  
  const hasSessionValidation = content.includes('getServerSession');
  const usesSessionUserId = content.includes('session.user.id');
  const hasAuthCheck = content.includes('Authentication required');
  
  if (!hasSessionValidation || !usesSessionUserId || !hasAuthCheck) {
    console.log('   ❌ IDOR vulnerability not properly fixed');
    return false;
  }
  
  console.log('   ✅ IDOR vulnerability successfully fixed');
  return true;
});

// Test 9: API Routes Security Enhancement
runTest('API Routes Security Enhancement', () => {
  const registerPath = path.join(__dirname, '..', 'src', 'app', 'api', 'auth', 'register', 'route.ts');
  const publishPath = path.join(__dirname, '..', 'src', 'app', 'api', 'posts', 'publish', 'route.ts');
  
  const registerContent = fs.readFileSync(registerPath, 'utf8');
  const publishContent = fs.readFileSync(publishPath, 'utf8');
  
  const registerHasRateLimit = registerContent.includes('withRateLimit');
  const registerHasValidation = registerContent.includes('validateRequestBody');
  const publishHasRateLimit = publishContent.includes('withRateLimit');
  const publishHasValidation = publishContent.includes('validateRequestBody');
  
  if (!registerHasRateLimit || !registerHasValidation) {
    console.log('   ❌ Register API security enhancements incomplete');
    return false;
  }
  
  if (!publishHasRateLimit || !publishHasValidation) {
    console.log('   ❌ Publish API security enhancements incomplete');
    return false;
  }
  
  console.log('   ✅ API routes properly secured with rate limiting and validation');
  return true;
});

// Test 10: Environment Configuration Security
runTest('Environment Configuration Security', () => {
  const envConfigPath = path.join(__dirname, '..', 'ENVIRONMENT-CONFIG.md');
  const content = fs.readFileSync(envConfigPath, 'utf8');
  
  // Check that real API keys have been replaced with placeholders
  const hasRealSupabaseKey = content.includes('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9');
  const hasPlaceholders = content.includes('your_supabase_project_url');
  const hasAdminEmailsConfig = content.includes('ADMIN_EMAILS');
  
  if (hasRealSupabaseKey) {
    console.log('   ❌ Real API keys still exposed in documentation');
    return false;
  }
  
  if (!hasPlaceholders || !hasAdminEmailsConfig) {
    console.log('   ❌ Environment configuration not properly updated');
    return false;
  }
  
  console.log('   ✅ Environment configuration properly secured');
  return true;
});

// Summary
console.log('\n🔒 Security Testing Summary');
console.log('===========================');
console.log(`✅ Tests Passed: ${testsPassed}`);
console.log(`❌ Tests Failed: ${testsFailed}`);
console.log(`📊 Success Rate: ${Math.round((testsPassed / (testsPassed + testsFailed)) * 100)}%\n`);

if (testsFailed === 0) {
  console.log('🎉 All security tests passed! Your application is significantly more secure.');
  console.log('\n📋 Next Steps:');
  console.log('1. Test authentication flows manually');
  console.log('2. Verify rate limiting works in development');
  console.log('3. Test input validation with invalid data');
  console.log('4. Deploy and test in production environment');
} else {
  console.log('⚠️  Some security tests failed. Please review and fix the issues above.');
  console.log('🔧 Run this script again after making fixes.');
}

console.log('\n🛡️  Security audit completed.');
