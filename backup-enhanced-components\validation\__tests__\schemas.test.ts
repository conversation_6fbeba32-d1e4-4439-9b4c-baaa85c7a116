import {
  signUpSchema,
  signInSchema,
  createPostSchema,
  socialAccountSchema,
  generateCaptionSchema,
  updateProfileSchema,
} from '../schemas';

describe('Validation Schemas', () => {
  describe('signUpSchema', () => {
    it('should validate correct signup data', () => {
      const validData = {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
      };

      const result = signUpSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject weak passwords', () => {
      const invalidData = {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        password: 'weak',
        confirmPassword: 'weak',
      };

      const result = signUpSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject mismatched passwords', () => {
      const invalidData = {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Different123',
      };

      const result = signUpSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        name: 'أحمد محمد',
        email: 'invalid-email',
        password: 'Password123',
        confirmPassword: 'Password123',
      };

      const result = signUpSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject short names', () => {
      const invalidData = {
        name: 'A',
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
      };

      const result = signUpSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('signInSchema', () => {
    it('should validate correct signin data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
      };

      const result = signInSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'Password123',
      };

      const result = signInSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject empty password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '',
      };

      const result = signInSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('createPostSchema', () => {
    it('should validate correct post data', () => {
      const validData = {
        content: 'هذا منشور تجريبي',
        socialAccountIds: ['123e4567-e89b-12d3-a456-************'],
        scheduledFor: '2024-12-31T23:59:59.000Z',
        mediaUrls: ['https://example.com/image.jpg'],
        tags: ['تجربة', 'منشور'],
      };

      const result = createPostSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject empty content', () => {
      const invalidData = {
        content: '',
        socialAccountIds: ['123e4567-e89b-12d3-a456-************'],
      };

      const result = createPostSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject content that is too long', () => {
      const invalidData = {
        content: 'a'.repeat(2001),
        socialAccountIds: ['123e4567-e89b-12d3-a456-************'],
      };

      const result = createPostSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject empty social account IDs', () => {
      const invalidData = {
        content: 'Valid content',
        socialAccountIds: [],
      };

      const result = createPostSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid UUID format', () => {
      const invalidData = {
        content: 'Valid content',
        socialAccountIds: ['invalid-uuid'],
      };

      const result = createPostSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid media URLs', () => {
      const invalidData = {
        content: 'Valid content',
        socialAccountIds: ['123e4567-e89b-12d3-a456-************'],
        mediaUrls: ['not-a-url'],
      };

      const result = createPostSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('socialAccountSchema', () => {
    it('should validate correct social account data', () => {
      const validData = {
        platform: 'TWITTER',
        accountId: 'twitter123',
        accountName: '@username',
        accessToken: 'access-token-123',
        refreshToken: 'refresh-token-123',
      };

      const result = socialAccountSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid platform', () => {
      const invalidData = {
        platform: 'INVALID_PLATFORM',
        accountId: 'twitter123',
        accountName: '@username',
        accessToken: 'access-token-123',
      };

      const result = socialAccountSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject empty required fields', () => {
      const invalidData = {
        platform: 'TWITTER',
        accountId: '',
        accountName: '@username',
        accessToken: 'access-token-123',
      };

      const result = socialAccountSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('generateCaptionSchema', () => {
    it('should validate correct caption generation data', () => {
      const validData = {
        prompt: 'أنشئ تعليقاً عن الطبيعة',
        language: 'ar',
        tone: 'inspirational',
        hashtags: true,
        maxLength: 280,
      };

      const result = generateCaptionSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should use default values', () => {
      const minimalData = {
        prompt: 'Create a caption about nature',
      };

      const result = generateCaptionSchema.safeParse(minimalData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.language).toBe('ar');
        expect(result.data.tone).toBe('professional');
        expect(result.data.hashtags).toBe(true);
        expect(result.data.maxLength).toBe(280);
      }
    });

    it('should reject empty prompt', () => {
      const invalidData = {
        prompt: '',
      };

      const result = generateCaptionSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject prompt that is too long', () => {
      const invalidData = {
        prompt: 'a'.repeat(501),
      };

      const result = generateCaptionSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid language', () => {
      const invalidData = {
        prompt: 'Valid prompt',
        language: 'invalid',
      };

      const result = generateCaptionSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid tone', () => {
      const invalidData = {
        prompt: 'Valid prompt',
        tone: 'invalid',
      };

      const result = generateCaptionSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid maxLength', () => {
      const invalidData = {
        prompt: 'Valid prompt',
        maxLength: 30, // Too short
      };

      const result = generateCaptionSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('updateProfileSchema', () => {
    it('should validate correct profile update data', () => {
      const validData = {
        name: 'أحمد محمد الجديد',
        email: '<EMAIL>',
        timezone: 'Asia/Riyadh',
        language: 'ar',
      };

      const result = updateProfileSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should allow partial updates', () => {
      const partialData = {
        name: 'أحمد محمد الجديد',
      };

      const result = updateProfileSchema.safeParse(partialData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
      };

      const result = updateProfileSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid language', () => {
      const invalidData = {
        language: 'invalid',
      };

      const result = updateProfileSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject name that is too long', () => {
      const invalidData = {
        name: 'a'.repeat(101),
      };

      const result = updateProfileSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });
});
