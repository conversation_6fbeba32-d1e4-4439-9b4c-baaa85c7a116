/**
 * Postiz Integration Interfaces
 * Adapted from Postiz for eWasl integration
 */

export interface ClientInformation {
  client_id: string;
  client_secret: string;
  instanceUrl: string;
}

export interface IAuthenticator {
  authenticate(params: {
    code: string;
    codeVerifier: string;
    refresh?: string;
  }, clientInformation?: ClientInformation): Promise<AuthTokenDetails>;
  
  refreshToken(refreshToken: string): Promise<AuthTokenDetails>;
  
  reConnect?(
    id: string,
    requiredId: string,
    accessToken: string
  ): Promise<AuthTokenDetails>;
  
  generateAuthUrl(clientInformation?: ClientInformation): Promise<GenerateAuthUrlResponse>;
  
  analytics?(
    id: string,
    accessToken: string,
    date: number
  ): Promise<AnalyticsData[]>;
  
  changeNickname?(
    id: string,
    accessToken: string,
    name: string
  ): Promise<{ name: string }>;
  
  changeProfilePicture?(
    id: string,
    accessToken: string,
    url: string
  ): Promise<{ url: string }>;
}

export interface AnalyticsData {
  label: string;
  data: Array<{
    total: string;
    date: string;
  }>;
  percentageChange: number;
}

export type GenerateAuthUrlResponse = {
  url: string;
  codeVerifier: string;
  state: string;
};

export type AuthTokenDetails = {
  id: string;
  name: string;
  error?: string;
  accessToken: string;
  refreshToken?: string;
  expiresIn?: number;
  picture?: string;
  username: string;
  additionalSettings?: {
    title: string;
    description: string;
    type: 'checkbox' | 'text' | 'textarea';
    value: any;
    regex?: string;
  }[];
};

export interface ISocialMediaIntegration {
  post(
    id: string,
    accessToken: string,
    postDetails: PostDetails[],
    integration: any
  ): Promise<PostResponse[]>;
}

export type PostResponse = {
  id: string;
  postId: string;
  releaseURL: string;
  status: string;
};

export type PostDetails = {
  id: string;
  message: string;
  settings: any;
  media?: MediaContent[];
  poll?: PollDetails;
};

export type PollDetails = {
  options: string[];
  duration: number;
};

export type MediaContent = {
  type: 'image' | 'video';
  url: string;
  path: string;
};

export interface SocialProvider extends IAuthenticator, ISocialMediaIntegration {
  identifier: string;
  refreshWait?: boolean;
  convertToJPEG?: boolean;
  isWeb3?: boolean;
  customFields?: () => Promise<{
    key: string;
    label: string;
    defaultValue?: string;
    validation: string;
    type: 'text' | 'password';
  }[]>;
  name: string;
  toolTip?: string;
  oneTimeToken?: boolean;
  isBetweenSteps: boolean;
  scopes: string[];
  externalUrl?: (url: string) => Promise<{
    client_id: string;
    client_secret: string;
  }>;
}

// eWasl-specific interfaces
export interface EWaslIntegration {
  id: string;
  platform: string;
  account_id: string;
  access_token: string;
  refresh_token?: string;
  expires_at?: Date;
  account_name: string;
  account_username: string;
  account_picture?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface EWaslPostDetails {
  content: string;
  media_urls?: string[];
  scheduled_at?: Date;
  platform_settings?: Record<string, any>;
}

export interface EWaslPostResponse {
  success: boolean;
  platform_post_id?: string;
  platform_url?: string;
  error?: string;
  retry_count?: number;
}
