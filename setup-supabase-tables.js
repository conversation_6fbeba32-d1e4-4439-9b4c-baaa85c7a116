const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function setupTables() {
  console.log('Setting up Supabase tables...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    return;
  }
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    // Create users table
    const { error: usersError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          name TEXT,
          email TEXT UNIQUE NOT NULL,
          email_verified TIMESTAMPTZ,
          image TEXT,
          password TEXT,
          role TEXT DEFAULT 'USER' CHECK (role IN ('USER', 'ADMIN')),
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
      `
    });
    
    if (usersError) {
      console.error('Error creating users table:', usersError);
    } else {
      console.log('✅ Users table created successfully');
    }
    
    // Create activities table
    const { error: activitiesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS activities (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          post_id TEXT,
          action TEXT NOT NULL CHECK (action IN ('CONNECT_SOCIAL', 'POST_SCHEDULED', 'POST_PUBLISHED', 'POST_FAILED', 'REGISTER', 'LOGIN', 'LOGOUT')),
          details TEXT,
          metadata JSONB,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_activities_user_id ON activities(user_id);
        CREATE INDEX IF NOT EXISTS idx_activities_action ON activities(action);
        CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);
      `
    });
    
    if (activitiesError) {
      console.error('Error creating activities table:', activitiesError);
    } else {
      console.log('✅ Activities table created successfully');
    }
    
    // Create social_accounts table
    const { error: socialError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS social_accounts (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          platform TEXT NOT NULL CHECK (platform IN ('TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK')),
          account_id TEXT NOT NULL,
          account_name TEXT NOT NULL,
          access_token TEXT NOT NULL,
          refresh_token TEXT,
          expires_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          UNIQUE(user_id, platform, account_id)
        );
        
        CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id ON social_accounts(user_id);
        CREATE INDEX IF NOT EXISTS idx_social_accounts_platform ON social_accounts(platform);
      `
    });
    
    if (socialError) {
      console.error('Error creating social_accounts table:', socialError);
    } else {
      console.log('✅ Social accounts table created successfully');
    }
    
    // Create posts table
    const { error: postsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS posts (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          content TEXT NOT NULL,
          media_url TEXT,
          status TEXT DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'SCHEDULED', 'PUBLISHED', 'FAILED')),
          scheduled_at TIMESTAMPTZ,
          published_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id);
        CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
        CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON posts(scheduled_at);
      `
    });
    
    if (postsError) {
      console.error('Error creating posts table:', postsError);
    } else {
      console.log('✅ Posts table created successfully');
    }
    
    console.log('🎉 Database setup completed!');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  }
}

setupTables();
