'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { 
  Repeat, 
  Calendar, 
  Clock, 
  Users, 
  Save,
  X,
  Plus
} from 'lucide-react';

interface SocialAccount {
  id: string;
  platform: string;
  account_name: string;
  account_id: string;
}

interface RecurringPostFormProps {
  socialAccounts: SocialAccount[];
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export function RecurringPostForm({ 
  socialAccounts, 
  onSubmit, 
  onCancel, 
  loading = false 
}: RecurringPostFormProps) {
  const [formData, setFormData] = useState({
    content: '',
    media_url: '',
    social_account_ids: [] as string[],
    recurring_pattern: {
      frequency: 'daily' as 'daily' | 'weekly' | 'monthly',
      interval: 1,
      days_of_week: [] as number[],
      day_of_month: 1,
      time: '09:00',
    },
    start_date: '',
    end_date: '',
    time_zone: 'UTC',
  });

  const [previewInstances, setPreviewInstances] = useState<Date[]>([]);

  const daysOfWeek = [
    { value: 0, label: 'الأحد' },
    { value: 1, label: 'الاثنين' },
    { value: 2, label: 'الثلاثاء' },
    { value: 3, label: 'الأربعاء' },
    { value: 4, label: 'الخميس' },
    { value: 5, label: 'الجمعة' },
    { value: 6, label: 'السبت' },
  ];

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('recurring_pattern.')) {
      const patternField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        recurring_pattern: {
          ...prev.recurring_pattern,
          [patternField]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }

    // Update preview when relevant fields change
    if (field.includes('recurring_pattern') || field === 'start_date' || field === 'end_date') {
      updatePreview();
    }
  };

  const handleSocialAccountToggle = (accountId: string) => {
    setFormData(prev => ({
      ...prev,
      social_account_ids: prev.social_account_ids.includes(accountId)
        ? prev.social_account_ids.filter(id => id !== accountId)
        : [...prev.social_account_ids, accountId],
    }));
  };

  const handleDayOfWeekToggle = (day: number) => {
    const newDays = formData.recurring_pattern.days_of_week.includes(day)
      ? formData.recurring_pattern.days_of_week.filter(d => d !== day)
      : [...formData.recurring_pattern.days_of_week, day];
    
    handleInputChange('recurring_pattern.days_of_week', newDays);
  };

  const updatePreview = () => {
    if (!formData.start_date) return;

    const instances = generatePreviewInstances(
      formData.recurring_pattern,
      new Date(formData.start_date),
      formData.end_date ? new Date(formData.end_date) : null
    );
    
    setPreviewInstances(instances.slice(0, 10)); // Show first 10 instances
  };

  const generatePreviewInstances = (pattern: any, startDate: Date, endDate: Date | null): Date[] => {
    const instances = [];
    const maxInstances = 10;
    let currentDate = new Date(startDate);
    
    // Set time from pattern
    const [hours, minutes] = pattern.time.split(':').map(Number);
    currentDate.setHours(hours, minutes, 0, 0);

    for (let i = 0; i < maxInstances; i++) {
      if (endDate && currentDate > endDate) break;

      // Check if this date matches the pattern
      if (shouldCreateInstance(currentDate, pattern)) {
        instances.push(new Date(currentDate));
      }

      // Advance to next date
      advanceDate(currentDate, pattern);
    }

    return instances;
  };

  const shouldCreateInstance = (date: Date, pattern: any): boolean => {
    switch (pattern.frequency) {
      case 'daily':
        return true;
      case 'weekly':
        return pattern.days_of_week.length === 0 || pattern.days_of_week.includes(date.getDay());
      case 'monthly':
        return date.getDate() === pattern.day_of_month;
      default:
        return false;
    }
  };

  const advanceDate = (date: Date, pattern: any): void => {
    switch (pattern.frequency) {
      case 'daily':
        date.setDate(date.getDate() + pattern.interval);
        break;
      case 'weekly':
        date.setDate(date.getDate() + (7 * pattern.interval));
        break;
      case 'monthly':
        date.setMonth(date.getMonth() + pattern.interval);
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.content.trim()) {
      toast.error('يرجى إدخال محتوى المنشور');
      return;
    }

    if (formData.social_account_ids.length === 0) {
      toast.error('يرجى اختيار حساب واحد على الأقل');
      return;
    }

    if (!formData.start_date) {
      toast.error('يرجى تحديد تاريخ البداية');
      return;
    }

    if (formData.recurring_pattern.frequency === 'weekly' && formData.recurring_pattern.days_of_week.length === 0) {
      toast.error('يرجى اختيار أيام الأسبوع للمنشورات الأسبوعية');
      return;
    }

    try {
      await onSubmit(formData);
      toast.success('تم إنشاء المنشورات المتكررة بنجاح');
    } catch (error) {
      toast.error('فشل في إنشاء المنشورات المتكررة');
    }
  };

  return (
    <div className="space-y-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Repeat className="h-5 w-5" />
            إنشاء منشورات متكررة
          </CardTitle>
          <CardDescription>
            قم بإنشاء سلسلة من المنشورات المجدولة بنمط متكرر
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Content */}
            <div className="space-y-2">
              <Label htmlFor="content">محتوى المنشور</Label>
              <Textarea
                id="content"
                placeholder="اكتب محتوى المنشور هنا..."
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                rows={4}
                maxLength={2800}
              />
              <div className="text-sm text-gray-500 text-left">
                {formData.content.length}/2800
              </div>
            </div>

            {/* Media URL */}
            <div className="space-y-2">
              <Label htmlFor="media_url">رابط الوسائط (اختياري)</Label>
              <Input
                id="media_url"
                type="url"
                placeholder="https://example.com/image.jpg"
                value={formData.media_url}
                onChange={(e) => handleInputChange('media_url', e.target.value)}
              />
            </div>

            {/* Social Accounts */}
            <div className="space-y-2">
              <Label>الحسابات الاجتماعية</Label>
              <div className="grid grid-cols-2 gap-2">
                {socialAccounts.map((account) => (
                  <div key={account.id} className="flex items-center space-x-2 space-x-reverse">
                    <Checkbox
                      id={account.id}
                      checked={formData.social_account_ids.includes(account.id)}
                      onCheckedChange={() => handleSocialAccountToggle(account.id)}
                    />
                    <Label htmlFor={account.id} className="flex items-center gap-2">
                      <Badge variant="outline">{account.platform}</Badge>
                      {account.account_name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Recurring Pattern */}
            <div className="space-y-4">
              <Label>نمط التكرار</Label>
              
              {/* Frequency */}
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>التكرار</Label>
                  <Select
                    value={formData.recurring_pattern.frequency}
                    onValueChange={(value) => handleInputChange('recurring_pattern.frequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">يومي</SelectItem>
                      <SelectItem value="weekly">أسبوعي</SelectItem>
                      <SelectItem value="monthly">شهري</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>كل</Label>
                  <Input
                    type="number"
                    min="1"
                    max="30"
                    value={formData.recurring_pattern.interval}
                    onChange={(e) => handleInputChange('recurring_pattern.interval', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>الوقت</Label>
                  <Input
                    type="time"
                    value={formData.recurring_pattern.time}
                    onChange={(e) => handleInputChange('recurring_pattern.time', e.target.value)}
                  />
                </div>
              </div>

              {/* Days of week for weekly */}
              {formData.recurring_pattern.frequency === 'weekly' && (
                <div className="space-y-2">
                  <Label>أيام الأسبوع</Label>
                  <div className="flex flex-wrap gap-2">
                    {daysOfWeek.map((day) => (
                      <Button
                        key={day.value}
                        type="button"
                        variant={formData.recurring_pattern.days_of_week.includes(day.value) ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handleDayOfWeekToggle(day.value)}
                      >
                        {day.label}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Day of month for monthly */}
              {formData.recurring_pattern.frequency === 'monthly' && (
                <div className="space-y-2">
                  <Label>يوم الشهر</Label>
                  <Input
                    type="number"
                    min="1"
                    max="31"
                    value={formData.recurring_pattern.day_of_month}
                    onChange={(e) => handleInputChange('recurring_pattern.day_of_month', parseInt(e.target.value))}
                  />
                </div>
              )}
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">تاريخ البداية</Label>
                <Input
                  id="start_date"
                  type="datetime-local"
                  value={formData.start_date}
                  onChange={(e) => handleInputChange('start_date', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end_date">تاريخ النهاية (اختياري)</Label>
                <Input
                  id="end_date"
                  type="datetime-local"
                  value={formData.end_date}
                  onChange={(e) => handleInputChange('end_date', e.target.value)}
                />
              </div>
            </div>

            {/* Preview */}
            {previewInstances.length > 0 && (
              <div className="space-y-2">
                <Label>معاينة المواعيد (أول 10 منشورات)</Label>
                <div className="bg-gray-50 p-4 rounded-lg space-y-2 max-h-40 overflow-y-auto">
                  {previewInstances.map((date, index) => (
                    <div key={index} className="text-sm flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {date.toLocaleDateString('ar-SA')} - {date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  ))}
                  {previewInstances.length === 10 && (
                    <div className="text-sm text-gray-500">... والمزيد</div>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-4">
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    جاري الإنشاء...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    إنشاء المنشورات المتكررة
                  </>
                )}
              </Button>
              <Button type="button" variant="outline" onClick={onCancel}>
                <X className="h-4 w-4 mr-2" />
                إلغاء
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
