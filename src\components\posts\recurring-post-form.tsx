'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon, Clock, RefreshCw, Plus, X } from 'lucide-react';
import { toast } from 'sonner';
import { TIMEZONE_OPTIONS, WEEKDAY_OPTIONS } from '@/lib/types/scheduling';

interface RecurringPostFormProps {
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  initialData?: any;
  isEditing?: boolean;
}

export function RecurringPostForm({
  onSuccess,
  onCancel,
  initialData,
  isEditing = false
}: RecurringPostFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    content: initialData?.content || '',
    mediaUrl: initialData?.mediaUrl || '',
    platforms: initialData?.platforms || [],
    patternType: initialData?.patternType || 'daily',
    patternConfig: {
      interval: initialData?.patternConfig?.interval || 1,
      time: initialData?.patternConfig?.time || '09:00',
      days: initialData?.patternConfig?.days || ['monday'],
      dayOfMonth: initialData?.patternConfig?.dayOfMonth || 1,
      dates: initialData?.patternConfig?.dates || [],
    },
    startDate: initialData?.startDate || '',
    endDate: initialData?.endDate || '',
    timezone: initialData?.timezone || 'UTC',
  });

  const [customDates, setCustomDates] = useState<string[]>(
    initialData?.patternConfig?.dates || []
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        throw new Error('عنوان المنشور مطلوب');
      }
      if (!formData.content.trim()) {
        throw new Error('محتوى المنشور مطلوب');
      }
      if (formData.platforms.length === 0) {
        throw new Error('يجب اختيار منصة واحدة على الأقل');
      }
      if (!formData.startDate) {
        throw new Error('تاريخ البداية مطلوب');
      }

      // Validate pattern-specific requirements
      if (formData.patternType === 'weekly' && formData.patternConfig.days.length === 0) {
        throw new Error('يجب اختيار يوم واحد على الأقل للنمط الأسبوعي');
      }
      if (formData.patternType === 'custom' && customDates.length === 0) {
        throw new Error('يجب إضافة تاريخ واحد على الأقل للنمط المخصص');
      }

      // Prepare request data
      const requestData = {
        title: formData.title,
        content: formData.content,
        mediaUrl: formData.mediaUrl || undefined,
        platforms: formData.platforms,
        pattern: {
          type: formData.patternType,
          config: {
            ...formData.patternConfig,
            dates: formData.patternType === 'custom' ? customDates : undefined,
          },
        },
        startDate: new Date(formData.startDate).toISOString(),
        endDate: formData.endDate ? new Date(formData.endDate).toISOString() : undefined,
        timezone: formData.timezone,
      };

      const url = isEditing
        ? `/api/posts/recurring/${initialData.id}`
        : '/api/posts/recurring';

      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `فشل في ${isEditing ? 'تحديث' : 'إنشاء'} المنشور المتكرر`);
      }

      toast.success(result.message || `تم ${isEditing ? 'تحديث' : 'إنشاء'} المنشور المتكرر بنجاح!`);
      onSuccess?.(result.data);

    } catch (error: any) {
      console.error('Error submitting recurring post:', error);
      toast.error(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlatformToggle = (platform: string) => {
    setFormData(prev => ({
      ...prev,
      platforms: prev.platforms.includes(platform)
        ? prev.platforms.filter((p: string) => p !== platform)
        : [...prev.platforms, platform]
    }));
  };

  const handleDayToggle = (day: string) => {
    setFormData(prev => ({
      ...prev,
      patternConfig: {
        ...prev.patternConfig,
        days: prev.patternConfig.days.includes(day)
          ? prev.patternConfig.days.filter((d: string) => d !== day)
          : [...prev.patternConfig.days, day]
      }
    }));
  };

  const addCustomDate = () => {
    const newDate = new Date().toISOString().split('T')[0] + 'T09:00';
    setCustomDates(prev => [...prev, newDate]);
  };

  const removeCustomDate = (index: number) => {
    setCustomDates(prev => prev.filter((_, i) => i !== index));
  };

  const updateCustomDate = (index: number, value: string) => {
    setCustomDates(prev => prev.map((date, i) => i === index ? value : date));
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5" />
          {isEditing ? 'تحديث المنشور المتكرر' : 'إنشاء منشور متكرر'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">المعلومات الأساسية</h3>

            <div>
              <Label htmlFor="title">عنوان المنشور *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="أدخل عنوان المنشور المتكرر"
                required
              />
            </div>

            <div>
              <Label htmlFor="content">محتوى المنشور *</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                placeholder="أدخل محتوى المنشور"
                rows={4}
                required
              />
              <div className="text-sm text-muted-foreground mt-1">
                {formData.content.length}/2000 حرف
              </div>
            </div>

            <div>
              <Label htmlFor="mediaUrl">رابط الوسائط (اختياري)</Label>
              <Input
                id="mediaUrl"
                type="url"
                value={formData.mediaUrl}
                onChange={(e) => setFormData({ ...formData, mediaUrl: e.target.value })}
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </div>

          <Separator />

          {/* Platforms Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">المنصات *</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'].map((platform) => (
                <div key={platform} className="flex items-center space-x-2">
                  <Checkbox
                    id={platform}
                    checked={formData.platforms.includes(platform)}
                    onCheckedChange={() => handlePlatformToggle(platform)}
                  />
                  <Label htmlFor={platform} className="cursor-pointer">
                    {platform}
                  </Label>
                </div>
              ))}
            </div>
            {formData.platforms.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.platforms.map((platform: string) => (
                  <Badge key={platform} variant="secondary">
                    {platform}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* Pattern Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">نمط التكرار</h3>

            <div>
              <Label htmlFor="patternType">نوع التكرار</Label>
              <Select
                value={formData.patternType}
                onValueChange={(value) => setFormData({ ...formData, patternType: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">يومي</SelectItem>
                  <SelectItem value="weekly">أسبوعي</SelectItem>
                  <SelectItem value="monthly">شهري</SelectItem>
                  <SelectItem value="custom">مخصص</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Daily Pattern */}
            {formData.patternType === 'daily' && (
              <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                <div>
                  <Label htmlFor="interval">كل كم يوم</Label>
                  <Input
                    id="interval"
                    type="number"
                    min="1"
                    max="365"
                    value={formData.patternConfig.interval}
                    onChange={(e) => setFormData({
                      ...formData,
                      patternConfig: { ...formData.patternConfig, interval: parseInt(e.target.value) || 1 }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="time">وقت النشر</Label>
                  <Input
                    id="time"
                    type="time"
                    value={formData.patternConfig.time}
                    onChange={(e) => setFormData({
                      ...formData,
                      patternConfig: { ...formData.patternConfig, time: e.target.value }
                    })}
                  />
                </div>
              </div>
            )}

            {/* Weekly Pattern */}
            {formData.patternType === 'weekly' && (
              <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                <div>
                  <Label>أيام الأسبوع *</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                    {WEEKDAY_OPTIONS.map(({ value, label }) => (
                      <div key={value} className="flex items-center space-x-2">
                        <Checkbox
                          id={value}
                          checked={formData.patternConfig.days.includes(value)}
                          onCheckedChange={() => handleDayToggle(value)}
                        />
                        <Label htmlFor={value} className="cursor-pointer text-sm">
                          {label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <Label htmlFor="weeklyTime">وقت النشر</Label>
                  <Input
                    id="weeklyTime"
                    type="time"
                    value={formData.patternConfig.time}
                    onChange={(e) => setFormData({
                      ...formData,
                      patternConfig: { ...formData.patternConfig, time: e.target.value }
                    })}
                  />
                </div>
              </div>
            )}

            {/* Monthly Pattern */}
            {formData.patternType === 'monthly' && (
              <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                <div>
                  <Label htmlFor="dayOfMonth">يوم من الشهر</Label>
                  <Input
                    id="dayOfMonth"
                    type="number"
                    min="1"
                    max="31"
                    value={formData.patternConfig.dayOfMonth}
                    onChange={(e) => setFormData({
                      ...formData,
                      patternConfig: { ...formData.patternConfig, dayOfMonth: parseInt(e.target.value) || 1 }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="monthlyTime">وقت النشر</Label>
                  <Input
                    id="monthlyTime"
                    type="time"
                    value={formData.patternConfig.time}
                    onChange={(e) => setFormData({
                      ...formData,
                      patternConfig: { ...formData.patternConfig, time: e.target.value }
                    })}
                  />
                </div>
              </div>
            )}

            {/* Custom Pattern */}
            {formData.patternType === 'custom' && (
              <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <Label>التواريخ المخصصة *</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCustomDate}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة تاريخ
                  </Button>
                </div>
                <div className="space-y-2">
                  {customDates.map((date, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        type="datetime-local"
                        value={date}
                        onChange={(e) => updateCustomDate(index, e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCustomDate(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  {customDates.length === 0 && (
                    <p className="text-sm text-muted-foreground">
                      لم يتم إضافة أي تواريخ مخصصة بعد
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Date Range */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الفترة الزمنية</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">تاريخ البداية *</Label>
                <Input
                  id="startDate"
                  type="datetime-local"
                  value={formData.startDate}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="endDate">تاريخ النهاية (اختياري)</Label>
                <Input
                  id="endDate"
                  type="datetime-local"
                  value={formData.endDate}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="timezone">المنطقة الزمنية</Label>
              <Select
                value={formData.timezone}
                onValueChange={(value) => setFormData({ ...formData, timezone: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TIMEZONE_OPTIONS.map(({ value, label }) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 pt-6">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                إلغاء
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  {isEditing ? 'جاري التحديث...' : 'جاري الإنشاء...'}
                </>
              ) : (
                <>
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {isEditing ? 'تحديث المنشور المتكرر' : 'إنشاء منشور متكرر'}
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
