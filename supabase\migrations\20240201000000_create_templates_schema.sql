-- Create content templates table
CREATE TABLE IF NOT EXISTS content_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  platform_compatibility TEXT[] NOT NULL DEFAULT '{}',
  language VARCHAR(10) NOT NULL DEFAULT 'ar',
  content_body TEXT NOT NULL,
  variables JSONB DEFAULT '[]',
  hashtags TEXT[] DEFAULT '{}',
  tone VARCHAR(50) NOT NULL DEFAULT 'friendly',
  industry_tags TEXT[] DEFAULT '{}',
  usage_count INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0.0,
  is_public BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);

-- Create template categories table
CREATE TABLE IF NOT EXISTS template_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  name_ar VARCHAR(100) NOT NULL,
  description TEXT,
  description_ar TEXT,
  icon VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create template ratings table
CREATE TABLE IF NOT EXISTS template_ratings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES content_templates(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(template_id, user_id)
);

-- Create template usage tracking table
CREATE TABLE IF NOT EXISTS template_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES content_templates(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  platform VARCHAR(50),
  success BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_content_templates_category ON content_templates(category);
CREATE INDEX IF NOT EXISTS idx_content_templates_language ON content_templates(language);
CREATE INDEX IF NOT EXISTS idx_content_templates_tone ON content_templates(tone);
CREATE INDEX IF NOT EXISTS idx_content_templates_platform ON content_templates USING GIN(platform_compatibility);
CREATE INDEX IF NOT EXISTS idx_content_templates_industry ON content_templates USING GIN(industry_tags);
CREATE INDEX IF NOT EXISTS idx_content_templates_usage_count ON content_templates(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_content_templates_rating ON content_templates(rating DESC);
CREATE INDEX IF NOT EXISTS idx_content_templates_created_at ON content_templates(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_template_usage_template_id ON template_usage(template_id);
CREATE INDEX IF NOT EXISTS idx_template_usage_user_id ON template_usage(user_id);

-- Create RLS policies
ALTER TABLE content_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_usage ENABLE ROW LEVEL SECURITY;

-- Templates policies
CREATE POLICY "Public templates are viewable by everyone" ON content_templates
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view their own templates" ON content_templates
  FOR SELECT USING (auth.uid() = created_by);

CREATE POLICY "Users can create templates" ON content_templates
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own templates" ON content_templates
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own templates" ON content_templates
  FOR DELETE USING (auth.uid() = created_by);

-- Categories policies (public read)
CREATE POLICY "Categories are viewable by everyone" ON template_categories
  FOR SELECT USING (is_active = true);

-- Ratings policies
CREATE POLICY "Users can view all ratings" ON template_ratings
  FOR SELECT USING (true);

CREATE POLICY "Users can create ratings" ON template_ratings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own ratings" ON template_ratings
  FOR UPDATE USING (auth.uid() = user_id);

-- Usage tracking policies
CREATE POLICY "Users can view their own usage" ON template_usage
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create usage records" ON template_usage
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to update template rating
CREATE OR REPLACE FUNCTION update_template_rating()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE content_templates 
  SET rating = (
    SELECT COALESCE(AVG(rating), 0)
    FROM template_ratings 
    WHERE template_id = COALESCE(NEW.template_id, OLD.template_id)
  )
  WHERE id = COALESCE(NEW.template_id, OLD.template_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update template rating
CREATE TRIGGER update_template_rating_trigger
  AFTER INSERT OR UPDATE OR DELETE ON template_ratings
  FOR EACH ROW EXECUTE FUNCTION update_template_rating();

-- Function to increment usage count
CREATE OR REPLACE FUNCTION increment_template_usage()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE content_templates 
  SET usage_count = usage_count + 1
  WHERE id = NEW.template_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to increment usage count
CREATE TRIGGER increment_template_usage_trigger
  AFTER INSERT ON template_usage
  FOR EACH ROW EXECUTE FUNCTION increment_template_usage();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at
CREATE TRIGGER update_content_templates_updated_at
  BEFORE UPDATE ON content_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
