/**
 * Job Queue Manager
 * Handles background job processing for scheduled posts
 * Using in-memory implementation (<PERSON><PERSON>/Bull removed)
 */

export interface PostJobData {
  postId: string;
  userId: string;
  content: string;
  platforms: string[];
  scheduledFor: string;
  mediaUrls?: string[];
  metadata?: Record<string, any>;
}

export interface JobResult {
  success: boolean;
  results: Array<{
    platform: string;
    success: boolean;
    postId?: string;
    error?: string;
  }>;
  completedAt: string;
}

// Simple in-memory job interface
interface InMemoryJob {
  id: string;
  data: PostJobData;
  scheduledFor: Date;
  status: 'waiting' | 'active' | 'completed' | 'failed';
  result?: JobResult;
  error?: string;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
}

class JobQueueManager {
  private static instance: JobQueueManager | null = null;
  private jobs: Map<string, InMemoryJob> = new Map();
  private isInitialized = false;
  private jobIdCounter = 0;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): JobQueueManager {
    if (!JobQueueManager.instance) {
      JobQueueManager.instance = new JobQueueManager();
    }
    return JobQueueManager.instance;
  }

  /**
   * Initialize job queue (in-memory implementation)
   */
  async initialize(): Promise<boolean> {
    try {
      // Start job processor
      this.startJobProcessor();

      this.isInitialized = true;
      console.log('✅ Job queue initialized with in-memory implementation');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize job queue:', error);
      return false;
    }
  }

  /**
   * Start job processor (checks for due jobs every minute)
   */
  private startJobProcessor(): void {
    setInterval(() => {
      this.processDueJobs();
    }, 60000); // Check every minute
  }

  /**
   * Process jobs that are due for execution
   */
  private async processDueJobs(): Promise<void> {
    const now = new Date();

    for (const [jobId, job] of this.jobs.entries()) {
      if (job.status === 'waiting' && job.scheduledFor <= now) {
        await this.executeJob(job);
      }
    }
  }

  /**
   * Execute a single job
   */
  private async executeJob(job: InMemoryJob): Promise<void> {
    try {
      job.status = 'active';
      job.processedAt = new Date();

      console.log(`📝 Processing job ${job.id} for user ${job.data.userId}`);

      const result = await this.processPostJob(job.data);

      job.status = 'completed';
      job.result = result;
      job.completedAt = new Date();

      console.log(`✅ Job ${job.id} completed:`, result);

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date();

      console.error(`❌ Job ${job.id} failed:`, error);
    }
  }

  /**
   * Process a post publishing job
   */
  private async processPostJob(jobData: PostJobData): Promise<JobResult> {
    const { postId, userId, content, platforms, mediaUrls, metadata } = jobData;
    
    console.log(`📝 Processing post job ${postId} for user ${userId}`);

    const results: JobResult['results'] = [];

    // Process each platform
    for (const platform of platforms) {
      try {
        // Import the social media publisher dynamically
        const { publishToSocialMedia } = await import('@/lib/social/publisher');
        
        const result = await publishToSocialMedia({
          platform: platform as any,
          content,
          mediaUrls,
          userId,
          metadata,
        });

        results.push({
          platform,
          success: result.success,
          postId: result.postId,
          error: result.error,
        });

      } catch (error) {
        console.error(`❌ Failed to publish to ${platform}:`, error);
        results.push({
          platform,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Update post status in database
    try {
      const { createClient } = await import('@/lib/supabase/server');
      const supabase = createClient();

      const allSuccessful = results.every(r => r.success);
      const anySuccessful = results.some(r => r.success);

      await supabase
        .from('posts')
        .update({
          status: allSuccessful ? 'published' : anySuccessful ? 'partially_published' : 'failed',
          published_at: allSuccessful ? new Date().toISOString() : null,
          publish_results: results,
          updated_at: new Date().toISOString(),
        })
        .eq('id', postId);

    } catch (error) {
      console.error('❌ Failed to update post status:', error);
    }

    return {
      success: results.some(r => r.success),
      results,
      completedAt: new Date().toISOString(),
    };
  }

  /**
   * Schedule a post for publishing
   */
  async schedulePost(
    postData: PostJobData,
    scheduledFor: Date,
    options?: any
  ): Promise<string | null> {
    try {
      if (!this.isInitialized) {
        throw new Error('Job queue not initialized');
      }

      if (scheduledFor.getTime() < Date.now()) {
        throw new Error('Cannot schedule post in the past');
      }

      const jobId = (++this.jobIdCounter).toString();
      const job: InMemoryJob = {
        id: jobId,
        data: postData,
        scheduledFor,
        status: 'waiting',
        createdAt: new Date(),
      };

      this.jobs.set(jobId, job);

      console.log(`📅 Scheduled post ${postData.postId} for ${scheduledFor.toISOString()}`);
      return jobId;

    } catch (error) {
      console.error('❌ Failed to schedule post:', error);
      return null;
    }
  }

  /**
   * Cancel a scheduled job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = this.jobs.get(jobId);
      if (job && job.status === 'waiting') {
        this.jobs.delete(jobId);
        console.log(`🗑️ Cancelled job ${jobId}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Failed to cancel job:', error);
      return false;
    }
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<any> {
    try {
      const job = this.jobs.get(jobId);
      if (!job) return null;

      return {
        id: job.id,
        data: job.data,
        status: job.status,
        createdAt: job.createdAt,
        processedAt: job.processedAt,
        completedAt: job.completedAt,
        result: job.result,
        error: job.error,
        scheduledFor: job.scheduledFor,
      };
    } catch (error) {
      console.error('❌ Failed to get job status:', error);
      return null;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<any> {
    try {
      const stats = {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        total: this.jobs.size,
      };

      for (const job of this.jobs.values()) {
        switch (job.status) {
          case 'waiting':
            stats.waiting++;
            break;
          case 'active':
            stats.active++;
            break;
          case 'completed':
            stats.completed++;
            break;
          case 'failed':
            stats.failed++;
            break;
        }
      }

      return stats;
    } catch (error) {
      console.error('❌ Failed to get queue stats:', error);
      return null;
    }
  }

  /**
   * Check if queue is healthy
   */
  isHealthy(): boolean {
    return this.isInitialized;
  }
}

export default JobQueueManager;
