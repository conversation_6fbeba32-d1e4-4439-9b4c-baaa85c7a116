/**
 * Content Formatter Service
 * Platform-specific content optimization and formatting
 * Handles character limits, hashtags, mentions, and Arabic text
 */

export type Platform = 'linkedin' | 'facebook' | 'instagram' | 'twitter' | 'x';

export interface FormattedContent {
  text: string;
  hashtags: string[];
  mentions: string[];
  links: string[];
  characterCount: number;
  isValid: boolean;
  warnings: string[];
  adaptations?: {
    [platform: string]: string;
  };
}

export interface PlatformLimits {
  maxCharacters: number;
  maxHashtags: number;
  maxMentions: number;
  recommendedHashtags: number;
  supportsMarkdown: boolean;
  supportsEmojis: boolean;
}

export class ContentFormatter {
  private readonly platformLimits: Record<Platform, PlatformLimits> = {
    linkedin: {
      maxCharacters: 3000,
      maxHashtags: 30,
      maxMentions: 50,
      recommendedHashtags: 5,
      supportsMarkdown: false,
      supportsEmojis: true
    },
    facebook: {
      maxCharacters: 63206,
      maxHashtags: 30,
      maxMentions: 50,
      recommendedHashtags: 10,
      supportsMarkdown: false,
      supportsEmojis: true
    },
    instagram: {
      maxCharacters: 2200,
      maxHashtags: 30,
      maxMentions: 20,
      recommendedHashtags: 11,
      supportsMarkdown: false,
      supportsEmojis: true
    },
    twitter: {
      maxCharacters: 280,
      maxHashtags: 10,
      maxMentions: 10,
      recommendedHashtags: 3,
      supportsMarkdown: false,
      supportsEmojis: true
    },
    x: {
      maxCharacters: 280,
      maxHashtags: 10,
      maxMentions: 10,
      recommendedHashtags: 3,
      supportsMarkdown: false,
      supportsEmojis: true
    }
  };

  /**
   * Format content for a specific platform
   */
  formatForPlatform(content: string, platform: Platform): FormattedContent {
    const limits = this.platformLimits[platform];
    const warnings: string[] = [];
    
    // Extract components
    const hashtags = this.extractHashtags(content);
    const mentions = this.extractMentions(content);
    const links = this.extractLinks(content);
    
    // Apply platform-specific formatting
    let formattedText = this.applyPlatformFormatting(content, platform);
    
    // Handle character limits
    if (formattedText.length > limits.maxCharacters) {
      formattedText = this.truncateContent(formattedText, limits.maxCharacters);
      warnings.push(`Content was truncated to fit ${platform} character limit (${limits.maxCharacters})`);
    }
    
    // Optimize hashtags
    const optimizedHashtags = this.optimizeHashtags(hashtags, platform);
    if (optimizedHashtags.length !== hashtags.length) {
      warnings.push('Hashtags were optimized for better performance');
    }
    
    // Check hashtag limits
    if (hashtags.length > limits.recommendedHashtags) {
      warnings.push(`Consider using fewer hashtags (recommended: ${limits.recommendedHashtags}, current: ${hashtags.length})`);
    }
    
    // Check mention limits
    if (mentions.length > limits.maxMentions) {
      warnings.push(`Too many mentions (max: ${limits.maxMentions}, current: ${mentions.length})`);
    }

    return {
      text: formattedText,
      hashtags: optimizedHashtags,
      mentions,
      links,
      characterCount: formattedText.length,
      isValid: this.validateContent(formattedText, platform),
      warnings
    };
  }

  /**
   * Create platform-specific adaptations of content
   */
  createCrossPlatformContent(content: string): FormattedContent {
    const adaptations: { [platform: string]: string } = {};
    const allWarnings: string[] = [];
    
    // Create adaptations for each platform
    Object.keys(this.platformLimits).forEach(platform => {
      const formatted = this.formatForPlatform(content, platform as Platform);
      adaptations[platform] = formatted.text;
      allWarnings.push(...formatted.warnings.map(w => `${platform}: ${w}`));
    });

    // Use the original content as base
    const baseFormatted = this.formatForPlatform(content, 'linkedin'); // LinkedIn has good balance
    
    return {
      ...baseFormatted,
      adaptations,
      warnings: [...new Set(allWarnings)] // Remove duplicates
    };
  }

  /**
   * Optimize hashtags for a specific platform
   */
  optimizeHashtags(hashtags: string[], platform: Platform): string[] {
    const limits = this.platformLimits[platform];
    
    if (hashtags.length <= limits.recommendedHashtags) {
      return hashtags;
    }
    
    // Prioritize hashtags based on platform
    const prioritized = this.prioritizeHashtags(hashtags, platform);
    return prioritized.slice(0, limits.recommendedHashtags);
  }

  /**
   * Handle Arabic text and RTL content
   */
  formatArabicContent(content: string, platform: Platform): FormattedContent {
    // Detect Arabic text
    const hasArabic = /[\u0600-\u06FF]/.test(content);
    
    if (!hasArabic) {
      return this.formatForPlatform(content, platform);
    }
    
    // Apply RTL-specific formatting
    let formattedContent = content;
    
    // Ensure proper spacing around Arabic hashtags
    formattedContent = formattedContent.replace(/([\u0600-\u06FF]+)(#[\u0600-\u06FFa-zA-Z0-9_]+)/g, '$1 $2');
    formattedContent = formattedContent.replace(/(#[\u0600-\u06FFa-zA-Z0-9_]+)([\u0600-\u06FF]+)/g, '$1 $2');
    
    // Handle mixed Arabic-English content
    formattedContent = this.optimizeMixedLanguageContent(formattedContent);
    
    const result = this.formatForPlatform(formattedContent, platform);
    
    // Add RTL-specific warnings
    if (platform === 'twitter' || platform === 'x') {
      result.warnings.push('Arabic content may display differently on Twitter due to RTL handling');
    }
    
    return result;
  }

  /**
   * Extract hashtags from content
   */
  private extractHashtags(content: string): string[] {
    const hashtagRegex = /#[\u0600-\u06FFa-zA-Z0-9_]+/g;
    return content.match(hashtagRegex) || [];
  }

  /**
   * Extract mentions from content
   */
  private extractMentions(content: string): string[] {
    const mentionRegex = /@[\u0600-\u06FFa-zA-Z0-9_]+/g;
    return content.match(mentionRegex) || [];
  }

  /**
   * Extract links from content
   */
  private extractLinks(content: string): string[] {
    const linkRegex = /https?:\/\/[^\s]+/g;
    return content.match(linkRegex) || [];
  }

  /**
   * Apply platform-specific formatting rules
   */
  private applyPlatformFormatting(content: string, platform: Platform): string {
    let formatted = content;
    
    switch (platform) {
      case 'linkedin':
        // LinkedIn prefers professional tone
        formatted = this.enhanceForLinkedIn(formatted);
        break;
      case 'twitter':
      case 'x':
        // Twitter prefers concise, engaging content
        formatted = this.enhanceForTwitter(formatted);
        break;
      case 'instagram':
        // Instagram is visual-focused with hashtags
        formatted = this.enhanceForInstagram(formatted);
        break;
      case 'facebook':
        // Facebook allows longer, more casual content
        formatted = this.enhanceForFacebook(formatted);
        break;
    }
    
    return formatted;
  }

  /**
   * Enhance content for LinkedIn
   */
  private enhanceForLinkedIn(content: string): string {
    // Add professional context if missing
    if (!content.includes('professional') && !content.includes('business') && !content.includes('career')) {
      // Could add professional context, but keep original for now
    }
    return content;
  }

  /**
   * Enhance content for Twitter
   */
  private enhanceForTwitter(content: string): string {
    // Ensure content is concise and engaging
    return content;
  }

  /**
   * Enhance content for Instagram
   */
  private enhanceForInstagram(content: string): string {
    // Instagram benefits from more hashtags and visual descriptions
    return content;
  }

  /**
   * Enhance content for Facebook
   */
  private enhanceForFacebook(content: string): string {
    // Facebook allows more casual, longer content
    return content;
  }

  /**
   * Truncate content while preserving hashtags and mentions
   */
  private truncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) {
      return content;
    }
    
    // Try to truncate at word boundary
    const truncated = content.substring(0, maxLength - 3);
    const lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > maxLength * 0.8) {
      return truncated.substring(0, lastSpace) + '...';
    }
    
    return truncated + '...';
  }

  /**
   * Prioritize hashtags based on platform preferences
   */
  private prioritizeHashtags(hashtags: string[], platform: Platform): string[] {
    // Simple prioritization - in production, this could use analytics data
    return hashtags.sort((a, b) => {
      // Prioritize shorter hashtags for Twitter
      if (platform === 'twitter' || platform === 'x') {
        return a.length - b.length;
      }
      // Keep original order for other platforms
      return 0;
    });
  }

  /**
   * Optimize mixed Arabic-English content
   */
  private optimizeMixedLanguageContent(content: string): string {
    // Ensure proper spacing between Arabic and English text
    return content
      .replace(/([\u0600-\u06FF])([a-zA-Z])/g, '$1 $2')
      .replace(/([a-zA-Z])([\u0600-\u06FF])/g, '$1 $2');
  }

  /**
   * Validate content for platform
   */
  private validateContent(content: string, platform: Platform): boolean {
    const limits = this.platformLimits[platform];
    
    if (content.length === 0 || content.length > limits.maxCharacters) {
      return false;
    }
    
    const hashtags = this.extractHashtags(content);
    const mentions = this.extractMentions(content);
    
    if (hashtags.length > limits.maxHashtags || mentions.length > limits.maxMentions) {
      return false;
    }
    
    return true;
  }

  /**
   * Get platform-specific recommendations
   */
  getRecommendations(content: string, platform: Platform): string[] {
    const recommendations: string[] = [];
    const limits = this.platformLimits[platform];
    const hashtags = this.extractHashtags(content);
    
    if (hashtags.length === 0) {
      recommendations.push(`Add ${limits.recommendedHashtags} relevant hashtags to increase reach`);
    }
    
    if (content.length < limits.maxCharacters * 0.5) {
      recommendations.push('Consider adding more detail to engage your audience');
    }
    
    if (platform === 'instagram' && !content.includes('📸') && !content.includes('🎥')) {
      recommendations.push('Consider adding visual emojis for Instagram');
    }
    
    return recommendations;
  }
}

export default ContentFormatter;
