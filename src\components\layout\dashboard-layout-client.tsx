"use client";

import React, { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { 
  PlusCircle, 
  MessageSquare, 
  Calendar, 
  BarChart3, 
  Users, 
  Settings, 
  LogOut, 
  Bell, 
  User,
  CreditCard,
  TestTube
} from "lucide-react";
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";

interface DashboardLayoutClientProps {
  children: React.ReactNode;
}

export default function DashboardLayoutClient({ children }: DashboardLayoutClientProps) {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      router.push('/auth/signin');
      return;
    }

    setUser(user);
    setIsLoading(false);
  };

  const handleLogout = async () => {
    const supabase = createClient();
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      toast.error('فشل في تسجيل الخروج');
      return;
    }
    
    toast.success('تم تسجيل الخروج بنجاح');
    router.push('/auth/signin');
  };

  const navigationItems = [
    { icon: BarChart3, label: 'لوحة التحكم', href: '/dashboard' },
    { icon: PlusCircle, label: 'المنشورات', href: '/posts' },
    { icon: Calendar, label: 'الجدولة', href: '/schedule' },
    { icon: Users, label: 'الحسابات', href: '/social' },
    { icon: BarChart3, label: 'التحليلات', href: '/analytics' },
    { icon: CreditCard, label: 'الفوترة', href: '/dashboard/billing' },
    { icon: Settings, label: 'الإعدادات', href: '/settings' },
    { icon: TestTube, label: '🧪 اختبار APIs', href: '/api-testing' }
  ];

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '3px solid #e5e7eb',
            borderTop: '3px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#6b7280' }}>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif'
    }}>
      {/* Sidebar */}
      <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        width: '18rem',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(24px)',
        borderLeft: '1px solid rgba(229, 231, 235, 0.5)',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        zIndex: 50,
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Logo Section */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid rgba(229, 231, 235, 0.5)'
        }}>
          <Link href="/dashboard" style={{ textDecoration: 'none' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                borderRadius: '0.75rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1.125rem',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
              }}>
                eW
              </div>
              <div>
                <h2 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(to right, #2563eb, #9333ea)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: 0
                }}>
                  eWasl
                </h2>
                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>منصة إدارة المحتوى</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <div style={{ flex: 1, padding: '1.5rem 1rem' }}>
          <p style={{
            fontSize: '0.75rem',
            fontWeight: '600',
            color: '#6b7280',
            textTransform: 'uppercase',
            letterSpacing: '0.05em',
            marginBottom: '0.75rem',
            padding: '0 0.75rem'
          }}>
            القائمة الرئيسية
          </p>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
            {navigationItems.map((item, index) => {
              const isActive = pathname === item.href || 
                (item.href !== '/dashboard' && pathname.startsWith(item.href));
              
              return (
                <Link
                  key={index}
                  href={item.href}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    height: '2.75rem',
                    padding: '0 0.75rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: isActive ? 'white' : '#374151',
                    textDecoration: 'none',
                    borderRadius: '0.5rem',
                    background: isActive ? 'linear-gradient(to right, #2563eb, #9333ea)' : 'transparent',
                    boxShadow: isActive ? '0 10px 15px -3px rgba(0, 0, 0, 0.1)' : 'none',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = '#eff6ff';
                      e.currentTarget.style.color = '#2563eb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.background = 'transparent';
                      e.currentTarget.style.color = '#374151';
                    }
                  }}
                >
                  <item.icon style={{ width: '1.25rem', height: '1.25rem', marginLeft: '0.75rem' }} />
                  {item.label}
                </Link>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div style={{ padding: '1rem', borderTop: '1px solid rgba(229, 231, 235, 0.5)' }}>
          <button 
            onClick={handleLogout}
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              height: '2.75rem',
              padding: '0 0.75rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#dc2626',
              background: 'transparent',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#fef2f2';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
            }}
          >
            <LogOut style={{ width: '1.25rem', height: '1.25rem', marginLeft: '0.75rem' }} />
            تسجيل الخروج
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ marginRight: '18rem' }}>
        {/* Header */}
        <header style={{
          position: 'sticky',
          top: 0,
          zIndex: 30,
          height: '4rem',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(24px)',
          borderBottom: '1px solid rgba(229, 231, 235, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          alignItems: 'center',
          padding: '0 1.5rem',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <div style={{
              width: '2.5rem',
              height: '2.5rem',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '0.875rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              eW
            </div>
            <div>
              <h1 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                margin: 0
              }}>
                {getPageTitle(pathname)}
              </h1>
              <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>
                {getPageDescription(pathname)}
              </p>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <button style={{
              position: 'relative',
              padding: '0.5rem',
              background: 'transparent',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              color: '#6b7280'
            }}>
              <Bell style={{ width: '1.25rem', height: '1.25rem' }} />
              <span style={{
                position: 'absolute',
                top: '0.25rem',
                right: '0.25rem',
                width: '0.5rem',
                height: '0.5rem',
                background: '#ef4444',
                borderRadius: '50%'
              }}></span>
            </button>
            <button style={{
              width: '2rem',
              height: '2rem',
              background: '#f3f4f6',
              border: 'none',
              borderRadius: '50%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6b7280'
            }}>
              <User style={{ width: '1rem', height: '1rem' }} />
            </button>
          </div>
        </header>

        {/* Page Content */}
        <main style={{
          minHeight: 'calc(100vh - 4rem)',
          background: 'linear-gradient(135deg, rgba(249, 250, 251, 0.3) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(239, 246, 255, 0.2) 100%)'
        }}>
          {children}
        </main>
      </div>

      {/* Mobile Overlay */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
          .sidebar {
            transform: translateX(100%);
            transition: transform 0.3s ease;
          }
          
          .main-content {
            margin-right: 0;
          }
        }
      `}</style>
    </div>
  );
}

function getPageTitle(pathname: string): string {
  const titles: Record<string, string> = {
    '/dashboard': 'لوحة التحكم',
    '/posts': 'إدارة المنشورات',
    '/schedule': 'جدولة المحتوى',
    '/social': 'الحسابات الاجتماعية',
    '/analytics': 'التحليلات والإحصائيات',
    '/dashboard/billing': 'إدارة الاشتراك',
    '/settings': 'الإعدادات',
    '/api-testing': 'اختبار APIs'
  };
  
  return titles[pathname] || 'eWasl';
}

function getPageDescription(pathname: string): string {
  const descriptions: Record<string, string> = {
    '/dashboard': 'نظرة عامة على أداء منصتك',
    '/posts': 'إنشاء وإدارة منشوراتك',
    '/schedule': 'جدولة المحتوى للنشر',
    '/social': 'إدارة حساباتك الاجتماعية',
    '/analytics': 'تحليل أداء المحتوى',
    '/dashboard/billing': 'إدارة الاشتراك والفوترة',
    '/settings': 'إعدادات الحساب والتطبيق',
    '/api-testing': 'اختبار تكاملات APIs'
  };
  
  return descriptions[pathname] || 'منصة إدارة وسائل التواصل الاجتماعي';
}
