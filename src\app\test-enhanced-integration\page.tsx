'use client';

/**
 * Enhanced Integration Testing Dashboard
 * Comprehensive testing interface for all social media integrations
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Play, 
  RefreshCw, 
  ExternalLink,
  AlertTriangle,
  Info
} from 'lucide-react';

interface TestResult {
  success: boolean;
  duration: number;
  error?: string;
  details?: any;
}

interface Provider {
  identifier: string;
  name: string;
  scopes: string[];
  isBetweenSteps: boolean;
}

export default function EnhancedIntegrationTestPage() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [integrationId, setIntegrationId] = useState('');
  const [testContent, setTestContent] = useState('Test post from eWasl Enhanced Integration 🚀 #testing');
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [recentTests, setRecentTests] = useState<any[]>([]);

  useEffect(() => {
    loadProviders();
    loadRecentTests();
  }, []);

  const loadProviders = async () => {
    try {
      const response = await fetch('/api/social/enhanced/test');
      const data = await response.json();
      if (data.success) {
        setProviders(data.providers);
        if (data.providers.length > 0) {
          setSelectedPlatform(data.providers[0].identifier);
        }
      }
    } catch (error) {
      console.error('Failed to load providers:', error);
    }
  };

  const loadRecentTests = async () => {
    try {
      const response = await fetch('/api/social/enhanced/test');
      const data = await response.json();
      if (data.success) {
        setRecentTests(data.recentTests);
      }
    } catch (error) {
      console.error('Failed to load recent tests:', error);
    }
  };

  const runTest = async (testType: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/social/enhanced/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testType,
          platform: selectedPlatform,
          integrationId: integrationId || undefined,
          content: testContent,
        }),
      });

      const data = await response.json();
      setTestResults(prev => ({
        ...prev,
        [testType]: data.testResults,
      }));

      await loadRecentTests();
    } catch (error) {
      console.error('Test failed:', error);
      setTestResults(prev => ({
        ...prev,
        [testType]: {
          results: { overall: { success: false, error: 'Test execution failed' } },
        },
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const connectPlatform = async () => {
    try {
      const response = await fetch('/api/social/enhanced/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: selectedPlatform,
        }),
      });

      const data = await response.json();
      if (data.success) {
        window.open(data.authUrl, '_blank');
      }
    } catch (error) {
      console.error('Failed to initiate connection:', error);
    }
  };

  const testConnection = async () => {
    if (!integrationId) {
      alert('Please enter an Integration ID');
      return;
    }

    try {
      const response = await fetch(`/api/social/enhanced/publish?integrationId=${integrationId}`);
      const data = await response.json();
      
      setTestResults(prev => ({
        ...prev,
        connection: {
          results: data,
          timestamp: new Date().toISOString(),
        },
      }));
    } catch (error) {
      console.error('Connection test failed:', error);
    }
  };

  const publishTest = async () => {
    if (!integrationId || !testContent) {
      alert('Please enter Integration ID and test content');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/social/enhanced/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          integrationId,
          content: testContent,
        }),
      });

      const data = await response.json();
      setTestResults(prev => ({
        ...prev,
        publish: {
          results: data,
          timestamp: new Date().toISOString(),
        },
      }));
    } catch (error) {
      console.error('Publish test failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    );
  };

  const formatDuration = (ms: number) => {
    return `${ms}ms`;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Enhanced Integration Testing</h1>
          <p className="text-muted-foreground">
            Comprehensive testing suite for social media integrations
          </p>
        </div>
        <Button onClick={loadRecentTests} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="testing" className="space-y-6">
        <TabsList>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="testing" className="space-y-6">
          {/* Platform Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Platform Configuration</CardTitle>
              <CardDescription>
                Select platform and configure test parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Platform</label>
                  <select
                    value={selectedPlatform}
                    onChange={(e) => setSelectedPlatform(e.target.value)}
                    className="w-full mt-1 p-2 border rounded-md"
                  >
                    {providers.map((provider) => (
                      <option key={provider.identifier} value={provider.identifier}>
                        {provider.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Integration ID (Optional)</label>
                  <Input
                    value={integrationId}
                    onChange={(e) => setIntegrationId(e.target.value)}
                    placeholder="Enter integration ID for connection tests"
                  />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Test Content</label>
                <Textarea
                  value={testContent}
                  onChange={(e) => setTestContent(e.target.value)}
                  placeholder="Enter test content for posting"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button onClick={connectPlatform} variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Connect Platform
                </Button>
                <Button onClick={testConnection} variant="outline">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Test Connection
                </Button>
                <Button onClick={publishTest} variant="outline" disabled={isLoading}>
                  <Play className="h-4 w-4 mr-2" />
                  Test Publishing
                </Button>
                <Button 
                  onClick={() => runTest('comprehensive')} 
                  variant="default"
                  disabled={isLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Run All Tests
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Individual Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Individual Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  onClick={() => runTest('auth_flow')} 
                  variant="outline"
                  disabled={isLoading}
                >
                  Test Auth Flow
                </Button>
                <Button 
                  onClick={() => runTest('connection')} 
                  variant="outline"
                  disabled={isLoading}
                >
                  Test Connection
                </Button>
                <Button 
                  onClick={() => runTest('posting')} 
                  variant="outline"
                  disabled={isLoading}
                >
                  Test Posting
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          {Object.entries(testResults).map(([testType, result]) => (
            <Card key={testType}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(result.results?.overall?.success || result.results?.success)}
                  {testType.charAt(0).toUpperCase() + testType.slice(1)} Test Results
                  <Badge variant="outline">
                    {formatDuration(result.results?.overall?.duration || result.results?.duration || 0)}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
                  {JSON.stringify(result.results, null, 2)}
                </pre>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Test History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTests.map((test, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-md">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(test.success)}
                      <div>
                        <div className="font-medium">{test.test_type} - {test.platform}</div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(test.created_at).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <Badge variant={test.success ? "default" : "destructive"}>
                      {test.success ? "Passed" : "Failed"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
