#!/usr/bin/env node

/**
 * Live OAuth Flow Simulation Testing
 * Simulates OAuth flows and validates callback URL consistency
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-OAuth-Simulator/1.0',
        ...options.headers
      },
      timeout: 15000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data,
            redirectLocation: res.headers.location
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: e.message,
            redirectLocation: res.headers.location
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function simulateOAuthInitiation(platform) {
  console.log(`\n🔐 SIMULATING ${platform.toUpperCase()} OAUTH INITIATION...\n`);
  
  try {
    // Step 1: Initiate OAuth flow
    console.log(`1. Initiating OAuth for ${platform}...`);
    const connectResponse = await makeRequest(`${BASE_URL}/api/social/connect`, {
      method: 'POST',
      body: { platform }
    });
    
    console.log(`   Status: ${connectResponse.status}`);
    
    if (connectResponse.status === 401) {
      console.log('   ⚠️  Authentication required (expected for production)');
      console.log('   📝 This indicates the endpoint is working but requires user login');
      return { status: 'AUTH_REQUIRED', platform };
    }
    
    if (connectResponse.data && connectResponse.data.authUrl) {
      console.log(`   ✅ Auth URL generated successfully`);
      console.log(`   🔗 Auth URL: ${connectResponse.data.authUrl.substring(0, 100)}...`);
      
      // Step 2: Analyze the OAuth URL
      const authUrl = new URL(connectResponse.data.authUrl);
      console.log(`\n2. Analyzing OAuth URL structure...`);
      console.log(`   🌐 Host: ${authUrl.host}`);
      console.log(`   📍 Path: ${authUrl.pathname}`);
      
      // Step 3: Check callback URL consistency
      const redirectUri = authUrl.searchParams.get('redirect_uri');
      const expectedCallback = `${BASE_URL}/api/social/callback/${platform}`;
      
      console.log(`\n3. Verifying callback URL consistency...`);
      console.log(`   📤 Expected: ${expectedCallback}`);
      console.log(`   📥 Actual: ${redirectUri}`);
      
      if (redirectUri === expectedCallback) {
        console.log(`   ✅ Callback URL is consistent!`);
      } else {
        console.log(`   ❌ Callback URL mismatch!`);
        return { status: 'CALLBACK_MISMATCH', platform, expected: expectedCallback, actual: redirectUri };
      }
      
      // Step 4: Verify OAuth parameters
      console.log(`\n4. Verifying OAuth parameters...`);
      const requiredParams = {
        twitter: ['client_id', 'redirect_uri', 'scope', 'response_type', 'state', 'code_challenge'],
        facebook: ['client_id', 'redirect_uri', 'scope', 'response_type', 'state'],
        instagram: ['client_id', 'redirect_uri', 'scope', 'response_type', 'state'],
        linkedin: ['client_id', 'redirect_uri', 'scope', 'response_type', 'state']
      };
      
      const platformParams = requiredParams[platform] || [];
      const missingParams = [];
      
      platformParams.forEach(param => {
        if (authUrl.searchParams.has(param)) {
          console.log(`   ✅ ${param}: ${authUrl.searchParams.get(param).substring(0, 50)}...`);
        } else {
          console.log(`   ❌ Missing: ${param}`);
          missingParams.push(param);
        }
      });
      
      if (missingParams.length === 0) {
        console.log(`   🎉 All required OAuth parameters present!`);
      } else {
        console.log(`   ⚠️  Missing parameters: ${missingParams.join(', ')}`);
      }
      
      return { 
        status: 'SUCCESS', 
        platform, 
        authUrl: connectResponse.data.authUrl,
        callbackConsistent: redirectUri === expectedCallback,
        missingParams
      };
      
    } else if (connectResponse.data && connectResponse.data.error) {
      console.log(`   ⚠️  Error: ${connectResponse.data.error}`);
      return { status: 'ERROR', platform, error: connectResponse.data.error };
    } else {
      console.log(`   ❌ No auth URL in response`);
      return { status: 'NO_AUTH_URL', platform };
    }
    
  } catch (error) {
    console.log(`   ❌ Request failed: ${error.message}`);
    return { status: 'REQUEST_FAILED', platform, error: error.message };
  }
}

async function testCallbackEndpoint(platform) {
  console.log(`\n🔗 TESTING ${platform.toUpperCase()} CALLBACK ENDPOINT...\n`);
  
  try {
    // Test callback endpoint with various scenarios
    const callbackUrl = `${BASE_URL}/api/social/callback/${platform}`;
    
    // Test 1: No parameters (should return error about missing code)
    console.log(`1. Testing callback without parameters...`);
    const noParamsResponse = await makeRequest(callbackUrl);
    console.log(`   Status: ${noParamsResponse.status}`);
    
    if (noParamsResponse.status === 404) {
      console.log(`   ❌ Endpoint not found - callback URL may be incorrect`);
      return { status: 'ENDPOINT_NOT_FOUND', platform };
    } else if ([400, 401, 307, 302].includes(noParamsResponse.status)) {
      console.log(`   ✅ Endpoint exists (${noParamsResponse.status} expected for missing params)`);
    } else {
      console.log(`   ⚠️  Unexpected status: ${noParamsResponse.status}`);
    }
    
    // Test 2: With invalid code (should return error about invalid code)
    console.log(`\n2. Testing callback with invalid code...`);
    const invalidCodeResponse = await makeRequest(`${callbackUrl}?code=invalid_test_code&state=test_state`);
    console.log(`   Status: ${invalidCodeResponse.status}`);
    
    if ([400, 401, 500].includes(invalidCodeResponse.status)) {
      console.log(`   ✅ Proper error handling for invalid code`);
    } else {
      console.log(`   ⚠️  Unexpected response to invalid code: ${invalidCodeResponse.status}`);
    }
    
    return { status: 'ENDPOINT_WORKING', platform };
    
  } catch (error) {
    console.log(`   ❌ Request failed: ${error.message}`);
    return { status: 'REQUEST_FAILED', platform, error: error.message };
  }
}

async function verifyConnectedAccounts() {
  console.log(`\n📱 VERIFYING CONNECTED ACCOUNTS...\n`);
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/accounts`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data && response.data.accounts) {
      const accounts = response.data.accounts;
      console.log(`✅ Found ${accounts.length} connected accounts:`);
      
      accounts.forEach((account, index) => {
        console.log(`   ${index + 1}. ${account.platform.toUpperCase()}: ${account.account_name}`);
        console.log(`      Status: ${account.status || 'unknown'}`);
        console.log(`      ID: ${account.account_id}`);
      });
      
      // Verify callback URL consistency for connected accounts
      console.log(`\n🔍 Verifying OAuth callback consistency for connected platforms...`);
      const connectedPlatforms = [...new Set(accounts.map(acc => acc.platform.toLowerCase()))];
      
      for (const platform of connectedPlatforms) {
        const expectedCallback = `${BASE_URL}/api/social/callback/${platform}`;
        console.log(`   ${platform.toUpperCase()}: ${expectedCallback} ✅`);
      }
      
      return { status: 'SUCCESS', accounts, connectedPlatforms };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAILED', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
    return { status: 'REQUEST_FAILED', error: error.message };
  }
}

async function runLiveOAuthSimulation() {
  console.log('🧪 LIVE OAUTH FLOW SIMULATION TESTING');
  console.log('=' .repeat(80));
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  const platforms = ['twitter', 'facebook', 'instagram', 'linkedin'];
  const results = {
    oauthInitiation: [],
    callbackTests: [],
    accountVerification: null,
    summary: { total: 0, passed: 0, failed: 0, authRequired: 0 }
  };
  
  // Test OAuth initiation for each platform
  console.log('🚀 PHASE 1: OAUTH INITIATION SIMULATION');
  console.log('=' .repeat(50));
  
  for (const platform of platforms) {
    const result = await simulateOAuthInitiation(platform);
    results.oauthInitiation.push(result);
    results.summary.total++;
    
    if (result.status === 'SUCCESS') results.summary.passed++;
    else if (result.status === 'AUTH_REQUIRED') results.summary.authRequired++;
    else results.summary.failed++;
  }
  
  // Test callback endpoints
  console.log('\n🔗 PHASE 2: CALLBACK ENDPOINT TESTING');
  console.log('=' .repeat(50));
  
  for (const platform of platforms) {
    const result = await testCallbackEndpoint(platform);
    results.callbackTests.push(result);
    results.summary.total++;
    
    if (result.status === 'ENDPOINT_WORKING') results.summary.passed++;
    else results.summary.failed++;
  }
  
  // Verify connected accounts
  console.log('\n📱 PHASE 3: CONNECTED ACCOUNTS VERIFICATION');
  console.log('=' .repeat(50));
  
  results.accountVerification = await verifyConnectedAccounts();
  results.summary.total++;
  
  if (results.accountVerification.status === 'SUCCESS') results.summary.passed++;
  else results.summary.failed++;
  
  // Generate final report
  console.log('\n📊 LIVE OAUTH SIMULATION REPORT');
  console.log('=' .repeat(80));
  console.log(`✅ Passed: ${results.summary.passed}`);
  console.log(`🔐 Auth Required: ${results.summary.authRequired}`);
  console.log(`❌ Failed: ${results.summary.failed}`);
  console.log(`📈 Success Rate: ${((results.summary.passed / results.summary.total) * 100).toFixed(1)}%`);
  
  // OAuth initiation summary
  console.log('\n🔐 OAuth Initiation Results:');
  results.oauthInitiation.forEach(result => {
    const icon = result.status === 'SUCCESS' ? '✅' : 
                 result.status === 'AUTH_REQUIRED' ? '🔐' : '❌';
    console.log(`  ${icon} ${result.platform.toUpperCase()}: ${result.status}`);
  });
  
  // Callback endpoint summary
  console.log('\n🔗 Callback Endpoint Results:');
  results.callbackTests.forEach(result => {
    const icon = result.status === 'ENDPOINT_WORKING' ? '✅' : '❌';
    console.log(`  ${icon} ${result.platform.toUpperCase()}: ${result.status}`);
  });
  
  // Connected accounts summary
  if (results.accountVerification.status === 'SUCCESS') {
    console.log(`\n📱 Connected Accounts: ${results.accountVerification.accounts.length}`);
    console.log(`   Platforms: ${results.accountVerification.connectedPlatforms.join(', ')}`);
  }
  
  console.log('\n🎯 RECOMMENDATIONS:');
  
  if (results.summary.authRequired > 0) {
    console.log('  🔐 OAuth endpoints require authentication (expected for production)');
    console.log('  📝 Manual testing with browser login required for full OAuth flow validation');
  }
  
  if (results.summary.failed === 0) {
    console.log('  🎉 All OAuth infrastructure is working correctly!');
    console.log('  ✅ Ready for live OAuth testing with real user accounts');
  } else {
    console.log('  ⚠️  Some endpoints need attention before live testing');
  }
  
  console.log('\n🏁 SIMULATION COMPLETE');
  console.log(`Finished at: ${new Date().toISOString()}`);
  
  return results;
}

if (require.main === module) {
  runLiveOAuthSimulation().then(results => {
    const successRate = (results.summary.passed / results.summary.total) * 100;
    process.exit(successRate >= 70 ? 0 : 1);
  }).catch(error => {
    console.error('❌ CRITICAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = { runLiveOAuthSimulation };
