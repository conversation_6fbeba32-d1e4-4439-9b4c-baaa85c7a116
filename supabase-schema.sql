-- eWasl Social Scheduler Database Schema for Supabase
-- This script creates all the necessary tables for the application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE "Role" AS ENUM ('USER', 'ADMIN');
CREATE TYPE "PostStatus" AS ENUM ('DRAFT', 'SCHEDULED', 'PUBLISHED', 'FAILED');
CREATE TYPE "Platform" AS ENUM ('TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK');
CREATE TYPE "Action" AS ENUM ('POST_CREATED', 'POST_SCHEDULED', 'POST_PUBLISHED', 'POST_FAILED', 'ACCOUNT_CONNECTED', 'ACCOUNT_DISCONNECTED');

-- User table
CREATE TABLE "User" (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    name TEXT,
    email TEXT UNIQUE NOT NULL,
    "emailVerified" TIMESTAMPTZ,
    image TEXT,
    password TEXT,
    role "Role" DEFAULT 'USER' NOT NULL,
    "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    "updatedAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- NextAuth Account table
CREATE TABLE "Account" (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "userId" TEXT NOT NULL,
    type TEXT NOT NULL,
    provider TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    refresh_token TEXT,
    access_token TEXT,
    expires_at INTEGER,
    token_type TEXT,
    scope TEXT,
    id_token TEXT,
    session_state TEXT,
    FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE,
    UNIQUE(provider, "providerAccountId")
);

-- NextAuth Session table
CREATE TABLE "Session" (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "sessionToken" TEXT UNIQUE NOT NULL,
    "userId" TEXT NOT NULL,
    expires TIMESTAMPTZ NOT NULL,
    FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
);

-- NextAuth VerificationToken table
CREATE TABLE "VerificationToken" (
    identifier TEXT NOT NULL,
    token TEXT UNIQUE NOT NULL,
    expires TIMESTAMPTZ NOT NULL,
    PRIMARY KEY (identifier, token)
);

-- SocialAccount table
CREATE TABLE "SocialAccount" (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "userId" TEXT NOT NULL,
    platform "Platform" NOT NULL,
    "accountId" TEXT NOT NULL,
    "accountName" TEXT NOT NULL,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "expiresAt" TIMESTAMPTZ,
    "isActive" BOOLEAN DEFAULT true NOT NULL,
    "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    "updatedAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE,
    UNIQUE("userId", platform)
);

-- Post table
CREATE TABLE "Post" (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "userId" TEXT NOT NULL,
    content TEXT NOT NULL,
    "mediaUrl" TEXT,
    status "PostStatus" DEFAULT 'DRAFT' NOT NULL,
    "scheduledAt" TIMESTAMPTZ,
    "publishedAt" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    "updatedAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
);

-- Activity table
CREATE TABLE "Activity" (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    "userId" TEXT NOT NULL,
    "postId" TEXT,
    action "Action" NOT NULL,
    details TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE,
    FOREIGN KEY ("postId") REFERENCES "Post"(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX "User_email_idx" ON "User"(email);
CREATE INDEX "Account_userId_idx" ON "Account"("userId");
CREATE INDEX "Session_userId_idx" ON "Session"("userId");
CREATE INDEX "Session_sessionToken_idx" ON "Session"("sessionToken");
CREATE INDEX "SocialAccount_userId_idx" ON "SocialAccount"("userId");
CREATE INDEX "Post_userId_idx" ON "Post"("userId");
CREATE INDEX "Post_status_idx" ON "Post"(status);
CREATE INDEX "Post_scheduledAt_idx" ON "Post"("scheduledAt");
CREATE INDEX "Activity_userId_idx" ON "Activity"("userId");
CREATE INDEX "Activity_postId_idx" ON "Activity"("postId");

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON "User" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_social_account_updated_at BEFORE UPDATE ON "SocialAccount" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_post_updated_at BEFORE UPDATE ON "Post" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- OAuth States table for secure OAuth flow
CREATE TABLE "oauth_states" (
  "id" UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  "state_token" TEXT NOT NULL UNIQUE,
  "user_id" TEXT NOT NULL,
  "oauth_token" TEXT NOT NULL,
  "oauth_token_secret" TEXT NOT NULL,
  "expires_at" TIMESTAMP WITH TIME ZONE NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for cleanup of expired states
CREATE INDEX "oauth_states_expires_at_idx" ON "oauth_states" ("expires_at");

-- Note: Admin users should be created through proper admin setup flow
-- Do not insert default admin credentials in production

-- Create RLS (Row Level Security) policies
ALTER TABLE "User" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Account" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Session" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "SocialAccount" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Post" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Activity" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "oauth_states" ENABLE ROW LEVEL SECURITY;

-- User policies
CREATE POLICY "Users can view their own data" ON "User" FOR SELECT USING (auth.uid()::text = id);
CREATE POLICY "Users can update their own data" ON "User" FOR UPDATE USING (auth.uid()::text = id);

-- Account policies
CREATE POLICY "Users can view their own accounts" ON "Account" FOR SELECT USING (auth.uid()::text = "userId");
CREATE POLICY "Users can manage their own accounts" ON "Account" FOR ALL USING (auth.uid()::text = "userId");

-- Session policies
CREATE POLICY "Users can view their own sessions" ON "Session" FOR SELECT USING (auth.uid()::text = "userId");
CREATE POLICY "Users can manage their own sessions" ON "Session" FOR ALL USING (auth.uid()::text = "userId");

-- SocialAccount policies
CREATE POLICY "Users can view their own social accounts" ON "SocialAccount" FOR SELECT USING (auth.uid()::text = "userId");
CREATE POLICY "Users can manage their own social accounts" ON "SocialAccount" FOR ALL USING (auth.uid()::text = "userId");

-- Post policies
CREATE POLICY "Users can view their own posts" ON "Post" FOR SELECT USING (auth.uid()::text = "userId");
CREATE POLICY "Users can manage their own posts" ON "Post" FOR ALL USING (auth.uid()::text = "userId");

-- Activity policies
CREATE POLICY "Users can view their own activities" ON "Activity" FOR SELECT USING (auth.uid()::text = "userId");
CREATE POLICY "Users can create activities" ON "Activity" FOR INSERT WITH CHECK (auth.uid()::text = "userId");

-- OAuth States policies (service role only for security)
CREATE POLICY "Service role can manage oauth states" ON "oauth_states" FOR ALL USING (auth.role() = 'service_role');