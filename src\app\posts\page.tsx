"use client";

import React, { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import EnhancedPostsHeader from "@/components/posts/enhanced-posts-header";
import EnhancedPostCard from "@/components/posts/enhanced-post-card";

interface Post {
  id: string;
  content: string;
  status: string;
  media_url?: string;
  scheduled_at?: string;
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export default function PostsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const router = useRouter();

  useEffect(() => {
    checkAuthAndLoadPosts();
  }, [statusFilter]);

  const checkAuthAndLoadPosts = async () => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      router.push('/auth/signin');
      return;
    }

    setUser(user);
    await loadPosts();
  };

  const loadPosts = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (statusFilter !== 'ALL') {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/posts?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load posts');
      }

      setPosts(data.posts || []);
    } catch (error: any) {
      console.error('Error loading posts:', error);
      toast.error('فشل في تحميل المنشورات');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "#6b7280";
      case "SCHEDULED":
        return "#3b82f6";
      case "PUBLISHED":
        return "#10b981";
      case "FAILED":
        return "#ef4444";
      default:
        return "#6b7280";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "مسودة";
      case "SCHEDULED":
        return "مجدول";
      case "PUBLISHED":
        return "منشور";
      case "FAILED":
        return "فشل";
      default:
        return "غير معروف";
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "𝕏";
      case "FACEBOOK":
        return "f";
      case "INSTAGRAM":
        return "📷";
      case "LINKEDIN":
        return "in";
      default:
        return "?";
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'غير محدد';
    try {
      return new Date(dateString).toLocaleDateString('ar-SA');
    } catch (error) {
      return 'غير محدد';
    }
  };

  const filteredPosts = posts.filter((post) => {
    const matchesSearch = post.content
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  const handleEditPost = (post: Post) => {
    router.push(`/posts/edit/${post.id}`);
  };

  const handleDeletePost = async (post: Post) => {
    if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا المنشور؟' : 'Are you sure you want to delete this post?')) {
      return;
    }

    try {
      const response = await fetch(`/api/posts/${post.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete post');
      }

      toast.success(language === 'ar' ? 'تم حذف المنشور بنجاح' : 'Post deleted successfully');
      await loadPosts();
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error(language === 'ar' ? 'فشل في حذف المنشور' : 'Failed to delete post');
    }
  };

  const handleViewPost = (post: Post) => {
    router.push(`/posts/${post.id}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="section-pro animate-pro-fade-in">
      {/* Enhanced Header */}
      <EnhancedPostsHeader
        language={language}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        totalPosts={posts.length}
      />

      {/* Posts Grid */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="grid-pro-1 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="card-pro card-pro-padding-md">
                <div className="loading-pro-card"></div>
              </div>
            ))}
          </div>
        ) : filteredPosts.length === 0 ? (
          <div className="card-pro card-pro-padding-lg text-center">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-h3 text-gray-900 mb-2">
              {language === 'ar' ? 'لا توجد منشورات' : 'No Posts Found'}
            </h3>
            <p className="text-body text-gray-600 mb-6">
              {language === 'ar'
                ? 'ابدأ بإنشاء منشورك الأول على جميع منصات التواصل الاجتماعي'
                : 'Start by creating your first post across all social media platforms'
              }
            </p>
            <Link href="/posts/new">
              <Button className="btn-pro-primary btn-pro-md">
                {language === 'ar' ? 'إنشاء منشور جديد' : 'Create New Post'}
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid-pro-1 gap-4">
            {filteredPosts.map((post, index) => (
              <EnhancedPostCard
                key={post.id}
                post={post}
                language={language}
                onEdit={handleEditPost}
                onDelete={handleDeletePost}
                onView={handleViewPost}
                style={{ animationDelay: `${index * 0.1}s` }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
