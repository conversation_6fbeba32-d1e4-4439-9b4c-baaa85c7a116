"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Lock, Eye, EyeOff, CheckCircle, AlertCircle } from "lucide-react";

const formSchema = z
  .object({
    password: z.string().min(8, {
      message: "كلمة المرور يجب أن تكون 8 أحرف على الأقل",
    }).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
      message: "كلمة المرور يجب أن تحتوي على حرف كبير وحرف صغير ورقم",
    }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "كلمات المرور غير متطابقة",
    path: ["confirmPassword"],
  });

export function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Check if we have valid reset tokens in the URL
  useEffect(() => {
    const checkTokens = async () => {
      const accessToken = searchParams.get('access_token');
      const refreshToken = searchParams.get('refresh_token');
      
      if (!accessToken || !refreshToken) {
        setIsValidToken(false);
        setError("رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية");
        return;
      }

      try {
        const supabase = createClient();
        
        // Set the session using the tokens from the URL
        const { data, error } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken,
        });

        if (error || !data.session) {
          setIsValidToken(false);
          setError("رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية");
        } else {
          setIsValidToken(true);
        }
      } catch (error) {
        console.error('Token validation error:', error);
        setIsValidToken(false);
        setError("حدث خطأ أثناء التحقق من الرابط");
      }
    };

    checkTokens();
  }, [searchParams]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Updating password...");
      const supabase = createClient();

      const { error: updateError } = await supabase.auth.updateUser({
        password: values.password
      });

      if (updateError) {
        console.error('Password update error:', updateError);
        setError("حدث خطأ أثناء تحديث كلمة المرور");
        toast.error("حدث خطأ أثناء تحديث كلمة المرور");
        return;
      }

      console.log("Password updated successfully");
      setIsSuccess(true);
      toast.success("تم تحديث كلمة المرور بنجاح");
      
      // Redirect to signin after a short delay
      setTimeout(() => {
        router.push("/auth/signin?message=password-updated");
      }, 2000);
    } catch (error: any) {
      console.error("Reset password error:", error);
      setError(error.message || "حدث خطأ أثناء تحديث كلمة المرور");
      toast.error(error.message || "حدث خطأ أثناء تحديث كلمة المرور");
    } finally {
      setIsLoading(false);
    }
  }

  // Show loading state while checking token validity
  if (isValidToken === null) {
    return (
      <div className="w-full" dir="rtl">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600">جاري التحقق من الرابط...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error if token is invalid
  if (isValidToken === false) {
    return (
      <div className="w-full" dir="rtl">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <AlertCircle className="h-16 w-16 text-red-600 mx-auto" />
              <h3 className="text-xl font-semibold text-red-800">رابط غير صالح</h3>
              <p className="text-red-700">{error}</p>
              <div className="pt-4 space-y-2">
                <Link href="/auth/forgot-password">
                  <Button variant="outline" className="w-full">
                    طلب رابط جديد
                  </Button>
                </Link>
                <Link href="/auth/signin">
                  <Button variant="ghost" className="w-full">
                    <ArrowRight className="ml-2 h-4 w-4" />
                    العودة إلى تسجيل الدخول
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show success message
  if (isSuccess) {
    return (
      <div className="w-full" dir="rtl">
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
              <h3 className="text-xl font-semibold text-green-800">تم تحديث كلمة المرور!</h3>
              <p className="text-green-700">
                تم تحديث كلمة المرور بنجاح. سيتم توجيهك إلى صفحة تسجيل الدخول.
              </p>
              <div className="pt-4">
                <Link href="/auth/signin">
                  <Button className="w-full">
                    <ArrowRight className="ml-2 h-4 w-4" />
                    الانتقال إلى تسجيل الدخول
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full" dir="rtl">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-700 font-medium">كلمة المرور الجديدة</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="أدخل كلمة المرور الجديدة"
                      className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors pl-10 pr-10"
                      {...field}
                    />
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-700 font-medium">تأكيد كلمة المرور</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="أدخل كلمة المرور مرة أخرى"
                      className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors pl-10 pr-10"
                      {...field}
                    />
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600 text-sm text-center">{error}</p>
            </div>
          )}

          <Button
            type="submit"
            className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
            disabled={isLoading}
          >
            {isLoading ? "جاري التحديث..." : "تحديث كلمة المرور"}
          </Button>

          <div className="text-center">
            <Link
              href="/auth/signin"
              className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200 inline-flex items-center"
            >
              <ArrowRight className="ml-2 h-4 w-4" />
              العودة إلى تسجيل الدخول
            </Link>
          </div>
        </form>
      </Form>
    </div>
  );
}
