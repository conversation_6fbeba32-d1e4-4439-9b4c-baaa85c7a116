/**
 * Enhanced Publishing Service
 * Handles real publishing to selected business accounts across all platforms
 */

import { createClient } from '@/lib/supabase/client';
import { FacebookPagesManager } from '@/lib/social/business-accounts/facebook-pages-manager';
import { LinkedInCompaniesManager } from '@/lib/social/business-accounts/linkedin-companies-manager';

export interface PublishingRequest {
  userId: string;
  content: string;
  platforms: PublishingPlatform[];
  mediaUrls?: string[];
  scheduledAt?: string;
  metadata?: {
    hashtags?: string[];
    mentions?: string[];
    location?: string;
    customFields?: Record<string, any>;
  };
}

export interface PublishingPlatform {
  platform: 'facebook' | 'linkedin' | 'instagram' | 'twitter';
  accountId: string;
  businessAccountId?: string;
  customContent?: string;
  platformSpecificOptions?: Record<string, any>;
}

export interface PublishingResult {
  platform: string;
  success: boolean;
  postId?: string;
  postUrl?: string;
  error?: string;
  publishedAt: string;
  engagement?: {
    likes?: number;
    shares?: number;
    comments?: number;
    views?: number;
  };
}

export interface PublishingResponse {
  success: boolean;
  results: PublishingResult[];
  totalPlatforms: number;
  successfulPlatforms: number;
  failedPlatforms: number;
  publishingId: string;
  scheduledAt?: string;
}

export class EnhancedPublishingService {
  private supabase = createClient();
  private facebookPagesManager = new FacebookPagesManager();
  private linkedinCompaniesManager = new LinkedInCompaniesManager();

  /**
   * Publish content to selected business accounts across platforms
   */
  async publishContent(request: PublishingRequest): Promise<PublishingResponse> {
    console.log('🚀 Starting enhanced publishing process...');
    console.log(`Platforms: ${request.platforms.map(p => p.platform).join(', ')}`);

    const publishingId = this.generatePublishingId();
    const results: PublishingResult[] = [];

    // Log publishing attempt
    await this.logPublishingAttempt(publishingId, request);

    // If scheduled, queue for later
    if (request.scheduledAt) {
      return await this.schedulePublishing(publishingId, request);
    }

    // Publish immediately to all platforms
    for (const platformConfig of request.platforms) {
      try {
        console.log(`📱 Publishing to ${platformConfig.platform}...`);
        
        const result = await this.publishToPlatform(
          platformConfig,
          request.content,
          request.mediaUrls,
          request.metadata
        );

        results.push(result);

        // Log individual result
        await this.logPublishingResult(publishingId, result);

      } catch (error: any) {
        console.error(`❌ Error publishing to ${platformConfig.platform}:`, error);
        
        const errorResult: PublishingResult = {
          platform: platformConfig.platform,
          success: false,
          error: error.message,
          publishedAt: new Date().toISOString()
        };

        results.push(errorResult);
        await this.logPublishingResult(publishingId, errorResult);
      }
    }

    const response: PublishingResponse = {
      success: results.some(r => r.success),
      results,
      totalPlatforms: request.platforms.length,
      successfulPlatforms: results.filter(r => r.success).length,
      failedPlatforms: results.filter(r => !r.success).length,
      publishingId
    };

    // Update final publishing status
    await this.updatePublishingStatus(publishingId, response);

    console.log(`✅ Publishing complete: ${response.successfulPlatforms}/${response.totalPlatforms} successful`);
    return response;
  }

  /**
   * Publish to a specific platform using business account
   */
  private async publishToPlatform(
    platformConfig: PublishingPlatform,
    content: string,
    mediaUrls?: string[],
    metadata?: any
  ): Promise<PublishingResult> {
    const { platform, accountId, businessAccountId } = platformConfig;
    const finalContent = platformConfig.customContent || content;

    // Get social account details
    const socialAccount = await this.getSocialAccount(accountId);
    if (!socialAccount) {
      throw new Error(`Social account not found: ${accountId}`);
    }

    // Get business account details if specified
    let businessAccount = null;
    if (businessAccountId) {
      businessAccount = await this.getBusinessAccount(platform, accountId, businessAccountId);
      if (!businessAccount) {
        throw new Error(`Business account not found: ${businessAccountId}`);
      }
    }

    switch (platform) {
      case 'facebook':
        return await this.publishToFacebook(
          socialAccount,
          businessAccount,
          finalContent,
          mediaUrls,
          metadata,
          platformConfig.platformSpecificOptions
        );

      case 'linkedin':
        return await this.publishToLinkedIn(
          socialAccount,
          businessAccount,
          finalContent,
          mediaUrls,
          metadata,
          platformConfig.platformSpecificOptions
        );

      case 'instagram':
        return await this.publishToInstagram(
          socialAccount,
          businessAccount,
          finalContent,
          mediaUrls,
          metadata,
          platformConfig.platformSpecificOptions
        );

      case 'twitter':
        return await this.publishToTwitter(
          socialAccount,
          finalContent,
          mediaUrls,
          metadata,
          platformConfig.platformSpecificOptions
        );

      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Publish to Facebook Page
   */
  private async publishToFacebook(
    socialAccount: any,
    businessAccount: any,
    content: string,
    mediaUrls?: string[],
    metadata?: any,
    options?: any
  ): Promise<PublishingResult> {
    try {
      console.log('📘 Publishing to Facebook Page...');

      // Use business account (page) if available, otherwise personal account
      const targetPageId = businessAccount?.page_id || 'me';
      const accessToken = businessAccount?.access_token || socialAccount.access_token;

      if (!accessToken) {
        throw new Error('No access token available for Facebook publishing');
      }

      // Prepare Facebook post data
      const postData: any = {
        message: content,
        access_token: accessToken
      };

      // Add media if provided
      if (mediaUrls && mediaUrls.length > 0) {
        // For now, use the first image URL
        // TODO: Implement proper media upload to Facebook
        postData.link = mediaUrls[0];
      }

      // Add location if provided
      if (metadata?.location) {
        postData.place = metadata.location;
      }

      // Make API call to Facebook
      const response = await fetch(`https://graph.facebook.com/v19.0/${targetPageId}/feed`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || 'Facebook publishing failed');
      }

      console.log('✅ Facebook post published successfully:', data.id);

      return {
        platform: 'facebook',
        success: true,
        postId: data.id,
        postUrl: `https://facebook.com/${data.id}`,
        publishedAt: new Date().toISOString(),
        engagement: {
          likes: 0,
          shares: 0,
          comments: 0
        }
      };

    } catch (error: any) {
      console.error('❌ Facebook publishing error:', error);
      throw error;
    }
  }

  /**
   * Publish to LinkedIn Company Page
   */
  private async publishToLinkedIn(
    socialAccount: any,
    businessAccount: any,
    content: string,
    mediaUrls?: string[],
    metadata?: any,
    options?: any
  ): Promise<PublishingResult> {
    try {
      console.log('💼 Publishing to LinkedIn...');

      const accessToken = socialAccount.access_token;
      if (!accessToken) {
        throw new Error('No access token available for LinkedIn publishing');
      }

      // Determine the author (company or person)
      let author = `urn:li:person:${socialAccount.platform_user_id}`;
      if (businessAccount?.organization_urn) {
        author = businessAccount.organization_urn;
      }

      // Prepare LinkedIn post data
      const postData = {
        author,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content
            },
            shareMediaCategory: mediaUrls && mediaUrls.length > 0 ? 'IMAGE' : 'NONE'
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
        }
      };

      // Add media if provided
      if (mediaUrls && mediaUrls.length > 0) {
        // TODO: Implement proper media upload to LinkedIn
        // For now, we'll post text-only
        console.log('⚠️ LinkedIn media upload not yet implemented, posting text only');
      }

      // Make API call to LinkedIn
      const response = await fetch('https://api.linkedin.com/v2/ugcPosts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'X-Restli-Protocol-Version': '2.0.0'
        },
        body: JSON.stringify(postData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'LinkedIn publishing failed');
      }

      console.log('✅ LinkedIn post published successfully:', data.id);

      return {
        platform: 'linkedin',
        success: true,
        postId: data.id,
        postUrl: `https://linkedin.com/feed/update/${data.id}`,
        publishedAt: new Date().toISOString(),
        engagement: {
          likes: 0,
          shares: 0,
          comments: 0
        }
      };

    } catch (error: any) {
      console.error('❌ LinkedIn publishing error:', error);
      throw error;
    }
  }

  /**
   * Publish to Instagram Business Account
   */
  private async publishToInstagram(
    socialAccount: any,
    businessAccount: any,
    content: string,
    mediaUrls?: string[],
    metadata?: any,
    options?: any
  ): Promise<PublishingResult> {
    try {
      console.log('📸 Publishing to Instagram...');

      // Instagram requires media for posts
      if (!mediaUrls || mediaUrls.length === 0) {
        throw new Error('Instagram posts require at least one image or video');
      }

      // TODO: Implement Instagram Business API publishing
      // For now, return a placeholder result
      console.log('⚠️ Instagram publishing not yet fully implemented');

      return {
        platform: 'instagram',
        success: false,
        error: 'Instagram publishing not yet implemented',
        publishedAt: new Date().toISOString()
      };

    } catch (error: any) {
      console.error('❌ Instagram publishing error:', error);
      throw error;
    }
  }

  /**
   * Publish to Twitter
   */
  private async publishToTwitter(
    socialAccount: any,
    content: string,
    mediaUrls?: string[],
    metadata?: any,
    options?: any
  ): Promise<PublishingResult> {
    try {
      console.log('🐦 Publishing to Twitter...');

      // TODO: Implement Twitter API v2 publishing
      // For now, return a placeholder result
      console.log('⚠️ Twitter publishing not yet fully implemented');

      return {
        platform: 'twitter',
        success: false,
        error: 'Twitter publishing not yet implemented',
        publishedAt: new Date().toISOString()
      };

    } catch (error: any) {
      console.error('❌ Twitter publishing error:', error);
      throw error;
    }
  }

  /**
   * Schedule publishing for later
   */
  private async schedulePublishing(
    publishingId: string,
    request: PublishingRequest
  ): Promise<PublishingResponse> {
    console.log(`⏰ Scheduling publishing for ${request.scheduledAt}`);

    // Store in database for scheduler to pick up
    const { error } = await this.supabase
      .from('scheduled_posts')
      .insert({
        id: publishingId,
        user_id: request.userId,
        content: request.content,
        platforms: request.platforms,
        media_urls: request.mediaUrls,
        scheduled_at: request.scheduledAt,
        metadata: request.metadata,
        status: 'scheduled',
        created_at: new Date().toISOString()
      });

    if (error) {
      throw new Error(`Failed to schedule post: ${error.message}`);
    }

    return {
      success: true,
      results: [],
      totalPlatforms: request.platforms.length,
      successfulPlatforms: 0,
      failedPlatforms: 0,
      publishingId,
      scheduledAt: request.scheduledAt
    };
  }

  /**
   * Get social account details
   */
  private async getSocialAccount(accountId: string): Promise<any> {
    const { data, error } = await this.supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .single();

    if (error) {
      console.error('Error fetching social account:', error);
      return null;
    }

    return data;
  }

  /**
   * Get business account details
   */
  private async getBusinessAccount(
    platform: string,
    socialAccountId: string,
    businessAccountId: string
  ): Promise<any> {
    switch (platform) {
      case 'facebook':
        return await this.facebookPagesManager.getPageData(socialAccountId, businessAccountId);
      case 'linkedin':
        return await this.linkedinCompaniesManager.getOrganizationData(socialAccountId, businessAccountId);
      default:
        return null;
    }
  }

  /**
   * Generate unique publishing ID
   */
  private generatePublishingId(): string {
    return `pub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log publishing attempt
   */
  private async logPublishingAttempt(publishingId: string, request: PublishingRequest): Promise<void> {
    try {
      await this.supabase
        .from('publishing_logs')
        .insert({
          id: publishingId,
          user_id: request.userId,
          platforms: request.platforms.map(p => p.platform),
          content_preview: request.content.substring(0, 100),
          status: 'started',
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Error logging publishing attempt:', error);
    }
  }

  /**
   * Log individual publishing result
   */
  private async logPublishingResult(publishingId: string, result: PublishingResult): Promise<void> {
    try {
      await this.supabase
        .from('publishing_results')
        .insert({
          publishing_id: publishingId,
          platform: result.platform,
          success: result.success,
          post_id: result.postId,
          post_url: result.postUrl,
          error_message: result.error,
          published_at: result.publishedAt,
          engagement_data: result.engagement,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Error logging publishing result:', error);
    }
  }

  /**
   * Update final publishing status
   */
  private async updatePublishingStatus(publishingId: string, response: PublishingResponse): Promise<void> {
    try {
      await this.supabase
        .from('publishing_logs')
        .update({
          status: response.success ? 'completed' : 'failed',
          successful_platforms: response.successfulPlatforms,
          failed_platforms: response.failedPlatforms,
          completed_at: new Date().toISOString()
        })
        .eq('id', publishingId);
    } catch (error) {
      console.error('Error updating publishing status:', error);
    }
  }
}
