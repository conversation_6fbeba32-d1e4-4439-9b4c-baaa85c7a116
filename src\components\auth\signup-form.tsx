"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { validateEmailForRegistration, validateEmailRealTime } from "@/lib/email/validation";
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";

const formSchema = z
  .object({
    name: z.string().min(2, {
      message: "الاسم يجب أن يكون حرفين على الأقل",
    }),
    email: z.string()
      .min(1, { message: "البريد الإلكتروني مطلوب" })
      .refine((email) => {
        const validation = validateEmailForRegistration(email);
        return validation.isValid;
      }, {
        message: "البريد الإلكتروني غير صالح أو غير مسموح",
      }),
    password: z.string().min(6, {
      message: "كلمة المرور يجب أن تكون 6 أحرف على الأقل",
    }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "كلمات المرور غير متطابقة",
    path: ["confirmPassword"],
  });

export function SignUpForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emailSuggestion, setEmailSuggestion] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const handleEmailChange = (email: string) => {
    const validation = validateEmailRealTime(email);
    if (validation.suggestion) {
      setEmailSuggestion(validation.suggestion);
    } else {
      setEmailSuggestion(null);
    }
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Starting registration process...');
      const supabase = createClient();

      // Use Supabase Auth to create user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
        options: {
          data: {
            name: values.name,
            role: 'USER'
          }
        }
      });

      if (authError) {
        console.error('Supabase auth error:', authError);
        throw new Error(authError.message || "حدث خطأ أثناء التسجيل");
      }

      if (!authData.user) {
        throw new Error("فشل في إنشاء المستخدم");
      }

      console.log('User created successfully:', authData.user.id);

      // Show success message
      toast.success("تم إنشاء الحساب بنجاح!");

      // Check if email confirmation is required
      if (authData.user && !authData.session) {
        toast.info("يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب");
        router.push("/auth/signin?message=check-email");
      } else {
        // User is automatically signed in
        router.push("/dashboard");
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message || "حدث خطأ أثناء التسجيل");
      toast.error(error.message || "حدث خطأ أثناء التسجيل");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="w-full" dir="rtl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">الاسم</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="أدخل اسمك"
                      className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">البريد الإلكتروني</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="أدخل بريدك الإلكتروني"
                      className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleEmailChange(e.target.value);
                      }}
                    />
                  </FormControl>
                  {emailSuggestion && (
                    <div className="text-sm text-blue-600">
                      هل تقصد:
                      <button
                        type="button"
                        className="underline mr-1"
                        onClick={() => {
                          form.setValue("email", emailSuggestion);
                          setEmailSuggestion(null);
                        }}
                      >
                        {emailSuggestion}
                      </button>
                      ؟
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">كلمة المرور</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="أدخل كلمة المرور"
                      className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">تأكيد كلمة المرور</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="أدخل كلمة المرور مرة أخرى"
                      className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600 text-sm text-center">{error}</p>
              </div>
            )}
            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  جاري التسجيل...
                </div>
              ) : (
                "إنشاء حساب"
              )}
            </Button>
          </form>
        </Form>

        {/* Sign In Link */}
        <div className="text-center mt-6">
          <p className="text-gray-600">
            لديك حساب بالفعل؟{" "}
            <Link href="/auth/signin" className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">
              تسجيل الدخول
            </Link>
          </p>
        </div>
    </div>
  );
}
