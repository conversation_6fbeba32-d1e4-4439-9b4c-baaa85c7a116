import { NextRequest, NextResponse } from 'next/server';
import { createTokenManager } from '@/lib/auth/token-manager';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Debug token manager functionality
 * GET /api/debug/token-manager?user_id=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id') || '3ddaeb03-2d95-4fff-abad-2a2c7dd25037'; // Demo User ID

    console.log('Debug: Testing token manager for user:', userId);

    const results = {
      userId,
      timestamp: new Date().toISOString(),
      tests: {} as any
    };

    // Test 1: Direct database query
    try {
      console.log('Test 1: Direct database query...');
      const supabase = createServiceRoleClient();
      
      const { data: directQuery, error: directError } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId);

      results.tests.directDatabaseQuery = {
        success: !directError,
        error: directError?.message,
        accountsFound: directQuery?.length || 0,
        accounts: directQuery?.map(acc => ({
          id: acc.id,
          platform: acc.platform,
          account_name: acc.account_name,
          created_at: acc.created_at
        })) || []
      };

      console.log('Direct query result:', results.tests.directDatabaseQuery);
    } catch (error: any) {
      results.tests.directDatabaseQuery = {
        success: false,
        error: error.message
      };
    }

    // Test 2: Token Manager with server client
    try {
      console.log('Test 2: Token Manager with server client...');
      const tokenManager = createTokenManager(true); // Server client
      const accounts = await tokenManager.getConnectedAccounts(userId);

      results.tests.tokenManagerServer = {
        success: true,
        accountsFound: accounts.length,
        accounts: accounts.map(acc => ({
          id: acc.id,
          platform: acc.platform,
          accountName: acc.accountName,
          createdAt: acc.createdAt
        }))
      };

      console.log('Token manager (server) result:', results.tests.tokenManagerServer);
    } catch (error: any) {
      results.tests.tokenManagerServer = {
        success: false,
        error: error.message
      };
    }

    // Test 3: Token Manager with client
    try {
      console.log('Test 3: Token Manager with client...');
      const tokenManager = createTokenManager(false); // Client
      const accounts = await tokenManager.getConnectedAccounts(userId);

      results.tests.tokenManagerClient = {
        success: true,
        accountsFound: accounts.length,
        accounts: accounts.map(acc => ({
          id: acc.id,
          platform: acc.platform,
          accountName: acc.accountName,
          createdAt: acc.createdAt
        }))
      };

      console.log('Token manager (client) result:', results.tests.tokenManagerClient);
    } catch (error: any) {
      results.tests.tokenManagerClient = {
        success: false,
        error: error.message
      };
    }

    // Test 4: Check specific LinkedIn account
    try {
      console.log('Test 4: Check specific LinkedIn account...');
      const tokenManager = createTokenManager(true);
      const linkedinAccount = await tokenManager.getTokens(userId, 'LINKEDIN');

      results.tests.linkedinSpecific = {
        success: !!linkedinAccount,
        found: !!linkedinAccount,
        account: linkedinAccount ? {
          id: linkedinAccount.id,
          platform: linkedinAccount.platform,
          accountName: linkedinAccount.accountName,
          accountId: linkedinAccount.accountId,
          hasAccessToken: !!linkedinAccount.accessToken,
          expiresAt: linkedinAccount.expiresAt?.toISOString()
        } : null
      };

      console.log('LinkedIn specific result:', results.tests.linkedinSpecific);
    } catch (error: any) {
      results.tests.linkedinSpecific = {
        success: false,
        error: error.message
      };
    }

    // Test 5: Check all users with LinkedIn accounts
    try {
      console.log('Test 5: Check all users with LinkedIn accounts...');
      const supabase = createServiceRoleClient();
      
      const { data: allLinkedIn, error: allError } = await supabase
        .from('social_accounts')
        .select('user_id, account_name, created_at')
        .eq('platform', 'LINKEDIN');

      results.tests.allLinkedInAccounts = {
        success: !allError,
        error: allError?.message,
        totalLinkedInAccounts: allLinkedIn?.length || 0,
        accounts: allLinkedIn?.map(acc => ({
          user_id: acc.user_id,
          account_name: acc.account_name,
          created_at: acc.created_at
        })) || []
      };

      console.log('All LinkedIn accounts result:', results.tests.allLinkedInAccounts);
    } catch (error: any) {
      results.tests.allLinkedInAccounts = {
        success: false,
        error: error.message
      };
    }

    return NextResponse.json({
      success: true,
      message: 'Token manager debug completed',
      results
    });

  } catch (error) {
    console.error('Token manager debug error:', error);
    return NextResponse.json({
      error: 'Debug test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Test with different user IDs
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id } = body;

    if (!user_id) {
      return NextResponse.json({
        error: 'Missing user_id in request body'
      }, { status: 400 });
    }

    // Redirect to GET with user_id as query parameter
    const url = new URL(request.url);
    url.searchParams.set('user_id', user_id);
    
    return NextResponse.redirect(url.toString());

  } catch (error) {
    return NextResponse.json({
      error: 'Invalid JSON in request body'
    }, { status: 400 });
  }
}
