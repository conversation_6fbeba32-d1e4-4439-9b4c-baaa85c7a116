/**
 * Enhanced Media Processing Service
 * Handles media upload, processing, optimization, and platform-specific formatting
 */

import { createClient } from '@/lib/supabase/client';

export interface MediaFile {
  id: string;
  userId: string;
  originalName: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  mediaType: 'image' | 'video' | 'document' | 'audio';
  url: string;
  thumbnailUrl?: string;
  metadata: MediaMetadata;
  platformVersions: PlatformMediaVersion[];
  uploadedAt: string;
  processedAt?: string;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  error?: string;
}

export interface MediaMetadata {
  width?: number;
  height?: number;
  duration?: number;
  aspectRatio?: string;
  colorProfile?: string;
  orientation?: number;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  camera?: {
    make?: string;
    model?: string;
    settings?: Record<string, any>;
  };
  tags?: string[];
  description?: string;
}

export interface PlatformMediaVersion {
  platform: 'facebook' | 'linkedin' | 'instagram' | 'twitter';
  url: string;
  width: number;
  height: number;
  fileSize: number;
  format: string;
  optimized: boolean;
  createdAt: string;
}

export interface MediaUploadRequest {
  file: File;
  userId: string;
  description?: string;
  tags?: string[];
  targetPlatforms?: string[];
  autoOptimize?: boolean;
}

export interface MediaUploadResponse {
  success: boolean;
  mediaFile?: MediaFile;
  uploadId: string;
  error?: string;
  processingStatus: 'pending' | 'processing' | 'complete' | 'failed';
}

export interface MediaProcessingOptions {
  platforms: string[];
  quality: 'low' | 'medium' | 'high' | 'original';
  autoResize: boolean;
  generateThumbnails: boolean;
  optimizeForWeb: boolean;
  preserveMetadata: boolean;
}

export class EnhancedMediaProcessingService {
  private supabase = createClient();
  private maxFileSize = 100 * 1024 * 1024; // 100MB
  private allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  private allowedVideoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/webm'];
  private allowedDocumentTypes = ['application/pdf', 'text/plain', 'application/msword'];

  // Platform-specific media requirements
  private platformRequirements = {
    facebook: {
      image: { maxWidth: 2048, maxHeight: 2048, maxSize: 4 * 1024 * 1024, formats: ['jpg', 'png'] },
      video: { maxWidth: 1920, maxHeight: 1080, maxSize: 100 * 1024 * 1024, maxDuration: 240, formats: ['mp4', 'mov'] }
    },
    linkedin: {
      image: { maxWidth: 1200, maxHeight: 1200, maxSize: 5 * 1024 * 1024, formats: ['jpg', 'png', 'gif'] },
      video: { maxWidth: 1920, maxHeight: 1080, maxSize: 200 * 1024 * 1024, maxDuration: 600, formats: ['mp4', 'mov', 'wmv'] }
    },
    instagram: {
      image: { maxWidth: 1080, maxHeight: 1080, maxSize: 8 * 1024 * 1024, formats: ['jpg', 'png'] },
      video: { maxWidth: 1080, maxHeight: 1920, maxSize: 100 * 1024 * 1024, maxDuration: 60, formats: ['mp4', 'mov'] }
    },
    twitter: {
      image: { maxWidth: 1024, maxHeight: 512, maxSize: 5 * 1024 * 1024, formats: ['jpg', 'png', 'gif', 'webp'] },
      video: { maxWidth: 1280, maxHeight: 1024, maxSize: 512 * 1024 * 1024, maxDuration: 140, formats: ['mp4', 'mov'] }
    }
  };

  /**
   * Upload and process media file
   */
  async uploadMedia(request: MediaUploadRequest): Promise<MediaUploadResponse> {
    console.log('📤 Starting media upload process...');
    console.log(`File: ${request.file.name}, Size: ${request.file.size}, Type: ${request.file.type}`);

    const uploadId = this.generateUploadId();

    try {
      // Validate file
      const validation = await this.validateFile(request.file);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Create media record
      const mediaFile = await this.createMediaRecord(request, uploadId);

      // Upload to storage
      const uploadResult = await this.uploadToStorage(request.file, mediaFile);
      if (!uploadResult.success) {
        throw new Error(uploadResult.error);
      }

      // Update media record with upload URL
      mediaFile.url = uploadResult.url;
      mediaFile.status = 'processing';

      // Extract metadata
      const metadata = await this.extractMetadata(request.file);
      mediaFile.metadata = metadata;

      // Process for platforms if requested
      if (request.autoOptimize && request.targetPlatforms) {
        const platformVersions = await this.processForPlatforms(
          mediaFile,
          request.targetPlatforms
        );
        mediaFile.platformVersions = platformVersions;
      }

      // Generate thumbnail for videos and large images
      if (mediaFile.mediaType === 'video' || 
          (mediaFile.mediaType === 'image' && mediaFile.fileSize > 1024 * 1024)) {
        const thumbnailUrl = await this.generateThumbnail(mediaFile);
        mediaFile.thumbnailUrl = thumbnailUrl;
      }

      // Update final status
      mediaFile.status = 'ready';
      mediaFile.processedAt = new Date().toISOString();

      // Save to database
      await this.saveMediaRecord(mediaFile);

      console.log('✅ Media upload and processing completed successfully');

      return {
        success: true,
        mediaFile,
        uploadId,
        processingStatus: 'complete'
      };

    } catch (error: any) {
      console.error('❌ Media upload error:', error);

      // Update status to error
      try {
        await this.updateMediaStatus(uploadId, 'error', error.message);
      } catch (updateError) {
        console.error('Error updating media status:', updateError);
      }

      return {
        success: false,
        uploadId,
        error: error.message,
        processingStatus: 'failed'
      };
    }
  }

  /**
   * Process existing media for specific platforms
   */
  async processForPlatforms(
    mediaFile: MediaFile,
    platforms: string[]
  ): Promise<PlatformMediaVersion[]> {
    console.log(`🔄 Processing media for platforms: ${platforms.join(', ')}`);

    const platformVersions: PlatformMediaVersion[] = [];

    for (const platform of platforms) {
      try {
        const requirements = this.platformRequirements[platform as keyof typeof this.platformRequirements];
        if (!requirements) {
          console.warn(`No requirements found for platform: ${platform}`);
          continue;
        }

        const mediaTypeReq = requirements[mediaFile.mediaType as keyof typeof requirements];
        if (!mediaTypeReq) {
          console.warn(`Platform ${platform} doesn't support media type: ${mediaFile.mediaType}`);
          continue;
        }

        // Check if media needs processing
        const needsProcessing = this.needsProcessingForPlatform(mediaFile, platform, mediaTypeReq);

        let processedUrl = mediaFile.url;
        let processedWidth = mediaFile.metadata.width || 0;
        let processedHeight = mediaFile.metadata.height || 0;
        let processedSize = mediaFile.fileSize;

        if (needsProcessing) {
          // Process media for platform
          const processed = await this.processMediaForPlatform(
            mediaFile,
            platform,
            mediaTypeReq
          );

          processedUrl = processed.url;
          processedWidth = processed.width;
          processedHeight = processed.height;
          processedSize = processed.fileSize;
        }

        const platformVersion: PlatformMediaVersion = {
          platform: platform as any,
          url: processedUrl,
          width: processedWidth,
          height: processedHeight,
          fileSize: processedSize,
          format: this.getFileExtension(processedUrl),
          optimized: needsProcessing,
          createdAt: new Date().toISOString()
        };

        platformVersions.push(platformVersion);

      } catch (error: any) {
        console.error(`Error processing for platform ${platform}:`, error);
        // Continue with other platforms
      }
    }

    return platformVersions;
  }

  /**
   * Get media files for user
   */
  async getUserMedia(
    userId: string,
    options: {
      mediaType?: string;
      limit?: number;
      offset?: number;
      sortBy?: 'uploadedAt' | 'fileName' | 'fileSize';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ media: MediaFile[]; total: number }> {
    try {
      let query = this.supabase
        .from('media_files')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .eq('status', 'ready');

      if (options.mediaType) {
        query = query.eq('media_type', options.mediaType);
      }

      if (options.sortBy) {
        query = query.order(options.sortBy, { 
          ascending: options.sortOrder === 'asc' 
        });
      } else {
        query = query.order('uploaded_at', { ascending: false });
      }

      if (options.limit) {
        const offset = options.offset || 0;
        query = query.range(offset, offset + options.limit - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`Failed to fetch media: ${error.message}`);
      }

      const media = (data || []).map(this.mapDatabaseToMediaFile);

      return {
        media,
        total: count || 0
      };

    } catch (error: any) {
      console.error('Error fetching user media:', error);
      throw error;
    }
  }

  /**
   * Delete media file
   */
  async deleteMedia(mediaId: string, userId: string): Promise<boolean> {
    try {
      // Get media record
      const { data: mediaRecord, error: fetchError } = await this.supabase
        .from('media_files')
        .select('*')
        .eq('id', mediaId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !mediaRecord) {
        throw new Error('Media file not found');
      }

      // Delete from storage
      await this.deleteFromStorage(mediaRecord.file_name);

      // Delete platform versions from storage
      if (mediaRecord.platform_versions) {
        for (const version of mediaRecord.platform_versions) {
          await this.deleteFromStorage(this.getFileNameFromUrl(version.url));
        }
      }

      // Delete thumbnail from storage
      if (mediaRecord.thumbnail_url) {
        await this.deleteFromStorage(this.getFileNameFromUrl(mediaRecord.thumbnail_url));
      }

      // Delete from database
      const { error: deleteError } = await this.supabase
        .from('media_files')
        .delete()
        .eq('id', mediaId)
        .eq('user_id', userId);

      if (deleteError) {
        throw new Error(`Failed to delete media record: ${deleteError.message}`);
      }

      console.log('✅ Media file deleted successfully');
      return true;

    } catch (error: any) {
      console.error('❌ Error deleting media:', error);
      throw error;
    }
  }

  /**
   * Validate uploaded file
   */
  private async validateFile(file: File): Promise<{ valid: boolean; error?: string }> {
    // Check file size
    if (file.size > this.maxFileSize) {
      return {
        valid: false,
        error: `File size too large. Maximum size is ${this.maxFileSize / (1024 * 1024)}MB`
      };
    }

    // Check file type
    const mediaType = this.getMediaType(file.type);
    if (!mediaType) {
      return {
        valid: false,
        error: `Unsupported file type: ${file.type}`
      };
    }

    // Additional validation based on media type
    if (mediaType === 'image' && !this.allowedImageTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Unsupported image type: ${file.type}`
      };
    }

    if (mediaType === 'video' && !this.allowedVideoTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Unsupported video type: ${file.type}`
      };
    }

    if (mediaType === 'document' && !this.allowedDocumentTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Unsupported document type: ${file.type}`
      };
    }

    return { valid: true };
  }

  /**
   * Create initial media record
   */
  private async createMediaRecord(
    request: MediaUploadRequest,
    uploadId: string
  ): Promise<MediaFile> {
    const fileName = `${uploadId}_${this.sanitizeFileName(request.file.name)}`;
    const mediaType = this.getMediaType(request.file.type);

    return {
      id: uploadId,
      userId: request.userId,
      originalName: request.file.name,
      fileName,
      fileSize: request.file.size,
      mimeType: request.file.type,
      mediaType: mediaType!,
      url: '',
      metadata: {
        tags: request.tags || [],
        description: request.description
      },
      platformVersions: [],
      uploadedAt: new Date().toISOString(),
      status: 'uploading'
    };
  }

  /**
   * Upload file to storage
   */
  private async uploadToStorage(
    file: File,
    mediaFile: MediaFile
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      console.log('📤 Uploading to Supabase storage...');

      const { data, error } = await this.supabase.storage
        .from('media')
        .upload(`${mediaFile.userId}/${mediaFile.fileName}`, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        throw new Error(`Storage upload failed: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = this.supabase.storage
        .from('media')
        .getPublicUrl(`${mediaFile.userId}/${mediaFile.fileName}`);

      return {
        success: true,
        url: urlData.publicUrl
      };

    } catch (error: any) {
      console.error('Storage upload error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract metadata from file
   */
  private async extractMetadata(file: File): Promise<MediaMetadata> {
    const metadata: MediaMetadata = {};

    try {
      if (file.type.startsWith('image/')) {
        // Extract image metadata
        const imageMetadata = await this.extractImageMetadata(file);
        Object.assign(metadata, imageMetadata);
      } else if (file.type.startsWith('video/')) {
        // Extract video metadata
        const videoMetadata = await this.extractVideoMetadata(file);
        Object.assign(metadata, videoMetadata);
      }
    } catch (error) {
      console.warn('Error extracting metadata:', error);
    }

    return metadata;
  }

  /**
   * Extract image metadata
   */
  private async extractImageMetadata(file: File): Promise<Partial<MediaMetadata>> {
    return new Promise((resolve) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        const metadata: Partial<MediaMetadata> = {
          width: img.naturalWidth,
          height: img.naturalHeight,
          aspectRatio: `${img.naturalWidth}:${img.naturalHeight}`
        };

        URL.revokeObjectURL(url);
        resolve(metadata);
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        resolve({});
      };

      img.src = url;
    });
  }

  /**
   * Extract video metadata
   */
  private async extractVideoMetadata(file: File): Promise<Partial<MediaMetadata>> {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      const url = URL.createObjectURL(file);

      video.onloadedmetadata = () => {
        const metadata: Partial<MediaMetadata> = {
          width: video.videoWidth,
          height: video.videoHeight,
          duration: video.duration,
          aspectRatio: `${video.videoWidth}:${video.videoHeight}`
        };

        URL.revokeObjectURL(url);
        resolve(metadata);
      };

      video.onerror = () => {
        URL.revokeObjectURL(url);
        resolve({});
      };

      video.src = url;
    });
  }

  /**
   * Helper methods
   */
  private generateUploadId(): string {
    return `media_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getMediaType(mimeType: string): 'image' | 'video' | 'document' | 'audio' | null {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) return 'document';
    return null;
  }

  private sanitizeFileName(fileName: string): string {
    return fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
  }

  private getFileExtension(url: string): string {
    return url.split('.').pop()?.toLowerCase() || '';
  }

  private getFileNameFromUrl(url: string): string {
    return url.split('/').pop() || '';
  }

  private needsProcessingForPlatform(
    mediaFile: MediaFile,
    platform: string,
    requirements: any
  ): boolean {
    if (!mediaFile.metadata.width || !mediaFile.metadata.height) return true;
    
    return (
      mediaFile.metadata.width > requirements.maxWidth ||
      mediaFile.metadata.height > requirements.maxHeight ||
      mediaFile.fileSize > requirements.maxSize
    );
  }

  private async processMediaForPlatform(
    mediaFile: MediaFile,
    platform: string,
    requirements: any
  ): Promise<{ url: string; width: number; height: number; fileSize: number }> {
    // TODO: Implement actual media processing (resize, compress, format conversion)
    // For now, return original media
    console.log(`⚠️ Media processing for ${platform} not yet implemented`);
    
    return {
      url: mediaFile.url,
      width: mediaFile.metadata.width || 0,
      height: mediaFile.metadata.height || 0,
      fileSize: mediaFile.fileSize
    };
  }

  private async generateThumbnail(mediaFile: MediaFile): Promise<string> {
    // TODO: Implement thumbnail generation
    console.log('⚠️ Thumbnail generation not yet implemented');
    return mediaFile.url;
  }

  private async saveMediaRecord(mediaFile: MediaFile): Promise<void> {
    const { error } = await this.supabase
      .from('media_files')
      .upsert({
        id: mediaFile.id,
        user_id: mediaFile.userId,
        original_name: mediaFile.originalName,
        file_name: mediaFile.fileName,
        file_size: mediaFile.fileSize,
        mime_type: mediaFile.mimeType,
        media_type: mediaFile.mediaType,
        url: mediaFile.url,
        thumbnail_url: mediaFile.thumbnailUrl,
        metadata: mediaFile.metadata,
        platform_versions: mediaFile.platformVersions,
        uploaded_at: mediaFile.uploadedAt,
        processed_at: mediaFile.processedAt,
        status: mediaFile.status,
        error: mediaFile.error
      });

    if (error) {
      throw new Error(`Failed to save media record: ${error.message}`);
    }
  }

  private async updateMediaStatus(
    uploadId: string,
    status: string,
    error?: string
  ): Promise<void> {
    await this.supabase
      .from('media_files')
      .update({ status, error })
      .eq('id', uploadId);
  }

  private async deleteFromStorage(fileName: string): Promise<void> {
    try {
      await this.supabase.storage
        .from('media')
        .remove([fileName]);
    } catch (error) {
      console.warn('Error deleting from storage:', error);
    }
  }

  private mapDatabaseToMediaFile(record: any): MediaFile {
    return {
      id: record.id,
      userId: record.user_id,
      originalName: record.original_name,
      fileName: record.file_name,
      fileSize: record.file_size,
      mimeType: record.mime_type,
      mediaType: record.media_type,
      url: record.url,
      thumbnailUrl: record.thumbnail_url,
      metadata: record.metadata || {},
      platformVersions: record.platform_versions || [],
      uploadedAt: record.uploaded_at,
      processedAt: record.processed_at,
      status: record.status,
      error: record.error
    };
  }
}
