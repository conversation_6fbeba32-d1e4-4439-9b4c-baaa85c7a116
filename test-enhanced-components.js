// Test script to verify enhanced social media components
// This script checks for Arabic text and RTL layout in the enhanced components

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Enhanced Social Media Components...\n');

// Test files to check
const testFiles = [
  'src/components/social/enhanced-social-accounts.tsx',
  'src/components/analytics/enhanced-analytics-dashboard.tsx',
  'src/components/testing/enhanced-api-testing.tsx'
];

// Arabic text patterns to verify
const arabicPatterns = [
  'إدارة الحسابات الاجتماعية',
  'لوحة التحليلات المتقدمة',
  'اختبار تكامل واجهات برمجة التطبيقات',
  'نظرة عامة',
  'التحليلات',
  'الاختبارات',
  'متصل',
  'غير متصل',
  'تحديث',
  'تصدير'
];

// RTL layout patterns
const rtlPatterns = [
  'dir="rtl"',
  'direction: rtl',
  'text-right',
  'mr-2', // margin-right
  'ml-2'  // margin-left (should be minimal in RTL)
];

// English text patterns that should NOT exist
const englishPatterns = [
  'language?: \'ar\' | \'en\'',
  'language = \'ar\'',
  'currentText.',
  'Connected',
  'Disconnected',
  'Overview',
  'Analytics',
  'Testing',
  'Success',
  'Error',
  'Loading...',
  'No data available'
];

let totalTests = 0;
let passedTests = 0;

function testFile(filePath) {
  console.log(`📁 Testing: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}\n`);
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  let fileTests = 0;
  let filePassed = 0;
  
  // Test 1: Check for Arabic text
  console.log('  🔤 Checking Arabic text...');
  let arabicFound = 0;
  arabicPatterns.forEach(pattern => {
    if (content.includes(pattern)) {
      arabicFound++;
    }
  });
  
  fileTests++;
  totalTests++;
  if (arabicFound >= 5) { // At least 5 Arabic patterns should be found
    console.log(`  ✅ Arabic text found (${arabicFound}/${arabicPatterns.length} patterns)`);
    filePassed++;
    passedTests++;
  } else {
    console.log(`  ❌ Insufficient Arabic text (${arabicFound}/${arabicPatterns.length} patterns)`);
  }
  
  // Test 2: Check for RTL layout
  console.log('  🔄 Checking RTL layout...');
  let rtlFound = 0;
  rtlPatterns.forEach(pattern => {
    if (content.includes(pattern)) {
      rtlFound++;
    }
  });
  
  fileTests++;
  totalTests++;
  if (rtlFound >= 1) { // At least 1 RTL pattern should be found
    console.log(`  ✅ RTL layout found (${rtlFound}/${rtlPatterns.length} patterns)`);
    filePassed++;
    passedTests++;
  } else {
    console.log(`  ❌ No RTL layout patterns found`);
  }
  
  // Test 3: Check for English text (should be minimal)
  console.log('  🚫 Checking for English text...');
  let englishFound = 0;
  englishPatterns.forEach(pattern => {
    if (content.includes(pattern)) {
      englishFound++;
    }
  });
  
  fileTests++;
  totalTests++;
  if (englishFound === 0) {
    console.log(`  ✅ No English text found`);
    filePassed++;
    passedTests++;
  } else {
    console.log(`  ❌ English text still present (${englishFound} patterns)`);
    englishPatterns.forEach(pattern => {
      if (content.includes(pattern)) {
        console.log(`    - Found: "${pattern}"`);
      }
    });
  }
  
  // Test 4: Check for language prop removal
  console.log('  🌐 Checking language prop removal...');
  fileTests++;
  totalTests++;
  if (!content.includes('language?:') && !content.includes('language =')) {
    console.log(`  ✅ Language prop removed`);
    filePassed++;
    passedTests++;
  } else {
    console.log(`  ❌ Language prop still present`);
  }
  
  console.log(`  📊 File Score: ${filePassed}/${fileTests} tests passed\n`);
}

// Run tests
testFiles.forEach(testFile);

// Summary
console.log('📋 TEST SUMMARY');
console.log('================');
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${totalTests - passedTests}`);
console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 All tests passed! Enhanced components are ready for Arabic/RTL usage.');
} else {
  console.log('\n⚠️  Some tests failed. Please review the issues above.');
}
