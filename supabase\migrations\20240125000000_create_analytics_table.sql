-- Create analytics table for storing post performance metrics
CREATE TABLE IF NOT EXISTS analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  platform TEXT NOT NULL CHECK (platform IN ('TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK', 'SNAPCHAT')),
  
  -- Engagement metrics
  likes INTEGER DEFAULT 0 CHECK (likes >= 0),
  shares INTEGER DEFAULT 0 CHECK (shares >= 0),
  comments INTEGER DEFAULT 0 CHECK (comments >= 0),
  
  -- Reach and impression metrics
  reach INTEGER DEFAULT 0 CHECK (reach >= 0),
  impressions INTEGER DEFAULT 0 CHECK (impressions >= 0),
  clicks INTEGER DEFAULT 0 CHECK (clicks >= 0),
  
  -- Calculated metrics
  engagement_rate DECIMAL(5,2) DEFAULT 0 CHECK (engagement_rate >= 0 AND engagement_rate <= 100),
  
  -- Platform-specific data
  platform_post_id TEXT, -- ID of the post on the specific platform
  
  -- Timestamps
  collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_analytics_post_id ON analytics(post_id);
CREATE INDEX IF NOT EXISTS idx_analytics_platform ON analytics(platform);
CREATE INDEX IF NOT EXISTS idx_analytics_collected_at ON analytics(collected_at);
CREATE INDEX IF NOT EXISTS idx_analytics_post_platform ON analytics(post_id, platform);

-- Create unique constraint to prevent duplicate analytics for same post/platform
CREATE UNIQUE INDEX IF NOT EXISTS idx_analytics_post_platform_unique 
ON analytics(post_id, platform);

-- Add RLS (Row Level Security) policies
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see analytics for their own posts
CREATE POLICY "Users can view their own analytics" ON analytics
  FOR SELECT USING (
    post_id IN (
      SELECT id FROM posts WHERE user_id = auth.uid()
    )
  );

-- Policy: Users can only insert analytics for their own posts
CREATE POLICY "Users can insert analytics for their posts" ON analytics
  FOR INSERT WITH CHECK (
    post_id IN (
      SELECT id FROM posts WHERE user_id = auth.uid()
    )
  );

-- Policy: Users can only update analytics for their own posts
CREATE POLICY "Users can update their own analytics" ON analytics
  FOR UPDATE USING (
    post_id IN (
      SELECT id FROM posts WHERE user_id = auth.uid()
    )
  );

-- Policy: Users can only delete analytics for their own posts
CREATE POLICY "Users can delete their own analytics" ON analytics
  FOR DELETE USING (
    post_id IN (
      SELECT id FROM posts WHERE user_id = auth.uid()
    )
  );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_analytics_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_analytics_updated_at
  BEFORE UPDATE ON analytics
  FOR EACH ROW
  EXECUTE FUNCTION update_analytics_updated_at();

-- Insert some sample analytics data for testing
INSERT INTO analytics (post_id, platform, likes, shares, comments, reach, impressions, clicks, engagement_rate, platform_post_id)
SELECT 
  p.id,
  'TWITTER',
  FLOOR(RANDOM() * 100) + 10, -- likes: 10-109
  FLOOR(RANDOM() * 20) + 2,   -- shares: 2-21
  FLOOR(RANDOM() * 30) + 5,   -- comments: 5-34
  FLOOR(RANDOM() * 1000) + 100, -- reach: 100-1099
  FLOOR(RANDOM() * 2000) + 200, -- impressions: 200-2199
  FLOOR(RANDOM() * 50) + 5,   -- clicks: 5-54
  ROUND((RANDOM() * 8 + 2)::numeric, 2), -- engagement_rate: 2-10%
  'twitter_' || FLOOR(RANDOM() * 1000000)::text
FROM posts p
WHERE p.status = 'PUBLISHED'
LIMIT 5
ON CONFLICT (post_id, platform) DO NOTHING;

INSERT INTO analytics (post_id, platform, likes, shares, comments, reach, impressions, clicks, engagement_rate, platform_post_id)
SELECT 
  p.id,
  'FACEBOOK',
  FLOOR(RANDOM() * 150) + 20, -- likes: 20-169
  FLOOR(RANDOM() * 30) + 5,   -- shares: 5-34
  FLOOR(RANDOM() * 40) + 8,   -- comments: 8-47
  FLOOR(RANDOM() * 1500) + 200, -- reach: 200-1699
  FLOOR(RANDOM() * 3000) + 400, -- impressions: 400-3399
  FLOOR(RANDOM() * 80) + 10,  -- clicks: 10-89
  ROUND((RANDOM() * 6 + 3)::numeric, 2), -- engagement_rate: 3-9%
  'facebook_' || FLOOR(RANDOM() * 1000000)::text
FROM posts p
WHERE p.status = 'PUBLISHED'
LIMIT 3
ON CONFLICT (post_id, platform) DO NOTHING;

INSERT INTO analytics (post_id, platform, likes, shares, comments, reach, impressions, clicks, engagement_rate, platform_post_id)
SELECT 
  p.id,
  'INSTAGRAM',
  FLOOR(RANDOM() * 200) + 30, -- likes: 30-229
  FLOOR(RANDOM() * 15) + 2,   -- shares: 2-16
  FLOOR(RANDOM() * 25) + 5,   -- comments: 5-29
  FLOOR(RANDOM() * 800) + 150, -- reach: 150-949
  FLOOR(RANDOM() * 1200) + 300, -- impressions: 300-1499
  FLOOR(RANDOM() * 40) + 8,   -- clicks: 8-47
  ROUND((RANDOM() * 10 + 4)::numeric, 2), -- engagement_rate: 4-14%
  'instagram_' || FLOOR(RANDOM() * 1000000)::text
FROM posts p
WHERE p.status = 'PUBLISHED'
LIMIT 4
ON CONFLICT (post_id, platform) DO NOTHING;

-- Create a view for easy analytics querying
CREATE OR REPLACE VIEW analytics_summary AS
SELECT 
  a.post_id,
  p.content,
  p.published_at,
  p.user_id,
  COUNT(DISTINCT a.platform) as platforms_count,
  SUM(a.likes) as total_likes,
  SUM(a.shares) as total_shares,
  SUM(a.comments) as total_comments,
  SUM(a.reach) as total_reach,
  SUM(a.impressions) as total_impressions,
  SUM(a.clicks) as total_clicks,
  AVG(a.engagement_rate) as avg_engagement_rate,
  SUM(a.likes + a.shares + a.comments) as total_engagement
FROM analytics a
JOIN posts p ON a.post_id = p.id
GROUP BY a.post_id, p.content, p.published_at, p.user_id;

-- Grant necessary permissions
GRANT SELECT ON analytics_summary TO authenticated;

-- Add comment to table
COMMENT ON TABLE analytics IS 'Stores performance metrics for social media posts across different platforms';
COMMENT ON COLUMN analytics.engagement_rate IS 'Engagement rate as a percentage (0-100)';
COMMENT ON COLUMN analytics.platform_post_id IS 'The ID of the post on the specific social media platform';
COMMENT ON VIEW analytics_summary IS 'Aggregated analytics data per post across all platforms';
