// Simple test to verify the Docker container is working
const http = require('http');

console.log('🚀 Testing Docker container...');
console.log('Environment:', process.env.NODE_ENV);
console.log('Port:', process.env.PORT || 3000);
console.log('Hostname:', process.env.HOSTNAME || '0.0.0.0');

// Test if we can make a request to the health endpoint
const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3000,
  path: '/api/health',
  method: 'GET'
};

const req = http.request(options, (res) => {
  console.log(`✅ Health check status: ${res.statusCode}`);
  res.on('data', (chunk) => {
    console.log(`Response: ${chunk}`);
  });
});

req.on('error', (e) => {
  console.error(`❌ Health check failed: ${e.message}`);
});

req.end();

// Also test the root path
const rootOptions = {
  hostname: 'localhost',
  port: process.env.PORT || 3000,
  path: '/',
  method: 'GET'
};

const rootReq = http.request(rootOptions, (res) => {
  console.log(`✅ Root path status: ${res.statusCode}`);
});

rootReq.on('error', (e) => {
  console.error(`❌ Root path failed: ${e.message}`);
});

rootReq.end();