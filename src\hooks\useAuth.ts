"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import type { User } from '@supabase/supabase-js';

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
  });
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        
        if (error) {
          console.error('Auth error:', error);
          setAuthState({
            user: null,
            loading: false,
            error: error.message,
          });
        } else {
          setAuthState({
            user,
            loading: false,
            error: null,
          });
        }
      } catch (error: any) {
        console.error('Auth check failed:', error);
        setAuthState({
          user: null,
          loading: false,
          error: error.message || 'Authentication failed',
        });
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        setAuthState({
          user: session?.user ?? null,
          loading: false,
          error: null,
        });

        // Handle auth events
        if (event === 'SIGNED_IN') {
          console.log('User signed in:', session?.user?.email);
        } else if (event === 'SIGNED_OUT') {
          console.log('User signed out');
          router.push('/auth/signin');
        } else if (event === 'TOKEN_REFRESHED') {
          console.log('Token refreshed for user:', session?.user?.email);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth, router]);

  const signOut = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Sign out error:', error);
        setAuthState(prev => ({
          ...prev,
          loading: false,
          error: error.message,
        }));
      } else {
        setAuthState({
          user: null,
          loading: false,
          error: null,
        });
        router.push('/auth/signin');
      }
    } catch (error: any) {
      console.error('Sign out failed:', error);
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Sign out failed',
      }));
    }
  };

  const refreshSession = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }));
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        setAuthState({
          user: null,
          loading: false,
          error: error.message,
        });
      } else {
        setAuthState({
          user,
          loading: false,
          error: null,
        });
      }
    } catch (error: any) {
      setAuthState({
        user: null,
        loading: false,
        error: error.message || 'Session refresh failed',
      });
    }
  };

  return {
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    isAuthenticated: !!authState.user,
    signOut,
    refreshSession,
  };
}

// Hook for protecting routes
export function useRequireAuth() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    }
  }, [user, loading, router]);

  return { user, loading };
}

// Hook for redirecting authenticated users
export function useRedirectIfAuthenticated() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  return { user, loading };
}
