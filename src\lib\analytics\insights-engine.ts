import { createClient } from '@/lib/supabase/server';
import { SchedulerLogger } from '../scheduler/scheduler-logger';

export interface ContentInsight {
  type: string;
  category: string;
  sentiment: string;
  length: number;
  hashtags: string[];
  hashtagCount: number;
  trendingHashtags: string[];
  avgEngagementRate: number;
  avgReach: number;
  performanceScore: number;
  bestPostingTimes: Record<string, number[]>;
  bestPostingDays: number[];
  platformPerformance: Record<string, any>;
  recommendations: string[];
}

export interface TrendAnalysis {
  hashtag: string;
  platform: string;
  usageCount: number;
  uniqueUsers: number;
  totalEngagement: number;
  trendScore: number;
  growthRate: number;
  peakUsageTime: Date | null;
  topCountries: string[];
  ageGroups: Record<string, number>;
  recommendation: string;
}

export interface PredictiveInsight {
  metric: string;
  currentValue: number;
  predictedValue: number;
  confidence: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  timeframe: string;
  factors: string[];
  recommendations: string[];
}

/**
 * AI-powered insights engine for content analysis and recommendations
 */
export class InsightsEngine {
  private logger: SchedulerLogger;

  constructor() {
    this.logger = new SchedulerLogger('insights-engine');
  }

  /**
   * Generate comprehensive content insights for a user
   */
  async generateContentInsights(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<ContentInsight[]> {
    try {
      this.logger.info('Generating content insights', { userId });

      const supabase = createClient();

      // Default to last 90 days if no dates provided
      const defaultEndDate = endDate || new Date();
      const defaultStartDate = startDate || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

      // Get posts with analytics data
      const { data: posts, error } = await supabase
        .from('posts')
        .select(`
          id,
          content,
          media_url,
          platforms,
          published_at,
          post_analytics (
            platform,
            engagement_rate,
            likes_count,
            comments_count,
            shares_count,
            reach,
            impressions
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'PUBLISHED')
        .gte('published_at', defaultStartDate.toISOString())
        .lte('published_at', defaultEndDate.toISOString());

      if (error) {
        throw error;
      }

      if (!posts || posts.length === 0) {
        return [];
      }

      // Analyze content patterns
      const contentTypes = this.categorizeContent(posts);
      const insights: ContentInsight[] = [];

      for (const [contentType, typePosts] of Object.entries(contentTypes)) {
        const insight = await this.analyzeContentType(contentType, typePosts as any[]);
        insights.push(insight);
      }

      // Store insights in database
      for (const insight of insights) {
        await this.storeContentInsight(userId, insight, defaultStartDate, defaultEndDate);
      }

      this.logger.info('Content insights generated successfully', {
        userId,
        insightsCount: insights.length,
      });

      return insights;

    } catch (error) {
      this.logger.error('Failed to generate content insights', error, { userId });
      throw error;
    }
  }

  /**
   * Categorize content by type
   */
  private categorizeContent(posts: any[]): Record<string, any[]> {
    const categories: Record<string, any[]> = {
      text: [],
      image: [],
      video: [],
      carousel: [],
    };

    posts.forEach(post => {
      let category = 'text';

      if (post.media_url) {
        if (this.isVideoUrl(post.media_url)) {
          category = 'video';
        } else if (Array.isArray(post.media_url)) {
          category = 'carousel';
        } else {
          category = 'image';
        }
      }

      categories[category].push(post);
    });

    // Remove empty categories
    Object.keys(categories).forEach(key => {
      if (categories[key].length === 0) {
        delete categories[key];
      }
    });

    return categories;
  }

  /**
   * Check if URL is a video
   */
  private isVideoUrl(url: string): boolean {
    if (typeof url !== 'string') return false;
    const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm'];
    return videoExtensions.some(ext => url.toLowerCase().includes(ext));
  }

  /**
   * Analyze a specific content type
   */
  private async analyzeContentType(contentType: string, posts: any[]): Promise<ContentInsight> {
    // Extract hashtags from all posts
    const allHashtags = this.extractHashtags(posts);
    const hashtagCounts = this.countHashtags(allHashtags);
    const topHashtags = Object.entries(hashtagCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([hashtag]) => hashtag);

    // Calculate performance metrics
    const performanceMetrics = this.calculatePerformanceMetrics(posts);

    // Analyze posting patterns
    const postingPatterns = this.analyzePostingPatterns(posts);

    // Generate sentiment analysis (simplified)
    const sentiment = this.analyzeSentiment(posts);

    // Calculate performance score
    const performanceScore = this.calculatePerformanceScore(performanceMetrics);

    // Generate recommendations
    const recommendations = this.generateContentRecommendations(
      contentType,
      performanceMetrics,
      postingPatterns,
      topHashtags
    );

    return {
      type: contentType,
      category: this.getContentCategory(posts),
      sentiment,
      length: this.getAverageContentLength(posts),
      hashtags: topHashtags,
      hashtagCount: Math.round(allHashtags.length / posts.length),
      trendingHashtags: await this.getTrendingHashtags(topHashtags),
      avgEngagementRate: performanceMetrics.avgEngagementRate,
      avgReach: performanceMetrics.avgReach,
      performanceScore,
      bestPostingTimes: postingPatterns.bestTimes,
      bestPostingDays: postingPatterns.bestDays,
      platformPerformance: performanceMetrics.platformBreakdown,
      recommendations,
    };
  }

  /**
   * Extract hashtags from posts
   */
  private extractHashtags(posts: any[]): string[] {
    const hashtags: string[] = [];

    posts.forEach(post => {
      if (post.content) {
        const matches = post.content.match(/#[\w\u0600-\u06FF]+/g);
        if (matches) {
          hashtags.push(...matches.map((tag: string) => tag.toLowerCase()));
        }
      }
    });

    return hashtags;
  }

  /**
   * Count hashtag occurrences
   */
  private countHashtags(hashtags: string[]): Record<string, number> {
    const counts: Record<string, number> = {};

    hashtags.forEach(hashtag => {
      counts[hashtag] = (counts[hashtag] || 0) + 1;
    });

    return counts;
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(posts: any[]): any {
    let totalEngagementRate = 0;
    let totalReach = 0;
    let totalImpressions = 0;
    let analyticsCount = 0;
    const platformMetrics: Record<string, any> = {};

    posts.forEach(post => {
      if (post.post_analytics && post.post_analytics.length > 0) {
        post.post_analytics.forEach((analytics: any) => {
          totalEngagementRate += analytics.engagement_rate || 0;
          totalReach += analytics.reach || 0;
          totalImpressions += analytics.impressions || 0;
          analyticsCount++;

          // Platform-specific metrics
          if (!platformMetrics[analytics.platform]) {
            platformMetrics[analytics.platform] = {
              posts: 0,
              totalEngagement: 0,
              totalReach: 0,
              avgEngagementRate: 0,
            };
          }

          const platform = platformMetrics[analytics.platform];
          platform.posts++;
          platform.totalEngagement += (analytics.likes_count + analytics.comments_count + analytics.shares_count);
          platform.totalReach += analytics.reach || 0;
          platform.avgEngagementRate += analytics.engagement_rate || 0;
        });
      }
    });

    // Calculate averages for platforms
    Object.values(platformMetrics).forEach((platform: any) => {
      if (platform.posts > 0) {
        platform.avgEngagementRate = platform.avgEngagementRate / platform.posts;
        platform.avgReach = platform.totalReach / platform.posts;
        platform.avgEngagement = platform.totalEngagement / platform.posts;
      }
    });

    return {
      avgEngagementRate: analyticsCount > 0 ? totalEngagementRate / analyticsCount : 0,
      avgReach: analyticsCount > 0 ? totalReach / analyticsCount : 0,
      avgImpressions: analyticsCount > 0 ? totalImpressions / analyticsCount : 0,
      platformBreakdown: platformMetrics,
    };
  }

  /**
   * Analyze posting patterns
   */
  private analyzePostingPatterns(posts: any[]): any {
    const hourCounts: Record<number, number> = {};
    const dayCounts: Record<number, number> = {};
    const hourEngagement: Record<number, number> = {};
    const dayEngagement: Record<number, number> = {};

    posts.forEach(post => {
      const publishedAt = new Date(post.published_at);
      const hour = publishedAt.getHours();
      const day = publishedAt.getDay();

      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
      dayCounts[day] = (dayCounts[day] || 0) + 1;

      // Calculate engagement for this post
      const engagement = post.post_analytics?.reduce((sum: number, analytics: any) =>
        sum + (analytics.likes_count + analytics.comments_count + analytics.shares_count), 0) || 0;

      hourEngagement[hour] = (hourEngagement[hour] || 0) + engagement;
      dayEngagement[day] = (dayEngagement[day] || 0) + engagement;
    });

    // Find best performing times and days
    const bestHours = Object.entries(hourEngagement)
      .map(([hour, engagement]) => ({
        hour: parseInt(hour),
        avgEngagement: engagement / (hourCounts[parseInt(hour)] || 1),
      }))
      .sort((a, b) => b.avgEngagement - a.avgEngagement)
      .slice(0, 3)
      .map(item => item.hour);

    const bestDays = Object.entries(dayEngagement)
      .map(([day, engagement]) => ({
        day: parseInt(day),
        avgEngagement: engagement / (dayCounts[parseInt(day)] || 1),
      }))
      .sort((a, b) => b.avgEngagement - a.avgEngagement)
      .slice(0, 3)
      .map(item => item.day);

    return {
      bestTimes: { general: bestHours },
      bestDays,
    };
  }

  /**
   * Analyze sentiment (simplified implementation)
   */
  private analyzeSentiment(posts: any[]): string {
    // This is a simplified sentiment analysis
    // In a real implementation, you would use a proper NLP service

    const positiveWords = ['رائع', 'ممتاز', 'جميل', 'مذهل', 'أحب', 'سعيد', 'فرح', 'نجح', 'great', 'amazing', 'love', 'happy', 'success'];
    const negativeWords = ['سيء', 'فشل', 'حزين', 'غضب', 'مشكلة', 'خطأ', 'bad', 'fail', 'sad', 'angry', 'problem', 'error'];

    let positiveCount = 0;
    let negativeCount = 0;

    posts.forEach(post => {
      if (post.content) {
        const content = post.content.toLowerCase();
        positiveWords.forEach(word => {
          if (content.includes(word)) positiveCount++;
        });
        negativeWords.forEach(word => {
          if (content.includes(word)) negativeCount++;
        });
      }
    });

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * Get content category based on content analysis
   */
  private getContentCategory(posts: any[]): string {
    // Analyze content to determine category
    // This is a simplified implementation
    const businessKeywords = ['عمل', 'شركة', 'منتج', 'خدمة', 'business', 'company', 'product', 'service'];
    const personalKeywords = ['شخصي', 'حياة', 'يوم', 'personal', 'life', 'day'];
    const promotionalKeywords = ['عرض', 'خصم', 'اشتري', 'offer', 'discount', 'buy'];

    let businessCount = 0;
    let personalCount = 0;
    let promotionalCount = 0;

    posts.forEach(post => {
      if (post.content) {
        const content = post.content.toLowerCase();
        businessKeywords.forEach(word => {
          if (content.includes(word)) businessCount++;
        });
        personalKeywords.forEach(word => {
          if (content.includes(word)) personalCount++;
        });
        promotionalKeywords.forEach(word => {
          if (content.includes(word)) promotionalCount++;
        });
      }
    });

    if (promotionalCount > businessCount && promotionalCount > personalCount) return 'promotional';
    if (businessCount > personalCount) return 'business';
    return 'personal';
  }

  /**
   * Get average content length
   */
  private getAverageContentLength(posts: any[]): number {
    const totalLength = posts.reduce((sum, post) => sum + (post.content?.length || 0), 0);
    return Math.round(totalLength / posts.length);
  }

  /**
   * Calculate performance score (0-100)
   */
  private calculatePerformanceScore(metrics: any): number {
    // This is a simplified scoring algorithm
    // In a real implementation, you would use more sophisticated metrics

    const engagementScore = Math.min(metrics.avgEngagementRate * 20, 40); // Max 40 points
    const reachScore = Math.min(metrics.avgReach / 100, 30); // Max 30 points
    const platformScore = Object.keys(metrics.platformBreakdown).length * 10; // Max 30 points

    return Math.round(engagementScore + reachScore + platformScore);
  }

  /**
   * Get trending hashtags
   */
  private async getTrendingHashtags(hashtags: string[]): Promise<string[]> {
    try {
      const supabase = createClient();

      // Get trending hashtags from the last 7 days
      const { data: trendingData } = await supabase
        .from('hashtag_analytics')
        .select('hashtag, trend_score')
        .in('hashtag', hashtags)
        .gte('period_date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
        .order('trend_score', { ascending: false })
        .limit(5);

      return trendingData?.map(item => item.hashtag) || [];

    } catch (error) {
      this.logger.warn('Failed to get trending hashtags', error as Record<string, any>);
      return [];
    }
  }

  /**
   * Generate content recommendations
   */
  private generateContentRecommendations(
    contentType: string,
    metrics: any,
    patterns: any,
    hashtags: string[]
  ): string[] {
    const recommendations: string[] = [];

    // Content type specific recommendations
    switch (contentType) {
      case 'image':
        recommendations.push('الصور تحقق أداءً جيداً - استمر في استخدام صور عالية الجودة');
        if (metrics.avgEngagementRate < 2) {
          recommendations.push('جرب إضافة نصوص أو اقتباسات على الصور لزيادة التفاعل');
        }
        break;

      case 'video':
        recommendations.push('الفيديوهات تحقق وصولاً أكبر - أنشئ المزيد من المحتوى المرئي');
        recommendations.push('تأكد من أن الفيديوهات قصيرة ومثيرة للاهتمام (أقل من دقيقة)');
        break;

      case 'text':
        if (metrics.avgEngagementRate > 3) {
          recommendations.push('المحتوى النصي يحقق تفاعلاً ممتازاً - استمر في هذا النهج');
        } else {
          recommendations.push('جرب إضافة صور أو فيديوهات لزيادة التفاعل مع المحتوى النصي');
        }
        break;
    }

    // Timing recommendations
    if (patterns.bestTimes.general.length > 0) {
      const bestHour = patterns.bestTimes.general[0];
      recommendations.push(`أفضل وقت للنشر هو الساعة ${bestHour}:00`);
    }

    // Hashtag recommendations
    if (hashtags.length > 0) {
      recommendations.push(`استخدم الهاشتاغات الأكثر فعالية: ${hashtags.slice(0, 3).join(', ')}`);
    }

    // Platform-specific recommendations
    const platforms = Object.keys(metrics.platformBreakdown);
    if (platforms.length > 1) {
      const bestPlatform = platforms.reduce((best, platform) =>
        metrics.platformBreakdown[platform].avgEngagementRate >
        metrics.platformBreakdown[best].avgEngagementRate ? platform : best
      );
      recommendations.push(`${bestPlatform} يحقق أفضل تفاعل لهذا النوع من المحتوى`);
    }

    return recommendations;
  }

  /**
   * Store content insight in database
   */
  private async storeContentInsight(
    userId: string,
    insight: ContentInsight,
    startDate: Date,
    endDate: Date
  ): Promise<void> {
    try {
      const supabase = createClient();

      const { error } = await supabase
        .from('content_insights')
        .upsert({
          user_id: userId,
          content_type: insight.type,
          content_category: insight.category,
          content_sentiment: insight.sentiment,
          content_length: insight.length,
          hashtags: insight.hashtags,
          hashtag_count: insight.hashtagCount,
          trending_hashtags: insight.trendingHashtags,
          avg_engagement_rate: insight.avgEngagementRate,
          avg_reach: insight.avgReach,
          performance_score: insight.performanceScore,
          best_posting_times: insight.bestPostingTimes,
          best_posting_days: insight.bestPostingDays,
          platform_performance: insight.platformPerformance,
          analysis_period_start: startDate.toISOString(),
          analysis_period_end: endDate.toISOString(),
        }, {
          onConflict: 'user_id,content_type,analysis_period_start',
        });

      if (error) {
        throw error;
      }

    } catch (error) {
      this.logger.error('Failed to store content insight', error);
    }
  }
}
