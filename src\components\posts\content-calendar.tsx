"use client";

import { useState, useEffect } from "react";
import { Calendar, ChevronLeft, ChevronRight, Plus, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import Link from "next/link";

interface Post {
  id: string;
  content: string;
  status: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED';
  scheduled_at?: string;
  published_at?: string;
  created_at: string;
  media_url?: string;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  posts: Post[];
}

export function ContentCalendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [calendarDays, setCalendarDays] = useState<CalendarDay[]>([]);

  useEffect(() => {
    loadPosts();
  }, [currentDate]);

  useEffect(() => {
    generateCalendarDays();
  }, [currentDate, posts]);

  const loadPosts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/posts');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load posts');
      }

      setPosts(data.posts || []);
    } catch (error: any) {
      console.error('Error loading posts:', error);
      toast.error('فشل في تحميل المنشورات');
    } finally {
      setIsLoading(false);
    }
  };

  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Get first day of the month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of week for the first day (0 = Sunday, 6 = Saturday)
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days: CalendarDay[] = [];
    const currentDateObj = new Date(startDate);
    
    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      const isCurrentMonth = currentDateObj.getMonth() === month;
      const dayPosts = posts.filter(post => {
        const postDate = post.scheduled_at ? new Date(post.scheduled_at) : 
                        post.published_at ? new Date(post.published_at) : 
                        new Date(post.created_at);
        return postDate.toDateString() === currentDateObj.toDateString();
      });

      days.push({
        date: new Date(currentDateObj),
        isCurrentMonth,
        posts: dayPosts
      });

      currentDateObj.setDate(currentDateObj.getDate() + 1);
    }

    setCalendarDays(days);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'SCHEDULED':
        return <Clock className="w-3 h-3 text-blue-500" />;
      case 'FAILED':
        return <AlertCircle className="w-3 h-3 text-red-500" />;
      default:
        return <div className="w-3 h-3 rounded-full bg-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800';
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

  return (
    <Card className="w-full" dir="rtl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            تقويم المحتوى
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('prev')}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
            <div className="text-lg font-semibold min-w-[120px] text-center">
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('next')}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">جاري تحميل التقويم...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1">
              {/* Day Headers */}
              {dayNames.map((day) => (
                <div
                  key={day}
                  className="p-2 text-center text-sm font-medium text-gray-500 bg-gray-50 rounded"
                >
                  {day}
                </div>
              ))}
              
              {/* Calendar Days */}
              {calendarDays.map((day, index) => (
                <div
                  key={index}
                  className={`min-h-[100px] p-1 border rounded-lg ${
                    day.isCurrentMonth 
                      ? 'bg-white border-gray-200' 
                      : 'bg-gray-50 border-gray-100'
                  } ${
                    day.date.toDateString() === new Date().toDateString()
                      ? 'ring-2 ring-blue-500'
                      : ''
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-sm font-medium ${
                      day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                    }`}>
                      {day.date.getDate()}
                    </span>
                    {day.isCurrentMonth && (
                      <Link href={`/posts/new?date=${day.date.toISOString()}`}>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 hover:bg-blue-100"
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </Link>
                    )}
                  </div>
                  
                  {/* Posts for this day */}
                  <div className="space-y-1">
                    {day.posts.slice(0, 3).map((post) => (
                      <div
                        key={post.id}
                        className={`p-1 rounded text-xs cursor-pointer hover:opacity-80 ${getStatusColor(post.status)}`}
                        title={post.content}
                      >
                        <div className="flex items-center gap-1">
                          {getStatusIcon(post.status)}
                          <span className="truncate">
                            {post.content.length > 20 
                              ? post.content.substring(0, 20) + '...' 
                              : post.content}
                          </span>
                        </div>
                      </div>
                    ))}
                    {day.posts.length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{day.posts.length - 3} المزيد
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Legend */}
            <div className="flex items-center justify-center gap-4 pt-4 border-t">
              <div className="flex items-center gap-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-gray-600">منشور</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-blue-500" />
                <span className="text-sm text-gray-600">مجدول</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-4 h-4 rounded-full bg-gray-400" />
                <span className="text-sm text-gray-600">مسودة</span>
              </div>
              <div className="flex items-center gap-1">
                <AlertCircle className="w-4 h-4 text-red-500" />
                <span className="text-sm text-gray-600">فشل</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
