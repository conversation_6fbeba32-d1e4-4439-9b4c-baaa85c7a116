/**
 * Social Media OAuth Configuration
 * Centralized configuration for all social media platform OAuth settings
 */

export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
  authUrl: string;
  tokenUrl: string;
  userInfoUrl?: string;
  enabled: boolean;
}

export interface PlatformConfig {
  [key: string]: OAuthConfig;
}

/**
 * Get OAuth configuration for all platforms
 */
export function getOAuthConfigs(): PlatformConfig {
  // Use localhost for development, production URL for production
  const baseUrl = process.env.NODE_ENV === 'development'
    ? 'http://localhost:3000'
    : (process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com');
  
  return {
    twitter: {
      clientId: process.env.X_CLIENT_ID || process.env.TWITTER_CLIENT_ID || '',
      clientSecret: process.env.X_CLIENT_SECRET || process.env.TWITTER_CLIENT_SECRET || '',
      redirectUri: `${baseUrl}/api/x/callback`,
      scope: ['tweet.read', 'tweet.write', 'users.read', 'media.upload', 'offline.access'],
      authUrl: 'https://x.com/i/oauth2/authorize',
      tokenUrl: 'https://api.x.com/2/oauth2/token',
      userInfoUrl: 'https://api.x.com/2/users/me',
      enabled: !!(process.env.X_CLIENT_ID || process.env.TWITTER_CLIENT_ID),
    },

    x: {
      clientId: process.env.X_CLIENT_ID || '',
      clientSecret: process.env.X_CLIENT_SECRET || '',
      redirectUri: `${baseUrl}/api/x/callback`,
      scope: ['tweet.read', 'tweet.write', 'users.read', 'follows.read', 'follows.write', 'offline.access'],
      authUrl: 'https://x.com/i/oauth2/authorize',
      tokenUrl: 'https://api.x.com/2/oauth2/token',
      userInfoUrl: 'https://api.x.com/2/users/me',
      enabled: !!(process.env.X_CLIENT_ID && process.env.X_CLIENT_SECRET),
    },
    
    facebook: {
      clientId: process.env.FACEBOOK_APP_ID || '',
      clientSecret: process.env.FACEBOOK_APP_SECRET || '',
      redirectUri: `${baseUrl}/api/facebook/callback`,
      scope: ['email', 'public_profile', 'pages_show_list', 'pages_manage_posts', 'pages_read_engagement', 'business_management'],
      authUrl: 'https://www.facebook.com/v19.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v19.0/oauth/access_token',
      userInfoUrl: 'https://graph.facebook.com/v19.0/me',
      enabled: !!(process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET),
    },
    
    instagram: {
      clientId: process.env.FACEBOOK_APP_ID || '',
      clientSecret: process.env.FACEBOOK_APP_SECRET || '',
      redirectUri: `${baseUrl}/api/facebook/callback`,
      scope: ['instagram_graph_user_profile', 'instagram_graph_user_media', 'instagram_content_publish', 'pages_show_list', 'pages_read_engagement', 'business_management'],
      authUrl: 'https://www.facebook.com/v19.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v19.0/oauth/access_token',
      userInfoUrl: 'https://graph.facebook.com/v19.0/me',
      enabled: !!(process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET),
    },
    
    linkedin: {
      clientId: process.env.LINKEDIN_CLIENT_ID || '',
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET || '',
      redirectUri: `${baseUrl}/api/linkedin/callback`,
      scope: ['openid', 'profile', 'w_member_social', 'email'],
      authUrl: 'https://www.linkedin.com/oauth/v2/authorization',
      tokenUrl: 'https://www.linkedin.com/oauth/v2/accessToken',
      userInfoUrl: 'https://api.linkedin.com/v2/userinfo',
      enabled: !!(process.env.LINKEDIN_CLIENT_ID && process.env.LINKEDIN_CLIENT_SECRET),
    },
    
    tiktok: {
      clientId: process.env.TIKTOK_CLIENT_ID || '',
      clientSecret: process.env.TIKTOK_CLIENT_SECRET || '',
      redirectUri: `${baseUrl}/api/social/callback/tiktok`,
      scope: ['user.info.basic', 'video.publish'],
      authUrl: 'https://www.tiktok.com/auth/authorize/',
      tokenUrl: 'https://open-api.tiktok.com/oauth/access_token/',
      userInfoUrl: 'https://open-api.tiktok.com/user/info/',
      enabled: !!(process.env.TIKTOK_CLIENT_ID && process.env.TIKTOK_CLIENT_SECRET),
    },
    
    snapchat: {
      clientId: process.env.SNAPCHAT_CLIENT_ID || '',
      clientSecret: process.env.SNAPCHAT_CLIENT_SECRET || '',
      redirectUri: `${baseUrl}/api/social/callback/snapchat`,
      scope: ['snapchat-marketing-api'],
      authUrl: 'https://accounts.snapchat.com/login/oauth2/authorize',
      tokenUrl: 'https://accounts.snapchat.com/login/oauth2/access_token',
      userInfoUrl: 'https://adsapi.snapchat.com/v1/me',
      enabled: !!(process.env.SNAPCHAT_CLIENT_ID && process.env.SNAPCHAT_CLIENT_SECRET),
    },
  };
}

/**
 * Get OAuth configuration for a specific platform
 */
export function getOAuthConfig(platform: string): OAuthConfig | null {
  const configs = getOAuthConfigs();
  return configs[platform.toLowerCase()] || null;
}

/**
 * Check if a platform is configured and enabled
 */
export function isPlatformEnabled(platform: string): boolean {
  const config = getOAuthConfig(platform);
  return config ? config.enabled : false;
}

/**
 * Get list of enabled platforms
 */
export function getEnabledPlatforms(): string[] {
  const configs = getOAuthConfigs();
  return Object.keys(configs).filter(platform => configs[platform].enabled);
}

/**
 * Get OAuth authorization URL for a platform
 */
export function getAuthorizationUrl(platform: string, state?: string): string | null {
  const config = getOAuthConfig(platform);
  if (!config || !config.enabled) return null;

  const params = new URLSearchParams({
    client_id: config.clientId,
    redirect_uri: config.redirectUri,
    scope: config.scope.join(' '),
    response_type: 'code',
    ...(state && { state }),
  });

  // Platform-specific parameters
  switch (platform.toLowerCase()) {
    case 'twitter':
      params.set('code_challenge', 'challenge');
      params.set('code_challenge_method', 'plain');
      break;
    case 'x':
      // X requires PKCE - generate a proper challenge
      params.set('code_challenge', 'demo_code_challenge_for_testing');
      params.set('code_challenge_method', 'S256');
      break;
    case 'facebook':
    case 'instagram':
      params.set('response_type', 'code');
      break;
    case 'linkedin':
      params.set('response_type', 'code');
      break;
  }

  return `${config.authUrl}?${params.toString()}`;
}

/**
 * Validate OAuth configuration
 */
export function validateOAuthConfig(platform: string): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const config = getOAuthConfig(platform);
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!config) {
    errors.push(`Platform ${platform} is not supported`);
    return { valid: false, errors, warnings };
  }

  if (!config.clientId) {
    errors.push(`Missing client ID for ${platform}`);
  }

  if (!config.clientSecret) {
    errors.push(`Missing client secret for ${platform}`);
  }

  if (!config.redirectUri) {
    errors.push(`Missing redirect URI for ${platform}`);
  }

  if (!config.scope || config.scope.length === 0) {
    warnings.push(`No scopes defined for ${platform}`);
  }

  if (!config.enabled) {
    warnings.push(`Platform ${platform} is not enabled`);
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Get platform configuration summary
 */
export function getPlatformSummary(): Array<{
  platform: string;
  enabled: boolean;
  configured: boolean;
  hasCredentials: boolean;
  scopes: string[];
}> {
  const configs = getOAuthConfigs();
  
  return Object.entries(configs).map(([platform, config]) => ({
    platform,
    enabled: config.enabled,
    configured: !!(config.clientId && config.clientSecret),
    hasCredentials: !!(config.clientId && config.clientSecret),
    scopes: config.scope,
  }));
}
