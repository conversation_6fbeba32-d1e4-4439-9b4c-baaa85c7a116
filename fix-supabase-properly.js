// PROPER script to fix ALL Supabase module-level initialization issues
const fs = require('fs');
const path = require('path');

console.log('🔧 PROPER SUPABASE BUILD ISSUE FIX');
console.log('==================================\n');

// Find all API route files
function findApiRoutes(dir, routes = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findApiRoutes(fullPath, routes);
    } else if (file === 'route.ts') {
      routes.push(fullPath);
    }
  });
  
  return routes;
}

// Properly fix Supabase client initialization
function fixSupabaseClient(content, filePath) {
  console.log(`  🔧 Fixing: ${filePath}`);
  
  // Step 1: Replace module-level client declarations with function
  const moduleClientPattern = /(const|let|export const) supabase = createClient\(\s*process\.env\.NEXT_PUBLIC_SUPABASE_URL!,\s*process\.env\.SUPABASE_SERVICE_ROLE_KEY!,\s*\{[^}]*\}\s*\);/g;
  
  if (moduleClientPattern.test(content)) {
    // Reset regex
    moduleClientPattern.lastIndex = 0;
    
    content = content.replace(
      moduleClientPattern,
      `function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}`
    );
  }
  
  // Step 2: Add client creation to exported functions that don't have it
  const exportFunctionPattern = /(export async function (GET|POST|PUT|DELETE|PATCH)\([^)]*\)\s*\{)/g;
  
  content = content.replace(exportFunctionPattern, (match, funcDecl) => {
    // Check if this function already has supabase client creation
    const funcStart = content.indexOf(match);
    const funcEnd = findFunctionEnd(content, funcStart);
    const funcBody = content.substring(funcStart, funcEnd);
    
    if (funcBody.includes('const supabase = getSupabaseClient()')) {
      return match; // Already has client creation
    }
    
    if (funcBody.includes('supabase.')) {
      // Function uses supabase but doesn't create client
      return `${funcDecl}\n  const supabase = getSupabaseClient();`;
    }
    
    return match; // Function doesn't use supabase
  });
  
  return content;
}

// Helper function to find the end of a function
function findFunctionEnd(content, startIndex) {
  let braceCount = 0;
  let inFunction = false;
  
  for (let i = startIndex; i < content.length; i++) {
    const char = content[i];
    
    if (char === '{') {
      braceCount++;
      inFunction = true;
    } else if (char === '}') {
      braceCount--;
      if (inFunction && braceCount === 0) {
        return i + 1;
      }
    }
  }
  
  return content.length;
}

// Main execution
const apiDir = 'src/app/api';
const routes = findApiRoutes(apiDir);

console.log(`📊 Processing ${routes.length} API route files\n`);

let totalFixed = 0;
let fixedFiles = [];

routes.forEach(routePath => {
  try {
    const content = fs.readFileSync(routePath, 'utf8');
    
    // Check if file needs fixing
    const needsFix = content.includes('const supabase = createClient(') ||
                     content.includes('export const supabase = createClient(') ||
                     content.includes('let supabase = createClient(');
    
    if (needsFix) {
      const fixedContent = fixSupabaseClient(content, routePath);
      fs.writeFileSync(routePath, fixedContent, 'utf8');
      fixedFiles.push(routePath);
      totalFixed++;
      console.log(`  ✅ Fixed: ${routePath}`);
    }
  } catch (error) {
    console.log(`  ❌ Error processing ${routePath}: ${error.message}`);
  }
});

console.log(`\n📈 RESULTS:`);
console.log(`Total API routes processed: ${routes.length}`);
console.log(`Files fixed: ${totalFixed}`);

if (totalFixed > 0) {
  console.log('\n🎉 ALL SUPABASE BUILD ISSUES FIXED!');
  console.log('✅ Module-level clients converted to runtime functions');
  console.log('✅ Environment variables now loaded at runtime');
  console.log('✅ Build should complete successfully');
  
  console.log('\n📋 Fixed files:');
  fixedFiles.forEach(file => {
    console.log(`  - ${file}`);
  });
} else {
  console.log('\n✅ No files needed fixing');
}

console.log('\n🚀 Ready for successful DigitalOcean deployment!');
