-- Advanced Scheduling System Migration
-- Adds support for recurring posts, bulk operations, and optimal timing

-- Add recurring post support to posts table
ALTER TABLE posts 
ADD COLUMN IF NOT EXISTS recurring_pattern JSONB,
ADD COLUMN IF NOT EXISTS recurring_end_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS parent_recurring_id UUID REFERENCES posts(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS is_recurring_parent BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS optimal_time_used BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS time_zone TEXT DEFAULT 'UTC',
ADD COLUMN IF NOT EXISTS scheduling_notes TEXT;

-- Create optimal posting times table
CREATE TABLE IF NOT EXISTS optimal_posting_times (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  platform platform_type NOT NULL,
  day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6),
  hour_of_day INTEGER CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
  engagement_score DECIMAL(5,2) DEFAULT 0,
  sample_size INTEGER DEFAULT 0,
  last_calculated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, platform, day_of_week, hour_of_day)
);

-- Create bulk operations tracking table
CREATE TABLE IF NOT EXISTS bulk_operations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  operation_type TEXT NOT NULL CHECK (operation_type IN ('bulk_schedule', 'bulk_import', 'bulk_delete', 'bulk_publish')),
  total_items INTEGER NOT NULL DEFAULT 0,
  completed_items INTEGER DEFAULT 0,
  failed_items INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  error_details JSONB,
  file_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);

-- Create scheduling queue for performance optimization
CREATE TABLE IF NOT EXISTS scheduling_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
  scheduled_for TIMESTAMPTZ NOT NULL,
  status TEXT DEFAULT 'queued' CHECK (status IN ('queued', 'processing', 'published', 'failed', 'cancelled')),
  retry_count INTEGER DEFAULT 0,
  last_attempt TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create content templates table
CREATE TABLE IF NOT EXISTS content_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  content TEXT NOT NULL,
  media_urls TEXT[],
  hashtags TEXT[],
  platforms platform_type[],
  category TEXT,
  is_public BOOLEAN DEFAULT false,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create post series table for related posts
CREATE TABLE IF NOT EXISTS post_series (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  total_posts INTEGER DEFAULT 0,
  published_posts INTEGER DEFAULT 0,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'paused')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add series reference to posts
ALTER TABLE posts 
ADD COLUMN IF NOT EXISTS series_id UUID REFERENCES post_series(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS series_order INTEGER;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON posts(scheduled_at) WHERE scheduled_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_posts_recurring_parent ON posts(parent_recurring_id) WHERE parent_recurring_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_posts_series ON posts(series_id) WHERE series_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_optimal_times_user_platform ON optimal_posting_times(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_scheduling_queue_scheduled_for ON scheduling_queue(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_scheduling_queue_status ON scheduling_queue(status);
CREATE INDEX IF NOT EXISTS idx_bulk_operations_user_status ON bulk_operations(user_id, status);

-- Create function to generate recurring post instances
CREATE OR REPLACE FUNCTION generate_recurring_instances(
  parent_post_id UUID,
  pattern JSONB,
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
  instance_count INTEGER := 0;
  current_date TIMESTAMPTZ := start_date;
  max_instances INTEGER := 100; -- Prevent infinite loops
  frequency TEXT;
  interval_value INTEGER;
  days_of_week INTEGER[];
  day_of_month INTEGER;
  time_parts TEXT[];
  hour_val INTEGER;
  minute_val INTEGER;
BEGIN
  -- Extract pattern parameters
  frequency := pattern->>'frequency';
  interval_value := COALESCE((pattern->>'interval')::INTEGER, 1);
  days_of_week := ARRAY(SELECT jsonb_array_elements_text(pattern->'days_of_week'))::INTEGER[];
  day_of_month := (pattern->>'day_of_month')::INTEGER;
  
  -- Extract time
  time_parts := string_to_array(pattern->>'time', ':');
  hour_val := time_parts[1]::INTEGER;
  minute_val := time_parts[2]::INTEGER;
  
  -- Set initial time
  current_date := date_trunc('day', current_date) + 
                  make_interval(hours => hour_val, mins => minute_val);
  
  -- Generate instances
  WHILE instance_count < max_instances AND (end_date IS NULL OR current_date <= end_date) LOOP
    -- Check if this date matches the pattern
    IF (frequency = 'daily') OR
       (frequency = 'weekly' AND (days_of_week IS NULL OR EXTRACT(dow FROM current_date)::INTEGER = ANY(days_of_week))) OR
       (frequency = 'monthly' AND (day_of_month IS NULL OR EXTRACT(day FROM current_date) = day_of_month)) THEN
      
      -- Create instance (this would be done in application code)
      instance_count := instance_count + 1;
    END IF;
    
    -- Advance to next date
    IF frequency = 'daily' THEN
      current_date := current_date + make_interval(days => interval_value);
    ELSIF frequency = 'weekly' THEN
      current_date := current_date + make_interval(weeks => interval_value);
    ELSIF frequency = 'monthly' THEN
      current_date := current_date + make_interval(months => interval_value);
    ELSE
      EXIT; -- Unknown frequency
    END IF;
  END LOOP;
  
  RETURN instance_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate optimal posting times
CREATE OR REPLACE FUNCTION calculate_optimal_times(user_uuid UUID, platform_name platform_type)
RETURNS TABLE(day_of_week INTEGER, hour_of_day INTEGER, score DECIMAL) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    EXTRACT(dow FROM p.created_at)::INTEGER as day_of_week,
    EXTRACT(hour FROM p.created_at)::INTEGER as hour_of_day,
    AVG(COALESCE(pa.engagement_rate, 0))::DECIMAL as score
  FROM posts p
  LEFT JOIN post_analytics pa ON p.id = pa.post_id AND pa.platform = platform_name
  WHERE p.user_id = user_uuid 
    AND p.status = 'PUBLISHED'
    AND p.created_at >= NOW() - INTERVAL '90 days'
  GROUP BY 
    EXTRACT(dow FROM p.created_at),
    EXTRACT(hour FROM p.created_at)
  HAVING COUNT(*) >= 3 -- Minimum sample size
  ORDER BY score DESC;
END;
$$ LANGUAGE plpgsql;

-- Create RLS policies for new tables
ALTER TABLE optimal_posting_times ENABLE ROW LEVEL SECURITY;
ALTER TABLE bulk_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduling_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_series ENABLE ROW LEVEL SECURITY;

-- RLS policies for optimal_posting_times
CREATE POLICY "Users can view their own optimal times" ON optimal_posting_times
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own optimal times" ON optimal_posting_times
  FOR ALL USING (user_id = auth.uid());

-- RLS policies for bulk_operations
CREATE POLICY "Users can view their own bulk operations" ON bulk_operations
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own bulk operations" ON bulk_operations
  FOR ALL USING (user_id = auth.uid());

-- RLS policies for scheduling_queue
CREATE POLICY "Users can view their own scheduled posts" ON scheduling_queue
  FOR SELECT USING (
    post_id IN (SELECT id FROM posts WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can manage their own scheduled posts" ON scheduling_queue
  FOR ALL USING (
    post_id IN (SELECT id FROM posts WHERE user_id = auth.uid())
  );

-- RLS policies for content_templates
CREATE POLICY "Users can view their own and public templates" ON content_templates
  FOR SELECT USING (user_id = auth.uid() OR is_public = true);

CREATE POLICY "Users can manage their own templates" ON content_templates
  FOR ALL USING (user_id = auth.uid());

-- RLS policies for post_series
CREATE POLICY "Users can view their own post series" ON post_series
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own post series" ON post_series
  FOR ALL USING (user_id = auth.uid());

-- Insert some default content templates
INSERT INTO content_templates (user_id, name, description, content, hashtags, platforms, category, is_public) VALUES
  (
    '00000000-0000-0000-0000-000000000000', -- System user
    'إعلان منتج جديد',
    'قالب للإعلان عن منتج جديد',
    'نحن متحمسون للإعلان عن منتجنا الجديد! 🎉\n\n[اسم المنتج] متاح الآن ويقدم [الفوائد الرئيسية].\n\n✨ المميزات:\n• [ميزة 1]\n• [ميزة 2]\n• [ميزة 3]\n\nاطلبه الآن واحصل على خصم خاص! 🛒',
    ARRAY['منتج_جديد', 'إعلان', 'عرض_خاص'],
    ARRAY['TWITTER', 'FACEBOOK', 'LINKEDIN']::platform_type[],
    'marketing',
    true
  ),
  (
    '00000000-0000-0000-0000-000000000000',
    'نصائح يومية',
    'قالب لمشاركة النصائح اليومية',
    'نصيحة اليوم 💡\n\n[النصيحة هنا]\n\nهذا سيساعدك على [الفائدة المتوقعة].\n\nجرب هذا وشاركنا تجربتك في التعليقات! 👇',
    ARRAY['نصائح', 'تطوير_الذات', 'يومي'],
    ARRAY['TWITTER', 'FACEBOOK', 'LINKEDIN']::platform_type[],
    'tips',
    true
  );

-- Create trigger to update post_series statistics
CREATE OR REPLACE FUNCTION update_series_stats()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.series_id IS NOT NULL THEN
    UPDATE post_series 
    SET total_posts = total_posts + 1,
        updated_at = NOW()
    WHERE id = NEW.series_id;
    
    IF NEW.status = 'PUBLISHED' THEN
      UPDATE post_series 
      SET published_posts = published_posts + 1
      WHERE id = NEW.series_id;
    END IF;
  END IF;
  
  IF TG_OP = 'UPDATE' AND OLD.status != NEW.status AND NEW.series_id IS NOT NULL THEN
    IF NEW.status = 'PUBLISHED' AND OLD.status != 'PUBLISHED' THEN
      UPDATE post_series 
      SET published_posts = published_posts + 1,
          updated_at = NOW()
      WHERE id = NEW.series_id;
    ELSIF OLD.status = 'PUBLISHED' AND NEW.status != 'PUBLISHED' THEN
      UPDATE post_series 
      SET published_posts = published_posts - 1,
          updated_at = NOW()
      WHERE id = NEW.series_id;
    END IF;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_series_stats_trigger
  AFTER INSERT OR UPDATE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_series_stats();

-- Add comments for documentation
COMMENT ON TABLE optimal_posting_times IS 'Stores calculated optimal posting times for each user and platform based on historical engagement data';
COMMENT ON TABLE bulk_operations IS 'Tracks bulk operations like importing multiple posts or bulk scheduling';
COMMENT ON TABLE scheduling_queue IS 'Queue for managing scheduled posts with retry logic and status tracking';
COMMENT ON TABLE content_templates IS 'Reusable content templates for quick post creation';
COMMENT ON TABLE post_series IS 'Groups related posts into series for better organization';

COMMENT ON FUNCTION generate_recurring_instances IS 'Generates recurring post instances based on pattern configuration';
COMMENT ON FUNCTION calculate_optimal_times IS 'Calculates optimal posting times based on historical engagement data';
