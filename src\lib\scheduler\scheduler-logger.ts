import { createClient } from '@/lib/supabase/server';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  id?: string;
  level: LogLevel;
  message: string;
  metadata?: Record<string, any>;
  timestamp: Date;
  component: string;
  user_id?: string;
  session_id?: string;
}

/**
 * Scheduler logging system with database persistence
 */
export class SchedulerLogger {
  private component: string;
  private sessionId: string;
  private logBuffer: LogEntry[] = [];
  private flushInterval?: NodeJS.Timeout;
  private readonly BUFFER_SIZE = 100;
  private readonly FLUSH_INTERVAL = 30000; // 30 seconds

  constructor(component: string = 'scheduler') {
    this.component = component;
    this.sessionId = this.generateSessionId();
    
    // Start periodic flush
    this.startPeriodicFlush();
  }

  /**
   * Log debug message
   */
  debug(message: string, metadata?: Record<string, any>): void {
    this.log('debug', message, metadata);
  }

  /**
   * Log info message
   */
  info(message: string, metadata?: Record<string, any>): void {
    this.log('info', message, metadata);
  }

  /**
   * Log warning message
   */
  warn(message: string, metadata?: Record<string, any>): void {
    this.log('warn', message, metadata);
  }

  /**
   * Log error message
   */
  error(message: string, error?: any, metadata?: Record<string, any>): void {
    const errorMetadata = {
      ...metadata,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
    };

    this.log('error', message, errorMetadata);
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, metadata?: Record<string, any>): void {
    const entry: LogEntry = {
      level,
      message,
      metadata,
      timestamp: new Date(),
      component: this.component,
      session_id: this.sessionId,
    };

    // Console output for development
    if (process.env.NODE_ENV === 'development') {
      const timestamp = entry.timestamp.toISOString();
      const levelUpper = level.toUpperCase().padEnd(5);
      const metadataStr = metadata ? ` ${JSON.stringify(metadata)}` : '';
      
      console.log(`[${timestamp}] ${levelUpper} [${this.component}] ${message}${metadataStr}`);
    }

    // Add to buffer
    this.logBuffer.push(entry);

    // Flush if buffer is full or if it's an error
    if (this.logBuffer.length >= this.BUFFER_SIZE || level === 'error') {
      this.flushLogs();
    }
  }

  /**
   * Start periodic log flushing
   */
  private startPeriodicFlush(): void {
    this.flushInterval = setInterval(() => {
      this.flushLogs();
    }, this.FLUSH_INTERVAL);
  }

  /**
   * Flush logs to database
   */
  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) {
      return;
    }

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      const supabase = createClient();

      // Insert logs into database
      const { error } = await supabase
        .from('scheduler_logs')
        .insert(
          logsToFlush.map(entry => ({
            level: entry.level,
            message: entry.message,
            metadata: entry.metadata || {},
            timestamp: entry.timestamp.toISOString(),
            component: entry.component,
            session_id: entry.session_id,
            user_id: entry.user_id,
          }))
        );

      if (error) {
        // If database insert fails, log to console as fallback
        console.error('Failed to flush logs to database:', error);
        console.log('Failed logs:', logsToFlush);
      }

    } catch (error) {
      console.error('Error flushing logs:', error);
      console.log('Failed logs:', logsToFlush);
    }
  }

  /**
   * Set user ID for subsequent logs
   */
  setUserId(userId: string): void {
    this.logBuffer.forEach(entry => {
      if (!entry.user_id) {
        entry.user_id = userId;
      }
    });
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalComponent: string, metadata?: Record<string, any>): SchedulerLogger {
    const childLogger = new SchedulerLogger(`${this.component}:${additionalComponent}`);
    childLogger.sessionId = this.sessionId;
    
    if (metadata) {
      // Override the log method to include additional metadata
      const originalLog = childLogger.log.bind(childLogger);
      childLogger.log = (level: LogLevel, message: string, entryMetadata?: Record<string, any>) => {
        const combinedMetadata = { ...metadata, ...entryMetadata };
        originalLog(level, message, combinedMetadata);
      };
    }

    return childLogger;
  }

  /**
   * Get recent logs from database
   */
  async getRecentLogs(options: {
    limit?: number;
    level?: LogLevel;
    component?: string;
    since?: Date;
  } = {}): Promise<LogEntry[]> {
    const supabase = createClient();
    
    let query = supabase
      .from('scheduler_logs')
      .select('*')
      .order('timestamp', { ascending: false });

    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.level) {
      query = query.eq('level', options.level);
    }

    if (options.component) {
      query = query.eq('component', options.component);
    }

    if (options.since) {
      query = query.gte('timestamp', options.since.toISOString());
    }

    const { data, error } = await query;

    if (error) {
      this.error('Failed to fetch logs from database', error);
      return [];
    }

    return (data || []).map(row => ({
      id: row.id,
      level: row.level,
      message: row.message,
      metadata: row.metadata,
      timestamp: new Date(row.timestamp),
      component: row.component,
      user_id: row.user_id,
      session_id: row.session_id,
    }));
  }

  /**
   * Get log statistics
   */
  async getLogStats(since?: Date): Promise<{
    total: number;
    byLevel: Record<LogLevel, number>;
    byComponent: Record<string, number>;
  }> {
    const supabase = createClient();
    
    let query = supabase
      .from('scheduler_logs')
      .select('level, component');

    if (since) {
      query = query.gte('timestamp', since.toISOString());
    }

    const { data, error } = await query;

    if (error) {
      this.error('Failed to fetch log stats', error);
      return {
        total: 0,
        byLevel: { debug: 0, info: 0, warn: 0, error: 0 },
        byComponent: {},
      };
    }

    const stats = {
      total: data?.length || 0,
      byLevel: { debug: 0, info: 0, warn: 0, error: 0 } as Record<LogLevel, number>,
      byComponent: {} as Record<string, number>,
    };

    data?.forEach(row => {
      stats.byLevel[row.level as LogLevel]++;
      stats.byComponent[row.component] = (stats.byComponent[row.component] || 0) + 1;
    });

    return stats;
  }

  /**
   * Clean up old logs
   */
  async cleanupOldLogs(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<number> { // Default 7 days
    const cutoffDate = new Date(Date.now() - maxAge);
    const supabase = createClient();

    const { count, error } = await supabase
      .from('scheduler_logs')
      .delete()
      .lt('timestamp', cutoffDate.toISOString());

    if (error) {
      this.error('Failed to cleanup old logs', error);
      return 0;
    }

    const deletedCount = count || 0;
    if (deletedCount > 0) {
      this.info(`Cleaned up ${deletedCount} old log entries`);
    }

    return deletedCount;
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    // Stop periodic flush
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = undefined;
    }

    // Flush remaining logs
    await this.flushLogs();
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
