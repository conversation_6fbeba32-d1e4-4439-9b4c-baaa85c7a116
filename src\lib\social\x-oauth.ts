/**
 * X (Twitter) OAuth 2.0 Service with PKCE
 * Implements OAuth 2.0 Authorization Code Flow with PKCE for X API v2
 */

import crypto from 'crypto';

export interface XOAuthConfig {
  clientId: string;
  clientSecret?: string; // Optional for public clients
  redirectUri: string;
  scopes: string[];
}

export interface PKCEChallenge {
  codeVerifier: string;
  codeChallenge: string;
  codeChallengeMethod: 'S256' | 'plain';
}

export interface XTokenResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  refresh_token?: string;
  scope: string;
}

export interface XUserProfile {
  id: string;
  name: string;
  username: string;
  profile_image_url?: string;
  verified?: boolean;
  public_metrics?: {
    followers_count: number;
    following_count: number;
    tweet_count: number;
  };
}

export class XOAuthService {
  private config: XOAuthConfig;

  constructor(config: XOAuthConfig) {
    this.config = config;
  }

  /**
   * Generate PKCE challenge for OAuth 2.0 flow
   */
  generatePKCEChallenge(): PKCEChallenge {
    // Generate code verifier (43-128 characters)
    const codeVerifier = crypto.randomBytes(32).toString('base64url');
    
    // Generate code challenge using S256 method
    const codeChallenge = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');

    return {
      codeVerifier,
      codeChallenge,
      codeChallengeMethod: 'S256'
    };
  }

  /**
   * Generate X OAuth authorization URL with PKCE
   */
  getAuthorizationUrl(state: string, pkceChallenge: PKCEChallenge): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.config.clientId,
      redirect_uri: this.config.redirectUri,
      scope: this.config.scopes.join(' '),
      state,
      code_challenge: pkceChallenge.codeChallenge,
      code_challenge_method: pkceChallenge.codeChallengeMethod,
    });

    return `https://x.com/i/oauth2/authorize?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(
    code: string,
    codeVerifier: string
  ): Promise<XTokenResponse> {
    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: this.config.clientId,
      code,
      redirect_uri: this.config.redirectUri,
      code_verifier: codeVerifier,
    });

    // Add client secret for confidential clients
    if (this.config.clientSecret) {
      tokenParams.append('client_secret', this.config.clientSecret);
    }

    const response = await fetch('https://api.x.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: tokenParams.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`X token exchange failed: ${response.status} ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<XTokenResponse> {
    const tokenParams = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      client_id: this.config.clientId,
    });

    // Add client secret for confidential clients
    if (this.config.clientSecret) {
      tokenParams.append('client_secret', this.config.clientSecret);
    }

    const response = await fetch('https://api.x.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: tokenParams.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`X token refresh failed: ${response.status} ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get user profile information
   */
  async getUserProfile(accessToken: string): Promise<XUserProfile> {
    const response = await fetch(
      'https://api.x.com/2/users/me?user.fields=id,name,username,profile_image_url,verified,public_metrics',
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`X profile fetch failed: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    return data.data;
  }

  /**
   * Revoke access token
   */
  async revokeToken(accessToken: string): Promise<void> {
    const response = await fetch('https://api.x.com/2/oauth2/revoke', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: new URLSearchParams({
        token: accessToken,
        client_id: this.config.clientId,
      }).toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`X token revocation failed: ${response.status} ${errorText}`);
    }
  }

  /**
   * Post a tweet
   */
  async postTweet(accessToken: string, text: string): Promise<any> {
    const response = await fetch('https://api.x.com/2/tweets', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`X tweet post failed: ${response.status} ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get user's tweets
   */
  async getUserTweets(accessToken: string, userId: string, maxResults: number = 10): Promise<any> {
    const params = new URLSearchParams({
      max_results: maxResults.toString(),
      'tweet.fields': 'created_at,public_metrics,context_annotations',
    });

    const response = await fetch(
      `https://api.x.com/2/users/${userId}/tweets?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`X tweets fetch failed: ${response.status} ${errorText}`);
    }

    return await response.json();
  }
}
