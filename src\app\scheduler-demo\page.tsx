'use client';

import { useState } from 'react';

export default function SchedulerDemoPage() {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);

  const runComprehensiveTest = async () => {
    setLoading(true);
    setTestResults({});

    try {
      const response = await fetch('/api/demo/scheduler-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      const data = await response.json();

      if (data.success) {
        setTestResults(data.results);
      } else {
        setTestResults({
          databaseTables: '❌ Test failed',
          schedulerHealth: '❌ Test failed',
          jobCreation: '❌ Test failed',
          postScheduling: '❌ Test failed',
          cronEndpoint: '❌ Test failed',
        });
      }

    } catch (error) {
      console.error('Test error:', error);
      setTestResults({
        databaseTables: '❌ Network error',
        schedulerHealth: '❌ Network error',
        jobCreation: '❌ Network error',
        postScheduling: '❌ Network error',
        cronEndpoint: '❌ Network error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🚀 eWasl Scheduler System Demo
          </h1>
          <p className="text-xl text-gray-600">
            Comprehensive background post scheduling system
          </p>
        </div>

        {/* Demo Controls */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="text-center">
            <button
              onClick={runComprehensiveTest}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-8 py-3 rounded-lg text-lg font-medium transition-colors"
            >
              {loading ? 'Running Tests...' : 'Run Comprehensive Test'}
            </button>
          </div>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Test Results</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="font-medium">Database Tables:</span>
                <span className="font-mono">{testResults.databaseTables}</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="font-medium">Scheduler Health:</span>
                <span className="font-mono">{testResults.schedulerHealth}</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="font-medium">Job Creation:</span>
                <span className="font-mono">{testResults.jobCreation}</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="font-medium">Post Scheduling:</span>
                <span className="font-mono">{testResults.postScheduling}</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="font-medium">Cron Endpoint:</span>
                <span className="font-mono">{testResults.cronEndpoint}</span>
              </div>
            </div>
          </div>
        )}

        {/* System Architecture */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">System Architecture</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="font-bold text-blue-900 mb-3">⚙️ Scheduler Engine</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Background processing</li>
                <li>• Job queue management</li>
                <li>• Retry logic</li>
                <li>• Priority handling</li>
              </ul>
            </div>
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="font-bold text-green-900 mb-3">📊 Database</h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• job_queue table</li>
                <li>• scheduler_status table</li>
                <li>• scheduler_logs table</li>
                <li>• post_publish_history table</li>
              </ul>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg">
              <h3 className="font-bold text-purple-900 mb-3">🔗 API Endpoints</h3>
              <ul className="text-sm text-purple-800 space-y-1">
                <li>• /api/scheduler/status</li>
                <li>• /api/scheduler/jobs</li>
                <li>• /api/posts/schedule</li>
                <li>• /api/cron/scheduler</li>
              </ul>
            </div>
            <div className="bg-orange-50 p-6 rounded-lg">
              <h3 className="font-bold text-orange-900 mb-3">📱 Publishers</h3>
              <ul className="text-sm text-orange-800 space-y-1">
                <li>• LinkedIn Publisher</li>
                <li>• Facebook Publisher</li>
                <li>• Instagram Publisher</li>
                <li>• Twitter/X Publisher</li>
              </ul>
            </div>
            <div className="bg-red-50 p-6 rounded-lg">
              <h3 className="font-bold text-red-900 mb-3">⏰ Cron Jobs</h3>
              <ul className="text-sm text-red-800 space-y-1">
                <li>• Vercel Cron (every 2 min)</li>
                <li>• Background service</li>
                <li>• Health monitoring</li>
                <li>• Auto-restart</li>
              </ul>
            </div>
            <div className="bg-indigo-50 p-6 rounded-lg">
              <h3 className="font-bold text-indigo-900 mb-3">📈 Monitoring</h3>
              <ul className="text-sm text-indigo-800 space-y-1">
                <li>• Real-time status</li>
                <li>• Job statistics</li>
                <li>• Error tracking</li>
                <li>• Performance metrics</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-bold text-gray-900 mb-3">✅ Implemented Features</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Multi-platform post scheduling</li>
                <li>• Background job processing</li>
                <li>• Automatic retry on failures</li>
                <li>• Priority-based job queue</li>
                <li>• Real-time status monitoring</li>
                <li>• Comprehensive error logging</li>
                <li>• Health check endpoints</li>
                <li>• Cron job integration</li>
                <li>• Timezone support</li>
                <li>• Bulk operations</li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-gray-900 mb-3">🚀 Production Ready</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Vercel deployment configured</li>
                <li>• Environment variables setup</li>
                <li>• Database schema optimized</li>
                <li>• Security authentication</li>
                <li>• Error handling & recovery</li>
                <li>• Performance monitoring</li>
                <li>• Scalable architecture</li>
                <li>• API documentation</li>
                <li>• Test suite included</li>
                <li>• Logging & analytics</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="text-center">
          <div className="space-x-4">
            <a
              href="/"
              className="text-blue-600 hover:text-blue-500 text-sm font-medium"
            >
              ← Back to Home
            </a>
            <a
              href="/test-stripe"
              className="text-blue-600 hover:text-blue-500 text-sm font-medium"
            >
              Test Stripe Integration
            </a>
            <a
              href="/billing"
              className="text-blue-600 hover:text-blue-500 text-sm font-medium"
            >
              Billing Dashboard →
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
