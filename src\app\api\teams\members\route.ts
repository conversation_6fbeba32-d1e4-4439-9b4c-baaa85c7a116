import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const inviteMemberSchema = z.object({
  organization_id: z.string().uuid().optional(),
  workspace_id: z.string().uuid().optional(),
  email: z.string().email(),
  role: z.enum(['admin', 'editor', 'reviewer', 'viewer', 'member']),
  message: z.string().optional(),
});

const updateMemberSchema = z.object({
  role: z.enum(['owner', 'admin', 'editor', 'reviewer', 'viewer', 'member']).optional(),
  permissions: z.record(z.any()).optional(),
  status: z.enum(['active', 'inactive']).optional(),
});

/**
 * Get team members
 * GET /api/teams/members?organization_id=xxx&workspace_id=xxx
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id');
    const workspaceId = searchParams.get('workspace_id');

    if (workspaceId) {
      // Get workspace members
      const { data: members, error } = await supabase
        .from('workspace_members')
        .select(`
          *,
          user:auth.users(
            id,
            email,
            raw_user_meta_data
          )
        `)
        .eq('workspace_id', workspaceId)
        .eq('status', 'active')
        .order('added_at', { ascending: false });

      if (error) {
        console.error('Error fetching workspace members:', error);
        return NextResponse.json({ error: 'Failed to fetch members' }, { status: 500 });
      }

      // Check if user has access to this workspace
      const userMember = (members as any[])?.find((m: any) => m.user_id === user.id);
      if (!userMember) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      return NextResponse.json({
        success: true,
        members: (members as any[])?.map((member: any) => ({
          id: member.id,
          user_id: member.user_id,
          email: member.user?.email,
          name: member.user?.raw_user_meta_data?.full_name || member.user?.email,
          avatar: member.user?.raw_user_meta_data?.avatar_url,
          role: member.role,
          permissions: member.permissions,
          status: member.status,
          added_at: member.added_at,
          added_by: member.added_by,
        })) || [],
        user_role: (userMember as any)?.role,
      });

    } else if (organizationId) {
      // Get organization members
      const { data: members, error } = await supabase
        .from('organization_members')
        .select(`
          *,
          user:auth.users(
            id,
            email,
            raw_user_meta_data
          )
        `)
        .eq('organization_id', organizationId)
        .eq('status', 'active')
        .order('joined_at', { ascending: false });

      if (error) {
        console.error('Error fetching organization members:', error);
        return NextResponse.json({ error: 'Failed to fetch members' }, { status: 500 });
      }

      // Check if user has access to this organization
      const userMember = (members as any[])?.find((m: any) => m.user_id === user.id);
      if (!userMember) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      return NextResponse.json({
        success: true,
        members: (members as any[])?.map((member: any) => ({
          id: member.id,
          user_id: member.user_id,
          email: member.user?.email,
          name: member.user?.raw_user_meta_data?.full_name || member.user?.email,
          avatar: member.user?.raw_user_meta_data?.avatar_url,
          role: member.role,
          permissions: member.permissions,
          status: member.status,
          joined_at: member.joined_at,
          invited_by: member.invited_by,
        })) || [],
        user_role: (userMember as any)?.role,
      });

    } else {
      return NextResponse.json({ error: 'Organization ID or Workspace ID required' }, { status: 400 });
    }

  } catch (error) {
    console.error('Members fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Invite team member
 * POST /api/teams/members
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validation = inviteMemberSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { organization_id, workspace_id, email, role, message } = validation.data;

    // Check if inviting user has permission
    if (workspace_id) {
      const { data: membership } = await supabase
        .from('workspace_members')
        .select('role')
        .eq('workspace_id', workspace_id)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (!membership || !['admin'].includes(membership.role)) {
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
      }
    } else if (organization_id) {
      const { data: membership } = await supabase
        .from('organization_members')
        .select('role')
        .eq('organization_id', organization_id)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (!membership || !['owner', 'admin'].includes(membership.role)) {
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
      }
    } else {
      return NextResponse.json({ error: 'Organization ID or Workspace ID required' }, { status: 400 });
    }

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('auth.users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      // User exists, add them directly
      if (workspace_id) {
        // Check if already a member
        const { data: existingMember } = await supabase
          .from('workspace_members')
          .select('id')
          .eq('workspace_id', workspace_id)
          .eq('user_id', existingUser.id)
          .single();

        if (existingMember) {
          return NextResponse.json({ error: 'User is already a member of this workspace' }, { status: 409 });
        }

        // Add to workspace
        const { data: newMember, error: addError } = await supabase
          .from('workspace_members')
          .insert({
            workspace_id,
            user_id: existingUser.id,
            role,
            status: 'active',
            added_by: user.id,
          })
          .select()
          .single();

        if (addError) {
          console.error('Error adding workspace member:', addError);
          return NextResponse.json({ error: 'Failed to add member' }, { status: 500 });
        }

        return NextResponse.json({
          success: true,
          member: newMember,
          message: 'Member added successfully',
        });

      } else if (organization_id) {
        // Check if already a member
        const { data: existingMember } = await supabase
          .from('organization_members')
          .select('id')
          .eq('organization_id', organization_id)
          .eq('user_id', existingUser.id)
          .single();

        if (existingMember) {
          return NextResponse.json({ error: 'User is already a member of this organization' }, { status: 409 });
        }

        // Add to organization
        const { data: newMember, error: addError } = await supabase
          .from('organization_members')
          .insert({
            organization_id,
            user_id: existingUser.id,
            role,
            status: 'active',
            invited_by: user.id,
          })
          .select()
          .single();

        if (addError) {
          console.error('Error adding organization member:', addError);
          return NextResponse.json({ error: 'Failed to add member' }, { status: 500 });
        }

        return NextResponse.json({
          success: true,
          member: newMember,
          message: 'Member added successfully',
        });
      }
    } else {
      // User doesn't exist, send invitation email
      // In a real implementation, you would send an email invitation
      // For now, we'll create a pending membership

      if (workspace_id) {
        const { data: invitation, error: inviteError } = await supabase
          .from('workspace_members')
          .insert({
            workspace_id,
            user_id: null, // Will be filled when user signs up
            role,
            status: 'pending',
            added_by: user.id,
            // Store email in permissions for now
            permissions: { invited_email: email, invitation_message: message },
          })
          .select()
          .single();

        if (inviteError) {
          console.error('Error creating workspace invitation:', inviteError);
          return NextResponse.json({ error: 'Failed to send invitation' }, { status: 500 });
        }

        return NextResponse.json({
          success: true,
          invitation,
          message: 'Invitation sent successfully',
        });

      } else if (organization_id) {
        const { data: invitation, error: inviteError } = await supabase
          .from('organization_members')
          .insert({
            organization_id,
            user_id: null, // Will be filled when user signs up
            role,
            status: 'pending',
            invited_by: user.id,
            // Store email in permissions for now
            permissions: { invited_email: email, invitation_message: message },
          })
          .select()
          .single();

        if (inviteError) {
          console.error('Error creating organization invitation:', inviteError);
          return NextResponse.json({ error: 'Failed to send invitation' }, { status: 500 });
        }

        return NextResponse.json({
          success: true,
          invitation,
          message: 'Invitation sent successfully',
        });
      }
    }

    return NextResponse.json({ error: 'Invalid request' }, { status: 400 });

  } catch (error) {
    console.error('Member invitation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Update team member
 * PUT /api/teams/members?id=xxx&type=workspace|organization
 */
export async function PUT(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('id');
    const memberType = searchParams.get('type'); // 'workspace' or 'organization'

    if (!memberId || !memberType) {
      return NextResponse.json({ error: 'Member ID and type required' }, { status: 400 });
    }

    const body = await request.json();
    const validation = updateMemberSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    // Check permissions and update member
    if (memberType === 'workspace') {
      // Get current member info and check permissions
      const { data: currentMember } = await supabase
        .from('workspace_members')
        .select('workspace_id, user_id, role')
        .eq('id', memberId)
        .single();

      if (!currentMember) {
        return NextResponse.json({ error: 'Member not found' }, { status: 404 });
      }

      // Check if user has permission to update this member
      const { data: userMembership } = await supabase
        .from('workspace_members')
        .select('role')
        .eq('workspace_id', currentMember.workspace_id)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (!userMembership || !['admin'].includes(userMembership.role)) {
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
      }

      // Update member
      const { data: updatedMember, error: updateError } = await supabase
        .from('workspace_members')
        .update(validation.data)
        .eq('id', memberId)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating workspace member:', updateError);
        return NextResponse.json({ error: 'Failed to update member' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        member: updatedMember,
      });

    } else if (memberType === 'organization') {
      // Similar logic for organization members
      const { data: currentMember } = await supabase
        .from('organization_members')
        .select('organization_id, user_id, role')
        .eq('id', memberId)
        .single();

      if (!currentMember) {
        return NextResponse.json({ error: 'Member not found' }, { status: 404 });
      }

      // Check if user has permission to update this member
      const { data: userMembership } = await supabase
        .from('organization_members')
        .select('role')
        .eq('organization_id', currentMember.organization_id)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (!userMembership || !['owner', 'admin'].includes(userMembership.role)) {
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
      }

      // Prevent changing owner role unless user is owner
      if (currentMember.role === 'owner' && userMembership.role !== 'owner') {
        return NextResponse.json({ error: 'Cannot modify owner role' }, { status: 403 });
      }

      // Update member
      const { data: updatedMember, error: updateError } = await supabase
        .from('organization_members')
        .update(validation.data)
        .eq('id', memberId)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating organization member:', updateError);
        return NextResponse.json({ error: 'Failed to update member' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        member: updatedMember,
      });
    }

    return NextResponse.json({ error: 'Invalid member type' }, { status: 400 });

  } catch (error) {
    console.error('Member update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Remove team member
 * DELETE /api/teams/members?id=xxx&type=workspace|organization
 */
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('id');
    const memberType = searchParams.get('type');

    if (!memberId || !memberType) {
      return NextResponse.json({ error: 'Member ID and type required' }, { status: 400 });
    }

    // Remove member based on type
    if (memberType === 'workspace') {
      const { error: deleteError } = await supabase
        .from('workspace_members')
        .delete()
        .eq('id', memberId);

      if (deleteError) {
        console.error('Error removing workspace member:', deleteError);
        return NextResponse.json({ error: 'Failed to remove member' }, { status: 500 });
      }

    } else if (memberType === 'organization') {
      const { error: deleteError } = await supabase
        .from('organization_members')
        .delete()
        .eq('id', memberId);

      if (deleteError) {
        console.error('Error removing organization member:', deleteError);
        return NextResponse.json({ error: 'Failed to remove member' }, { status: 500 });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Member removed successfully',
    });

  } catch (error) {
    console.error('Member removal error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
