#!/bin/bash

# eWasl Production Deployment Script
# Configuration-focused deployment for 90%+ complete system

set -e

echo "🚀 eWasl Production Deployment - Configuration Phase"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
APP_ID="92d1f7b6-85f2-47e2-8a69-d823b1586159"
DOMAIN="app.ewasl.com"
REDIS_CLUSTER_NAME="ewasl-redis-production"

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
        log_error "doctl CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if doctl is authenticated
    if ! doctl account get &> /dev/null; then
        log_error "doctl is not authenticated. Please run 'doctl auth init' first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Phase 1: Infrastructure Setup
setup_infrastructure() {
    log_info "Phase 1: Setting up infrastructure..."
    
    # Create Redis cluster
    log_info "Creating Redis cluster for background jobs..."
    
    # Check if Redis cluster already exists
    if doctl databases list | grep -q "$REDIS_CLUSTER_NAME"; then
        log_warning "Redis cluster '$REDIS_CLUSTER_NAME' already exists"
        REDIS_INFO=$(doctl databases get $REDIS_CLUSTER_NAME --format ID,Name,Status,Host,Port --no-header)
        echo "Redis Info: $REDIS_INFO"
    else
        log_info "Creating new Redis cluster..."
        doctl databases create $REDIS_CLUSTER_NAME \
            --engine redis \
            --size db-s-1vcpu-1gb \
            --region nyc3 \
            --num-nodes 1
        
        log_info "Waiting for Redis cluster to be ready..."
        while true; do
            STATUS=$(doctl databases get $REDIS_CLUSTER_NAME --format Status --no-header)
            if [ "$STATUS" = "online" ]; then
                break
            fi
            echo "Redis status: $STATUS - waiting..."
            sleep 30
        done
        
        log_success "Redis cluster created successfully"
    fi
    
    # Create Spaces bucket for media
    log_info "Creating Spaces bucket for media storage..."
    
    if doctl spaces ls | grep -q "ewasl-media-production"; then
        log_warning "Spaces bucket 'ewasl-media-production' already exists"
    else
        doctl spaces create ewasl-media-production --region nyc3
        log_success "Spaces bucket created successfully"
    fi
}

# Phase 2: Update App Configuration
update_app_configuration() {
    log_info "Phase 2: Updating app configuration..."
    
    # Get Redis connection details
    log_info "Retrieving Redis connection details..."
    REDIS_HOST=$(doctl databases get $REDIS_CLUSTER_NAME --format Host --no-header)
    REDIS_PORT=$(doctl databases get $REDIS_CLUSTER_NAME --format Port --no-header)
    REDIS_PASSWORD=$(doctl databases connection $REDIS_CLUSTER_NAME --format Password --no-header)
    
    log_info "Redis Host: $REDIS_HOST"
    log_info "Redis Port: $REDIS_PORT"
    
    # Update app configuration with Redis details
    log_info "Updating app configuration with production settings..."
    
    # Create temporary app spec with updated Redis configuration
    cp .do/app-simple.yaml .do/app-production-temp.yaml
    
    # Replace Redis placeholders
    sed -i "s/redis-cluster-do-user-123456-0.b.db.ondigitalocean.com/$REDIS_HOST/g" .do/app-production-temp.yaml
    sed -i "s/25061/$REDIS_PORT/g" .do/app-production-temp.yaml
    sed -i "s/PLACEHOLDER_REDIS_PASSWORD/$REDIS_PASSWORD/g" .do/app-production-temp.yaml
    
    # Update the app
    doctl apps update $APP_ID --spec .do/app-production-temp.yaml
    
    log_success "App configuration updated"
    
    # Clean up temporary file
    rm .do/app-production-temp.yaml
}

# Phase 3: Deploy and Monitor
deploy_and_monitor() {
    log_info "Phase 3: Deploying and monitoring..."
    
    log_info "Waiting for deployment to complete..."
    
    # Monitor deployment progress
    while true; do
        DEPLOYMENT_STATUS=$(doctl apps get $APP_ID --format Phase --no-header)
        log_info "Deployment status: $DEPLOYMENT_STATUS"
        
        if [ "$DEPLOYMENT_STATUS" = "ACTIVE" ]; then
            log_success "Deployment completed successfully!"
            break
        elif [ "$DEPLOYMENT_STATUS" = "ERROR" ]; then
            log_error "Deployment failed!"
            exit 1
        fi
        
        sleep 30
    done
    
    # Test application health
    log_info "Testing application health..."
    
    if curl -f "https://$DOMAIN/api/system/health" > /dev/null 2>&1; then
        log_success "Application health check passed"
    else
        log_warning "Application health check failed - may need time to start"
    fi
}

# Phase 4: Configuration Verification
verify_configuration() {
    log_info "Phase 4: Verifying configuration..."
    
    # Test API endpoints
    log_info "Testing API endpoints..."
    
    # Test Stripe configuration
    if curl -f "https://$DOMAIN/api/billing/health" > /dev/null 2>&1; then
        log_success "Stripe billing API is accessible"
    else
        log_warning "Stripe billing API test failed"
    fi
    
    # Test scheduler
    if curl -f "https://$DOMAIN/api/scheduler/status" > /dev/null 2>&1; then
        log_success "Background scheduler API is accessible"
    else
        log_warning "Background scheduler API test failed"
    fi
    
    # Test media processing
    if curl -f "https://$DOMAIN/api/test/enhanced-media-pipeline-final" > /dev/null 2>&1; then
        log_success "Media processing pipeline is accessible"
    else
        log_warning "Media processing pipeline test failed"
    fi
}

# Main execution
main() {
    echo
    log_info "Starting eWasl production deployment..."
    echo
    
    check_prerequisites
    setup_infrastructure
    update_app_configuration
    deploy_and_monitor
    verify_configuration
    
    echo
    log_success "🎉 Production deployment completed!"
    echo
    echo "📋 Next Steps:"
    echo "1. Complete Stripe webhook configuration"
    echo "2. Configure social media OAuth applications"
    echo "3. Set up OpenAI API key"
    echo "4. Test end-to-end functionality"
    echo
    echo "🌐 Application URL: https://$DOMAIN"
    echo "📊 Health Check: https://$DOMAIN/api/system/health"
    echo
}

# Run main function
main "$@"
