import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/scheduler/jobs/[id] - Get specific job details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Fetching job details:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get job details
    const { data: job, error } = await supabase
      .from('job_queue')
      .select('*')
      .eq('id', id)
      .eq('created_by', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Job not found' },
          { status: 404 }
        );
      }
      throw error;
    }

    // Calculate job duration and progress
    const jobDetails = {
      ...job,
      duration: calculateJobDuration(job),
      progress: calculateJobProgress(job),
      nextRetry: calculateNextRetry(job),
      canRetry: canRetryJob(job),
      canCancel: canCancelJob(job),
    };

    // Get related data based on job type
    let relatedData = null;
    try {
      relatedData = await getRelatedJobData(supabase, job);
    } catch (error) {
      console.warn('Failed to fetch related job data:', error);
    }

    return NextResponse.json({
      success: true,
      data: {
        job: jobDetails,
        relatedData,
      },
    });

  } catch (error: any) {
    console.error('Error fetching job details:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch job details',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/scheduler/jobs/[id] - Update job (retry, cancel)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Updating job:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    // Get current job
    const { data: job, error: fetchError } = await supabase
      .from('job_queue')
      .select('*')
      .eq('id', id)
      .eq('created_by', user.id)
      .single();

    if (fetchError || !job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    let updateData: any = {
      updated_at: new Date().toISOString(),
    };

    switch (action) {
      case 'retry':
        if (!canRetryJob(job)) {
          return NextResponse.json(
            { error: 'Job cannot be retried' },
            { status: 400 }
          );
        }

        updateData = {
          ...updateData,
          status: 'pending',
          attempts: 0,
          error_message: null,
          error_details: null,
          started_at: null,
          completed_at: null,
          scheduled_at: new Date().toISOString(),
        };
        break;

      case 'cancel':
        if (!canCancelJob(job)) {
          return NextResponse.json(
            { error: 'Job cannot be cancelled' },
            { status: 400 }
          );
        }

        updateData = {
          ...updateData,
          status: 'cancelled',
          completed_at: new Date().toISOString(),
          error_message: 'Job cancelled by user',
        };
        break;

      case 'reschedule':
        const { scheduledAt } = body;
        if (!scheduledAt) {
          return NextResponse.json(
            { error: 'scheduledAt is required for reschedule action' },
            { status: 400 }
          );
        }

        if (job.status !== 'pending') {
          return NextResponse.json(
            { error: 'Only pending jobs can be rescheduled' },
            { status: 400 }
          );
        }

        updateData = {
          ...updateData,
          scheduled_at: new Date(scheduledAt).toISOString(),
        };
        break;

      case 'update_priority':
        const { priority } = body;
        if (typeof priority !== 'number' || priority < 0 || priority > 10) {
          return NextResponse.json(
            { error: 'Priority must be a number between 0 and 10' },
            { status: 400 }
          );
        }

        if (job.status !== 'pending') {
          return NextResponse.json(
            { error: 'Only pending jobs can have priority updated' },
            { status: 400 }
          );
        }

        updateData = {
          ...updateData,
          priority,
        };
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: retry, cancel, reschedule, update_priority' },
          { status: 400 }
        );
    }

    // Update job
    const { data: updatedJob, error: updateError } = await supabase
      .from('job_queue')
      .update(updateData)
      .eq('id', id)
      .eq('created_by', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating job:', updateError);
      return NextResponse.json(
        { error: 'Failed to update job' },
        { status: 500 }
      );
    }

    console.log(`Job ${id} ${action} completed successfully`);

    return NextResponse.json({
      success: true,
      data: updatedJob,
      message: getActionMessage(action),
    });

  } catch (error: any) {
    console.error('Error updating job:', error);
    return NextResponse.json(
      {
        error: 'Failed to update job',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/scheduler/jobs/[id] - Delete specific job
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Deleting job:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if job exists and belongs to user
    const { data: job, error: fetchError } = await supabase
      .from('job_queue')
      .select('status')
      .eq('id', id)
      .eq('created_by', user.id)
      .single();

    if (fetchError || !job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    // Don't allow deletion of processing jobs
    if (job.status === 'processing') {
      return NextResponse.json(
        { error: 'Cannot delete processing jobs. Cancel first.' },
        { status: 400 }
      );
    }

    // Delete the job
    const { error: deleteError } = await supabase
      .from('job_queue')
      .delete()
      .eq('id', id)
      .eq('created_by', user.id);

    if (deleteError) {
      console.error('Error deleting job:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete job' },
        { status: 500 }
      );
    }

    console.log(`Job ${id} deleted successfully`);

    return NextResponse.json({
      success: true,
      message: 'تم حذف المهمة بنجاح',
    });

  } catch (error: any) {
    console.error('Error deleting job:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete job',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper functions
function calculateJobDuration(job: any): number | null {
  if (!job.started_at) return null;
  
  const endTime = job.completed_at ? new Date(job.completed_at) : new Date();
  const startTime = new Date(job.started_at);
  
  return endTime.getTime() - startTime.getTime();
}

function calculateJobProgress(job: any): number {
  switch (job.status) {
    case 'pending': return 0;
    case 'processing': return 50;
    case 'completed': return 100;
    case 'failed': return 100;
    case 'cancelled': return 100;
    default: return 0;
  }
}

function calculateNextRetry(job: any): Date | null {
  if (job.status !== 'failed' || job.attempts >= job.max_attempts) {
    return null;
  }

  // Calculate exponential backoff
  const baseDelay = 60000; // 1 minute
  const delay = Math.min(baseDelay * Math.pow(2, job.attempts), 3600000); // Max 1 hour
  
  return new Date(Date.now() + delay);
}

function canRetryJob(job: any): boolean {
  return job.status === 'failed' && job.attempts < job.max_attempts;
}

function canCancelJob(job: any): boolean {
  return ['pending', 'processing'].includes(job.status);
}

function getActionMessage(action: string): string {
  const messages: Record<string, string> = {
    retry: 'تم إعادة جدولة المهمة للمحاولة مرة أخرى',
    cancel: 'تم إلغاء المهمة',
    reschedule: 'تم تحديث موعد المهمة',
    update_priority: 'تم تحديث أولوية المهمة',
  };
  
  return messages[action] || 'تم تحديث المهمة';
}

async function getRelatedJobData(supabase: any, job: any): Promise<any> {
  switch (job.job_type) {
    case 'publish-post':
      if (job.job_data.postId) {
        const { data: post } = await supabase
          .from('posts')
          .select('id, content, scheduled_at, status')
          .eq('id', job.job_data.postId)
          .single();
        return { post };
      }
      break;

    case 'generate-recurring-posts':
      if (job.job_data.recurringPostId) {
        const { data: recurringPost } = await supabase
          .from('recurring_posts')
          .select('id, title, pattern_type, is_active')
          .eq('id', job.job_data.recurringPostId)
          .single();
        return { recurringPost };
      }
      break;

    case 'process-bulk-import':

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
      if (job.job_data.operationId) {
        const { data: bulkOperation } = await supabase
          .from('bulk_operations')
          .select('id, operation_type, status, total_items')
          .eq('id', job.job_data.operationId)
          .single();
        return { bulkOperation };
      }
      break;
  }

  return null;
}
