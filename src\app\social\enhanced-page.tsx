'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/components/auth/supabase-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Twitter, Facebook, Instagram, Linkedin, Plus, Settings, Trash2, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { BusinessAccountSelector } from '@/components/social/business-account-selector';
import EnhancedSocialAccounts from '@/components/social/enhanced-social-accounts';
import EnhancedAnalyticsDashboard from '@/components/analytics/enhanced-analytics-dashboard';
import EnhancedApiTesting from '@/components/testing/enhanced-api-testing';

interface SocialAccount {
  id: string;
  platform: 'TWITTER' | 'FACEBOOK' | 'INSTAGRAM' | 'LINKEDIN';
  account_id: string;
  account_name: string;
  created_at: string;
}

const platformConfig = {
  TWITTER: {
    name: 'Twitter',
    icon: Twitter,
    color: 'bg-blue-500',
  },
  FACEBOOK: {
    name: 'Facebook',
    icon: Facebook,
    color: 'bg-blue-600',
  },
  INSTAGRAM: {
    name: 'Instagram',
    icon: Instagram,
    color: 'bg-pink-500',
  },
  LINKEDIN: {
    name: 'LinkedIn',
    icon: Linkedin,
    color: 'bg-blue-700',
  },
};

export default function EnhancedSocialPage() {
  const { user } = useSupabase();
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [showBusinessSelector, setShowBusinessSelector] = useState(false);
  const [pendingConnection, setPendingConnection] = useState<{
    platform: string;
    accessToken: string;
  } | null>(null);

  useEffect(() => {
    if (user) {
      fetchSocialAccounts();
    }
  }, [user]);

  // Handle URL parameters for success/error messages
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const account = urlParams.get('account');
    const error = urlParams.get('error');

    if (success && account) {
      switch (success) {
        case 'facebook_connected':
          toast.success(`تم ربط حساب Facebook بنجاح: ${decodeURIComponent(account)}`);
          break;
        case 'x_connected':
          toast.success(`تم ربط حساب X/Twitter بنجاح: ${decodeURIComponent(account)}`);
          break;
        case 'linkedin_connected':
          toast.success(`تم ربط حساب LinkedIn بنجاح: ${decodeURIComponent(account)}`);
          break;
        case 'instagram_connected':
          toast.success(`تم ربط حساب Instagram بنجاح: ${decodeURIComponent(account)}`);
          break;
        default:
          toast.success(`تم ربط الحساب بنجاح: ${decodeURIComponent(account)}`);
      }

      // Clean up URL parameters
      window.history.replaceState({}, '', window.location.pathname);

      // Refresh accounts after a short delay
      setTimeout(() => {
        fetchSocialAccounts();
      }, 1000);
    }

    if (error) {
      toast.error(`خطأ في الربط: ${decodeURIComponent(error)}`);
      // Clean up URL parameters
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, []);

  const fetchSocialAccounts = async () => {
    try {
      const response = await fetch(`/api/social/accounts?userId=${user?.id}`);
      if (response.ok) {
        const data = await response.json();
        setAccounts(data.accounts || []);
      }
    } catch (error) {
      console.error('Error fetching social accounts:', error);
      toast.error('فشل في تحميل الحسابات الاجتماعية');
    } finally {
      setLoading(false);
    }
  };

  const handleConnectTwitter = async () => {
    if (!user) return;

    setConnecting('TWITTER');
    try {
      const response = await fetch(`/api/auth/twitter?userId=${user.id}`);
      if (response.ok) {
        const data = await response.json();
        window.open(data.authUrl, '_blank', 'width=600,height=600');
        
        // Listen for the callback
        const checkConnection = setInterval(async () => {
          await fetchSocialAccounts();
          const hasTwitter = accounts.some(acc => acc.platform === 'TWITTER');
          if (hasTwitter) {
            clearInterval(checkConnection);
            toast.success('تم ربط حساب Twitter بنجاح!');
          }
        }, 2000);

        // Stop checking after 2 minutes
        setTimeout(() => clearInterval(checkConnection), 120000);
      } else {
        toast.error('فشل في بدء عملية ربط Twitter');
      }
    } catch (error) {
      console.error('Error connecting Twitter:', error);
      toast.error('حدث خطأ أثناء ربط Twitter');
    } finally {
      setConnecting(null);
    }
  };

  const handleDisconnect = async (accountId: string, platform: string) => {
    try {
      console.log('🔌 Enhanced Page: Starting disconnect process', { accountId, platform });

      // Show loading state
      toast.loading('جاري قطع الاتصال...', { id: 'enhanced-disconnect-loading' });

      const response = await fetch('/api/social/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId,
          userId: user?.id,
        }),
      });

      // Dismiss loading toast
      toast.dismiss('enhanced-disconnect-loading');

      const data = await response.json();
      console.log('📡 Enhanced Page: Disconnect response', {
        status: response.status,
        success: data.success,
        error: data.error
      });

      if (response.ok && data.success) {
        console.log('✅ Enhanced Page: Disconnect successful, updating UI');
        setAccounts(prevAccounts => {
          const updatedAccounts = prevAccounts.filter(acc => acc.id !== accountId);
          console.log('📊 Enhanced Page: Updated accounts count', {
            before: prevAccounts.length,
            after: updatedAccounts.length
          });
          return updatedAccounts;
        });

        const platformName = platformConfig[platform as keyof typeof platformConfig]?.name || platform;
        toast.success(`تم قطع الاتصال مع ${platformName} بنجاح`);
        console.log('🎉 Enhanced Page: Disconnect process completed successfully');
      } else {
        console.error('❌ Enhanced Page: Disconnect failed', data);
        const errorMessage = data.error || data.details || 'فشل في قطع الاتصال';
        toast.error(errorMessage);
      }
    } catch (error: any) {
      console.error('💥 Enhanced Page: Disconnect error:', error);
      toast.dismiss('enhanced-disconnect-loading');
      toast.error(`حدث خطأ أثناء قطع الاتصال: ${error.message || 'خطأ غير معروف'}`);
    }
  };

  const handleTestConnection = async (account: SocialAccount) => {
    try {
      console.log('🔍 Enhanced Page: Testing connection for', account.platform);
      toast.loading('جاري اختبار الاتصال...', { id: 'test-connection-loading' });

      const response = await fetch('/api/social-accounts/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: account.platform,
          accountId: account.account_id,
        }),
      });

      toast.dismiss('test-connection-loading');
      const result = await response.json();

      if (result.success) {
        toast.success(`✅ اختبار الاتصال مع ${account.platform} نجح`);
      } else {
        toast.error(`❌ اختبار الاتصال مع ${account.platform} فشل: ${result.error}`);
      }
    } catch (error: any) {
      toast.dismiss('test-connection-loading');
      toast.error(`خطأ في اختبار الاتصال: ${error.message}`);
    }
  };

  const handleTestPublish = async (account: SocialAccount) => {
    try {
      console.log('📤 Enhanced Page: Testing publish for', account.platform);
      toast.loading('جاري اختبار النشر...', { id: 'test-publish-loading' });

      const testContent = `[اختبار] مرحباً من منصة eWasl! 🚀 هذا منشور تجريبي لاختبار التكامل مع ${account.platform}. #eWasl #اختبار`;

      const response = await fetch('/api/posts/test-publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: account.platform,
          content: testContent,
          accountId: account.account_id,
          isTest: true,
        }),
      });

      toast.dismiss('test-publish-loading');
      const result = await response.json();

      if (result.success) {
        toast.success(`✅ اختبار النشر على ${account.platform} نجح!`);
        if (result.url) {
          toast.info(`🔗 رابط المنشور: ${result.url}`);
        }
      } else {
        toast.error(`❌ اختبار النشر على ${account.platform} فشل: ${result.error}`);
      }
    } catch (error: any) {
      toast.dismiss('test-publish-loading');
      toast.error(`خطأ في اختبار النشر: ${error.message}`);
    }
  };

  const getConnectedPlatforms = () => {
    return accounts.map(acc => acc.platform);
  };

  const getAvailablePlatforms = () => {
    const connected = getConnectedPlatforms();
    return Object.keys(platformConfig).filter(platform =>
      !connected.includes(platform as any)
    ) as Array<keyof typeof platformConfig>;
  };

  const handleConnectPlatform = async (platform: string) => {
    if (!user) return;

    setConnecting(platform);
    try {
      console.log(`🔗 Starting OAuth flow for ${platform}...`);

      const response = await fetch('/api/social/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ platform }),
      });

      if (!response.ok) {
        throw new Error('Failed to initiate OAuth flow');
      }

      const data = await response.json();

      // Add popup parameter to auth URL
      const authUrlWithPopup = data.authUrl + (data.authUrl.includes('?') ? '&' : '?') + 'popup=true';

      // Open OAuth popup
      const popup = window.open(
        authUrlWithPopup,
        'social-auth',
        'width=600,height=600,scrollbars=yes,resizable=yes'
      );

      // Listen for postMessage from popup
      const handleMessage = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'oauth_success') {
          clearInterval(checkClosed);
          setConnecting(null);
          window.removeEventListener('message', handleMessage);

          // Show success message
          toast.success(`تم ربط حساب ${event.data.platform} بنجاح!`);

          // Reload accounts
          fetchSocialAccounts();
        }
      };

      window.addEventListener('message', handleMessage);

      // Monitor popup for completion (fallback)
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          setConnecting(null);
          window.removeEventListener('message', handleMessage);
          // Reload accounts after a short delay
          setTimeout(() => {
            fetchSocialAccounts();
          }, 1000);
        }
      }, 1000);

      // Stop checking after 2 minutes
      setTimeout(() => {
        clearInterval(checkClosed);
        setConnecting(null);
      }, 120000);

    } catch (error: any) {
      console.error(`Error connecting ${platform}:`, error);
      toast.error(`حدث خطأ أثناء ربط ${platform}: ${error.message}`);
      setConnecting(null);
    }
  };

  const handleBusinessAccountSelected = async (account: any) => {
    try {
      console.log('🏢 Business account selected:', account);

      // Here you would typically store the selected business account
      // For now, we'll just show a success message and refresh accounts
      toast.success(`تم ربط ${account.name} بنجاح!`);

      setShowBusinessSelector(false);
      setPendingConnection(null);

      // Refresh accounts
      await fetchSocialAccounts();

    } catch (error: any) {
      console.error('Business account selection error:', error);
      toast.error(`خطأ في ربط الحساب التجاري: ${error.message}`);
    }
  };

  const handleBusinessSelectorCancel = () => {
    setShowBusinessSelector(false);
    setPendingConnection(null);
    setConnecting(null);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Show business account selector if needed
  if (showBusinessSelector && pendingConnection) {
    return (
      <div className="container mx-auto p-6">
        <BusinessAccountSelector
          platform={pendingConnection.platform}
          accessToken={pendingConnection.accessToken}
          onAccountSelected={handleBusinessAccountSelected}
          onCancel={handleBusinessSelectorCancel}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Enhanced Social Media Management */}
      <Tabs defaultValue="accounts" className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">إدارة الحسابات الاجتماعية المتقدمة</h1>
            <p className="text-gray-600 mt-2">إدارة شاملة ومتقدمة لحساباتك على منصات التواصل الاجتماعي</p>
          </div>
        </div>

        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="accounts">إدارة الحسابات</TabsTrigger>
          <TabsTrigger value="analytics">التحليلات المتقدمة</TabsTrigger>
          <TabsTrigger value="testing">اختبار التكامل</TabsTrigger>
        </TabsList>

        <TabsContent value="accounts" className="space-y-6">
          <EnhancedSocialAccounts userId={user?.id || ''} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <EnhancedAnalyticsDashboard userId={user?.id || ''} />
        </TabsContent>

        <TabsContent value="testing" className="space-y-6">
          <EnhancedApiTesting userId={user?.id || ''} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
