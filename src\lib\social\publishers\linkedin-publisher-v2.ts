/**
 * Enhanced LinkedIn Publisher V2
 * Real API implementation for LinkedIn posting with media support
 * Replaces mock implementation
 */

export interface SocialAccount {
  id: string;
  platform: string;
  access_token: string;
  account_id: string;
  account_name: string;
}

export interface PostContent {
  content: string;
  mediaUrl?: string;
  mediaType?: 'IMAGE' | 'VIDEO' | 'DOCUMENT';
}

export interface PublishResult {
  success: boolean;
  postId?: string;
  url?: string;
  error?: string;
  platformResponse?: any;
}

export interface LinkedInMediaUpload {
  asset: string;
  uploadUrl: string;
}

export class LinkedInPublisherV2 {
  private readonly apiUrl = 'https://api.linkedin.com/v2';

  /**
   * Publish a post to LinkedIn with real API calls
   */
  async publishPost(account: SocialAccount, content: PostContent): Promise<PublishResult> {
    try {
      console.log('Publishing to LinkedIn:', {
        accountId: account.account_id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl
      });

      // Validate token first
      await this.validateToken(account.access_token);

      // Determine if this is a personal or organization account
      const isOrganization = account.account_id.length > 10; // Organization IDs are typically longer
      const authorUrn = isOrganization
        ? `urn:li:organization:${account.account_id}`
        : `urn:li:person:${account.account_id}`;

      console.log('LinkedIn posting configuration:', {
        accountId: account.account_id,
        isOrganization,
        authorUrn
      });

      let shareData: any = {
        author: authorUrn,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content.content
            },
            shareMediaCategory: content.mediaUrl ? 'IMAGE' : 'NONE'
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
        }
      };

      // Handle media upload if present
      if (content.mediaUrl) {
        try {
          const mediaUrn = await this.uploadMedia(account, content.mediaUrl, content.mediaType);
          
          shareData.specificContent['com.linkedin.ugc.ShareContent'].media = [{
            status: 'READY',
            description: { text: content.content },
            media: mediaUrn,
            title: { text: 'Shared via eWasl' }
          }];

          // Update media category based on type
          if (content.mediaType === 'VIDEO') {
            shareData.specificContent['com.linkedin.ugc.ShareContent'].shareMediaCategory = 'VIDEO';
          } else if (content.mediaType === 'DOCUMENT') {
            shareData.specificContent['com.linkedin.ugc.ShareContent'].shareMediaCategory = 'ARTICLE';
          }
        } catch (mediaError) {
          console.warn('Media upload failed, posting without media:', mediaError);
          shareData.specificContent['com.linkedin.ugc.ShareContent'].shareMediaCategory = 'NONE';
        }
      }

      // Post to LinkedIn
      const response = await fetch(`${this.apiUrl}/ugcPosts`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
          'Content-Type': 'application/json',
          'X-Restli-Protocol-Version': '2.0.0'
        },
        body: JSON.stringify(shareData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`LinkedIn API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(`LinkedIn posting error: ${result.error.message}`);
      }

      // Extract post ID from URN
      const postId = result.id.split(':').pop();
      const postUrl = `https://www.linkedin.com/feed/update/${result.id}`;

      console.log('LinkedIn post published successfully:', {
        postId,
        postUrl,
        urn: result.id
      });

      return {
        success: true,
        postId: postId || result.id,
        url: postUrl,
        platformResponse: result
      };

    } catch (error) {
      console.error('LinkedIn publishing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Upload media to LinkedIn
   */
  private async uploadMedia(account: SocialAccount, mediaUrl: string, mediaType?: string): Promise<string> {
    try {
      console.log('Uploading media to LinkedIn:', { mediaUrl, mediaType });

      // Step 1: Register upload
      const registerData = {
        registerUploadRequest: {
          recipes: ['urn:li:digitalmedia:uploading:MediaUploadHttpRequest'],
          owner: `urn:li:person:${account.account_id}`,
          serviceRelationships: [{
            relationshipType: 'OWNER',
            identifier: 'urn:li:userGeneratedContent'
          }]
        }
      };

      const registerResponse = await fetch(`${this.apiUrl}/assets?action=registerUpload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(registerData)
      });

      if (!registerResponse.ok) {
        throw new Error(`Failed to register upload: ${registerResponse.statusText}`);
      }

      const registerResult = await registerResponse.json();
      
      if (registerResult.error) {
        throw new Error(`LinkedIn register upload error: ${registerResult.error.message}`);
      }

      const uploadUrl = registerResult.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'].uploadUrl;
      const asset = registerResult.value.asset;

      // Step 2: Download media from URL
      const mediaResponse = await fetch(mediaUrl);
      if (!mediaResponse.ok) {
        throw new Error(`Failed to download media: ${mediaResponse.statusText}`);
      }

      const mediaBuffer = await mediaResponse.arrayBuffer();

      // Step 3: Upload to LinkedIn
      const uploadResponse = await fetch(uploadUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`
        },
        body: mediaBuffer
      });

      if (!uploadResponse.ok) {
        throw new Error(`LinkedIn media upload failed: ${uploadResponse.statusText}`);
      }

      console.log('LinkedIn media uploaded successfully:', asset);
      return asset;

    } catch (error) {
      console.error('LinkedIn media upload error:', error);
      throw error;
    }
  }

  /**
   * Validate LinkedIn access token
   */
  private async validateToken(accessToken: string): Promise<void> {
    try {
      const response = await fetch(`${this.apiUrl}/people/(id:me)`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('LinkedIn token is invalid or expired');
        }
        throw new Error(`LinkedIn token validation failed: ${response.statusText}`);
      }

      const userData = await response.json();
      
      if (userData.error) {
        throw new Error(`LinkedIn API error: ${userData.error.message}`);
      }

    } catch (error) {
      console.error('LinkedIn token validation error:', error);
      throw error;
    }
  }

  /**
   * Get LinkedIn post analytics
   */
  async getPostAnalytics(postUrn: string, accessToken: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.apiUrl}/socialActions/${postUrn}/statistics`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(`LinkedIn analytics error: ${data.error.message}`);
      }

      return data;

    } catch (error) {
      console.error('LinkedIn analytics fetch error:', error);
      throw error;
    }
  }

  /**
   * Test LinkedIn connection
   */
  async testConnection(account: SocialAccount): Promise<any> {
    try {
      console.log('Testing LinkedIn connection:', {
        accountId: account.account_id
      });

      const response = await fetch(
        `${this.apiUrl}/people/(id:${account.account_id})?projection=(id,firstName,lastName,profilePicture(displayImage~:playableStreams))`,
        {
          headers: {
            'Authorization': `Bearer ${account.access_token}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Connection test failed: ${response.statusText}`);
      }

      const userData = await response.json();
      
      if (userData.error) {
        throw new Error(`LinkedIn API error: ${userData.error.message}`);
      }

      console.log('LinkedIn connection test successful:', {
        firstName: userData.firstName?.localized?.en_US,
        lastName: userData.lastName?.localized?.en_US
      });

      return {
        success: true,
        accountInfo: {
          id: userData.id,
          firstName: userData.firstName?.localized?.en_US,
          lastName: userData.lastName?.localized?.en_US,
          profilePicture: userData.profilePicture?.displayImage
        }
      };

    } catch (error) {
      console.error('LinkedIn connection test failed:', error);
      throw error;
    }
  }

  /**
   * Get LinkedIn rate limit status
   */
  async getRateLimitStatus(accessToken: string): Promise<any> {
    try {
      // LinkedIn doesn't provide explicit rate limit headers, but we can check with a simple API call
      const response = await fetch(`${this.apiUrl}/people/(id:me)`, {
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      return {
        status: response.status,
        remaining: response.headers.get('X-RateLimit-Remaining'),
        reset: response.headers.get('X-RateLimit-Reset'),
        limit: response.headers.get('X-RateLimit-Limit')
      };

    } catch (error) {
      console.error('Error checking LinkedIn rate limits:', error);
      return null;
    }
  }

  /**
   * Format content for LinkedIn (character limits, hashtags, etc.)
   */
  formatContent(content: string): { text: string; isValid: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let formattedText = content;

    // LinkedIn has a 3000 character limit
    if (formattedText.length > 3000) {
      formattedText = formattedText.substring(0, 2997) + '...';
      warnings.push('Content was truncated to fit LinkedIn character limit');
    }

    // Check hashtag count (LinkedIn recommends max 5 hashtags)
    const hashtags = formattedText.match(/#\w+/g) || [];
    if (hashtags.length > 5) {
      warnings.push('LinkedIn recommends using no more than 5 hashtags for optimal reach');
    }

    return {
      text: formattedText,
      isValid: formattedText.length > 0 && formattedText.length <= 3000,
      warnings
    };
  }
}

export default LinkedInPublisherV2;
