-- Advanced Content Creation System Migration
-- This migration adds tables for AI usage tracking and content templates

-- AI Usage Logs Table
CREATE TABLE IF NOT EXISTS ai_usage_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- 'generate', 'optimize', 'hashtags'
    platform VARCHAR(20) NOT NULL, -- 'twitter', 'facebook', 'instagram', etc.
    language VARCHAR(5) NOT NULL DEFAULT 'ar', -- 'ar', 'en'
    prompt_length INTEGER,
    response_length INTEGER,
    tokens_used INTEGER,
    processing_time_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Template Usage Logs Table
CREATE TABLE IF NOT EXISTS template_usage_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    template_id VARCHAR(100) NOT NULL,
    template_name VARCHAR(255),
    template_category VARCHAR(50),
    variables_used JSONB DEFAULT '{}',
    success BOOLEAN DEFAULT true,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Custom Content Templates Table (for user-created templates)
CREATE TABLE IF NOT EXISTS custom_content_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    platform TEXT[] NOT NULL, -- Array of platforms
    language VARCHAR(10) NOT NULL DEFAULT 'ar',
    content TEXT NOT NULL,
    variables JSONB DEFAULT '[]',
    hashtags TEXT[] DEFAULT '{}',
    tone VARCHAR(20) NOT NULL,
    industry TEXT[] DEFAULT '{}',
    is_public BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    tags TEXT[] DEFAULT '{}',
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    rating_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Generation History Table
CREATE TABLE IF NOT EXISTS content_generation_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    prompt TEXT NOT NULL,
    generated_content TEXT NOT NULL,
    platform VARCHAR(20) NOT NULL,
    tone VARCHAR(20) NOT NULL,
    language VARCHAR(5) NOT NULL DEFAULT 'ar',
    hashtags TEXT[] DEFAULT '{}',
    keywords TEXT[] DEFAULT '{}',
    target_audience TEXT,
    ai_model VARCHAR(50),
    processing_time_ms INTEGER,
    tokens_used INTEGER,
    quality_score DECIMAL(3,2),
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    was_used BOOLEAN DEFAULT false,
    post_id UUID REFERENCES posts(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Optimization History Table
CREATE TABLE IF NOT EXISTS content_optimization_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    original_content TEXT NOT NULL,
    optimized_content TEXT NOT NULL,
    from_platform VARCHAR(20) NOT NULL,
    to_platform VARCHAR(20) NOT NULL,
    language VARCHAR(5) NOT NULL DEFAULT 'ar',
    optimization_type VARCHAR(50) DEFAULT 'platform_adaptation',
    ai_model VARCHAR(50),
    processing_time_ms INTEGER,
    tokens_used INTEGER,
    improvement_score DECIMAL(3,2),
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    was_used BOOLEAN DEFAULT false,
    post_id UUID REFERENCES posts(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Hashtag Suggestions History Table
CREATE TABLE IF NOT EXISTS hashtag_suggestions_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    platform VARCHAR(20) NOT NULL,
    language VARCHAR(5) NOT NULL DEFAULT 'ar',
    suggested_hashtags JSONB NOT NULL, -- Array of hashtag objects with popularity, relevance, etc.
    selected_hashtags TEXT[] DEFAULT '{}',
    ai_model VARCHAR(50),
    processing_time_ms INTEGER,
    tokens_used INTEGER,
    relevance_score DECIMAL(3,2),
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    post_id UUID REFERENCES posts(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Usage Statistics View
CREATE OR REPLACE VIEW ai_usage_statistics AS
SELECT 
    user_id,
    DATE_TRUNC('day', timestamp) as usage_date,
    action,
    platform,
    language,
    COUNT(*) as usage_count,
    AVG(processing_time_ms) as avg_processing_time,
    SUM(tokens_used) as total_tokens,
    COUNT(CASE WHEN success = true THEN 1 END) as successful_requests,
    COUNT(CASE WHEN success = false THEN 1 END) as failed_requests
FROM ai_usage_logs
GROUP BY user_id, DATE_TRUNC('day', timestamp), action, platform, language;

-- Template Usage Statistics View
CREATE OR REPLACE VIEW template_usage_statistics AS
SELECT 
    template_id,
    template_name,
    template_category,
    COUNT(*) as usage_count,
    COUNT(DISTINCT user_id) as unique_users,
    DATE_TRUNC('month', timestamp) as usage_month
FROM template_usage_logs
WHERE success = true
GROUP BY template_id, template_name, template_category, DATE_TRUNC('month', timestamp);

-- Content Quality Metrics View
CREATE OR REPLACE VIEW content_quality_metrics AS
SELECT 
    user_id,
    platform,
    language,
    AVG(quality_score) as avg_quality_score,
    AVG(user_rating) as avg_user_rating,
    COUNT(*) as total_generations,
    COUNT(CASE WHEN was_used = true THEN 1 END) as used_generations,
    (COUNT(CASE WHEN was_used = true THEN 1 END)::DECIMAL / COUNT(*)) * 100 as usage_rate
FROM content_generation_history
WHERE quality_score IS NOT NULL
GROUP BY user_id, platform, language;

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_user_timestamp ON ai_usage_logs(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_action_platform ON ai_usage_logs(action, platform);
CREATE INDEX IF NOT EXISTS idx_template_usage_logs_user_template ON template_usage_logs(user_id, template_id);
CREATE INDEX IF NOT EXISTS idx_template_usage_logs_timestamp ON template_usage_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_custom_templates_user_category ON custom_content_templates(user_id, category);
CREATE INDEX IF NOT EXISTS idx_custom_templates_public_featured ON custom_content_templates(is_public, is_featured) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_content_generation_user_created ON content_generation_history(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_optimization_user_created ON content_optimization_history(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_hashtag_suggestions_user_created ON hashtag_suggestions_history(user_id, created_at DESC);

-- Row Level Security (RLS) Policies

-- AI Usage Logs RLS
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own AI usage logs" ON ai_usage_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own AI usage logs" ON ai_usage_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Template Usage Logs RLS
ALTER TABLE template_usage_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own template usage logs" ON template_usage_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own template usage logs" ON template_usage_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Custom Content Templates RLS
ALTER TABLE custom_content_templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view public templates and their own templates" ON custom_content_templates
    FOR SELECT USING (is_public = true OR auth.uid() = user_id);

CREATE POLICY "Users can insert their own templates" ON custom_content_templates
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own templates" ON custom_content_templates
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own templates" ON custom_content_templates
    FOR DELETE USING (auth.uid() = user_id);

-- Content Generation History RLS
ALTER TABLE content_generation_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own content generation history" ON content_generation_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own content generation history" ON content_generation_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own content generation history" ON content_generation_history
    FOR UPDATE USING (auth.uid() = user_id);

-- Content Optimization History RLS
ALTER TABLE content_optimization_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own content optimization history" ON content_optimization_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own content optimization history" ON content_optimization_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own content optimization history" ON content_optimization_history
    FOR UPDATE USING (auth.uid() = user_id);

-- Hashtag Suggestions History RLS
ALTER TABLE hashtag_suggestions_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own hashtag suggestions history" ON hashtag_suggestions_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own hashtag suggestions history" ON hashtag_suggestions_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own hashtag suggestions history" ON hashtag_suggestions_history
    FOR UPDATE USING (auth.uid() = user_id);

-- Functions for updating usage counts and ratings

-- Function to update template usage count
CREATE OR REPLACE FUNCTION update_template_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.success = true THEN
        UPDATE custom_content_templates 
        SET usage_count = usage_count + 1,
            updated_at = NOW()
        WHERE id = NEW.template_id::UUID;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for template usage count
CREATE TRIGGER trigger_update_template_usage_count
    AFTER INSERT ON template_usage_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_template_usage_count();

-- Function to update template rating
CREATE OR REPLACE FUNCTION update_template_rating(
    template_id_param UUID,
    new_rating INTEGER
)
RETURNS VOID AS $$
DECLARE
    current_rating DECIMAL(3,2);
    current_count INTEGER;
    new_avg_rating DECIMAL(3,2);
BEGIN
    SELECT rating, rating_count INTO current_rating, current_count
    FROM custom_content_templates
    WHERE id = template_id_param;
    
    IF current_count = 0 THEN
        new_avg_rating = new_rating;
    ELSE
        new_avg_rating = ((current_rating * current_count) + new_rating) / (current_count + 1);
    END IF;
    
    UPDATE custom_content_templates
    SET rating = new_avg_rating,
        rating_count = current_count + 1,
        updated_at = NOW()
    WHERE id = template_id_param;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON ai_usage_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON template_usage_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON custom_content_templates TO authenticated;
GRANT SELECT, INSERT, UPDATE ON content_generation_history TO authenticated;
GRANT SELECT, INSERT, UPDATE ON content_optimization_history TO authenticated;
GRANT SELECT, INSERT, UPDATE ON hashtag_suggestions_history TO authenticated;
GRANT SELECT ON ai_usage_statistics TO authenticated;
GRANT SELECT ON template_usage_statistics TO authenticated;
GRANT SELECT ON content_quality_metrics TO authenticated;
