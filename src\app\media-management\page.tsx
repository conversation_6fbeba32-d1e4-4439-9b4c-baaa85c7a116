'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  Image, 
  Video, 
  FileText, 
  Music,
  BarChart3, 
  Settings,
  RefreshCw,
  TrendingUp,
  HardDrive,
  Zap,
  Users
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { MediaManagementDashboard } from '@/components/media/media-management-dashboard';
import { createClient } from '@/lib/supabase/client';

interface MediaStats {
  totalFiles: number;
  totalSize: number;
  imageCount: number;
  videoCount: number;
  documentCount: number;
  audioCount: number;
  storageUsed: number;
  storageLimit: number;
  processingQueue: number;
  optimizedFiles: number;
}

export default function MediaManagementPage() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<MediaStats>({
    totalFiles: 0,
    totalSize: 0,
    imageCount: 0,
    videoCount: 0,
    documentCount: 0,
    audioCount: 0,
    storageUsed: 0,
    storageLimit: 1024 * 1024 * 1024, // 1GB default
    processingQueue: 0,
    optimizedFiles: 0
  });
  const [activeTab, setActiveTab] = useState('media');
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkAuthAndLoadData();
  }, []);

  const checkAuthAndLoadData = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        router.push('/auth/signin');
        return;
      }

      setUser(user);
      await loadMediaStats();
      
    } catch (error) {
      console.error('Error checking auth:', error);
      router.push('/auth/signin');
    } finally {
      setIsLoading(false);
    }
  };

  const loadMediaStats = async () => {
    try {
      // Load media statistics
      const response = await fetch('/api/media/enhanced?limit=1000');
      if (response.ok) {
        const data = await response.json();
        const mediaFiles = data.data?.media || [];

        // Calculate statistics
        const newStats: MediaStats = {
          totalFiles: mediaFiles.length,
          totalSize: mediaFiles.reduce((sum: number, file: any) => sum + file.fileSize, 0),
          imageCount: mediaFiles.filter((f: any) => f.mediaType === 'image').length,
          videoCount: mediaFiles.filter((f: any) => f.mediaType === 'video').length,
          documentCount: mediaFiles.filter((f: any) => f.mediaType === 'document').length,
          audioCount: mediaFiles.filter((f: any) => f.mediaType === 'audio').length,
          storageUsed: mediaFiles.reduce((sum: number, file: any) => sum + file.fileSize, 0),
          storageLimit: 1024 * 1024 * 1024, // 1GB
          processingQueue: mediaFiles.filter((f: any) => f.status === 'processing').length,
          optimizedFiles: mediaFiles.filter((f: any) => f.platformVersions?.length > 0).length
        };

        setStats(newStats);
      }

    } catch (error) {
      console.error('Error loading media stats:', error);
    }
  };

  const refreshData = async () => {
    await loadMediaStats();
    toast.success('تم تحديث البيانات');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStoragePercentage = () => {
    return Math.round((stats.storageUsed / stats.storageLimit) * 100);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 max-w-6xl">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
          <span className="mr-3 text-lg">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold">إدارة الوسائط المتقدمة</h1>
            <p className="text-muted-foreground">
              رفع ومعالجة وتحسين ملفات الوسائط لجميع منصات التواصل الاجتماعي
            </p>
          </div>
          <Button onClick={refreshData} variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            تحديث
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">إجمالي الملفات</p>
                  <p className="text-2xl font-bold">{stats.totalFiles}</p>
                </div>
                <FileText className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">حجم التخزين</p>
                  <p className="text-2xl font-bold">{formatFileSize(stats.storageUsed)}</p>
                  <p className="text-xs text-muted-foreground">
                    {getStoragePercentage()}% من {formatFileSize(stats.storageLimit)}
                  </p>
                </div>
                <HardDrive className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">ملفات محسنة</p>
                  <p className="text-2xl font-bold">{stats.optimizedFiles}</p>
                  <p className="text-xs text-muted-foreground">
                    {stats.totalFiles > 0 ? Math.round((stats.optimizedFiles / stats.totalFiles) * 100) : 0}% محسن
                  </p>
                </div>
                <Zap className="w-8 h-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">قيد المعالجة</p>
                  <p className="text-2xl font-bold">{stats.processingQueue}</p>
                  <p className="text-xs text-muted-foreground">ملفات في الانتظار</p>
                </div>
                <RefreshCw className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Media Type Breakdown */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Image className="w-6 h-6 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">الصور</p>
                  <p className="text-lg font-bold">{stats.imageCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Video className="w-6 h-6 text-purple-500" />
                <div>
                  <p className="text-sm font-medium">الفيديوهات</p>
                  <p className="text-lg font-bold">{stats.videoCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <FileText className="w-6 h-6 text-green-500" />
                <div>
                  <p className="text-sm font-medium">المستندات</p>
                  <p className="text-lg font-bold">{stats.documentCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Music className="w-6 h-6 text-orange-500" />
                <div>
                  <p className="text-sm font-medium">الصوتيات</p>
                  <p className="text-lg font-bold">{stats.audioCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="media" className="flex items-center gap-2">
            <Upload className="w-4 h-4" />
            إدارة الوسائط
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            التحليلات
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            الإعدادات
          </TabsTrigger>
        </TabsList>

        <TabsContent value="media" className="space-y-6">
          <MediaManagementDashboard />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                تحليلات الوسائط
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Storage Usage Chart */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">استخدام التخزين</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>المستخدم</span>
                      <span>{formatFileSize(stats.storageUsed)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-500 h-3 rounded-full transition-all"
                        style={{ width: `${getStoragePercentage()}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0</span>
                      <span>{formatFileSize(stats.storageLimit)}</span>
                    </div>
                  </div>
                </div>

                {/* File Type Distribution */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">توزيع أنواع الملفات</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Image className="w-4 h-4 text-blue-500" />
                        <span className="text-sm">الصور</span>
                      </div>
                      <span className="text-sm font-medium">{stats.imageCount}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Video className="w-4 h-4 text-purple-500" />
                        <span className="text-sm">الفيديوهات</span>
                      </div>
                      <span className="text-sm font-medium">{stats.videoCount}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-green-500" />
                        <span className="text-sm">المستندات</span>
                      </div>
                      <span className="text-sm font-medium">{stats.documentCount}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Music className="w-4 h-4 text-orange-500" />
                        <span className="text-sm">الصوتيات</span>
                      </div>
                      <span className="text-sm font-medium">{stats.audioCount}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="mt-6 pt-6 border-t">
                <h3 className="text-lg font-semibold mb-4">مقاييس الأداء</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">معدل التحسين</p>
                    <p className="text-xl font-bold">
                      {stats.totalFiles > 0 ? Math.round((stats.optimizedFiles / stats.totalFiles) * 100) : 0}%
                    </p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <HardDrive className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">متوسط حجم الملف</p>
                    <p className="text-xl font-bold">
                      {stats.totalFiles > 0 ? formatFileSize(stats.totalSize / stats.totalFiles) : '0 Bytes'}
                    </p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <Zap className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">ملفات قيد المعالجة</p>
                    <p className="text-xl font-bold">{stats.processingQueue}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                إعدادات الوسائط
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">إعدادات التحسين التلقائي</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">تحسين الصور تلقائياً</p>
                        <p className="text-sm text-muted-foreground">ضغط وتحسين الصور عند الرفع</p>
                      </div>
                      <Badge>مفعل</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">إنشاء نسخ للمنصات</p>
                        <p className="text-sm text-muted-foreground">إنشاء نسخ محسنة لكل منصة</p>
                      </div>
                      <Badge>مفعل</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">إنشاء صور مصغرة</p>
                        <p className="text-sm text-muted-foreground">إنشاء صور مصغرة للفيديوهات</p>
                      </div>
                      <Badge>مفعل</Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">حدود التخزين</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">الحد الأقصى لحجم الملف</p>
                        <p className="text-sm text-muted-foreground">100 ميجابايت لكل ملف</p>
                      </div>
                      <Badge variant="outline">100 MB</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">إجمالي التخزين المتاح</p>
                        <p className="text-sm text-muted-foreground">المساحة الإجمالية المتاحة</p>
                      </div>
                      <Badge variant="outline">{formatFileSize(stats.storageLimit)}</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
