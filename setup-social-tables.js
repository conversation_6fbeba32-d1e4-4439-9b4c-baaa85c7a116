const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function setupSocialTables() {
  console.log('Setting up social media tables...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    return;
  }
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    console.log('Creating social_accounts table...');
    
    // Create social_accounts table
    const { error: socialError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS social_accounts (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          user_id TEXT NOT NULL,
          platform TEXT NOT NULL CHECK (platform IN ('TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK', 'SNAPCHAT')),
          account_id TEXT NOT NULL,
          account_name TEXT NOT NULL,
          access_token TEXT NOT NULL,
          refresh_token TEXT,
          expires_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          UNIQUE(user_id, platform, account_id)
        );
        
        CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id ON social_accounts(user_id);
        CREATE INDEX IF NOT EXISTS idx_social_accounts_platform ON social_accounts(platform);
      `
    });
    
    if (socialError) {
      console.error('Error creating social_accounts table:', socialError);
    } else {
      console.log('✅ Social accounts table created successfully');
    }
    
    console.log('Creating activities table...');
    
    // Create activities table
    const { error: activitiesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS activities (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          user_id TEXT NOT NULL,
          post_id TEXT,
          action TEXT NOT NULL CHECK (action IN ('CONNECT_SOCIAL', 'POST_SCHEDULED', 'POST_PUBLISHED', 'POST_FAILED', 'REGISTER', 'LOGIN', 'LOGOUT')),
          details TEXT,
          metadata JSONB,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_activities_user_id ON activities(user_id);
        CREATE INDEX IF NOT EXISTS idx_activities_action ON activities(action);
        CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);
      `
    });
    
    if (activitiesError) {
      console.error('Error creating activities table:', activitiesError);
    } else {
      console.log('✅ Activities table created successfully');
    }
    
    console.log('Creating posts table...');
    
    // Create posts table
    const { error: postsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS posts (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          user_id TEXT NOT NULL,
          content TEXT NOT NULL,
          media_url TEXT,
          status TEXT DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'SCHEDULED', 'PUBLISHED', 'FAILED')),
          scheduled_at TIMESTAMPTZ,
          published_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id);
        CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
        CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON posts(scheduled_at);
      `
    });
    
    if (postsError) {
      console.error('Error creating posts table:', postsError);
    } else {
      console.log('✅ Posts table created successfully');
    }
    
    // Test table access
    console.log('Testing table access...');
    
    const { data: socialTest, error: socialTestError } = await supabase
      .from('social_accounts')
      .select('count', { count: 'exact', head: true });
    
    if (socialTestError) {
      console.error('❌ Social accounts table test failed:', socialTestError);
    } else {
      console.log('✅ Social accounts table accessible');
    }
    
    const { data: activitiesTest, error: activitiesTestError } = await supabase
      .from('activities')
      .select('count', { count: 'exact', head: true });
    
    if (activitiesTestError) {
      console.error('❌ Activities table test failed:', activitiesTestError);
    } else {
      console.log('✅ Activities table accessible');
    }
    
    console.log('🎉 Social media database setup completed!');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  }
}

setupSocialTables();
