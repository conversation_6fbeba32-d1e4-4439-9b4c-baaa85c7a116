import { NextRequest, NextResponse } from 'next/server';
import { 
  getOAuthConfigs, 
  getPlatformSummary, 
  validateOAuthConfig,
  getEnabledPlatforms 
} from '@/lib/social/oauth-config';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Get OAuth configuration status for all platforms
 * GET /api/social/oauth-status
 */
export async function GET(request: NextRequest) {
  try {
    const configs = getOAuthConfigs();
    const platformSummary = getPlatformSummary();
    const enabledPlatforms = getEnabledPlatforms();

    // Validate each platform configuration
    const validationResults = Object.keys(configs).map(platform => ({
      platform,
      ...validateOAuthConfig(platform)
    }));

    // Count statistics
    const stats = {
      total: Object.keys(configs).length,
      enabled: enabledPlatforms.length,
      configured: platformSummary.filter(p => p.configured).length,
      ready: platformSummary.filter(p => p.enabled && p.configured).length,
    };

    // Environment variable status
    const envStatus = {
      twitter: {
        clientId: !!process.env.TWITTER_CLIENT_ID,
        clientSecret: !!process.env.TWITTER_CLIENT_SECRET,
        apiKey: !!process.env.TWITTER_API_KEY,
        apiSecret: !!process.env.TWITTER_API_SECRET,
        bearerToken: !!process.env.TWITTER_BEARER_TOKEN,
      },
      x: {
        clientId: !!process.env.X_CLIENT_ID,
        clientSecret: !!process.env.X_CLIENT_SECRET,
        apiKey: !!process.env.X_API_KEY,
        apiSecret: !!process.env.X_API_SECRET,
        bearerToken: !!process.env.X_BEARER_TOKEN,
      },
      facebook: {
        appId: !!process.env.FACEBOOK_APP_ID,
        appSecret: !!process.env.FACEBOOK_APP_SECRET,
      },
      instagram: {
        appId: !!process.env.INSTAGRAM_APP_ID || !!process.env.FACEBOOK_APP_ID,
        appSecret: !!process.env.INSTAGRAM_APP_SECRET || !!process.env.FACEBOOK_APP_SECRET,
      },
      linkedin: {
        clientId: !!process.env.LINKEDIN_CLIENT_ID,
        clientSecret: !!process.env.LINKEDIN_CLIENT_SECRET,
      },
      tiktok: {
        clientId: !!process.env.TIKTOK_CLIENT_ID,
        clientSecret: !!process.env.TIKTOK_CLIENT_SECRET,
      },
      snapchat: {
        clientId: !!process.env.SNAPCHAT_CLIENT_ID,
        clientSecret: !!process.env.SNAPCHAT_CLIENT_SECRET,
      },
    };

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (stats.enabled === 0) {
      recommendations.push('No social media platforms are configured. Add API credentials to enable platforms.');
    }
    
    if (stats.configured < stats.total) {
      const unconfigured = platformSummary.filter(p => !p.configured).map(p => p.platform);
      recommendations.push(`Configure missing platforms: ${unconfigured.join(', ')}`);
    }

    validationResults.forEach(result => {
      if (result.errors.length > 0) {
        recommendations.push(`${result.platform}: ${result.errors.join(', ')}`);
      }
    });

    if (enabledPlatforms.length > 0) {
      recommendations.push('Test OAuth flows for enabled platforms');
      recommendations.push('Set up webhook endpoints for real-time updates');
    }

    const response = {
      status: stats.ready > 0 ? 'partial' : stats.configured > 0 ? 'configured' : 'not-configured',
      timestamp: new Date().toISOString(),
      statistics: stats,
      platforms: platformSummary,
      enabledPlatforms,
      validationResults,
      environmentVariables: envStatus,
      recommendations,
      nextSteps: [
        'Obtain API credentials from social media platforms',
        'Configure environment variables',
        'Test OAuth authorization flows',
        'Implement webhook handlers for real-time updates',
        'Set up rate limiting and error handling',
      ],
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('OAuth status check failed:', error);
    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Failed to get OAuth status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Test OAuth configuration for a specific platform
 * POST /api/social/oauth-status
 */
export async function POST(request: NextRequest) {
  try {
    const { platform, action } = await request.json();

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'validate':
        const validation = validateOAuthConfig(platform);
        return NextResponse.json({
          platform,
          ...validation,
          timestamp: new Date().toISOString(),
        });

      case 'test-auth-url':
        const { getAuthorizationUrl } = await import('@/lib/social/oauth-config');
        const authUrl = getAuthorizationUrl(platform, 'test-state');
        
        return NextResponse.json({
          platform,
          success: !!authUrl,
          authUrl,
          message: authUrl ? 'Authorization URL generated successfully' : 'Failed to generate authorization URL',
          timestamp: new Date().toISOString(),
        });

      case 'check-credentials':
        const config = await import('@/lib/social/oauth-config');
        const platformConfig = config.getOAuthConfig(platform);
        
        if (!platformConfig) {
          return NextResponse.json({
            platform,
            success: false,
            message: 'Platform not supported',
          });
        }

        return NextResponse.json({
          platform,
          success: platformConfig.enabled,
          configured: !!(platformConfig.clientId && platformConfig.clientSecret),
          enabled: platformConfig.enabled,
          hasClientId: !!platformConfig.clientId,
          hasClientSecret: !!platformConfig.clientSecret,
          scopes: platformConfig.scope,
          message: platformConfig.enabled ? 'Platform is configured and enabled' : 'Platform is not configured or disabled',
          timestamp: new Date().toISOString(),
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: validate, test-auth-url, or check-credentials' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('OAuth test failed:', error);
    return NextResponse.json(
      {
        error: 'OAuth test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
