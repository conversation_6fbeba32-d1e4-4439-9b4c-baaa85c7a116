import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const createOrganizationSchema = z.object({
  name: z.string().min(1).max(200),
  slug: z.string().min(1).max(100).regex(/^[a-z0-9-]+$/),
  description: z.string().optional(),
  website_url: z.string().url().optional(),
  subscription_plan: z.enum(['free', 'pro', 'enterprise']).default('free'),
});

const updateOrganizationSchema = z.object({
  name: z.string().min(1).max(200).optional(),
  description: z.string().optional(),
  website_url: z.string().url().optional(),
  logo_url: z.string().url().optional(),
  settings: z.record(z.any()).optional(),
  timezone: z.string().optional(),
});

/**
 * Get user's organizations
 * GET /api/teams/organizations
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organizations with role information
    const { data: organizations, error } = await supabase
      .from('organizations')
      .select(`
        *,
        organization_members!inner(
          role,
          status,
          joined_at
        )
      `)
      .eq('organization_members.user_id', user.id)
      .eq('organization_members.status', 'active')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching organizations:', error);
      return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 });
    }

    // Get member counts for each organization
    const organizationsWithCounts = await Promise.all(
      organizations.map(async (org) => {
        const { count: memberCount } = await supabase
          .from('organization_members')
          .select('*', { count: 'exact', head: true })
          .eq('organization_id', org.id)
          .eq('status', 'active');

        const { count: workspaceCount } = await supabase
          .from('workspaces')
          .select('*', { count: 'exact', head: true })
          .eq('organization_id', org.id)
          .eq('is_active', true);

        return {
          ...org,
          member_count: memberCount || 0,
          workspace_count: workspaceCount || 0,
          user_role: org.organization_members[0]?.role,
        };
      })
    );

    return NextResponse.json({
      success: true,
      organizations: organizationsWithCounts,
    });

  } catch (error) {
    console.error('Organizations fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Create new organization
 * POST /api/teams/organizations
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validation = createOrganizationSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { name, slug, description, website_url, subscription_plan } = validation.data;

    // Check if slug is already taken
    const { data: existingOrg } = await supabase
      .from('organizations')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingOrg) {
      return NextResponse.json({ error: 'Organization slug already exists' }, { status: 409 });
    }

    // Set limits based on subscription plan
    const planLimits = {
      free: { max_users: 3, max_social_accounts: 5, max_posts_per_month: 100 },
      pro: { max_users: 25, max_social_accounts: 50, max_posts_per_month: 1000 },
      enterprise: { max_users: 100, max_social_accounts: 200, max_posts_per_month: 10000 },
    };

    const limits = planLimits[subscription_plan];

    // Create organization
    const { data: organization, error: createError } = await supabase
      .from('organizations')
      .insert({
        name,
        slug,
        description,
        website_url,
        subscription_plan,
        ...limits,
        created_by: user.id,
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating organization:', createError);
      return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 });
    }

    // Add creator as owner
    const { error: memberError } = await supabase
      .from('organization_members')
      .insert({
        organization_id: organization.id,
        user_id: user.id,
        role: 'owner',
        status: 'active',
      });

    if (memberError) {
      console.error('Error adding organization owner:', memberError);
      // Rollback organization creation
      await supabase.from('organizations').delete().eq('id', organization.id);
      return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 });
    }

    // Create default workspace
    const { data: defaultWorkspace, error: workspaceError } = await supabase
      .from('workspaces')
      .insert({
        organization_id: organization.id,
        name: 'General',
        slug: 'general',
        description: 'Default workspace for general content',
        created_by: user.id,
      })
      .select()
      .single();

    if (workspaceError) {
      console.error('Error creating default workspace:', workspaceError);
    } else {
      // Add creator to default workspace
      await supabase
        .from('workspace_members')
        .insert({
          workspace_id: defaultWorkspace.id,
          user_id: user.id,
          role: 'admin',
          status: 'active',
          added_by: user.id,
        });
    }

    return NextResponse.json({
      success: true,
      organization: {
        ...organization,
        member_count: 1,
        workspace_count: 1,
        user_role: 'owner',
      },
    });

  } catch (error) {
    console.error('Organization creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Update organization
 * PUT /api/teams/organizations
 */
export async function PUT(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('id');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
    }

    // Check if user has permission to update organization
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!membership || !['owner', 'admin'].includes(membership.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validation = updateOrganizationSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    // Update organization
    const { data: organization, error: updateError } = await supabase
      .from('organizations')
      .update(validation.data)
      .eq('id', organizationId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating organization:', updateError);
      return NextResponse.json({ error: 'Failed to update organization' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      organization,
    });

  } catch (error) {
    console.error('Organization update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Delete organization
 * DELETE /api/teams/organizations
 */
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('id');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
    }

    // Check if user is owner
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organizationId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!membership || membership.role !== 'owner') {
      return NextResponse.json({ error: 'Only organization owners can delete organizations' }, { status: 403 });
    }

    // Delete organization (cascades to all related data)
    const { error: deleteError } = await supabase
      .from('organizations')
      .delete()
      .eq('id', organizationId);

    if (deleteError) {
      console.error('Error deleting organization:', deleteError);
      return NextResponse.json({ error: 'Failed to delete organization' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Organization deleted successfully',
    });

  } catch (error) {
    console.error('Organization deletion error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
