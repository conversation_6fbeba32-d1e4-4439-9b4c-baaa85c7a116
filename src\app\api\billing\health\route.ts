import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ewasl-payment-system',
      version: '1.0.0',
      message: 'Payment system is operational'
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        service: 'ewasl-payment-system',
        error: 'Health check failed'
      },
      { status: 500 }
    );
  }
}
