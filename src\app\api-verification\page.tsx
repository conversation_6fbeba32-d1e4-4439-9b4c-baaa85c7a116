'use client';

import { useState } from 'react';

interface TestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  timestamp: string;
}

export default function APIVerificationPage() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (result: Omit<TestResult, 'timestamp'>) => {
    const newResult: TestResult = {
      ...result,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [newResult, ...prev]);
  };

  const testAPI = async (name: string, url: string, options?: RequestInit) => {
    setIsLoading(true);
    addResult({ success: false, message: `🔄 Testing ${name}...` });
    
    try {
      const response = await fetch(url, options);
      const data = await response.json();
      
      if (response.ok) {
        addResult({
          success: true,
          message: `✅ ${name} SUCCESS`,
          data: data
        });
      } else {
        addResult({
          success: false,
          message: `❌ ${name} FAILED (${response.status})`,
          error: JSON.stringify(data)
        });
      }
    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ ${name} ERROR`,
        error: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runComprehensiveTest = async () => {
    setResults([]);
    addResult({ success: true, message: '🚀 Starting Priority 2 API Verification with Production Credentials...' });
    
    // Test 1: Health API
    await testAPI('Health API', '/api/health');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 2: Social Connect API
    await testAPI('Social Connect API', '/api/social/connect');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 3: Twitter Connection Test
    await testAPI('Twitter Connection Test', '/api/social-accounts/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'TWITTER',
        accessToken: 'test_token_production',
        accountId: 'test_account'
      })
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 4: Facebook Connection Test
    await testAPI('Facebook Connection Test', '/api/social-accounts/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'FACEBOOK',
        accessToken: 'test_token_production',
        accountId: 'test_account'
      })
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 5: LinkedIn Connection Test
    await testAPI('LinkedIn Connection Test', '/api/social-accounts/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'LINKEDIN',
        accessToken: 'test_token_production',
        accountId: 'test_account'
      })
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 6: Instagram Connection Test
    await testAPI('Instagram Connection Test', '/api/social-accounts/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'INSTAGRAM',
        accessToken: 'test_token_production',
        accountId: 'test_account'
      })
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 7: Twitter Test Publish
    await testAPI('Twitter Test Publish', '/api/posts/test-publish', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'TWITTER',
        content: '[PRODUCTION TEST] eWasl Priority 2 API Integration Verification! 🚀 #eWasl #SocialMedia',
        accessToken: 'test_token_production',
        isTest: true
      })
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 8: Facebook Test Publish
    await testAPI('Facebook Test Publish', '/api/posts/test-publish', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        platform: 'FACEBOOK',
        content: '[PRODUCTION TEST] eWasl Social Media Management Platform - API Integration Complete! 🎯',
        accessToken: 'test_token_production',
        isTest: true
      })
    });
    
    addResult({ success: true, message: '🎯 Priority 2 API Verification Complete!' });
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '2rem',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          marginBottom: '2rem',
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: '0.5rem'
          }}>
            🎯 Priority 2 API Verification
          </h1>
          <p style={{ color: '#666', fontSize: '1.1rem', marginBottom: '1rem' }}>
            Complete Core Publishing - API Integration with Production Credentials
          </p>
          <div style={{
            background: '#e8f5e8',
            padding: '1rem',
            borderRadius: '0.5rem',
            border: '1px solid #4CAF50'
          }}>
            <strong>🔑 Production Credentials Loaded:</strong><br/>
            Twitter, Facebook, Instagram, LinkedIn, Snapchat APIs
          </div>
        </div>

        {/* Test Controls */}
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          marginBottom: '2rem'
        }}>
          <h2 style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#333' }}>
            🧪 Comprehensive API Test Suite
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            marginBottom: '1.5rem'
          }}>
            <button
              onClick={() => testAPI('Health API', '/api/health')}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #4CAF50, #45a049)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🏥 Health API
            </button>

            <button
              onClick={() => testAPI('Social Connect', '/api/social/connect')}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #2196F3, #1976D2)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🔗 Social Connect
            </button>

            <button
              onClick={() => testAPI('Twitter Test', '/api/social-accounts/test-connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  platform: 'TWITTER',
                  accessToken: 'test_token_production',
                  accountId: 'test_account'
                })
              })}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #1DA1F2, #0d8bd9)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              🐦 Twitter Test
            </button>

            <button
              onClick={() => testAPI('Facebook Test', '/api/social-accounts/test-connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  platform: 'FACEBOOK',
                  accessToken: 'test_token_production',
                  accountId: 'test_account'
                })
              })}
              disabled={isLoading}
              style={{
                padding: '1rem',
                background: isLoading ? '#ccc' : 'linear-gradient(45deg, #4267B2, #365899)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontWeight: 'bold',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '1rem'
              }}
            >
              📘 Facebook Test
            </button>
          </div>

          <button
            onClick={runComprehensiveTest}
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '1.5rem',
              background: isLoading ? '#ccc' : 'linear-gradient(45deg, #E91E63, #C2185B)',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              fontWeight: 'bold',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontSize: '1.2rem'
            }}
          >
            {isLoading ? '⏳ Running Comprehensive Test...' : '🎯 Run Complete API Verification'}
          </button>
        </div>

        {/* Results */}
        {results.length > 0 && (
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '1rem',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', color: '#333', margin: 0 }}>
                📊 API Test Results ({results.length})
              </h2>
              <button
                onClick={() => setResults([])}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#f44336',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}
              >
                🗑️ Clear
              </button>
            </div>
            
            <div style={{
              maxHeight: '600px',
              overflowY: 'auto',
              background: '#f5f5f5',
              padding: '1rem',
              borderRadius: '0.5rem',
              fontFamily: 'monospace'
            }}>
              {results.map((result, index) => (
                <div
                  key={index}
                  style={{
                    padding: '1rem',
                    marginBottom: '0.5rem',
                    background: result.message.includes('✅') ? '#e8f5e8' : 
                               result.message.includes('❌') ? '#ffeaea' : 
                               result.message.includes('🔄') ? '#fff3cd' : '#fff',
                    borderRadius: '0.25rem',
                    fontSize: '0.9rem',
                    borderLeft: result.message.includes('✅') ? '4px solid #4CAF50' :
                               result.message.includes('❌') ? '4px solid #f44336' :
                               result.message.includes('🔄') ? '4px solid #ff9800' : '4px solid #2196F3'
                  }}
                >
                  <div style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>
                    [{result.timestamp}] {result.message}
                  </div>
                  {result.data && (
                    <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.5rem' }}>
                      <strong>Response:</strong> {JSON.stringify(result.data, null, 2)}
                    </div>
                  )}
                  {result.error && (
                    <div style={{ fontSize: '0.8rem', color: '#d32f2f', marginTop: '0.5rem' }}>
                      <strong>Error:</strong> {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status */}
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          background: 'rgba(255,255,255,0.9)',
          borderRadius: '0.5rem',
          textAlign: 'center'
        }}>
          <p style={{ margin: 0, color: '#666' }}>
            🎯 <strong>Priority 2 Status:</strong> API Integration with Production Credentials - Ready for Final Verification
          </p>
        </div>
      </div>
    </div>
  );
}
