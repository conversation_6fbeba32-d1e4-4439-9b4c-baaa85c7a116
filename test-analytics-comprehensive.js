#!/usr/bin/env node

/**
 * Comprehensive Analytics & Performance Dashboard Testing Suite
 * Tests all aspects of Task 1.9 implementation
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

console.log('🧪 Starting Comprehensive Analytics & Performance Dashboard Testing...\n');

// Test configuration
const BASE_URL = 'http://localhost:3001';
let testResults = [];
let passedTests = 0;
let totalTests = 0;
let criticalIssues = [];
let warnings = [];

function logTest(testName, passed, details = '', critical = false) {
  totalTests++;
  const status = passed ? '✅' : '❌';
  const result = `${status} ${testName}`;
  
  console.log(result);
  testResults.push(result);
  
  if (details) {
    console.log(`   ${details}`);
    testResults.push(`   ${details}`);
  }
  
  if (passed) {
    passedTests++;
  } else if (critical) {
    criticalIssues.push(testName);
  } else {
    warnings.push(testName);
  }
}

async function testAPI(endpoint, method = 'GET', body = null, expectedStatus = 200) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.text();
    
    return {
      status: response.status,
      data: data,
      success: response.status === expectedStatus,
      json: response.headers.get('content-type')?.includes('application/json') ? JSON.parse(data) : null
    };
  } catch (error) {
    return {
      status: 0,
      data: error.message,
      success: false,
      json: null
    };
  }
}

function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, filePath));
}

function fileContains(filePath, searchText) {
  if (!fileExists(filePath)) return false;
  const content = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
  return content.includes(searchText);
}

async function runComprehensiveAnalyticsTests() {
  console.log('📊 Phase 1: Analytics API Testing\n');
  
  // Test 1: Analytics Overview API Structure
  console.log('🔍 Testing Analytics Overview API...');
  const overviewTest = await testAPI('/api/analytics/overview?period=7d', 'GET', null, 401);
  logTest('Analytics Overview API - Auth Required', overviewTest.status === 401, `Status: ${overviewTest.status}`, true);
  
  // Test 2: Analytics Collection API Structure
  console.log('\n📈 Testing Analytics Collection API...');
  const collectionTest = await testAPI('/api/analytics/collect', 'POST', {
    post_id: 'test-id',
    platform: 'TWITTER',
    likes: 10
  }, 401);
  logTest('Analytics Collection API - Auth Required', collectionTest.status === 401, `Status: ${collectionTest.status}`, true);
  
  console.log('\n📄 Phase 2: Page Accessibility Testing\n');
  
  // Test 3: Analytics Dashboard Page
  const dashboardTest = await testAPI('/analytics');
  logTest('Analytics Dashboard Page Accessible', dashboardTest.success, `Status: ${dashboardTest.status}`, true);
  
  // Test 4: Analytics Test Page
  const testPageTest = await testAPI('/test-analytics');
  logTest('Analytics Test Page Accessible', testPageTest.success, `Status: ${testPageTest.status}`);
  
  console.log('\n🎨 Phase 3: Component Implementation Testing\n');
  
  // Test 5: Analytics Dashboard Page Implementation
  const dashboardPagePath = 'src/app/analytics/page.tsx';
  const dashboardExists = fileExists(dashboardPagePath);
  logTest('Analytics Dashboard Page Implementation', dashboardExists, '', true);
  
  if (dashboardExists) {
    logTest('Dashboard has overview cards', fileContains(dashboardPagePath, 'Overview Cards'));
    logTest('Dashboard has platform metrics', fileContains(dashboardPagePath, 'Platform Performance'));
    logTest('Dashboard has top posts', fileContains(dashboardPagePath, 'Top Performing Posts'));
    logTest('Dashboard has period selection', fileContains(dashboardPagePath, 'Select'));
    logTest('Dashboard has export functionality', fileContains(dashboardPagePath, 'Download'));
    logTest('Dashboard has Arabic RTL support', fileContains(dashboardPagePath, "direction: 'rtl'"));
  }
  
  console.log('\n🔗 Phase 4: API Implementation Testing\n');
  
  // Test 6: Analytics Overview API Implementation
  const overviewApiPath = 'src/app/api/analytics/overview/route.ts';
  const overviewApiExists = fileExists(overviewApiPath);
  logTest('Analytics Overview API Implementation', overviewApiExists, '', true);
  
  if (overviewApiExists) {
    logTest('Overview API has period filtering', fileContains(overviewApiPath, 'period'));
    logTest('Overview API has platform metrics', fileContains(overviewApiPath, 'platform_metrics'));
    logTest('Overview API has daily metrics', fileContains(overviewApiPath, 'daily_metrics'));
    logTest('Overview API has top posts', fileContains(overviewApiPath, 'top_posts'));
    logTest('Overview API has authentication', fileContains(overviewApiPath, 'auth.getUser'));
    logTest('Overview API has activity logging', fileContains(overviewApiPath, 'activities'));
  }
  
  // Test 7: Analytics Collection API Implementation
  const collectionApiPath = 'src/app/api/analytics/collect/route.ts';
  const collectionApiExists = fileExists(collectionApiPath);
  logTest('Analytics Collection API Implementation', collectionApiExists, '', true);
  
  if (collectionApiExists) {
    logTest('Collection API has validation', fileContains(collectionApiPath, 'zod') || fileContains(collectionApiPath, 'schema'));
    logTest('Collection API has platform support', fileContains(collectionApiPath, 'TWITTER') && fileContains(collectionApiPath, 'FACEBOOK'));
    logTest('Collection API has metrics tracking', fileContains(collectionApiPath, 'likes') && fileContains(collectionApiPath, 'engagement_rate'));
    logTest('Collection API has authentication', fileContains(collectionApiPath, 'auth.getUser'));
    logTest('Collection API has upsert logic', fileContains(collectionApiPath, 'update') && fileContains(collectionApiPath, 'insert'));
  }
  
  console.log('\n🗄️ Phase 5: Database Schema Testing\n');
  
  // Test 8: Database Migration
  const migrationPath = 'supabase/migrations/20240125000000_create_analytics_table.sql';
  const migrationExists = fileExists(migrationPath);
  logTest('Analytics Database Migration', migrationExists, '', true);
  
  if (migrationExists) {
    logTest('Migration creates analytics table', fileContains(migrationPath, 'CREATE TABLE') && fileContains(migrationPath, 'analytics'));
    logTest('Migration has proper constraints', fileContains(migrationPath, 'CHECK') && fileContains(migrationPath, 'REFERENCES'));
    logTest('Migration has indexes', fileContains(migrationPath, 'CREATE INDEX'));
    logTest('Migration has RLS policies', fileContains(migrationPath, 'ROW LEVEL SECURITY') && fileContains(migrationPath, 'CREATE POLICY'));
    logTest('Migration has sample data', fileContains(migrationPath, 'INSERT INTO analytics'));
    logTest('Migration has analytics view', fileContains(migrationPath, 'CREATE') && fileContains(migrationPath, 'VIEW'));
  }
  
  console.log('\n🔒 Phase 6: Security Testing\n');
  
  // Test 9: Authentication Requirements
  logTest('Overview API requires authentication', fileContains(overviewApiPath, 'auth.getUser') && fileContains(overviewApiPath, '401'));
  logTest('Collection API requires authentication', fileContains(collectionApiPath, 'auth.getUser') && fileContains(collectionApiPath, '401'));
  logTest('APIs have user isolation', fileContains(overviewApiPath, 'user_id') && fileContains(collectionApiPath, 'user_id'));
  
  console.log('\n📱 Phase 7: User Interface Testing\n');
  
  // Test 10: Dashboard UI Components
  if (dashboardExists) {
    logTest('Dashboard has loading states', fileContains(dashboardPagePath, 'isLoading') && fileContains(dashboardPagePath, 'Loading'));
    logTest('Dashboard has error handling', fileContains(dashboardPagePath, 'error') && fileContains(dashboardPagePath, 'toast'));
    logTest('Dashboard has responsive design', fileContains(dashboardPagePath, 'grid') && fileContains(dashboardPagePath, 'auto-fit'));
    logTest('Dashboard has interactive elements', fileContains(dashboardPagePath, 'onClick') && fileContains(dashboardPagePath, 'Button'));
    logTest('Dashboard has proper styling', fileContains(dashboardPagePath, 'background') && fileContains(dashboardPagePath, 'borderRadius'));
  }
  
  console.log('\n🧪 Phase 8: Test Suite Verification\n');
  
  // Test 11: Test Page Implementation
  const testPagePath = 'src/app/test-analytics/page.tsx';
  const testPageExists = fileExists(testPagePath);
  logTest('Analytics Test Page Implementation', testPageExists);
  
  if (testPageExists) {
    logTest('Test page has overview test', fileContains(testPagePath, 'testAnalyticsOverview'));
    logTest('Test page has collection test', fileContains(testPagePath, 'testAnalyticsCollection'));
    logTest('Test page has dashboard test', fileContains(testPagePath, 'testAnalyticsDashboard'));
    logTest('Test page has comprehensive test', fileContains(testPagePath, 'runFullAnalyticsTest') || fileContains(testPagePath, 'comprehensive'));
  }
  
  console.log('\n📊 Phase 9: Data Flow Testing\n');
  
  // Test 12: API Response Structure
  if (overviewApiExists) {
    logTest('Overview API returns structured data', 
      fileContains(overviewApiPath, 'overview') && 
      fileContains(overviewApiPath, 'platform_metrics') && 
      fileContains(overviewApiPath, 'daily_metrics'));
    
    logTest('Overview API calculates metrics', 
      fileContains(overviewApiPath, 'totalLikes') && 
      fileContains(overviewApiPath, 'avgEngagementRate'));
  }
  
  if (collectionApiExists) {
    logTest('Collection API validates input', 
      fileContains(collectionApiPath, 'schema') || fileContains(collectionApiPath, 'validation'));
    
    logTest('Collection API handles conflicts', 
      fileContains(collectionApiPath, 'existing') && 
      fileContains(collectionApiPath, 'update'));
  }
  
  console.log('\n🌐 Phase 10: Integration Testing\n');
  
  // Test 13: Navigation Integration
  const sidebarPath = 'src/components/layout/sidebar.tsx';
  const sidebarExists = fileExists(sidebarPath);
  logTest('Sidebar Navigation Exists', sidebarExists);
  
  if (sidebarExists) {
    logTest('Sidebar has analytics link', fileContains(sidebarPath, '/analytics'));
    logTest('Sidebar has analytics label', fileContains(sidebarPath, 'التحليلات') || fileContains(sidebarPath, 'Analytics'));
  }
  
  // Final Results
  console.log('\n' + '='.repeat(80));
  console.log('🎯 ANALYTICS & PERFORMANCE DASHBOARD TEST RESULTS');
  console.log('='.repeat(80));
  console.log(`📊 Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  console.log(`🚨 Critical Issues: ${criticalIssues.length}`);
  console.log(`⚠️ Warnings: ${warnings.length}`);
  
  if (criticalIssues.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES DETECTED:');
    criticalIssues.forEach(issue => console.log(`   • ${issue}`));
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️ WARNINGS:');
    warnings.forEach(warning => console.log(`   • ${warning}`));
  }
  
  const successRate = (passedTests / totalTests) * 100;
  
  if (successRate >= 95 && criticalIssues.length === 0) {
    console.log('\n🎉 ANALYTICS & PERFORMANCE DASHBOARD FULLY FUNCTIONAL!');
    console.log('✅ All critical functionality verified');
    console.log('✅ Analytics data collection system working');
    console.log('✅ Performance metrics overview implemented');
    console.log('✅ Platform-specific analytics functional');
    console.log('✅ Interactive dashboard interface ready');
    console.log('✅ Database schema and security configured');
    console.log('✅ API endpoints properly implemented');
    console.log('✅ User interface components working');
    console.log('✅ Arabic RTL support throughout');
    console.log('\n🚀 TASK 1.9 COMPLETED SUCCESSFULLY - READY FOR PRODUCTION!');
    return true;
  } else if (successRate >= 85 && criticalIssues.length === 0) {
    console.log('\n⚠️ ANALYTICS DASHBOARD MOSTLY FUNCTIONAL');
    console.log('✅ Core functionality working');
    console.log('⚠️ Some minor improvements recommended');
    console.log('\n📋 TASK 1.9 SUBSTANTIALLY COMPLETE - MINOR FIXES NEEDED');
    return false;
  } else {
    console.log('\n❌ ANALYTICS DASHBOARD NEEDS ATTENTION');
    console.log('❌ Critical issues detected that must be resolved');
    console.log('\n🔧 TASK 1.9 REQUIRES FIXES BEFORE PROCEEDING');
    return false;
  }
}

// Run the comprehensive test suite
runComprehensiveAnalyticsTests()
  .then(success => {
    console.log(`\n🏁 Analytics Testing completed. Ready for next task: ${success}`);
    
    console.log('\n📋 Detailed Test Results:');
    console.log('='.repeat(80));
    testResults.forEach(result => console.log(result));
    
    console.log('\n🔗 Test URLs for Manual Verification:');
    console.log('• Analytics Dashboard: http://localhost:3001/analytics');
    console.log('• Analytics Testing: http://localhost:3001/test-analytics');
    console.log('• Analytics Overview API: GET /api/analytics/overview');
    console.log('• Analytics Collection API: POST /api/analytics/collect');
    
    console.log('\n📁 Key Implementation Files:');
    console.log('• Analytics Dashboard: src/app/analytics/page.tsx');
    console.log('• Overview API: src/app/api/analytics/overview/route.ts');
    console.log('• Collection API: src/app/api/analytics/collect/route.ts');
    console.log('• Database Migration: supabase/migrations/20240125000000_create_analytics_table.sql');
    console.log('• Test Suite: src/app/test-analytics/page.tsx');
    
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n❌ Testing failed with error:', error);
    process.exit(1);
  });
