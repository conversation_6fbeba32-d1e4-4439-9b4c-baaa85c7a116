import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for member invitation
const inviteMemberSchema = z.object({
  email: z.string().email(),
  role: z.enum(['ADMIN', 'MANAGER', 'EDITOR', 'VIEWER']).default('VIEWER'),
  message: z.string().optional(),
});

// Validation schema for member role update
const updateMemberSchema = z.object({
  role: z.enum(['ADMIN', 'MANAGER', 'EDITOR', 'VIEWER']),
  title: z.string().optional(),
  department: z.string().optional(),
  notes: z.string().optional(),
});

// GET - Get workspace members
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching workspace members...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id: workspaceId } = await params;

    // Check if user has access to this workspace
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember) {
      return NextResponse.json(
        { error: 'Access denied to workspace' },
        { status: 403 }
      );
    }

    // Get workspace members with user details
    const { data: members, error: membersError } = await supabase
      .from('workspace_members')
      .select(`
        id,
        role,
        status,
        title,
        department,
        notes,
        invited_by,
        invited_at,
        joined_at,
        created_at,
        updated_at,
        user_id
      `)
      .eq('workspace_id', workspaceId)
      .order('created_at', { ascending: false });

    if (membersError) {
      console.error('Error fetching members:', membersError);
      return NextResponse.json(
        { error: 'Failed to fetch workspace members' },
        { status: 500 }
      );
    }

    // Get user profiles for members
    const userIds = members?.map(m => m.user_id) || [];
    const { data: profiles } = await supabase.auth.admin.listUsers();

    const userProfiles = profiles?.users?.reduce((acc, profile) => {
      acc[profile.id] = {
        email: profile.email,
        created_at: profile.created_at,
        last_sign_in_at: profile.last_sign_in_at,
        user_metadata: profile.user_metadata
      };
      return acc;
    }, {} as Record<string, any>) || {};

    // Combine member data with user profiles
    const membersWithProfiles = members?.map(member => ({
      ...member,
      user: userProfiles[member.user_id] || null
    })) || [];

    // Get pending invitations
    const { data: invitations, error: invitationsError } = await supabase
      .from('workspace_invitations')
      .select(`
        id,
        email,
        role,
        status,
        invited_by,
        message,
        expires_at,
        created_at
      `)
      .eq('workspace_id', workspaceId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false });

    if (invitationsError) {
      console.error('Error fetching invitations:', invitationsError);
    }

    console.log(`Found ${membersWithProfiles.length} members and ${invitations?.length || 0} pending invitations`);

    return NextResponse.json({
      success: true,
      members: membersWithProfiles,
      invitations: invitations || [],
      user_role: userMember.role
    }, { status: 200 });

  } catch (error) {
    console.error('Members fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Invite new member
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    console.log('Inviting new member...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id: workspaceId } = await params;

    // Check if user has permission to invite members
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember || !['OWNER', 'ADMIN', 'MANAGER'].includes(userMember.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to invite members' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = inviteMemberSchema.parse(body);

    console.log('Inviting member:', validatedData);

    // Check if user is already a member
    const { data: existingMember } = await supabase
      .from('workspace_members')
      .select('id')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .single();

    if (existingMember) {
      return NextResponse.json(
        { error: 'User is already a member of this workspace' },
        { status: 400 }
      );
    }

    // Check if there's already a pending invitation
    const { data: existingInvitation } = await supabase
      .from('workspace_invitations')
      .select('id')
      .eq('workspace_id', workspaceId)
      .eq('email', validatedData.email)
      .eq('status', 'pending')
      .single();

    if (existingInvitation) {
      return NextResponse.json(
        { error: 'Invitation already sent to this email' },
        { status: 400 }
      );
    }

    // Get workspace details for invitation
    const { data: workspace } = await supabase
      .from('workspaces')
      .select('name, limits')
      .eq('id', workspaceId)
      .single();

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check team member limits
    const { count: currentMembers } = await supabase
      .from('workspace_members')
      .select('*', { count: 'exact', head: true })
      .eq('workspace_id', workspaceId)
      .eq('status', 'active');

    const memberLimit = workspace.limits?.team_members || 1;
    if ((currentMembers || 0) >= memberLimit) {
      return NextResponse.json(
        { error: 'Team member limit reached for current plan' },
        { status: 400 }
      );
    }

    // Create invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('workspace_invitations')
      .insert({
        workspace_id: workspaceId,
        email: validatedData.email,
        role: validatedData.role,
        invited_by: user.id,
        message: validatedData.message,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      })
      .select()
      .single();

    if (invitationError) {
      console.error('Error creating invitation:', invitationError);
      return NextResponse.json(
        { error: 'Failed to create invitation' },
        { status: 500 }
      );
    }

    // TODO: Send invitation email
    // This would integrate with your email service (SendGrid, etc.)
    console.log('TODO: Send invitation email to:', validatedData.email);

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: workspaceId,
        action: 'MEMBER_INVITED',
        details: `Invited ${validatedData.email} as ${validatedData.role}`,
        created_at: new Date().toISOString(),
      });

    console.log('Member invitation created successfully:', invitation.id);

    return NextResponse.json({
      success: true,
      invitation,
      message: 'Invitation sent successfully'
    }, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid invitation data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Member invitation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update member role/details
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    console.log('Updating member...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id: workspaceId } = await params;

    // Get member ID from query params
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('member_id');

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    // Check if user has permission to update members
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember || !['OWNER', 'ADMIN'].includes(userMember.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update members' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateMemberSchema.parse(body);

    console.log('Updating member:', memberId, validatedData);

    // Update member
    const { data: member, error: updateError } = await supabase
      .from('workspace_members')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', memberId)
      .eq('workspace_id', workspaceId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating member:', updateError);
      return NextResponse.json(
        { error: 'Failed to update member' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: workspaceId,
        action: 'MEMBER_UPDATED',
        details: `Updated member role to ${validatedData.role}`,
        created_at: new Date().toISOString(),
      });

    console.log('Member updated successfully:', memberId);

    return NextResponse.json({
      success: true,
      member,
      message: 'Member updated successfully'
    }, { status: 200 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid member data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Member update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Remove member
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    console.log('Removing member...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id: workspaceId } = await params;

    // Get member ID from query params
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('member_id');

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    // Check if user has permission to remove members
    const { data: userMember } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!userMember || !['OWNER', 'ADMIN'].includes(userMember.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to remove members' },
        { status: 403 }
      );
    }

    // Get member details before removal
    const { data: memberToRemove } = await supabase
      .from('workspace_members')
      .select('user_id, role')
      .eq('id', memberId)
      .eq('workspace_id', workspaceId)
      .single();

    if (!memberToRemove) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Prevent removing the last owner
    if (memberToRemove.role === 'OWNER') {
      const { count: ownerCount } = await supabase
        .from('workspace_members')
        .select('*', { count: 'exact', head: true })
        .eq('workspace_id', workspaceId)
        .eq('role', 'OWNER')
        .eq('status', 'active');

      if ((ownerCount || 0) <= 1) {
        return NextResponse.json(
          { error: 'Cannot remove the last workspace owner' },
          { status: 400 }
        );
      }
    }

    // Remove member
    const { error: removeError } = await supabase
      .from('workspace_members')
      .delete()
      .eq('id', memberId)
      .eq('workspace_id', workspaceId);

    if (removeError) {
      console.error('Error removing member:', removeError);
      return NextResponse.json(
        { error: 'Failed to remove member' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: workspaceId,
        action: 'MEMBER_REMOVED',
        details: `Removed member from workspace`,
        created_at: new Date().toISOString(),
      });

    console.log('Member removed successfully:', memberId);

    return NextResponse.json({
      success: true,
      message: 'Member removed successfully'
    }, { status: 200 });

  } catch (error) {
    console.error('Member removal error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
