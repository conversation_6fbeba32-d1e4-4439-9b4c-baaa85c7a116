const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testDatabaseStructure() {
  console.log('🔍 Testing Database Structure...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing required environment variables');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  // Required tables for eWasl
  const requiredTables = [
    'users',
    'social_accounts', 
    'posts',
    'post_social_accounts',
    'activities',
    'subscriptions',
    'analytics_data',
    'content_templates',
    'oauth_states'
  ];

  console.log('📋 Testing Required Tables...\n');

  for (const table of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table '${table}': ${error.message}`);
      } else {
        console.log(`✅ Table '${table}': Accessible`);
        
        // Get row count for important tables
        if (['users', 'social_accounts', 'posts'].includes(table)) {
          const { count, error: countError } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          
          if (!countError) {
            console.log(`   📊 Rows: ${count}`);
          }
        }
      }
    } catch (error) {
      console.log(`❌ Table '${table}': ${error.message}`);
    }
  }

  // Test specific data integrity
  console.log('\n🔍 Testing Data Integrity...\n');

  try {
    // Check users table structure
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, name, role, created_at')
      .limit(3);

    if (usersError) {
      console.log(`❌ Users data test: ${usersError.message}`);
    } else {
      console.log(`✅ Users table structure: OK`);
      console.log(`   Sample data: ${users.length} users found`);
    }

    // Check social_accounts table
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('id, user_id, platform, account_id, is_active')
      .limit(3);

    if (accountsError) {
      console.log(`❌ Social accounts test: ${accountsError.message}`);
    } else {
      console.log(`✅ Social accounts table: OK`);
      console.log(`   Connected accounts: ${accounts.length}`);
    }

    // Check posts table
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('id, user_id, content, status, created_at')
      .limit(3);

    if (postsError) {
      console.log(`❌ Posts test: ${postsError.message}`);
    } else {
      console.log(`✅ Posts table: OK`);
      console.log(`   Total posts: ${posts.length}`);
    }

  } catch (error) {
    console.error(`❌ Data integrity test failed: ${error.message}`);
  }

  // Test RLS policies
  console.log('\n🔒 Testing RLS Policies...\n');

  try {
    // Test with anon client (should be restricted)
    const anonClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    const { data: anonData, error: anonError } = await anonClient
      .from('users')
      .select('*')
      .limit(1);

    if (anonError && anonError.message.includes('RLS')) {
      console.log('✅ RLS policies are active and working');
    } else if (anonError) {
      console.log(`⚠️  RLS test result: ${anonError.message}`);
    } else {
      console.log('⚠️  RLS might not be properly configured (anon access allowed)');
    }

  } catch (error) {
    console.log(`⚠️  RLS test failed: ${error.message}`);
  }

  console.log('\n🎯 Database Structure Test Complete!');
}

// Run the test
testDatabaseStructure().catch(console.error);
