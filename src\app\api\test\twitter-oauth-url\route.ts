import { NextRequest, NextResponse } from 'next/server';
import { TwitterEnhancedProvider } from '@/lib/social/postiz-integration/providers/twitter-enhanced';
import * as crypto from 'crypto';

/**
 * Test Twitter OAuth 2.0 URL generation with PKCE
 * POST /api/test/twitter-oauth-url
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Testing Twitter OAuth 2.0 URL generation with PKCE...');

    // Check environment variables
    const clientId = process.env.X_CLIENT_ID || process.env.TWITTER_CLIENT_ID;
    const clientSecret = process.env.X_CLIENT_SECRET || process.env.TWITTER_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      return NextResponse.json({
        success: false,
        error: 'Twitter/X OAuth credentials not configured',
        details: {
          clientId: clientId ? 'configured' : 'missing',
          clientSecret: clientSecret ? 'configured' : 'missing',
        }
      }, { status: 400 });
    }

    // Create Twitter provider instance
    const twitterProvider = new TwitterEnhancedProvider();

    // Generate OAuth URL with PKCE
    const authResult = await twitterProvider.generateAuthUrl();

    // Extract PKCE parameters from the URL for verification
    const url = new URL(authResult.url);
    const codeChallenge = url.searchParams.get('code_challenge');
    const codeChallengeMethod = url.searchParams.get('code_challenge_method');
    const scopes = url.searchParams.get('scope');
    const redirectUri = url.searchParams.get('redirect_uri');
    const responseType = url.searchParams.get('response_type');
    const state = url.searchParams.get('state');

    // Verify PKCE implementation
    const pkceVerification = {
      codeVerifierPresent: !!authResult.codeVerifier,
      codeChallengePresent: !!codeChallenge,
      codeChallengeMethod: codeChallengeMethod,
      codeChallengeMethodCorrect: codeChallengeMethod === 'S256',
      codeVerifierLength: authResult.codeVerifier?.length || 0,
      codeChallengeLength: codeChallenge?.length || 0,
    };

    // Verify code challenge generation
    let codeChallengeValid = false;
    if (authResult.codeVerifier && codeChallenge) {
      const expectedChallenge = crypto
        .createHash('sha256')
        .update(authResult.codeVerifier)
        .digest('base64url');
      codeChallengeValid = expectedChallenge === codeChallenge;
    }

    console.log('Twitter OAuth URL generation successful:', {
      state: authResult.state,
      codeVerifierLength: authResult.codeVerifier?.length,
      codeChallengeValid,
      scopes,
    });

    return NextResponse.json({
      success: true,
      authUrl: authResult.url,
      state: authResult.state,
      codeVerifier: authResult.codeVerifier,
      codeChallenge,
      redirectUri,
      scopes,
      pkceVerification,
      codeChallengeValid,
      urlComponents: {
        baseUrl: url.origin + url.pathname,
        clientId: url.searchParams.get('client_id'),
        responseType,
        redirectUri,
        scope: scopes,
        state,
        codeChallenge,
        codeChallengeMethod,
      },
      environmentCheck: {
        clientId: clientId ? `${clientId.substring(0, 10)}...` : 'missing',
        clientSecret: clientSecret ? 'configured' : 'missing',
        appUrl: process.env.NEXT_PUBLIC_APP_URL,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('Twitter OAuth URL generation failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

/**
 * Get Twitter OAuth configuration info
 * GET /api/test/twitter-oauth-url
 */
export async function GET() {
  try {
    const clientId = process.env.X_CLIENT_ID || process.env.TWITTER_CLIENT_ID;
    const clientSecret = process.env.X_CLIENT_SECRET || process.env.TWITTER_CLIENT_SECRET;
    const appUrl = process.env.NEXT_PUBLIC_APP_URL;

    return NextResponse.json({
      configured: !!(clientId && clientSecret),
      environment: {
        clientId: clientId ? `${clientId.substring(0, 10)}...` : 'missing',
        clientSecret: clientSecret ? 'configured' : 'missing',
        appUrl,
        callbackUrl: `${appUrl}/api/social/callback/twitter`,
      },
      scopes: [
        'tweet.read',
        'tweet.write', 
        'users.read',
        'media.upload',
        'offline.access',
      ],
      oauthEndpoint: 'https://twitter.com/i/oauth2/authorize',
      tokenEndpoint: 'https://api.twitter.com/2/oauth2/token',
      pkceRequired: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    return NextResponse.json({
      error: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
