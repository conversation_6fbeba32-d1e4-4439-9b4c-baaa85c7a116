import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for post publishing
const publishPostSchema = z.object({
  postId: z.string().min(1, 'Post ID is required'),
  platforms: z.array(z.string()).optional(),
  publishNow: z.boolean().default(false),
});


// POST - Publish post to social media platforms
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Publishing post to social media platforms...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log('Publish request:', { ...body, postId: body.postId });

    // Validate request body
    const validation = publishPostSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    const { postId, platforms, publishNow } = validation.data;

    // Get post details
    const { data: post, error: postError } = await supabase
      .from('posts')
      .select('*')
      .eq('id', postId)
      .eq('user_id', user.id)
      .single();

    if (postError || !post) {
      console.error('Post not found:', postError);
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // Get user's connected social accounts
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'CONNECTED');

    if (accountsError) {
      console.error('Error fetching social accounts:', accountsError);
      return NextResponse.json(
        { error: 'Failed to fetch social accounts' },
        { status: 500 }
      );
    }

    if (!socialAccounts || socialAccounts.length === 0) {
      return NextResponse.json(
        { error: 'No connected social accounts found' },
        { status: 400 }
      );
    }

    console.log(`Publishing post ${postId} to ${socialAccounts.length} platforms`);

    const publishResults = [];
    let successCount = 0;
    let failureCount = 0;

    // Publish to each connected platform
    for (const account of socialAccounts) {

      try {
        console.log(`Publishing to ${account.platform} account: ${account.account_name}`);

        let publishResult;

        switch (account.platform) {
          case 'TWITTER':
            publishResult = await publishToTwitter(post, account);
            break;
          case 'FACEBOOK':
            publishResult = await publishToFacebook(post, account);
            break;
          case 'LINKEDIN':
            publishResult = await publishToLinkedIn(post, account);
            break;
          case 'INSTAGRAM':
            publishResult = await publishToInstagram(post, account);
            break;
          default:
            publishResult = {
              success: false,
              error: `Platform ${account.platform} not supported yet`,
              platformPostId: null
            };
        }

        publishResults.push({
          platform: account.platform,
          accountName: account.account_name,
          success: publishResult.success,
          error: publishResult.error,
          platformPostId: publishResult.platformPostId
        });

        if (publishResult.success) {
          successCount++;
        } else {
          failureCount++;
        }

        // Log individual platform result
        await supabase
          .from('activities')
          .insert({
            user_id: user.id,
            action: publishResult.success ? 'POST_PUBLISHED' : 'POST_PUBLISH_FAILED',
            details: `${account.platform}: ${publishResult.success ? 'Success' : publishResult.error}`,
            created_at: new Date().toISOString(),
          });

      } catch (error: any) {
        console.error(`Error publishing to ${account.platform}:`, error);
        publishResults.push({
          platform: account.platform,
          accountName: account.account_name,
          success: false,
          error: error.message || 'Unknown error',
          platformPostId: null
        });
        failureCount++;
      }
    }

    // Update post status based on results
    let newStatus = 'PUBLISHED';
    if (successCount === 0) {
      newStatus = 'FAILED';
    } else if (failureCount > 0) {
      newStatus = 'PARTIALLY_PUBLISHED';
    }

    // Update post in database
    const { error: updateError } = await supabase
      .from('posts')
      .update({
        status: newStatus,
        published_at: successCount > 0 ? new Date().toISOString() : null,
        updated_at: new Date().toISOString(),
      })
      .eq('id', postId)
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error updating post status:', updateError);
    }

    // Log overall result
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'POST_PUBLISH_COMPLETED',
        details: `Published to ${successCount}/${socialAccounts.length} platforms`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      postId,
      results: publishResults,
      summary: {
        total: socialAccounts.length,
        successful: successCount,
        failed: failureCount,
        status: newStatus
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Post publishing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Platform-specific publishing functions
async function publishToTwitter(post: any, account: any) {
  try {
    console.log('Publishing to Twitter:', { content: post.content.substring(0, 50) + '...' });

    // Use Twitter Enhanced Provider
    const { TwitterEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/twitter-enhanced');
    const twitterProvider = new TwitterEnhancedProvider();

    // Prepare post data
    const twitterPost = {
      text: post.content,
      media: post.media_url ? [post.media_url] : undefined,
    };

    // Post to Twitter
    const result = await twitterService.postTweet(twitterPost);

    return {
      success: result.success,
      error: result.success ? null : 'Failed to post to Twitter',
      platformPostId: result.postId || null
    };
  } catch (error: any) {
    console.error('Twitter publishing error:', error);
    return {
      success: false,
      error: error.message || 'Twitter publishing failed',
      platformPostId: null
    };
  }
}

async function publishToFacebook(post: any, account: any) {
  try {
    console.log('Publishing to Facebook:', { content: post.content.substring(0, 50) + '...' });

    // Use Facebook Enhanced Provider
    const { FacebookEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/facebook-enhanced');
    const facebookProvider = new FacebookEnhancedProvider();

    // Prepare post data
    const facebookPost = {
      message: post.content,
      media: post.media_url ? [post.media_url] : undefined,
      link: undefined, // Can be added later for link posts
    };

    // Post to Facebook page (assuming account has page access token)
    const result = await facebookService.postToFacebook(
      account.account_id, // Page ID
      account.access_token, // Page access token
      facebookPost
    );

    return {
      success: result.success,
      error: result.success ? null : result.error,
      platformPostId: result.postId || null
    };
  } catch (error: any) {
    console.error('Facebook publishing error:', error);
    return {
      success: false,
      error: error.message || 'Facebook publishing failed',
      platformPostId: null
    };
  }
}

async function publishToLinkedIn(post: any, account: any) {
  try {
    console.log('Publishing to LinkedIn:', { content: post.content.substring(0, 50) + '...' });

    // Use LinkedIn Enhanced Provider
    const { LinkedInEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/linkedin-enhanced');
    const linkedinProvider = new LinkedInEnhancedProvider();

    // Prepare post data
    const linkedinPost = {
      text: post.content,
      media: post.media_url ? [post.media_url] : undefined,
      link: undefined, // Can be added later for link posts
    };

    // Post to LinkedIn (using account ID as author ID)
    const result = await linkedinService.postToLinkedIn(
      account.access_token,
      linkedinPost,
      account.account_id // LinkedIn person/organization ID
    );

    return {
      success: result.success,
      error: result.success ? null : result.error,
      platformPostId: result.postId || null
    };
  } catch (error: any) {
    console.error('LinkedIn publishing error:', error);
    return {
      success: false,
      error: error.message || 'LinkedIn publishing failed',
      platformPostId: null
    };
  }
}

async function publishToInstagram(post: any, account: any) {
  try {
    console.log('Publishing to Instagram:', { content: post.content.substring(0, 50) + '...' });

    // Use Instagram Enhanced Provider (uses Facebook Graph API)
    const { InstagramEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/instagram-enhanced');

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
    const instagramProvider = new InstagramEnhancedProvider();

    // Instagram requires media for posts
    if (!post.media_url) {
      return {
        success: false,
        error: 'Instagram posts require media (image or video)',
        platformPostId: null
      };
    }

    // Prepare Instagram post data
    const instagramPost = {
      caption: post.content,
      media_type: 'IMAGE' as const, // Default to image, could be enhanced to detect video
      image_url: post.media_url,
      video_url: undefined,
    };

    // Post to Instagram (using account ID as Instagram business account ID)
    const result = await facebookService.postToInstagram(
      account.account_id, // Instagram Business Account ID
      account.access_token, // Page access token
      instagramPost
    );

    return {
      success: result.success,
      error: result.success ? null : result.error,
      platformPostId: result.postId || null
    };
  } catch (error: any) {
    console.error('Instagram publishing error:', error);
    return {
      success: false,
      error: error.message || 'Instagram publishing failed',
      platformPostId: null
    };
  }
}
