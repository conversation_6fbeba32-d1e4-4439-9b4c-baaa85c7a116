import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Comprehensive service role client test
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Testing service role client implementation...');

    const results = {
      timestamp: new Date().toISOString(),
      environment: {
        hasServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        serviceRoleKeyLength: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0,
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || 'not set',
        nodeEnv: process.env.NODE_ENV
      },
      tests: {} as any
    };

    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';

    // Test 1: Service Role Client Creation
    try {
      console.log('Test 1: Creating service role client...');
      const supabase = createServiceRoleClient();
      results.tests.clientCreation = {
        success: true,
        message: 'Service role client created successfully'
      };
    } catch (error: any) {
      results.tests.clientCreation = {
        success: false,
        error: error.message
      };
      return NextResponse.json(results, { status: 500 });
    }

    // Test 2: Basic Database Connection
    try {
      console.log('Test 2: Testing basic database connection...');
      const supabase = createServiceRoleClient();
      const { data, error } = await supabase
        .from('social_accounts')
        .select('count(*)', { count: 'exact' });

      results.tests.databaseConnection = {
        success: !error,
        error: error?.message,
        totalAccounts: data?.[0]?.count || 0
      };
    } catch (error: any) {
      results.tests.databaseConnection = {
        success: false,
        error: error.message
      };
    }

    // Test 3: Demo User Accounts Query
    try {
      console.log('Test 3: Querying Demo User accounts...');
      const supabase = createServiceRoleClient();
      const { data: accounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId);

      results.tests.demoUserQuery = {
        success: !error,
        error: error?.message,
        accountsFound: accounts?.length || 0,
        accounts: accounts?.map(acc => ({
          id: acc.id,
          platform: acc.platform,
          account_name: acc.account_name,
          created_at: acc.created_at
        })) || []
      };

      console.log(`✅ Found ${accounts?.length || 0} accounts for Demo User`);
    } catch (error: any) {
      results.tests.demoUserQuery = {
        success: false,
        error: error.message
      };
    }

    // Test 4: LinkedIn Specific Query
    try {
      console.log('Test 4: Querying LinkedIn accounts specifically...');
      const supabase = createServiceRoleClient();
      const { data: linkedinAccounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'LINKEDIN');

      results.tests.linkedinQuery = {
        success: !error,
        error: error?.message,
        linkedinAccountsFound: linkedinAccounts?.length || 0,
        linkedinAccounts: linkedinAccounts?.map(acc => ({
          id: acc.id,
          account_name: acc.account_name,
          account_id: acc.account_id,
          expires_at: acc.expires_at,
          created_at: acc.created_at
        })) || []
      };

      console.log(`✅ Found ${linkedinAccounts?.length || 0} LinkedIn accounts`);
    } catch (error: any) {
      results.tests.linkedinQuery = {
        success: false,
        error: error.message
      };
    }

    // Test 5: LinkedIn API Connection Test
    try {
      console.log('Test 5: Testing LinkedIn API connections...');
      const supabase = createServiceRoleClient();

      const { data: linkedinAccounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'LINKEDIN');

      if (error || !linkedinAccounts?.length) {
        results.tests.linkedinApiTest = {
          success: false,
          error: 'No LinkedIn accounts found for testing'
        };
      } else {
        const connectionTests = [];

        for (const account of linkedinAccounts) {
          try {
            // Test LinkedIn API connection
            const response = await fetch(`https://api.linkedin.com/v2/userinfo`, {
              headers: {
                'Authorization': `Bearer ${account.access_token}`,
                'Accept': 'application/json',
              },
            });

            if (response.ok) {
              const userData = await response.json();
              connectionTests.push({
                accountName: account.account_name,
                success: true,
                userInfo: {
                  name: userData.name,
                  email: userData.email
                },
                readyForPosting: true
              });
            } else {
              const errorText = await response.text();
              connectionTests.push({
                accountName: account.account_name,
                success: false,
                error: `API error: ${response.status} - ${errorText.substring(0, 100)}`,
                readyForPosting: false
              });
            }
          } catch (error: any) {
            connectionTests.push({
              accountName: account.account_name,
              success: false,
              error: error.message,
              readyForPosting: false
            });
          }
        }

        results.tests.linkedinApiTest = {
          success: connectionTests.some(test => test.success),
          totalAccounts: linkedinAccounts.length,
          successfulConnections: connectionTests.filter(test => test.success).length,
          readyForPosting: connectionTests.filter(test => test.readyForPosting).length,
          connectionTests
        };
      }

      console.log('✅ LinkedIn API test completed');
    } catch (error: any) {
      results.tests.linkedinApiTest = {
        success: false,
        error: error.message
      };
    }

    // Test 6: Facebook API Connection Test
    try {
      console.log('Test 6: Testing Facebook API connections...');
      const supabase = createServiceRoleClient();

      const { data: facebookAccounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'FACEBOOK');

      if (error || !facebookAccounts?.length) {
        results.tests.facebookApiTest = {
          success: false,
          error: 'No Facebook accounts found for testing'
        };
      } else {
        const connectionTests = [];

        for (const account of facebookAccounts) {
          try {
            // Test Facebook Graph API connection
            const response = await fetch(`https://graph.facebook.com/v19.0/me?fields=id,name,email`, {
              headers: {
                'Authorization': `Bearer ${account.access_token}`,
                'Accept': 'application/json',
              },
            });

            if (response.ok) {
              const userData = await response.json();
              connectionTests.push({
                accountName: account.account_name,
                success: true,
                userInfo: {
                  id: userData.id,
                  name: userData.name,
                  email: userData.email
                },
                readyForPosting: true
              });
            } else {
              const errorText = await response.text();
              connectionTests.push({
                accountName: account.account_name,
                success: false,
                error: `API error: ${response.status} - ${errorText.substring(0, 100)}`,
                readyForPosting: false
              });
            }
          } catch (error: any) {
            connectionTests.push({
              accountName: account.account_name,
              success: false,
              error: error.message,
              readyForPosting: false
            });
          }
        }

        results.tests.facebookApiTest = {
          success: connectionTests.some(test => test.success),
          totalAccounts: facebookAccounts.length,
          successfulConnections: connectionTests.filter(test => test.success).length,
          readyForPosting: connectionTests.filter(test => test.readyForPosting).length,
          connectionTests
        };
      }

      console.log('✅ Facebook API test completed');
    } catch (error: any) {
      results.tests.facebookApiTest = {
        success: false,
        error: error.message
      };
    }

    // Test 7: Facebook Account Cleanup (if requested)
    const searchParams = request.nextUrl.searchParams;
    const cleanupFacebook = searchParams.get('cleanup_facebook') === 'true';

    if (cleanupFacebook) {
      try {
        console.log('Test 7: Facebook account cleanup requested...');
        const supabase = createServiceRoleClient();

        // Get corrupted Facebook accounts
        const { data: facebookAccounts, error } = await supabase
          .from('social_accounts')
          .select('*')
          .eq('user_id', demoUserId)
          .eq('platform', 'FACEBOOK');

        if (!error && facebookAccounts?.length > 0) {
          const knownCorruptedAccountIds = [
            'test-fb-**********.347804', // "Test Facebook Account"
            'ewasl_page' // "@eWasl Page"
          ];

          const corruptedAccounts = facebookAccounts.filter(acc =>
            knownCorruptedAccountIds.includes(acc.account_id)
          );

          if (corruptedAccounts.length > 0) {
            console.log(`Removing ${corruptedAccounts.length} corrupted Facebook accounts...`);

            const removalResults = [];
            for (const account of corruptedAccounts) {
              try {
                const { error: deleteError } = await supabase
                  .from('social_accounts')
                  .delete()
                  .eq('id', account.id);

                if (!deleteError) {
                  removalResults.push({
                    accountName: account.account_name,
                    success: true
                  });
                  console.log(`✅ Removed: ${account.account_name}`);
                } else {
                  removalResults.push({
                    accountName: account.account_name,
                    success: false,
                    error: deleteError.message
                  });
                }
              } catch (removeError: any) {
                removalResults.push({
                  accountName: account.account_name,
                  success: false,
                  error: removeError.message
                });
              }
            }

            results.tests.facebookCleanup = {
              success: removalResults.every(r => r.success),
              corruptedAccountsFound: corruptedAccounts.length,
              successfulRemovals: removalResults.filter(r => r.success).length,
              failedRemovals: removalResults.filter(r => !r.success).length,
              removalResults
            };
          } else {
            results.tests.facebookCleanup = {
              success: true,
              message: 'No corrupted Facebook accounts found'
            };
          }
        } else {
          results.tests.facebookCleanup = {
            success: true,
            message: 'No Facebook accounts found'
          };
        }

        console.log('✅ Facebook cleanup test completed');
      } catch (error: any) {
        results.tests.facebookCleanup = {
          success: false,
          error: error.message
        };
      }
    }

    // Overall assessment
    const allTestsPassed = Object.values(results.tests).every((test: any) => test.success);
    const linkedinTestsPassed = results.tests.demoUserQuery?.success &&
                               results.tests.linkedinQuery?.success &&
                               results.tests.linkedinApiTest?.success;
    const facebookTestsPassed = results.tests.demoUserQuery?.success &&
                               results.tests.facebookApiTest?.success;
    const socialIntegrationsWorking = linkedinTestsPassed || facebookTestsPassed;

    let statusMessage = '❌ Critical social media tests failed';
    if (allTestsPassed) {
      statusMessage = '✅ All service role client tests passed!';
    } else if (linkedinTestsPassed && facebookTestsPassed) {
      statusMessage = '🎉 LinkedIn and Facebook integrations are working!';
    } else if (linkedinTestsPassed) {
      statusMessage = '⚠️ Some tests failed but LinkedIn integration is working!';
    } else if (facebookTestsPassed) {
      statusMessage = '⚠️ Some tests failed but Facebook integration is working!';
    }

    return NextResponse.json({
      success: allTestsPassed,
      message: statusMessage,
      results,
      summary: {
        totalTests: Object.keys(results.tests).length,
        passedTests: Object.values(results.tests).filter((test: any) => test.success).length,
        serviceRoleWorking: allTestsPassed,
        linkedinIntegrationWorking: linkedinTestsPassed,
        facebookIntegrationWorking: facebookTestsPassed,
        socialIntegrationsWorking: socialIntegrationsWorking,
        linkedinAccountsReady: results.tests.linkedinApiTest?.readyForPosting || 0,
        facebookAccountsReady: results.tests.facebookApiTest?.readyForPosting || 0,
        facebookCleanupExecuted: !!results.tests.facebookCleanup,
        facebookAccountsRemoved: results.tests.facebookCleanup?.successfulRemovals || 0
      }
    });

  } catch (error) {
    console.error('❌ Service role test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Service role client test failed completely'
    }, { status: 500 });
  }
}
