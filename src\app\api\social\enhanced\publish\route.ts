/**
 * Enhanced Social Media Publishing API
 * Handles posting with improved error handling and retry logic
 */

import { NextRequest, NextResponse } from 'next/server';
import { integrationManager } from '@/lib/social/postiz-integration/integration-manager';
import { supabaseServiceRole } from '@/lib/supabase/service-role';
import { PostDetails } from '@/lib/social/postiz-integration/interfaces';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    const { 
      integrationId, 
      content, 
      mediaUrls = [], 
      scheduledAt,
      platformSettings = {} 
    } = await request.json();

    if (!integrationId || !content) {
      return NextResponse.json(
        { error: 'Integration ID and content are required' },
        { status: 400 }
      );
    }

    // Validate integration exists and is active
    const supabase = supabaseServiceRole;
    const { data: integration, error: integrationError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', integrationId)
      .eq('is_active', true)
      .single();

    if (integrationError || !integration) {
      return NextResponse.json(
        { error: 'Integration not found or inactive' },
        { status: 404 }
      );
    }

    // Prepare post details
    const postDetails: PostDetails[] = [{
      id: `post_${Date.now()}`,
      message: content,
      settings: platformSettings,
      media: mediaUrls.map((url: string, index: number) => ({
        type: url.includes('.mp4') || url.includes('.mov') ? 'video' as const : 'image' as const,
        url,
        path: `media_${index}`,
      })),
    }];

    // If scheduled, save to database for later processing
    if (scheduledAt) {
      const { data: scheduledPost, error: scheduleError } = await supabase
        .from('scheduled_posts')
        .insert({
          user_id: integration.user_id,
          integration_id: integrationId,
          content,
          media_urls: mediaUrls,
          scheduled_at: scheduledAt,
          platform_settings: platformSettings,
          status: 'scheduled',
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (scheduleError) {
        throw scheduleError;
      }

      return NextResponse.json({
        success: true,
        scheduled: true,
        postId: scheduledPost.id,
        scheduledAt,
        message: 'Post scheduled successfully',
      });
    }

    // Publish immediately
    let results;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        results = await integrationManager.post(integrationId, postDetails);
        break; // Success, exit retry loop
      } catch (error) {
        retryCount++;
        console.error(`[Enhanced Publish API] Attempt ${retryCount} failed:`, error);
        
        if (retryCount >= maxRetries) {
          throw error; // Final attempt failed
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }

    if (!results || results.length === 0) {
      throw new Error('No results returned from publishing');
    }

    // Save post record to database
    const { data: savedPost, error: saveError } = await supabase
      .from('posts')
      .insert({
        user_id: integration.user_id,
        integration_id: integrationId,
        content,
        media_urls: mediaUrls,
        platform_post_id: results[0].postId,
        platform_url: results[0].releaseURL,
        status: results[0].status === 'posted' ? 'published' : 'failed',
        platform_settings: platformSettings,
        published_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (saveError) {
      console.error('[Enhanced Publish API] Failed to save post record:', saveError);
      // Don't fail the request if post was successful but saving failed
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: integration.user_id,
        platform: integration.platform,
        action: 'post_published',
        details: {
          post_id: savedPost?.id,
          platform_post_id: results[0].postId,
          platform_url: results[0].releaseURL,
          content_length: content.length,
          media_count: mediaUrls.length,
          retry_count: retryCount,
        },
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      results,
      post: savedPost ? {
        id: savedPost.id,
        platformPostId: results[0].postId,
        platformUrl: results[0].releaseURL,
        status: results[0].status,
        publishedAt: savedPost.published_at,
      } : null,
      retryCount,
    });
  } catch (error) {
    console.error('[Enhanced Publish API] Error:', error);
    
    // Try to log the error
    try {
      
      await supabase
        .from('activity_logs')
        .insert({
          user_id: 'system', // or get from request context
          platform: 'system',
          action: 'publish_error',
          details: {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            timestamp: new Date().toISOString(),
          },
          created_at: new Date().toISOString(),
        });
    } catch (logError) {
      console.error('[Enhanced Publish API] Failed to log error:', logError);
    }

    return NextResponse.json(
      { 
        error: 'Failed to publish post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    const { searchParams } = new URL(request.url);
    const integrationId = searchParams.get('integrationId');

    if (!integrationId) {
      return NextResponse.json(
        { error: 'Integration ID is required' },
        { status: 400 }
      );
    }

    // Test connection
    const connectionTest = await integrationManager.testConnection(integrationId);

    return NextResponse.json({
      success: connectionTest.success,
      ...connectionTest,
    });
  } catch (error) {
    console.error('[Enhanced Publish API] Connection test error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to test connection',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
