/**
 * Social Media Configuration Validator
 * Validates environment variables and OAuth configurations for all platforms
 */

export interface ConfigValidationResult {
  platform: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  requiredVars: string[];
  missingVars: string[];
}

export interface OverallValidationResult {
  isValid: boolean;
  platforms: ConfigValidationResult[];
  summary: {
    total: number;
    valid: number;
    invalid: number;
    warnings: number;
  };
}

export class SocialMediaConfigValidator {
  private readonly platformConfigs = {
    twitter: {
      name: 'Twitter/X',
      requiredVars: ['TWITTER_CLIENT_ID', 'TWITTER_CLIENT_SECRET'],
      alternativeVars: ['X_CLIENT_ID', 'X_CLIENT_SECRET'],
      apiVersion: 'v2',
      oauthType: 'OAuth 2.0 with PKCE'
    },
    facebook: {
      name: 'Facebook',
      requiredVars: ['FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET'],
      apiVersion: 'v19.0',
      oauthType: 'OAuth 2.0'
    },
    instagram: {
      name: 'Instagram',
      requiredVars: ['FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET'], // Uses Facebook OAuth
      apiVersion: 'Graph API v19.0',
      oauthType: 'OAuth 2.0 (via Facebook)'
    },
    linkedin: {
      name: 'LinkedIn',
      requiredVars: ['LINKEDIN_CLIENT_ID', 'LINKEDIN_CLIENT_SECRET'],
      apiVersion: 'v2',
      oauthType: 'OAuth 2.0'
    },
    tiktok: {
      name: 'TikTok',
      requiredVars: ['TIKTOK_CLIENT_ID', 'TIKTOK_CLIENT_SECRET'],
      apiVersion: 'v1',
      oauthType: 'OAuth 2.0',
      optional: true
    },
    snapchat: {
      name: 'Snapchat',
      requiredVars: ['SNAPCHAT_CLIENT_ID', 'SNAPCHAT_CLIENT_SECRET'],
      apiVersion: 'v1',
      oauthType: 'OAuth 2.0',
      optional: true
    }
  };

  /**
   * Validate all social media platform configurations
   */
  validateAllPlatforms(): OverallValidationResult {
    const results: ConfigValidationResult[] = [];

    for (const [platform, config] of Object.entries(this.platformConfigs)) {
      const result = this.validatePlatform(platform, config);
      results.push(result);
    }

    const validCount = results.filter(r => r.isValid).length;
    const warningCount = results.reduce((sum, r) => sum + r.warnings.length, 0);

    return {
      isValid: results.filter(r => !r.platform.includes('optional')).every(r => r.isValid),
      platforms: results,
      summary: {
        total: results.length,
        valid: validCount,
        invalid: results.length - validCount,
        warnings: warningCount
      }
    };
  }

  /**
   * Validate a specific platform configuration
   */
  validatePlatform(platform: string, config: any): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingVars: string[] = [];

    // Check required environment variables
    for (const varName of config.requiredVars) {
      const value = process.env[varName];
      if (!value || value.trim() === '') {
        missingVars.push(varName);
        errors.push(`Missing required environment variable: ${varName}`);
      }
    }

    // Check alternative variables (for Twitter/X)
    if (config.alternativeVars && missingVars.length > 0) {
      const hasAlternatives = config.alternativeVars.every((varName: string) => {
        const value = process.env[varName];
        return value && value.trim() !== '';
      });

      if (hasAlternatives) {
        // Clear errors if alternatives are present
        errors.length = 0;
        missingVars.length = 0;
        warnings.push(`Using alternative environment variables: ${config.alternativeVars.join(', ')}`);
      }
    }

    // Platform-specific validations
    switch (platform) {
      case 'twitter':
        this.validateTwitterConfig(errors, warnings);
        break;
      case 'facebook':
        this.validateFacebookConfig(errors, warnings);
        break;
      case 'instagram':
        this.validateInstagramConfig(errors, warnings);
        break;
      case 'linkedin':
        this.validateLinkedInConfig(errors, warnings);
        break;
    }

    // Check callback URL configuration
    this.validateCallbackUrls(platform, errors, warnings);

    const isValid = errors.length === 0 && (config.optional || missingVars.length === 0);

    return {
      platform: `${config.name}${config.optional ? ' (Optional)' : ''}`,
      isValid,
      errors,
      warnings,
      requiredVars: config.requiredVars,
      missingVars
    };
  }

  /**
   * Validate Twitter-specific configuration
   */
  private validateTwitterConfig(errors: string[], warnings: string[]): void {
    const clientId = process.env.TWITTER_CLIENT_ID || process.env.X_CLIENT_ID;
    
    if (clientId) {
      // Twitter Client IDs should be alphanumeric
      if (!/^[a-zA-Z0-9_-]+$/.test(clientId)) {
        warnings.push('Twitter Client ID format may be invalid');
      }
    }

    // Check for OAuth 2.0 migration
    if (process.env.TWITTER_API_KEY || process.env.TWITTER_API_SECRET) {
      warnings.push('Detected legacy Twitter API keys. Ensure OAuth 2.0 credentials are configured.');
    }
  }

  /**
   * Validate Facebook-specific configuration
   */
  private validateFacebookConfig(errors: string[], warnings: string[]): void {
    const appId = process.env.FACEBOOK_APP_ID;
    
    if (appId) {
      // Facebook App IDs should be numeric
      if (!/^\d+$/.test(appId)) {
        errors.push('Facebook App ID should be numeric');
      }
    }

    // Check for app review requirements
    warnings.push('Facebook apps require review for production use with advanced permissions');
  }

  /**
   * Validate Instagram-specific configuration
   */
  private validateInstagramConfig(errors: string[], warnings: string[]): void {
    // Instagram uses Facebook OAuth, so check Facebook config
    const appId = process.env.FACEBOOK_APP_ID;
    
    if (!appId) {
      errors.push('Instagram requires Facebook App ID (uses Facebook OAuth)');
    }

    warnings.push('Instagram requires business account connected to Facebook page');
    warnings.push('Instagram Graph API permissions may require Facebook app review');
  }

  /**
   * Validate LinkedIn-specific configuration
   */
  private validateLinkedInConfig(errors: string[], warnings: string[]): void {
    const clientId = process.env.LINKEDIN_CLIENT_ID;
    
    if (clientId) {
      // LinkedIn Client IDs have specific format
      if (clientId.length < 10) {
        warnings.push('LinkedIn Client ID seems too short');
      }
    }

    warnings.push('LinkedIn Marketing Developer Platform may be required for advanced features');
  }

  /**
   * Validate callback URL configuration
   */
  private validateCallbackUrls(platform: string, errors: string[], warnings: string[]): void {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXTAUTH_URL;
    
    if (!baseUrl) {
      errors.push('NEXT_PUBLIC_APP_URL or NEXTAUTH_URL must be configured for OAuth callbacks');
      return;
    }

    // Check if using localhost in production
    if (baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1')) {
      if (process.env.NODE_ENV === 'production') {
        errors.push('Cannot use localhost URLs in production environment');
      } else {
        warnings.push('Using localhost URL - ensure this matches your OAuth app configuration');
      }
    }

    // Check HTTPS requirement
    if (!baseUrl.startsWith('https://') && process.env.NODE_ENV === 'production') {
      errors.push('Production callback URLs must use HTTPS');
    }

    // Platform-specific callback URL checks
    const expectedCallbacks = this.getExpectedCallbackUrls(platform, baseUrl);
    if (expectedCallbacks.length > 0) {
      warnings.push(`Expected callback URLs: ${expectedCallbacks.join(', ')}`);
    }
  }

  /**
   * Get expected callback URLs for a platform
   */
  private getExpectedCallbackUrls(platform: string, baseUrl: string): string[] {
    const callbacks: { [key: string]: string[] } = {
      twitter: [`${baseUrl}/api/social/callback/twitter`],
      facebook: [`${baseUrl}/api/social/callback/facebook`],
      instagram: [`${baseUrl}/api/social/callback/instagram`],
      linkedin: [`${baseUrl}/api/social/callback/linkedin`],
      tiktok: [`${baseUrl}/api/social/callback/tiktok`],
      snapchat: [`${baseUrl}/api/social/callback/snapchat`]
    };

    return callbacks[platform] || [];
  }

  /**
   * Generate configuration report
   */
  generateReport(): string {
    const validation = this.validateAllPlatforms();
    let report = '🔧 Social Media Configuration Report\n';
    report += '=' .repeat(50) + '\n\n';

    // Summary
    report += `📊 Summary:\n`;
    report += `  Total Platforms: ${validation.summary.total}\n`;
    report += `  ✅ Valid: ${validation.summary.valid}\n`;
    report += `  ❌ Invalid: ${validation.summary.invalid}\n`;
    report += `  ⚠️  Warnings: ${validation.summary.warnings}\n\n`;

    // Platform details
    for (const platform of validation.platforms) {
      const status = platform.isValid ? '✅' : '❌';
      report += `${status} ${platform.platform}\n`;
      
      if (platform.errors.length > 0) {
        report += `  Errors:\n`;
        platform.errors.forEach(error => report += `    - ${error}\n`);
      }
      
      if (platform.warnings.length > 0) {
        report += `  Warnings:\n`;
        platform.warnings.forEach(warning => report += `    - ${warning}\n`);
      }
      
      if (platform.missingVars.length > 0) {
        report += `  Missing Variables:\n`;
        platform.missingVars.forEach(varName => report += `    - ${varName}\n`);
      }
      
      report += '\n';
    }

    // Recommendations
    report += '💡 Recommendations:\n';
    if (!validation.isValid) {
      report += '  - Configure missing environment variables\n';
      report += '  - Check OAuth app settings in respective developer portals\n';
      report += '  - Ensure callback URLs match your app configuration\n';
    } else {
      report += '  - All critical configurations are valid\n';
      report += '  - Review warnings for optimization opportunities\n';
    }

    return report;
  }

  /**
   * Check if a specific platform is properly configured
   */
  isPlatformConfigured(platform: string): boolean {
    const config = this.platformConfigs[platform as keyof typeof this.platformConfigs];
    if (!config) return false;

    const result = this.validatePlatform(platform, config);
    return result.isValid;
  }

  /**
   * Get list of configured platforms
   */
  getConfiguredPlatforms(): string[] {
    return Object.keys(this.platformConfigs).filter(platform => 
      this.isPlatformConfigured(platform)
    );
  }
}

export default SocialMediaConfigValidator;
