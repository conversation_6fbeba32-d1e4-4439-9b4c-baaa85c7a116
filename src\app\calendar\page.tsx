'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { ContentCalendar } from '@/components/posts/content-calendar';
import { Button } from '@/components/ui/button';
import { Plus, Calendar, BarChart3, Users } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

export default function CalendarPage() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalPosts: 0,
    scheduledPosts: 0,
    publishedPosts: 0,
    connectedAccounts: 0
  });

  useEffect(() => {
    checkAuth();
    loadStats();
  }, []);

  const checkAuth = async () => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      toast.error('يرجى تسجيل الدخول أولاً');
      window.location.href = '/auth/signin';
      return;
    }
    
    setUser(user);
    setIsLoading(false);
  };

  const loadStats = async () => {
    try {
      const supabase = createClient();
      
      // Load posts stats
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('status');

      if (postsError) {
        console.error('Error loading posts stats:', postsError);
      } else {
        const totalPosts = posts?.length || 0;
        const scheduledPosts = posts?.filter(p => p.status === 'SCHEDULED').length || 0;
        const publishedPosts = posts?.filter(p => p.status === 'PUBLISHED').length || 0;
        
        // Load connected accounts
        const { data: accounts, error: accountsError } = await supabase
          .from('social_accounts')
          .select('id')
          .eq('status', 'CONNECTED');

        if (accountsError) {
          console.error('Error loading accounts stats:', accountsError);
        }

        setStats({
          totalPosts,
          scheduledPosts,
          publishedPosts,
          connectedAccounts: accounts?.length || 0
        });
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#6b7280' }}>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              margin: '0 0 0.5rem 0'
            }}>
              📅 تقويم المحتوى
            </h1>
            <p style={{
              color: '#6b7280',
              fontSize: '1rem',
              margin: 0
            }}>
              خطط وجدول منشوراتك على وسائل التواصل الاجتماعي
            </p>
          </div>
          <Link href="/posts/new">
            <Button style={{
              background: 'linear-gradient(to right, #2563eb, #3b82f6)',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.5rem',
              border: 'none',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <Plus style={{ width: '1.25rem', height: '1.25rem' }} />
              منشور جديد
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            background: 'white',
            borderRadius: '0.75rem',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                borderRadius: '0.75rem',
                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Calendar style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
              </div>
              <div>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                  {stats.totalPosts}
                </p>
                <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                  إجمالي المنشورات
                </p>
              </div>
            </div>
          </div>

          <div style={{
            background: 'white',
            borderRadius: '0.75rem',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                borderRadius: '0.75rem',
                background: 'linear-gradient(135deg, #f59e0b, #d97706)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <BarChart3 style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
              </div>
              <div>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                  {stats.scheduledPosts}
                </p>
                <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                  منشورات مجدولة
                </p>
              </div>
            </div>
          </div>

          <div style={{
            background: 'white',
            borderRadius: '0.75rem',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                borderRadius: '0.75rem',
                background: 'linear-gradient(135deg, #10b981, #059669)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <BarChart3 style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
              </div>
              <div>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                  {stats.publishedPosts}
                </p>
                <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                  منشورات منشورة
                </p>
              </div>
            </div>
          </div>

          <div style={{
            background: 'white',
            borderRadius: '0.75rem',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                borderRadius: '0.75rem',
                background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Users style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
              </div>
              <div>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                  {stats.connectedAccounts}
                </p>
                <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                  حسابات متصلة
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Content Calendar */}
        <ContentCalendar />

        {/* Quick Actions */}
        <div style={{
          marginTop: '2rem',
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{
            background: 'white',
            borderRadius: '0.75rem',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb'
          }}>
            <h3 style={{
              fontSize: '1.125rem',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 1rem 0'
            }}>
              إجراءات سريعة
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              <Link href="/posts/new" style={{ textDecoration: 'none' }}>
                <button style={{
                  width: '100%',
                  padding: '0.75rem',
                  background: '#f3f4f6',
                  border: '1px solid #e5e7eb',
                  borderRadius: '0.5rem',
                  color: '#374151',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  textAlign: 'right'
                }}>
                  📝 إنشاء منشور جديد
                </button>
              </Link>
              <Link href="/posts" style={{ textDecoration: 'none' }}>
                <button style={{
                  width: '100%',
                  padding: '0.75rem',
                  background: '#f3f4f6',
                  border: '1px solid #e5e7eb',
                  borderRadius: '0.5rem',
                  color: '#374151',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  textAlign: 'right'
                }}>
                  📋 عرض جميع المنشورات
                </button>
              </Link>
              <Link href="/social" style={{ textDecoration: 'none' }}>
                <button style={{
                  width: '100%',
                  padding: '0.75rem',
                  background: '#f3f4f6',
                  border: '1px solid #e5e7eb',
                  borderRadius: '0.5rem',
                  color: '#374151',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  textAlign: 'right'
                }}>
                  🔗 إدارة الحسابات الاجتماعية
                </button>
              </Link>
            </div>
          </div>

          <div style={{
            background: 'white',
            borderRadius: '0.75rem',
            padding: '1.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb'
          }}>
            <h3 style={{
              fontSize: '1.125rem',
              fontWeight: '600',
              color: '#111827',
              margin: '0 0 1rem 0'
            }}>
              نصائح للتقويم
            </h3>
            <div style={{ fontSize: '0.875rem', color: '#6b7280', lineHeight: '1.5' }}>
              <p style={{ margin: '0 0 0.5rem 0' }}>• انقر على أي يوم لإنشاء منشور جديد</p>
              <p style={{ margin: '0 0 0.5rem 0' }}>• استخدم الألوان لتمييز حالة المنشورات</p>
              <p style={{ margin: '0 0 0.5rem 0' }}>• خطط للمحتوى مسبقاً لضمان الانتظام</p>
              <p style={{ margin: 0 }}>• راجع الإحصائيات لتحسين الأداء</p>
            </div>
          </div>
        </div>

        {/* Task Status */}
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          background: '#eff6ff',
          borderRadius: '0.5rem',
          border: '1px solid #bfdbfe'
        }}>
          <p style={{ color: '#1d4ed8', fontWeight: '500', margin: 0 }}>
            📝 Task 1.8 Status: Post Creation & Scheduling System
          </p>
          <p style={{ color: '#1d4ed8', fontSize: '0.875rem', marginTop: '0.5rem', margin: 0 }}>
            ✅ AI Caption Generation with OpenRouter<br/>
            ✅ Content Calendar with visual scheduling<br/>
            ✅ Post Publishing workflow to social platforms<br/>
            ✅ Enhanced scheduling features<br/>
            🔄 Testing comprehensive post management system...
          </p>
        </div>
      </div>
    </div>
  );
}
