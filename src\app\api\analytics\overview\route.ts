import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for analytics query
const analyticsSchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  platforms: z.array(z.string()).optional(),
});

// GET - Get analytics overview
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching analytics overview...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d';
    const platforms = searchParams.get('platforms')?.split(',') || [];

    console.log('Analytics request:', { period, platforms, userId: user.id });

    // Calculate date range based on period
    const now = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get posts data with analytics
    let postsQuery = supabase
      .from('posts')
      .select(`
        id,
        content,
        status,
        created_at,
        published_at,
        scheduled_at,
        media_url,
        analytics (
          id,
          post_id,
          platform,
          likes,
          shares,
          comments,
          reach,
          impressions,
          engagement_rate,
          clicks,
          updated_at
        )
      `)
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    // Filter by platforms if specified
    if (platforms.length > 0) {
      postsQuery = postsQuery.in('analytics.platform', platforms);
    }

    const { data: posts, error: postsError } = await postsQuery;

    if (postsError) {
      console.error('Error fetching posts:', postsError);
      return NextResponse.json(
        { error: 'Failed to fetch analytics data' },
        { status: 500 }
      );
    }

    // Calculate overview metrics
    const totalPosts = posts?.length || 0;
    const publishedPosts = posts?.filter(p => p.status === 'PUBLISHED').length || 0;
    const scheduledPosts = posts?.filter(p => p.status === 'SCHEDULED').length || 0;
    const draftPosts = posts?.filter(p => p.status === 'DRAFT').length || 0;

    // Calculate engagement metrics
    let totalLikes = 0;
    let totalShares = 0;
    let totalComments = 0;
    let totalReach = 0;
    let totalImpressions = 0;
    let totalClicks = 0;
    let totalEngagementRate = 0;
    let analyticsCount = 0;

    const platformMetrics: Record<string, any> = {};
    const dailyMetrics: Record<string, any> = {};

    posts?.forEach(post => {
      if (post.analytics && post.analytics.length > 0) {
        post.analytics.forEach((analytics: any) => {
          totalLikes += analytics.likes || 0;
          totalShares += analytics.shares || 0;
          totalComments += analytics.comments || 0;
          totalReach += analytics.reach || 0;
          totalImpressions += analytics.impressions || 0;
          totalClicks += analytics.clicks || 0;
          totalEngagementRate += analytics.engagement_rate || 0;
          analyticsCount++;

          // Platform-specific metrics
          const platform = analytics.platform;
          if (!platformMetrics[platform]) {
            platformMetrics[platform] = {
              platform,
              posts: 0,
              likes: 0,
              shares: 0,
              comments: 0,
              reach: 0,
              impressions: 0,
              clicks: 0,
              engagement_rate: 0
            };
          }

          platformMetrics[platform].posts++;
          platformMetrics[platform].likes += analytics.likes || 0;
          platformMetrics[platform].shares += analytics.shares || 0;
          platformMetrics[platform].comments += analytics.comments || 0;
          platformMetrics[platform].reach += analytics.reach || 0;
          platformMetrics[platform].impressions += analytics.impressions || 0;
          platformMetrics[platform].clicks += analytics.clicks || 0;
          platformMetrics[platform].engagement_rate += analytics.engagement_rate || 0;

          // Daily metrics for charts
          const date = new Date(analytics.updated_at || post.published_at || post.created_at).toISOString().split('T')[0];
          if (!dailyMetrics[date]) {
            dailyMetrics[date] = {
              date,
              posts: 0,
              likes: 0,
              shares: 0,
              comments: 0,
              reach: 0,
              impressions: 0,
              engagement_rate: 0
            };
          }

          dailyMetrics[date].posts++;
          dailyMetrics[date].likes += analytics.likes || 0;
          dailyMetrics[date].shares += analytics.shares || 0;
          dailyMetrics[date].comments += analytics.comments || 0;
          dailyMetrics[date].reach += analytics.reach || 0;
          dailyMetrics[date].impressions += analytics.impressions || 0;
          dailyMetrics[date].engagement_rate += analytics.engagement_rate || 0;
        });
      }
    });

    // Calculate averages
    const avgEngagementRate = analyticsCount > 0 ? totalEngagementRate / analyticsCount : 0;
    const avgReachPerPost = publishedPosts > 0 ? totalReach / publishedPosts : 0;
    const avgEngagementPerPost = publishedPosts > 0 ? (totalLikes + totalShares + totalComments) / publishedPosts : 0;

    // Calculate platform averages
    Object.values(platformMetrics).forEach((platform: any) => {
      if (platform.posts > 0) {
        platform.avg_engagement_rate = platform.engagement_rate / platform.posts;
        platform.avg_reach = platform.reach / platform.posts;
        platform.avg_engagement = (platform.likes + platform.shares + platform.comments) / platform.posts;
      }
    });

    // Get top performing posts
    const topPosts = posts
      ?.filter(p => p.analytics && p.analytics.length > 0)
      .map(p => {
        const totalEngagement = p.analytics.reduce((sum: number, a: any) => 
          sum + (a.likes || 0) + (a.shares || 0) + (a.comments || 0), 0);
        return {
          id: p.id,
          content: p.content.substring(0, 100) + (p.content.length > 100 ? '...' : ''),
          published_at: p.published_at,
          total_engagement: totalEngagement,
          analytics: p.analytics
        };
      })
      .sort((a, b) => b.total_engagement - a.total_engagement)
      .slice(0, 5) || [];

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'ANALYTICS_VIEWED',
        details: `Viewed analytics overview for ${period} period`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      period,
      date_range: {
        start: startDate.toISOString(),
        end: now.toISOString()
      },
      overview: {
        total_posts: totalPosts,
        published_posts: publishedPosts,
        scheduled_posts: scheduledPosts,
        draft_posts: draftPosts,
        total_likes: totalLikes,
        total_shares: totalShares,
        total_comments: totalComments,
        total_reach: totalReach,
        total_impressions: totalImpressions,
        total_clicks: totalClicks,
        avg_engagement_rate: Math.round(avgEngagementRate * 100) / 100,
        avg_reach_per_post: Math.round(avgReachPerPost),
        avg_engagement_per_post: Math.round(avgEngagementPerPost)
      },
      platform_metrics: Object.values(platformMetrics),
      daily_metrics: Object.values(dailyMetrics).sort((a: any, b: any) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      ),
      top_posts: topPosts
    }, { status: 200 });

  } catch (error) {
    console.error('Analytics overview error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
