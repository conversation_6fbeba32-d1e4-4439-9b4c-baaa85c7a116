"use client";

import React from "react";
import Link from "next/link";
import { PlusCircle, BarChart3, Calendar, Users, Settings, LogOut, Bell, User, Save } from "lucide-react";

export default function SettingsPage() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif'
    }}>
      {/* Sidebar */}
      <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        width: '18rem',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(24px)',
        borderLeft: '1px solid rgba(229, 231, 235, 0.5)',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        zIndex: 50,
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Logo Section */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid rgba(229, 231, 235, 0.5)'
        }}>
          <Link href="/dashboard" style={{ textDecoration: 'none' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                borderRadius: '0.75rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1.125rem',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
              }}>
                eW
              </div>
              <div>
                <h2 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(to right, #2563eb, #9333ea)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: 0
                }}>
                  eWasl
                </h2>
                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>منصة إدارة المحتوى</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <div style={{ flex: 1, padding: '1.5rem 1rem' }}>
          <p style={{
            fontSize: '0.75rem',
            fontWeight: '600',
            color: '#6b7280',
            textTransform: 'uppercase',
            letterSpacing: '0.05em',
            marginBottom: '0.75rem',
            padding: '0 0.75rem'
          }}>
            القائمة الرئيسية
          </p>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
            {[
              { icon: BarChart3, label: 'لوحة التحكم', href: '/dashboard' },
              { icon: PlusCircle, label: 'المنشورات', href: '/posts' },
              { icon: Calendar, label: 'الجدولة', href: '/schedule' },
              { icon: Users, label: 'الحسابات', href: '/social' },
              { icon: BarChart3, label: 'التحليلات', href: '/analytics' },
              { icon: Settings, label: 'الإعدادات', href: '/settings', active: true }
            ].map((item, index) => (
              <Link
                key={index}
                href={item.href}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  height: '2.75rem',
                  padding: '0 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: item.active ? 'white' : '#374151',
                  textDecoration: 'none',
                  borderRadius: '0.5rem',
                  background: item.active ? 'linear-gradient(to right, #2563eb, #9333ea)' : 'transparent',
                  boxShadow: item.active ? '0 10px 15px -3px rgba(0, 0, 0, 0.1)' : 'none',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.currentTarget.style.background = '#eff6ff';
                    e.currentTarget.style.color = '#2563eb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.currentTarget.style.background = 'transparent';
                    e.currentTarget.style.color = '#374151';
                  }
                }}
              >
                <item.icon style={{ width: '1.25rem', height: '1.25rem', marginLeft: '0.75rem' }} />
                {item.label}
              </Link>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div style={{ padding: '1rem', borderTop: '1px solid rgba(229, 231, 235, 0.5)' }}>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            height: '2.75rem',
            padding: '0 0.75rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#dc2626',
            background: 'transparent',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}>
            <LogOut style={{ width: '1.25rem', height: '1.25rem', marginLeft: '0.75rem' }} />
            تسجيل الخروج
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ marginRight: '18rem' }}>
        {/* Header */}
        <header style={{
          position: 'sticky',
          top: 0,
          zIndex: 30,
          height: '4rem',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(24px)',
          borderBottom: '1px solid rgba(229, 231, 235, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          alignItems: 'center',
          padding: '0 1.5rem',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <div style={{
              width: '2.5rem',
              height: '2.5rem',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '0.875rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              eW
            </div>
            <div>
              <h1 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                margin: 0
              }}>
                الإعدادات
              </h1>
              <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>إدارة إعدادات حسابك ومنصتك</p>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <button style={{
              position: 'relative',
              padding: '0.5rem',
              background: 'transparent',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              color: '#6b7280'
            }}>
              <Bell style={{ width: '1.25rem', height: '1.25rem' }} />
              <span style={{
                position: 'absolute',
                top: '0.25rem',
                right: '0.25rem',
                width: '0.5rem',
                height: '0.5rem',
                background: '#ef4444',
                borderRadius: '50%'
              }}></span>
            </button>
            <button style={{
              width: '2rem',
              height: '2rem',
              background: '#f3f4f6',
              border: 'none',
              borderRadius: '50%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6b7280'
            }}>
              <User style={{ width: '1rem', height: '1rem' }} />
            </button>
          </div>
        </header>

        {/* Main Content */}
        <main style={{
          minHeight: 'calc(100vh - 4rem)',
          background: 'linear-gradient(135deg, rgba(249, 250, 251, 0.3) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(239, 246, 255, 0.2) 100%)',
          padding: '1.5rem'
        }}>
          <div style={{ maxWidth: '80rem', margin: '0 auto' }}>
            {/* Welcome Section */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(4px)',
              borderRadius: '1rem',
              padding: '2rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.5)',
              marginBottom: '2rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, #fbbf24, #f59e0b)',
                  borderRadius: '0.75rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}>
                  <Settings style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
                </div>
                <div>
                  <h1 style={{
                    fontSize: '1.875rem',
                    fontWeight: 'bold',
                    background: 'linear-gradient(to right, #2563eb, #9333ea)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    margin: 0
                  }}>
                    الإعدادات
                  </h1>
                  <p style={{ color: '#4b5563', fontSize: '1.125rem', margin: '0.5rem 0 0 0' }}>
                    إدارة إعدادات حسابك ومنصتك الشخصية
                  </p>
                </div>
              </div>
            </div>

            {/* Settings Content */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(4px)',
              borderRadius: '1rem',
              padding: '2rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.5)',
              marginBottom: '2rem'
            }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#111827', margin: '0 0 1rem 0' }}>
                إعدادات الحساب
              </h2>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '2rem' }}>
                إدارة وتخصيص إعدادات حسابك الشخصي
              </p>

              <div style={{
                background: '#f9fafb',
                borderRadius: '0.75rem',
                padding: '1.5rem',
                border: '1px solid #e5e7eb'
              }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#111827', margin: '0 0 1rem 0' }}>
                  المعلومات الأساسية
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '1rem'
                }}>
                  <div>
                    <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.5rem' }}>
                      الاسم
                    </label>
                    <input
                      type="text"
                      defaultValue="أحمد محمد"
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '0.375rem',
                        fontSize: '0.875rem',
                        outline: 'none'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.5rem' }}>
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '0.375rem',
                        fontSize: '0.875rem',
                        outline: 'none'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(4px)',
              borderRadius: '1rem',
              padding: '1.5rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.5)',
              textAlign: 'center'
            }}>
              <button style={{
                padding: '1rem 2rem',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                color: 'white',
                border: 'none',
                borderRadius: '0.75rem',
                fontWeight: '600',
                fontSize: '1rem',
                cursor: 'pointer',
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.2s ease'
              }}>
                <Save style={{ width: '1.25rem', height: '1.25rem' }} />
                حفظ التغييرات
              </button>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}