"use client";

import { useState } from "react";
import { createClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle, Mail, Send, RefreshCw } from "lucide-react";

export default function TestEmailSystemPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [testEmail, setTestEmail] = useState("<EMAIL>");
  const [resetEmail, setResetEmail] = useState("<EMAIL>");

  const addResult = (test: string, status: 'success' | 'error' | 'warning', message: string, details?: any) => {
    const result = {
      id: Date.now(),
      test,
      status,
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev]);
    
    if (status === 'success') {
      toast.success(`✅ ${test}: ${message}`);
    } else if (status === 'error') {
      toast.error(`❌ ${test}: ${message}`);
    } else {
      toast.warning(`⚠️ ${test}: ${message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    toast.info("تم مسح نتائج الاختبار");
  };

  const testSignupWithEmail = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();
      
      // Generate unique email for testing
      const uniqueEmail = `test+${Date.now()}@example.com`;
      
      const { data, error } = await supabase.auth.signUp({
        email: uniqueEmail,
        password: 'TestPassword123!',
        options: {
          data: {
            name: 'Test User',
            role: 'USER'
          }
        }
      });

      if (error) {
        addResult("Signup Email Test", "error", error.message, { error });
      } else if (data.user) {
        if (data.session) {
          addResult("Signup Email Test", "warning", `User created and signed in immediately (email confirmation disabled): ${uniqueEmail}`, { 
            user: data.user,
            session: "Created immediately"
          });
        } else {
          addResult("Signup Email Test", "success", `Confirmation email should be sent to: ${uniqueEmail}`, { 
            user: data.user,
            emailSent: "Confirmation required"
          });
        }
      } else {
        addResult("Signup Email Test", "warning", "No user data returned");
      }
    } catch (error: any) {
      addResult("Signup Email Test", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testPasswordResetEmail = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();
      
      const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        addResult("Password Reset Email", "error", error.message, { error });
      } else {
        addResult("Password Reset Email", "success", `Reset email should be sent to: ${resetEmail}`, {
          email: resetEmail,
          redirectUrl: `${window.location.origin}/auth/reset-password`
        });
      }
    } catch (error: any) {
      addResult("Password Reset Email", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testEmailChangeRequest = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();
      
      // First check if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        addResult("Email Change Test", "warning", "User must be logged in to test email change");
        setIsLoading(false);
        return;
      }

      const newEmail = `newemail+${Date.now()}@example.com`;
      
      const { error } = await supabase.auth.updateUser({
        email: newEmail
      });

      if (error) {
        addResult("Email Change Test", "error", error.message, { error });
      } else {
        addResult("Email Change Test", "success", `Email change confirmation should be sent to: ${newEmail}`, {
          oldEmail: user.email,
          newEmail: newEmail
        });
      }
    } catch (error: any) {
      addResult("Email Change Test", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const checkSupabaseEmailSettings = async () => {
    setIsLoading(true);
    try {
      // This will test if Supabase can send emails by attempting a password reset
      // to a known test email
      const supabase = createClient();
      
      const { error } = await supabase.auth.resetPasswordForEmail('<EMAIL>', {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        if (error.message.includes('rate limit') || error.message.includes('too many')) {
          addResult("Supabase Email Config", "warning", "Rate limited - email system is working but too many requests", { error });
        } else if (error.message.includes('invalid') || error.message.includes('not found')) {
          addResult("Supabase Email Config", "success", "Email system is working (user not found is expected)", { error });
        } else {
          addResult("Supabase Email Config", "error", error.message, { error });
        }
      } else {
        addResult("Supabase Email Config", "success", "Email system is configured and working");
      }
    } catch (error: any) {
      addResult("Supabase Email Config", "error", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const runAllEmailTests = async () => {
    clearResults();
    addResult("Email Test Suite", "warning", "Starting comprehensive email system tests...");
    
    await checkSupabaseEmailSettings();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testSignupWithEmail();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testPasswordResetEmail();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testEmailChangeRequest();
    
    addResult("Email Test Suite", "success", "All email tests completed!");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <Mail className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
            Email System Test
          </h1>
          <p className="text-gray-600 text-lg">اختبار شامل لنظام البريد الإلكتروني</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="space-y-6">
            {/* Email Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Send className="h-5 w-5" />
                  Email Test Configuration
                </CardTitle>
                <CardDescription>Configure test email addresses</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Test Email for Signup</label>
                  <Input
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Test Email for Password Reset</label>
                  <Input
                    type="email"
                    value={resetEmail}
                    onChange={(e) => setResetEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Test Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Email Test Actions</CardTitle>
                <CardDescription>Run individual or comprehensive email tests</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={runAllEmailTests} 
                  className="w-full"
                  disabled={isLoading}
                >
                  📧 Run All Email Tests
                </Button>
                
                <div className="grid grid-cols-1 gap-2">
                  <Button 
                    onClick={checkSupabaseEmailSettings} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Check Email Config
                  </Button>
                  <Button 
                    onClick={testSignupWithEmail} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Test Signup Email
                  </Button>
                  <Button 
                    onClick={testPasswordResetEmail} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Test Password Reset
                  </Button>
                  <Button 
                    onClick={testEmailChangeRequest} 
                    variant="outline"
                    disabled={isLoading}
                  >
                    Test Email Change
                  </Button>
                </div>
                
                <Button 
                  onClick={clearResults} 
                  variant="ghost" 
                  className="w-full"
                >
                  Clear Results
                </Button>
              </CardContent>
            </Card>

            {/* Email Configuration Info */}
            <Card>
              <CardHeader>
                <CardTitle>📋 Email Configuration Guide</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <p><strong>Supabase SMTP Settings:</strong></p>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>Enable SMTP in Supabase Dashboard</li>
                  <li>Configure SendGrid or other SMTP provider</li>
                  <li>Set sender email and name</li>
                  <li>Enable email confirmations</li>
                  <li>Test with real email addresses</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Test Results */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Email Test Results</CardTitle>
                <CardDescription>
                  {testResults.length} test results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {testResults.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">
                      No test results yet. Run email tests to see results here.
                    </p>
                  ) : (
                    testResults.map((result) => (
                      <div
                        key={result.id}
                        className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                      >
                        <div className="flex items-start gap-3">
                          {getStatusIcon(result.status)}
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">{result.test}</h4>
                              <span className="text-xs text-gray-500">
                                {result.timestamp}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {result.message}
                            </p>
                            {result.details && (
                              <details className="mt-2">
                                <summary className="text-xs text-gray-500 cursor-pointer">
                                  View Details
                                </summary>
                                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                                  {JSON.stringify(result.details, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
