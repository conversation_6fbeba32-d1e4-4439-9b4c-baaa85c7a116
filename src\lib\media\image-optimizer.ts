/**
 * Advanced Image Optimization Engine with Sharp Integration
 * Handles automatic image optimization, format conversion, and platform-specific resizing
 * Uses Sharp library for production-grade image processing
 */

import sharp from 'sharp';
import ContentAnalyzer from './content-analyzer';
import SmartFormatSelector, { FormatSupport, OptimizationStrategy } from './smart-format-selector';

export interface ImageOptimizationOptions {
  quality?: number; // 1-100
  format?: 'jpeg' | 'png' | 'webp' | 'avif' | 'auto';
  width?: number;
  height?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  background?: string;
  progressive?: boolean;
  lossless?: boolean;
  effort?: number; // 1-9 for WebP/AVIF
  platform?: string; // For platform-specific optimization
}

export interface OptimizationResult {
  success: boolean;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  format: string;
  dimensions: {
    width: number;
    height: number;
  };
  optimizedBuffer: Buffer;
  metadata: ImageMetadata;
  error?: string;
}

export interface ImageMetadata {
  format: string;
  width: number;
  height: number;
  channels: number;
  density: number;
  hasAlpha: boolean;
  isAnimated?: boolean;
  pages?: number;
}

export interface PlatformOptimization {
  platform: string;
  maxWidth: number;
  maxHeight: number;
  quality: number;
  format: string;
  aspectRatio?: number;
}

export class ImageOptimizer {
  private platformSettings: Map<string, PlatformOptimization> = new Map();
  private contentAnalyzer: ContentAnalyzer;
  private formatSelector: SmartFormatSelector;

  constructor() {
    this.initializePlatformSettings();
    this.contentAnalyzer = new ContentAnalyzer();
    this.formatSelector = new SmartFormatSelector();
  }

  /**
   * Initialize platform-specific optimization settings
   */
  private initializePlatformSettings(): void {
    // Instagram optimization settings
    this.platformSettings.set('instagram', {
      platform: 'instagram',
      maxWidth: 1080,
      maxHeight: 1080,
      quality: 85,
      format: 'jpeg',
      aspectRatio: 1, // Square format preferred
    });

    // Facebook optimization settings
    this.platformSettings.set('facebook', {
      platform: 'facebook',
      maxWidth: 1200,
      maxHeight: 630,
      quality: 80,
      format: 'jpeg',
      aspectRatio: 1.91, // 1.91:1 ratio for link previews
    });

    // Twitter optimization settings
    this.platformSettings.set('twitter', {
      platform: 'twitter',
      maxWidth: 1024,
      maxHeight: 512,
      quality: 80,
      format: 'jpeg',
      aspectRatio: 2, // 2:1 ratio for cards
    });

    // LinkedIn optimization settings
    this.platformSettings.set('linkedin', {
      platform: 'linkedin',
      maxWidth: 1200,
      maxHeight: 627,
      quality: 85,
      format: 'jpeg',
      aspectRatio: 1.91, // Professional content
    });

    // General web optimization
    this.platformSettings.set('web', {
      platform: 'web',
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 85,
      format: 'webp',
    });

    // Thumbnail optimization
    this.platformSettings.set('thumbnail', {
      platform: 'thumbnail',
      maxWidth: 300,
      maxHeight: 300,
      quality: 80,
      format: 'webp',
    });
  }

  /**
   * AI-powered image optimization with intelligent format selection
   */
  async optimizeImageAI(
    inputBuffer: Buffer,
    options: ImageOptimizationOptions = {},
    browserSupport?: FormatSupport,
    strategy?: OptimizationStrategy
  ): Promise<OptimizationResult> {
    try {
      console.log('Starting AI-powered image optimization:', {
        inputSize: inputBuffer.length,
        options,
        strategy: strategy?.strategy || 'balanced'
      });

      // Analyze content using AI
      const contentAnalysis = await this.contentAnalyzer.analyzeImage(inputBuffer);

      // Get smart format recommendation
      const formatRecommendation = await this.formatSelector.selectOptimalFormat(
        contentAnalysis,
        false, // isVideo
        options.platform,
        browserSupport,
        strategy
      );

      // Use AI recommendations for optimization
      const aiOptions: ImageOptimizationOptions = {
        ...options,
        format: (formatRecommendation.primaryFormat as any) || options.format,
        quality: options.quality || formatRecommendation.quality,
      };

      console.log('AI recommendations:', {
        format: formatRecommendation.primaryFormat,
        quality: formatRecommendation.quality,
        expectedReduction: formatRecommendation.expectedSizeReduction + '%',
        reasoning: formatRecommendation.reasoning
      });

      // Perform optimization with AI-enhanced settings
      const result = await this.optimizeImage(inputBuffer, aiOptions);

      // Enhance result with AI insights
      if (result.success) {
        result.metadata = {
          ...result.metadata,
          aiAnalysis: contentAnalysis,
          formatRecommendation,
          aiEnhanced: true
        } as any;
      }

      return result;

    } catch (error) {
      console.error('AI-powered optimization error:', error);
      // Fallback to standard optimization
      return this.optimizeImage(inputBuffer, options);
    }
  }

  /**
   * Optimize image with automatic format selection and compression
   */
  async optimizeImage(
    inputBuffer: Buffer,
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizationResult> {
    try {
      console.log('Starting image optimization:', {
        inputSize: inputBuffer.length,
        options
      });

      // Extract metadata from original image
      const metadata = await this.extractImageMetadata(inputBuffer);
      
      // Determine optimal format
      const targetFormat = this.determineOptimalFormat(metadata, options);
      
      // Calculate optimal dimensions
      const targetDimensions = this.calculateOptimalDimensions(metadata, options);
      
      // Apply optimization
      const optimizedBuffer = await this.processImage(inputBuffer, {
        ...options,
        format: targetFormat,
        width: targetDimensions.width,
        height: targetDimensions.height,
      });

      // Calculate compression metrics
      const compressionRatio = (inputBuffer.length - optimizedBuffer.length) / inputBuffer.length;
      
      console.log('Image optimization completed:', {
        originalSize: inputBuffer.length,
        optimizedSize: optimizedBuffer.length,
        compressionRatio: Math.round(compressionRatio * 100) + '%',
        format: targetFormat
      });

      return {
        success: true,
        originalSize: inputBuffer.length,
        optimizedSize: optimizedBuffer.length,
        compressionRatio,
        format: targetFormat,
        dimensions: targetDimensions,
        optimizedBuffer,
        metadata,
      };

    } catch (error) {
      console.error('Image optimization error:', error);
      return {
        success: false,
        originalSize: inputBuffer.length,
        optimizedSize: 0,
        compressionRatio: 0,
        format: 'unknown',
        dimensions: { width: 0, height: 0 },
        optimizedBuffer: Buffer.alloc(0),
        metadata: {} as ImageMetadata,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Optimize image for specific platform
   */
  async optimizeForPlatform(
    inputBuffer: Buffer,
    platform: string
  ): Promise<OptimizationResult> {
    const platformConfig = this.platformSettings.get(platform.toLowerCase());
    
    if (!platformConfig) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    console.log('Optimizing for platform:', {
      platform,
      config: platformConfig
    });

    return this.optimizeImage(inputBuffer, {
      width: platformConfig.maxWidth,
      height: platformConfig.maxHeight,
      quality: platformConfig.quality,
      format: platformConfig.format as any,
      fit: 'cover',
      platform,
    });
  }

  /**
   * Generate multiple optimized versions for different platforms
   */
  async generateMultipleVersions(
    inputBuffer: Buffer,
    platforms: string[]
  ): Promise<Map<string, OptimizationResult>> {
    const results = new Map<string, OptimizationResult>();

    console.log(`Generating optimized versions for ${platforms.length} platforms`);

    for (const platform of platforms) {
      try {
        const result = await this.optimizeForPlatform(inputBuffer, platform);
        results.set(platform, result);
      } catch (error) {
        console.error(`Failed to optimize for ${platform}:`, error);
        results.set(platform, {
          success: false,
          originalSize: inputBuffer.length,
          optimizedSize: 0,
          compressionRatio: 0,
          format: 'unknown',
          dimensions: { width: 0, height: 0 },
          optimizedBuffer: Buffer.alloc(0),
          metadata: {} as ImageMetadata,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }

  /**
   * Extract image metadata using Sharp
   */
  private async extractImageMetadata(buffer: Buffer): Promise<ImageMetadata> {
    try {
      const image = sharp(buffer);
      const metadata = await image.metadata();

      return {
        format: metadata.format || 'unknown',
        width: metadata.width || 0,
        height: metadata.height || 0,
        channels: metadata.channels || 3,
        density: metadata.density || 72,
        hasAlpha: metadata.hasAlpha || false,
        isAnimated: metadata.pages ? metadata.pages > 1 : false,
        pages: metadata.pages,
      };
    } catch (error) {
      console.error('Failed to extract image metadata:', error);
      // Return fallback metadata
      return {
        format: 'unknown',
        width: 0,
        height: 0,
        channels: 3,
        density: 72,
        hasAlpha: false,
        isAnimated: false,
      };
    }
  }

  /**
   * Determine optimal format based on image characteristics
   */
  private determineOptimalFormat(
    metadata: ImageMetadata,
    options: ImageOptimizationOptions
  ): string {
    // If format is explicitly specified
    if (options.format && options.format !== 'auto') {
      return options.format;
    }

    // Platform-specific format selection
    if (options.platform) {
      const platformConfig = this.platformSettings.get(options.platform.toLowerCase());
      if (platformConfig) {
        return platformConfig.format;
      }
    }

    // Automatic format selection based on image characteristics
    if (metadata.hasAlpha) {
      return 'webp'; // WebP handles transparency well
    }

    if (metadata.isAnimated) {
      return 'webp'; // WebP supports animation
    }

    // For photos, prefer WebP for better compression
    if (metadata.channels >= 3) {
      return 'webp';
    }

    // Default to JPEG for compatibility
    return 'jpeg';
  }

  /**
   * Calculate optimal dimensions
   */
  private calculateOptimalDimensions(
    metadata: ImageMetadata,
    options: ImageOptimizationOptions
  ): { width: number; height: number } {
    let targetWidth = options.width || metadata.width;
    let targetHeight = options.height || metadata.height;

    // Platform-specific dimension constraints
    if (options.platform) {
      const platformConfig = this.platformSettings.get(options.platform.toLowerCase());
      if (platformConfig) {
        targetWidth = Math.min(targetWidth, platformConfig.maxWidth);
        targetHeight = Math.min(targetHeight, platformConfig.maxHeight);

        // Maintain aspect ratio if specified
        if (platformConfig.aspectRatio) {
          const currentRatio = targetWidth / targetHeight;
          if (currentRatio !== platformConfig.aspectRatio) {
            if (currentRatio > platformConfig.aspectRatio) {
              targetWidth = targetHeight * platformConfig.aspectRatio;
            } else {
              targetHeight = targetWidth / platformConfig.aspectRatio;
            }
          }
        }
      }
    }

    // Ensure dimensions don't exceed original
    targetWidth = Math.min(targetWidth, metadata.width);
    targetHeight = Math.min(targetHeight, metadata.height);

    return {
      width: Math.round(targetWidth),
      height: Math.round(targetHeight),
    };
  }

  /**
   * Process image with optimization settings using Sharp
   */
  private async processImage(
    inputBuffer: Buffer,
    options: ImageOptimizationOptions
  ): Promise<Buffer> {
    try {
      console.log('Processing image with Sharp:', options);

      let image = sharp(inputBuffer);

      // Resize if dimensions are specified
      if (options.width || options.height) {
        image = image.resize(options.width, options.height, {
          fit: this.getSharpFit(options.fit || 'cover'),
          background: options.background || { r: 255, g: 255, b: 255, alpha: 1 },
          withoutEnlargement: true, // Don't upscale images
        });
      }

      // Apply format-specific optimizations
      const format = options.format || 'jpeg';
      const quality = options.quality || 85;

      switch (format) {
        case 'jpeg':
          image = image.jpeg({
            quality,
            progressive: options.progressive !== false,
            mozjpeg: true, // Use mozjpeg encoder for better compression
          });
          break;

        case 'png':
          image = image.png({
            quality,
            compressionLevel: 9,
            progressive: options.progressive !== false,
            palette: !options.lossless, // Use palette for smaller files unless lossless
          });
          break;

        case 'webp':
          image = image.webp({
            quality,
            lossless: options.lossless || false,
            effort: options.effort || 6,
            smartSubsample: true,
          });
          break;

        case 'avif':
          image = image.avif({
            quality,
            lossless: options.lossless || false,
            effort: options.effort || 4,
          });
          break;

        default:
          // Default to JPEG
          image = image.jpeg({ quality, progressive: true });
      }

      const processedBuffer = await image.toBuffer();

      console.log('Sharp processing completed:', {
        originalSize: inputBuffer.length,
        processedSize: processedBuffer.length,
        compressionRatio: Math.round(((inputBuffer.length - processedBuffer.length) / inputBuffer.length) * 100) + '%',
        format
      });

      return processedBuffer;

    } catch (error) {
      console.error('Sharp processing error:', error);
      // Fallback: return original buffer if processing fails
      return inputBuffer;
    }
  }

  /**
   * Convert fit option to Sharp fit enum
   */
  private getSharpFit(fit: string): keyof sharp.FitEnum {
    const fitMap: Record<string, keyof sharp.FitEnum> = {
      'cover': 'cover',
      'contain': 'contain',
      'fill': 'fill',
      'inside': 'inside',
      'outside': 'outside',
    };

    return fitMap[fit] || 'cover';
  }

  /**
   * Get platform optimization settings
   */
  getPlatformSettings(platform: string): PlatformOptimization | undefined {
    return this.platformSettings.get(platform.toLowerCase());
  }

  /**
   * Add or update platform settings
   */
  setPlatformSettings(platform: string, settings: PlatformOptimization): void {
    this.platformSettings.set(platform.toLowerCase(), settings);
  }

  /**
   * Get supported platforms
   */
  getSupportedPlatforms(): string[] {
    return Array.from(this.platformSettings.keys());
  }

  /**
   * Validate image buffer using Sharp
   */
  async validateImage(buffer: Buffer): Promise<{ isValid: boolean; format?: string; error?: string }> {
    try {
      const image = sharp(buffer);
      const metadata = await image.metadata();

      if (metadata.format) {
        return {
          isValid: true,
          format: metadata.format
        };
      } else {
        return {
          isValid: false,
          error: 'Unable to determine image format'
        };
      }

    } catch (error) {
      // Fallback to header-based validation
      try {
        const header = buffer.slice(0, 12);

        // JPEG
        if (header[0] === 0xFF && header[1] === 0xD8) {
          return { isValid: true, format: 'jpeg' };
        }

        // PNG
        if (header.slice(0, 8).equals(Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]))) {
          return { isValid: true, format: 'png' };
        }

        // WebP
        if (header.slice(0, 4).equals(Buffer.from('RIFF', 'ascii')) &&
            header.slice(8, 12).equals(Buffer.from('WEBP', 'ascii'))) {
          return { isValid: true, format: 'webp' };
        }

        // GIF
        if (header.slice(0, 6).equals(Buffer.from('GIF87a', 'ascii')) ||
            header.slice(0, 6).equals(Buffer.from('GIF89a', 'ascii'))) {
          return { isValid: true, format: 'gif' };
        }

        return { isValid: false, error: 'Unsupported image format' };

      } catch (fallbackError) {
        return { isValid: false, error: fallbackError instanceof Error ? fallbackError.message : String(fallbackError) };
      }
    }
  }
}

export default ImageOptimizer;
