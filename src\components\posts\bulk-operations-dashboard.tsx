'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  RefreshCw, 
  MoreVertical, 
  Trash2, 
  X,
  CheckCircle,
  XCircle,
  Clock,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

interface BulkOperation {
  id: string;
  operationType: string;
  status: string;
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
}

interface BulkOperationsDashboardProps {
  refreshTrigger?: number;
}

export function BulkOperationsDashboard({ refreshTrigger }: BulkOperationsDashboardProps) {
  const [operations, setOperations] = useState<BulkOperation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [cancelDialog, setCancelDialog] = useState<{ open: boolean; operation?: BulkOperation }>({ open: false });
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchOperations();
  }, [refreshTrigger]);

  // Auto-refresh for processing operations
  useEffect(() => {
    const hasProcessing = operations.some(op => op.status === 'processing');
    if (!hasProcessing) return;

    const interval = setInterval(() => {
      fetchOperations();
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(interval);
  }, [operations]);

  const fetchOperations = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/posts/bulk-operations');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في جلب العمليات المجمعة');
      }

      setOperations(result.data.operations || []);
    } catch (error: any) {
      console.error('Error fetching operations:', error);
      toast.error(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const cancelOperation = async (operationId: string) => {
    try {
      setProcessingIds(prev => new Set(prev).add(operationId));

      const response = await fetch(`/api/posts/bulk-operations/${operationId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'cancel' }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في إلغاء العملية');
      }

      toast.success(result.message);
      fetchOperations();
    } catch (error: any) {
      console.error('Error cancelling operation:', error);
      toast.error(error.message);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(operationId);
        return newSet;
      });
      setCancelDialog({ open: false });
    }
  };

  const retryOperation = async (operationId: string) => {
    try {
      setProcessingIds(prev => new Set(prev).add(operationId));

      const response = await fetch(`/api/posts/bulk-operations/${operationId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'retry' }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في إعادة المحاولة');
      }

      toast.success(result.message);
      fetchOperations();
    } catch (error: any) {
      console.error('Error retrying operation:', error);
      toast.error(error.message);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(operationId);
        return newSet;
      });
    }
  };

  const deleteOperation = async (operationId: string) => {
    try {
      setProcessingIds(prev => new Set(prev).add(operationId));

      const response = await fetch(`/api/posts/bulk-operations/${operationId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في حذف العملية');
      }

      setOperations(prev => prev.filter(op => op.id !== operationId));
      toast.success(result.message);
    } catch (error: any) {
      console.error('Error deleting operation:', error);
      toast.error(error.message);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(operationId);
        return newSet;
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'cancelled':
        return <X className="h-4 w-4 text-gray-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      completed: 'default',
      failed: 'destructive',
      processing: 'default',
      cancelled: 'secondary',
      pending: 'secondary',
    };

    const labels: Record<string, string> = {
      completed: 'مكتمل',
      failed: 'فشل',
      processing: 'قيد المعالجة',
      cancelled: 'ملغي',
      pending: 'في الانتظار',
    };

    return (
      <Badge variant={variants[status] || 'secondary'}>
        {getStatusIcon(status)}
        <span className="mr-1">{labels[status] || status}</span>
      </Badge>
    );
  };

  const getProgressPercentage = (operation: BulkOperation) => {
    if (operation.totalItems === 0) return 0;
    return Math.round((operation.processedItems / operation.totalItems) * 100);
  };

  const formatDate = (date: Date) => {
    try {
      return formatDistanceToNow(date, { addSuffix: true, locale: ar });
    } catch {
      return 'تاريخ غير صالح';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        جاري تحميل العمليات المجمعة...
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">العمليات المجمعة</h3>
        <Button variant="outline" size="sm" onClick={fetchOperations}>
          <RefreshCw className="h-4 w-4 mr-2" />
          تحديث
        </Button>
      </div>

      {operations.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Clock className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">لا توجد عمليات مجمعة</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {operations.map((operation) => (
            <Card key={operation.id} className={`transition-opacity ${processingIds.has(operation.id) ? 'opacity-50' : ''}`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {getStatusBadge(operation.status)}
                    <span className="font-medium">
                      {operation.operationType === 'import' ? 'استيراد CSV' : operation.operationType}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {formatDate(operation.createdAt)}
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {operation.status === 'processing' && (
                          <DropdownMenuItem 
                            onClick={() => setCancelDialog({ open: true, operation })}
                          >
                            <X className="h-4 w-4 mr-2" />
                            إلغاء
                          </DropdownMenuItem>
                        )}
                        {operation.status === 'failed' && (
                          <DropdownMenuItem onClick={() => retryOperation(operation.id)}>
                            <RotateCcw className="h-4 w-4 mr-2" />
                            إعادة المحاولة
                          </DropdownMenuItem>
                        )}
                        {!['processing'].includes(operation.status) && (
                          <DropdownMenuItem 
                            onClick={() => deleteOperation(operation.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            حذف
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Progress Bar */}
                {operation.status === 'processing' && (
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>التقدم</span>
                      <span>{getProgressPercentage(operation)}%</span>
                    </div>
                    <Progress value={getProgressPercentage(operation)} className="h-2" />
                  </div>
                )}

                {/* Statistics */}
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-lg font-semibold">{operation.totalItems}</div>
                    <div className="text-xs text-muted-foreground">إجمالي</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-blue-600">{operation.processedItems}</div>
                    <div className="text-xs text-muted-foreground">معالج</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-green-600">{operation.successfulItems}</div>
                    <div className="text-xs text-muted-foreground">نجح</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-red-600">{operation.failedItems}</div>
                    <div className="text-xs text-muted-foreground">فشل</div>
                  </div>
                </div>

                {/* Timing Info */}
                {(operation.startedAt || operation.completedAt) && (
                  <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
                    {operation.startedAt && (
                      <span>بدأ: {formatDate(operation.startedAt)}</span>
                    )}
                    {operation.completedAt && (
                      <span className="mr-4">انتهى: {formatDate(operation.completedAt)}</span>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Cancel Confirmation Dialog */}
      <AlertDialog open={cancelDialog.open} onOpenChange={(open) => setCancelDialog({ open })}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد الإلغاء</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من إلغاء هذه العملية المجمعة؟
              سيتم إيقاف المعالجة وقد تفقد التقدم الحالي.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => cancelDialog.operation && cancelOperation(cancelDialog.operation.id)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              إلغاء العملية
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
