import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createOAuthManager } from '@/lib/auth/oauth-manager';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET - Handle OAuth callbacks for all platforms
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ platform: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { platform: platformParam } = await params;
    const platform = platformParam.toUpperCase();
    const { searchParams } = new URL(request.url);

    console.log(`Processing ${platform} OAuth callback...`);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated during callback');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/social?error=authentication_required`
      );
    }

    // Use enhanced OAuth manager
    const oauthManager = createOAuthManager();
    const result = await oauthManager.handleCallback(platform, searchParams, user.id);

    if (result.success && result.accountData) {
      console.log(`${platform} OAuth callback successful for user ${user.id}`);

      // Log successful connection activity
      await supabase
        .from('activities')
        .insert({
          user_id: user.id,
          action: 'SOCIAL_ACCOUNT_CONNECTED',
          metadata: {
            platform,
            accountId: result.accountData.accountId,
            accountName: result.accountData.accountName,
            timestamp: new Date().toISOString(),
          },
        });

      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/social?success=${platform.toLowerCase()}_connected&account=${result.accountData.accountName}`
      );
    } else {
      console.error(`${platform} OAuth callback failed:`, result.error);

      // Log failed connection activity
      await supabase
        .from('activities')
        .insert({
          user_id: user.id,
          action: 'SOCIAL_ACCOUNT_CONNECTION_FAILED',
          metadata: {
            platform,
            error: result.error,
            timestamp: new Date().toISOString(),
          },
        });

      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/social?error=${platform.toLowerCase()}_callback_failed&message=${encodeURIComponent(result.error || 'Unknown error')}`
      );
    }

  } catch (error: any) {
    console.error('OAuth callback error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/social?error=callback_failed&message=${encodeURIComponent(error.message)}`
    );
  }
}