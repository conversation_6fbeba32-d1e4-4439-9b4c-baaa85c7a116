import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { AnalyticsCollector } from '@/lib/analytics/analytics-collector';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/analytics/collection - Get analytics collection status
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching analytics collection status...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const collector = new AnalyticsCollector();
    const stats = await collector.getCollectionStats();

    // Get recent collection jobs for this user
    const { data: recentJobs, error: jobsError } = await supabase
      .from('analytics_collection_jobs')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(20);

    if (jobsError) {
      console.error('Error fetching collection jobs:', jobsError);
    }

    // Get posts that need analytics collection
    const { data: postsNeedingAnalytics, error: postsError } = await supabase
      .from('posts')
      .select('id, content, published_at, platforms, analytics_collected')
      .eq('user_id', user.id)
      .eq('status', 'PUBLISHED')
      .eq('analytics_collected', false)
      .gte('published_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
      .order('published_at', { ascending: false })
      .limit(10);

    if (postsError) {
      console.error('Error fetching posts needing analytics:', postsError);
    }

    return NextResponse.json({
      success: true,
      data: {
        collectionStats: stats,
        recentJobs: recentJobs || [],
        postsNeedingAnalytics: postsNeedingAnalytics || [],
        summary: {
          totalJobsToday: stats.totalToday,
          pendingJobs: stats.pending,
          completedJobs: stats.completed,
          failedJobs: stats.failed,
          postsAwaitingCollection: postsNeedingAnalytics?.length || 0,
        },
      },
    });

  } catch (error: any) {
    console.error('Error fetching analytics collection status:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch collection status',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/analytics/collection - Trigger analytics collection
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Triggering analytics collection...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { type, targetId, priority, delay } = body;

    if (!type || !targetId) {
      return NextResponse.json(
        { error: 'Type and target ID are required' },
        { status: 400 }
      );
    }

    const collector = new AnalyticsCollector();

    switch (type) {
      case 'post':
        // Validate post belongs to user
        const { data: post, error: postError } = await supabase
          .from('posts')
          .select('id, user_id')
          .eq('id', targetId)
          .eq('user_id', user.id)
          .single();

        if (postError || !post) {
          return NextResponse.json(
            { error: 'Post not found or access denied' },
            { status: 404 }
          );
        }

        if (delay && delay > 0) {
          // Schedule collection
          await collector.schedulePostAnalytics(targetId, user.id, {
            delay: delay * 1000, // Convert seconds to milliseconds
            priority: priority || 5,
          });
        } else {
          // Collect immediately
          await collector.collectPostAnalyticsNow(targetId, user.id);
        }
        break;

      case 'account':
        // Validate social account belongs to user
        const { data: socialAccount, error: accountError } = await supabase
          .from('social_accounts')
          .select('id, user_id')
          .eq('id', targetId)
          .eq('user_id', user.id)
          .single();

        if (accountError || !socialAccount) {
          return NextResponse.json(
            { error: 'Social account not found or access denied' },
            { status: 404 }
          );
        }

        await collector.scheduleAccountAnalytics(targetId, user.id, 'daily');
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid collection type. Supported types: post, account' },
          { status: 400 }
        );
    }

    console.log(`Analytics collection triggered for ${type}: ${targetId}`);

    return NextResponse.json({
      success: true,
      message: delay && delay > 0 
        ? `تم جدولة جمع التحليلات خلال ${delay} ثانية`
        : 'تم تشغيل جمع التحليلات',
      data: {
        type,
        targetId,
        scheduled: !!(delay && delay > 0),
        delay: delay || 0,
      },
    });

  } catch (error: any) {
    console.error('Error triggering analytics collection:', error);
    return NextResponse.json(
      {
        error: 'Failed to trigger analytics collection',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/analytics/collection - Cancel or cleanup collection jobs
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Cleaning up analytics collection jobs...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action, jobIds, daysToKeep } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let deletedCount = 0;

    switch (action) {
      case 'cancel_pending':
        // Cancel pending jobs for this user
        const { count: cancelCount, error: cancelError } = await supabase
          .from('analytics_collection_jobs')
          .update({
            status: 'cancelled',
            completed_at: new Date().toISOString(),
          })
          .eq('user_id', user.id)
          .eq('status', 'pending');

        if (cancelError) {
          throw cancelError;
        }

        deletedCount = cancelCount || 0;
        break;

      case 'delete_specific':
        if (!jobIds || !Array.isArray(jobIds)) {
          return NextResponse.json(
            { error: 'Job IDs array is required for delete_specific action' },
            { status: 400 }
          );
        }

        const { count: deleteCount, error: deleteError } = await supabase
          .from('analytics_collection_jobs')
          .delete()
          .eq('user_id', user.id)
          .in('id', jobIds)
          .in('status', ['completed', 'failed', 'cancelled']);

        if (deleteError) {
          throw deleteError;
        }

        deletedCount = deleteCount || 0;
        break;

      case 'cleanup_old':
        const collector = new AnalyticsCollector();
        deletedCount = await collector.cleanupOldJobs(daysToKeep || 30);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: cancel_pending, delete_specific, cleanup_old' },
          { status: 400 }
        );
    }

    console.log(`Analytics collection cleanup completed: ${action}, deleted: ${deletedCount}`);

    return NextResponse.json({
      success: true,
      message: `تم ${action === 'cancel_pending' ? 'إلغاء' : 'حذف'} ${deletedCount} مهمة`,
      data: {
        action,
        deletedCount,
      },
    });

  } catch (error: any) {
    console.error('Error cleaning up analytics collection:', error);
    return NextResponse.json(
      {
        error: 'Failed to cleanup analytics collection',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
