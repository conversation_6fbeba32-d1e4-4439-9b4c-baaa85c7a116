import { NextRequest, NextResponse } from 'next/server';
import { SubscriptionService } from '@/lib/stripe/subscription-service';
import { createClient } from '@/lib/supabase/server';
import { getPlanByStripePriceId } from '@/lib/stripe/config';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    const { priceId, billingCycle = 'monthly', paymentMethodId, couponId } = await request.json();

    // Validate required fields
    if (!priceId) {
      return NextResponse.json(
        { error: 'Price ID is required' },
        { status: 400 }
      );
    }

    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user details
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Validate plan exists
    const plan = getPlanByStripePriceId(priceId);
    if (!plan) {
      return NextResponse.json(
        { error: 'Invalid price ID' },
        { status: 400 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Get or create Stripe customer
    const customer = await subscriptionService.getOrCreateCustomer(
      userData.id,
      userData.email,
      userData.name
    );

    // Create subscription
    const subscription = await subscriptionService.createSubscription(
      customer.id,
      priceId,
      {
        paymentMethodId,
        couponId,
        trialDays: 14, // 14-day trial for all paid plans
      }
    );

    // Extract client secret for payment confirmation
    const clientSecret = subscription.latest_invoice?.payment_intent?.client_secret;

    return NextResponse.json({
      success: true,
      subscriptionId: subscription.id,
      clientSecret,
      status: subscription.status,
      trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    });

  } catch (error) {
    console.error('Create subscription error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to create subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
