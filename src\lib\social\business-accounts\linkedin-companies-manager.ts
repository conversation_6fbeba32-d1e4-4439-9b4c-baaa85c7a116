/**
 * LinkedIn Companies Manager
 * Handles LinkedIn Company Pages integration and management
 */

import { createClient } from '@/lib/supabase/client';

export interface LinkedInOrganization {
  id: string;
  name: string;
  vanityName?: string;
  logoV2?: {
    original?: string;
    cropped?: string;
  };
  website?: {
    localized?: {
      [key: string]: string;
    };
    preferredLocale?: {
      country: string;
      language: string;
    };
  };
  industries?: string[];
  description?: {
    localized?: {
      [key: string]: string;
    };
    preferredLocale?: {
      country: string;
      language: string;
    };
  };
  staffCount?: number;
  staffCountRange?: {
    start: number;
    end: number;
  };
  locations?: Array<{
    country: string;
    city?: string;
    postalCode?: string;
  }>;
  followerCount?: number;
}

export interface LinkedInOrganizationAcl {
  organization: string; // Organization URN
  role: string; // ADMINISTRATOR, CONTENT_ADMIN, etc.
  state: string; // APPROVED, PENDING, etc.
}

export interface LinkedInOrganizationsResponse {
  elements: LinkedInOrganizationAcl[];
  paging?: {
    count: number;
    start: number;
    total: number;
  };
}

export class LinkedInCompaniesManager {
  private supabase = createClient();

  /**
   * Fetch user's LinkedIn Organizations using their access token
   */
  async fetchUserOrganizations(userAccessToken: string): Promise<LinkedInOrganization[]> {
    try {
      console.log('Fetching LinkedIn Organizations for user...');

      // First, get organization ACLs (permissions)
      const aclResponse = await fetch(
        'https://api.linkedin.com/v2/organizationAcls?q=roleAssignee&projection=(elements*(organization~(id,name,vanityName,logoV2,website,industries,description,staffCount,staffCountRange,locations,followerCount),role,state))',
        {
          headers: {
            'Authorization': `Bearer ${userAccessToken}`,
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0',
          }
        }
      );

      if (!aclResponse.ok) {
        const errorData = await aclResponse.json();
        throw new Error(`LinkedIn API error: ${errorData.message || 'Unknown error'}`);
      }

      const aclData: LinkedInOrganizationsResponse = await aclResponse.json();
      
      console.log(`Found ${aclData.elements.length} LinkedIn Organizations`);
      
      // Filter organizations where user has admin or content admin permissions
      const manageableOrganizations = aclData.elements.filter(acl => 
        acl.state === 'APPROVED' && 
        (acl.role === 'ADMINISTRATOR' || acl.role === 'CONTENT_ADMIN')
      );

      console.log(`User can manage ${manageableOrganizations.length} organizations`);
      
      // Extract organization data from the ACL response
      const organizations: LinkedInOrganization[] = manageableOrganizations.map(acl => {
        // The organization data is embedded in the ACL response due to projection
        const orgData = (acl as any)['organization~'];
        return {
          id: orgData.id,
          name: orgData.name?.localized?.en_US || orgData.name,
          vanityName: orgData.vanityName,
          logoV2: orgData.logoV2,
          website: orgData.website,
          industries: orgData.industries,
          description: orgData.description,
          staffCount: orgData.staffCount,
          staffCountRange: orgData.staffCountRange,
          locations: orgData.locations,
          followerCount: orgData.followerCount || 0,
        };
      });

      return organizations;

    } catch (error) {
      console.error('Error fetching LinkedIn Organizations:', error);
      throw error;
    }
  }

  /**
   * Store LinkedIn Organizations in database for a user
   */
  async storeUserOrganizations(
    userId: string, 
    socialAccountId: string, 
    organizations: LinkedInOrganization[]
  ): Promise<void> {
    try {
      console.log(`Storing ${organizations.length} LinkedIn Organizations for user ${userId}`);

      // Prepare organizations data for storage
      const organizationsData = organizations.map(org => ({
        social_account_id: socialAccountId,
        organization_id: org.id,
        organization_name: org.name,
        organization_urn: `urn:li:organization:${org.id}`,
        logo_url: org.logoV2?.original || org.logoV2?.cropped,
        website: this.extractWebsiteUrl(org.website),
        industry: org.industries?.[0] || null,
        description: this.extractDescription(org.description),
        employee_count: this.formatEmployeeCount(org.staffCount, org.staffCountRange),
        headquarters_country: org.locations?.[0]?.country,
        headquarters_city: org.locations?.[0]?.city,
        follower_count: org.followerCount || 0,
        permissions: ['CONTENT_ADMIN'], // Default permission
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      // Delete existing organizations for this social account
      const { error: deleteError } = await this.supabase
        .from('linkedin_companies')
        .delete()
        .eq('social_account_id', socialAccountId);

      if (deleteError) {
        console.error('Error deleting existing organizations:', deleteError);
        throw deleteError;
      }

      // Insert new organizations
      const { error: insertError } = await this.supabase
        .from('linkedin_companies')
        .insert(organizationsData);

      if (insertError) {
        console.error('Error storing LinkedIn Organizations:', insertError);
        throw insertError;
      }

      console.log('Successfully stored LinkedIn Organizations');

    } catch (error) {
      console.error('Error storing LinkedIn Organizations:', error);
      throw error;
    }
  }

  /**
   * Get stored LinkedIn Organizations for a social account
   */
  async getStoredOrganizations(socialAccountId: string): Promise<any[]> {
    try {
      const { data: organizations, error } = await this.supabase
        .from('linkedin_companies')
        .select('*')
        .eq('social_account_id', socialAccountId)
        .order('organization_name');

      if (error) {
        console.error('Error fetching stored organizations:', error);
        throw error;
      }

      return organizations || [];

    } catch (error) {
      console.error('Error getting stored organizations:', error);
      throw error;
    }
  }

  /**
   * Update selected organization for posting
   */
  async updateSelectedOrganization(
    socialAccountId: string, 
    selectedOrganizationId: string
  ): Promise<void> {
    try {
      console.log(`Updating selected organization for account ${socialAccountId}: ${selectedOrganizationId}`);

      // Update the social account with selected organization
      const { error } = await this.supabase
        .from('social_accounts')
        .update({
          business_account_id: selectedOrganizationId,
          business_account_type: 'COMPANY',
          updated_at: new Date().toISOString(),
        })
        .eq('id', socialAccountId);

      if (error) {
        console.error('Error updating selected organization:', error);
        throw error;
      }

      console.log('Successfully updated selected organization');

    } catch (error) {
      console.error('Error updating selected organization:', error);
      throw error;
    }
  }

  /**
   * Get organization data for posting
   */
  async getOrganizationData(
    socialAccountId: string, 
    organizationId: string
  ): Promise<any | null> {
    try {
      const { data: organization, error } = await this.supabase
        .from('linkedin_companies')
        .select('*')
        .eq('social_account_id', socialAccountId)
        .eq('organization_id', organizationId)
        .single();

      if (error) {
        console.error('Error fetching organization data:', error);
        return null;
      }

      return organization;

    } catch (error) {
      console.error('Error getting organization data:', error);
      return null;
    }
  }

  /**
   * Validate organization permissions for posting
   */
  async validateOrganizationPermissions(
    userAccessToken: string, 
    organizationId: string
  ): Promise<boolean> {
    try {
      console.log(`Validating permissions for organization ${organizationId}`);

      const response = await fetch(
        `https://api.linkedin.com/v2/organizationAcls?q=roleAssignee&organization=urn:li:organization:${organizationId}`,
        {
          headers: {
            'Authorization': `Bearer ${userAccessToken}`,
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0',
          }
        }
      );

      if (!response.ok) {
        console.error('Failed to validate organization permissions');
        return false;
      }

      const data = await response.json();
      const acls = data.elements || [];

      // Check if user has required permissions
      const hasAdminPermission = acls.some((acl: any) => 
        acl.role === 'ADMINISTRATOR' && acl.state === 'APPROVED'
      );
      const hasContentPermission = acls.some((acl: any) => 
        acl.role === 'CONTENT_ADMIN' && acl.state === 'APPROVED'
      );

      console.log(`Organization permissions - ADMIN: ${hasAdminPermission}, CONTENT_ADMIN: ${hasContentPermission}`);

      return hasAdminPermission || hasContentPermission;

    } catch (error) {
      console.error('Error validating organization permissions:', error);
      return false;
    }
  }

  /**
   * Refresh organization data
   */
  async refreshOrganizations(
    socialAccountId: string, 
    userAccessToken: string
  ): Promise<void> {
    try {
      console.log('Refreshing LinkedIn Organizations...');

      // Fetch fresh organizations data
      const organizations = await this.fetchUserOrganizations(userAccessToken);
      
      // Update stored organizations
      await this.storeUserOrganizations('', socialAccountId, organizations);

      console.log('Successfully refreshed organizations');

    } catch (error) {
      console.error('Error refreshing organizations:', error);
      throw error;
    }
  }

  /**
   * Get organization posting configuration
   */
  async getOrganizationPostingConfig(socialAccountId: string): Promise<{
    selectedOrganizationId: string | null;
    availableOrganizations: any[];
    hasValidPermissions: boolean;
  }> {
    try {
      // Get social account with selected organization
      const { data: account, error: accountError } = await this.supabase
        .from('social_accounts')
        .select('business_account_id')
        .eq('id', socialAccountId)
        .single();

      if (accountError) {
        throw accountError;
      }

      // Get available organizations
      const availableOrganizations = await this.getStoredOrganizations(socialAccountId);

      // For now, assume permissions are valid if organizations exist
      // In production, you might want to validate with LinkedIn API
      const hasValidPermissions = availableOrganizations.length > 0;

      return {
        selectedOrganizationId: account?.business_account_id || null,
        availableOrganizations,
        hasValidPermissions,
      };

    } catch (error) {
      console.error('Error getting organization posting config:', error);
      throw error;
    }
  }

  // Helper methods
  private extractWebsiteUrl(website: any): string | null {
    if (!website?.localized) return null;
    
    const locale = website.preferredLocale;
    const localeKey = locale ? `${locale.language}_${locale.country}` : 'en_US';
    
    return website.localized[localeKey] || 
           website.localized['en_US'] || 
           Object.values(website.localized)[0] as string || 
           null;
  }

  private extractDescription(description: any): string | null {
    if (!description?.localized) return null;
    
    const locale = description.preferredLocale;
    const localeKey = locale ? `${locale.language}_${locale.country}` : 'en_US';
    
    return description.localized[localeKey] || 
           description.localized['en_US'] || 
           Object.values(description.localized)[0] as string || 
           null;
  }

  private formatEmployeeCount(staffCount?: number, staffCountRange?: { start: number; end: number }): string | null {
    if (staffCount) {
      return staffCount.toString();
    }
    
    if (staffCountRange) {
      return `${staffCountRange.start}-${staffCountRange.end}`;
    }
    
    return null;
  }
}
