import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Debug LinkedIn userinfo endpoint
 * GET /api/debug/linkedin-userinfo?access_token=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accessToken = searchParams.get('access_token');

    if (!accessToken) {
      return NextResponse.json({
        error: 'Missing access_token parameter',
        usage: 'GET /api/debug/linkedin-userinfo?access_token=YOUR_TOKEN'
      }, { status: 400 });
    }

    console.log('Testing LinkedIn userinfo endpoint with token:', accessToken.substring(0, 10) + '...');

    // Test different LinkedIn API endpoints
    const endpoints = [
      {
        name: 'userinfo (v2)',
        url: 'https://api.linkedin.com/v2/userinfo',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        }
      },
      {
        name: 'people/~',
        url: 'https://api.linkedin.com/v2/people/~',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        }
      },
      {
        name: 'people/~ with projection',
        url: 'https://api.linkedin.com/v2/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams))',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        }
      }
    ];

    const results = [];

    for (const endpoint of endpoints) {
      try {
        console.log(`Testing ${endpoint.name}: ${endpoint.url}`);
        
        const response = await fetch(endpoint.url, {
          headers: endpoint.headers,
        });

        const responseText = await response.text();
        let responseData;
        
        try {
          responseData = JSON.parse(responseText);
        } catch {
          responseData = responseText;
        }

        results.push({
          endpoint: endpoint.name,
          url: endpoint.url,
          status: response.status,
          statusText: response.statusText,
          success: response.ok,
          headers: Object.fromEntries(response.headers.entries()),
          data: responseData
        });

        console.log(`${endpoint.name} result:`, {
          status: response.status,
          success: response.ok,
          data: typeof responseData === 'object' ? JSON.stringify(responseData).substring(0, 200) : responseData.substring(0, 200)
        });

      } catch (error) {
        results.push({
          endpoint: endpoint.name,
          url: endpoint.url,
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false
        });

        console.error(`${endpoint.name} error:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'LinkedIn userinfo endpoint test completed',
      accessToken: accessToken.substring(0, 10) + '...',
      results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    });

  } catch (error) {
    console.error('LinkedIn userinfo debug error:', error);
    return NextResponse.json({
      error: 'Debug test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Handle POST requests for testing with token in body
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { access_token } = body;

    if (!access_token) {
      return NextResponse.json({
        error: 'Missing access_token in request body'
      }, { status: 400 });
    }

    // Redirect to GET with token as query parameter
    const url = new URL(request.url);
    url.searchParams.set('access_token', access_token);
    
    return NextResponse.redirect(url.toString());

  } catch (error) {
    return NextResponse.json({
      error: 'Invalid JSON in request body'
    }, { status: 400 });
  }
}
