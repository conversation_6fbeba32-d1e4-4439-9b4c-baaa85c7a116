'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  CheckCircle, 
  Users, 
  Globe, 
  Building, 
  Verified, 
  MapPin,
  Facebook,
  Linkedin,
  Instagram,
  Twitter
} from 'lucide-react';
import { BusinessAccount } from '@/lib/social/business-accounts/business-account-types';

interface BusinessAccountCardProps {
  account: BusinessAccount;
  isSelected: boolean;
  onSelect: (accountId: string) => void;
  className?: string;
}

export function BusinessAccountCard({ 
  account, 
  isSelected, 
  onSelect,
  className = ''
}: BusinessAccountCardProps) {
  
  const formatFollowerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const formatLocation = (account: BusinessAccount) => {
    const headquarters = account.metadata?.headquarters;
    if (!headquarters) return null;
    
    const parts = [];
    if (headquarters.city) parts.push(headquarters.city);
    if (headquarters.country) parts.push(headquarters.country);
    
    return parts.length > 0 ? parts.join(', ') : null;
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toUpperCase()) {
      case 'FACEBOOK':
        return <Facebook className="w-4 h-4 text-blue-600" />;
      case 'LINKEDIN':
        return <Linkedin className="w-4 h-4 text-blue-700" />;
      case 'INSTAGRAM':
        return <Instagram className="w-4 h-4 text-pink-600" />;
      case 'TWITTER':
        return <Twitter className="w-4 h-4 text-blue-400" />;
      default:
        return <Building className="w-4 h-4" />;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toUpperCase()) {
      case 'FACEBOOK':
        return 'bg-blue-500';
      case 'LINKEDIN':
        return 'bg-blue-600';
      case 'INSTAGRAM':
        return 'bg-pink-500';
      case 'TWITTER':
        return 'bg-blue-400';
      default:
        return 'bg-gray-500';
    }
  };

  const getAccountTypeLabel = (type: string, platform: string) => {
    switch (type) {
      case 'PAGE':
        return 'صفحة';
      case 'COMPANY':
        return 'شركة';
      case 'BUSINESS':
        return 'حساب تجاري';
      case 'PERSONAL':
        return 'حساب شخصي';
      default:
        return type;
    }
  };

  return (
    <div
      className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
        isSelected
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
          : 'border-gray-200 hover:border-gray-300'
      } ${className}`}
      onClick={() => onSelect(account.id)}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {/* Platform indicator */}
          <div className={`w-2 h-12 rounded-full ${getPlatformColor(account.platform)}`} />
          
          {/* Account avatar */}
          <Avatar className="w-12 h-12">
            <AvatarImage src={account.profilePictureUrl} alt={account.name} />
            <AvatarFallback>
              {account.type === 'COMPANY' ? (
                <Building className="w-6 h-6" />
              ) : (
                account.name.substring(0, 2).toUpperCase()
              )}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            {/* Account name and verification */}
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-sm">{account.name}</h3>
              {account.metadata?.isVerified && (
                <Verified className="w-4 h-4 text-blue-500" />
              )}
              {isSelected && (
                <CheckCircle className="w-4 h-4 text-green-500" />
              )}
            </div>
            
            {/* Account metadata */}
            <div className="flex items-center gap-4 mt-1 flex-wrap">
              {/* Platform and type */}
              <div className="flex items-center gap-1">
                {getPlatformIcon(account.platform)}
                <span className="text-xs text-muted-foreground">
                  {getAccountTypeLabel(account.type, account.platform)}
                </span>
              </div>
              
              {/* Follower count */}
              {account.followerCount && account.followerCount > 0 && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Users className="w-3 h-3" />
                  {formatFollowerCount(account.followerCount)}
                </div>
              )}
              
              {/* Category/Industry */}
              {account.category && (
                <Badge variant="secondary" className="text-xs">
                  {account.category}
                </Badge>
              )}
              
              {/* Employee count (LinkedIn) */}
              {account.metadata?.employeeCount && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Building className="w-3 h-3" />
                  {account.metadata.employeeCount} موظف
                </div>
              )}
              
              {/* Fan count (Facebook) */}
              {account.metadata?.fanCount && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Users className="w-3 h-3" />
                  {formatFollowerCount(account.metadata.fanCount)} معجب
                </div>
              )}
              
              {/* Location */}
              {formatLocation(account) && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <MapPin className="w-3 h-3" />
                  {formatLocation(account)}
                </div>
              )}
              
              {/* Website */}
              {account.website && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Globe className="w-3 h-3" />
                  موقع إلكتروني
                </div>
              )}
            </div>
            
            {/* Description */}
            {account.metadata?.description && (
              <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                {account.metadata.description}
              </p>
            )}
            
            {/* About (Facebook) */}
            {account.metadata?.about && (
              <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                {account.metadata.about}
              </p>
            )}
          </div>
        </div>
        
        {/* Selection indicator */}
        {isSelected && (
          <Badge variant="default" className="bg-green-500">
            مختارة
          </Badge>
        )}
      </div>
    </div>
  );
}
