import { SocialAccount, PostContent } from '../social-media-publisher';
import { SchedulerLogger } from '../scheduler-logger';
import TwitterPublisherV2 from '@/lib/social/publishers/twitter-publisher-v2';
import ContentFormatter from '@/lib/social/content-formatter';

export interface TwitterPublishResult {
  postId: string;
  url: string;
}

/**
 * Enhanced Twitter publisher using Twitter API v2 with OAuth 2.0
 * Integrates with the new TwitterPublisherV2 service
 */
export class TwitterPublisher {
  private logger: SchedulerLogger;
  private publisherV2: TwitterPublisherV2;
  private contentFormatter: ContentFormatter;
  private API_BASE = 'https://api.twitter.com/2';

  constructor() {
    this.logger = new SchedulerLogger('twitter-publisher');
    this.publisherV2 = new TwitterPublisherV2();
    this.contentFormatter = new ContentFormatter();
  }

  /**
   * Publish a post to Twitter using enhanced V2 publisher
   */
  async publish(account: SocialAccount, content: PostContent): Promise<TwitterPublishResult> {
    try {
      this.logger.info('Publishing to Twitter with V2 publisher', {
        accountId: account.account_id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
      });

      // Format content for Twitter (280 character limit)
      const formattedContent = this.contentFormatter.formatForPlatform(content.content, 'twitter');

      if (!formattedContent.isValid) {
        throw new Error(`Content validation failed: ${formattedContent.warnings.join(', ')}`);
      }

      if (formattedContent.warnings.length > 0) {
        this.logger.warn('Content formatting warnings', { warnings: formattedContent.warnings });
      }

      // Prepare content for V2 publisher
      const publishContent = {
        content: formattedContent.text,
        mediaUrl: content.mediaUrl,
        mediaType: (content.mediaType || (content.mediaUrl ? this.detectMediaType(content.mediaUrl) : undefined)) as "IMAGE" | "VIDEO" | "GIF" | undefined
      };

      // Use V2 publisher for real API calls
      const result = await this.publisherV2.publishPost(account, publishContent);

      if (!result.success) {
        throw new Error(result.error || 'Twitter publishing failed');
      }

      this.logger.info('Tweet published successfully with V2', {
        postId: result.postId,
        postUrl: result.url,
      });

      return {
        postId: result.postId!,
        url: result.url!,
      };

    } catch (error) {
      this.logger.error('Failed to publish tweet with V2', error);
      throw error;
    }
  }

  /**
   * Upload media to Twitter
   */
  private async uploadMedia(account: SocialAccount, mediaUrl: string): Promise<string> {
    try {
      this.logger.info('Uploading media to Twitter', { mediaUrl });

      // Download media from URL
      const mediaResponse = await fetch(mediaUrl);
      if (!mediaResponse.ok) {
        throw new Error(`Failed to download media: ${mediaResponse.statusText}`);
      }

      const mediaBuffer = await mediaResponse.arrayBuffer();
      const mediaType = mediaResponse.headers.get('content-type') || 'image/jpeg';

      // Upload to Twitter
      const uploadResponse = await this.uploadMediaToTwitter(account, mediaBuffer, mediaType);

      this.logger.info('Media uploaded successfully', {
        mediaId: uploadResponse.media_id_string,
      });

      return uploadResponse.media_id_string;

    } catch (error) {
      this.logger.error('Failed to upload media', error);
      throw error;
    }
  }

  /**
   * Upload media buffer to Twitter
   */
  private async uploadMediaToTwitter(
    account: SocialAccount,
    mediaBuffer: ArrayBuffer,
    mediaType: string
  ): Promise<any> {
    const uploadUrl = 'https://upload.twitter.com/1.1/media/upload.json';

    const formData = new FormData();
    formData.append('media', new Blob([mediaBuffer], { type: mediaType }));

    const response = await fetch(uploadUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${account.access_token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Twitter media upload failed: ${response.status} ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Make authenticated request to Twitter API
   */
  private async makeTwitterRequest(
    account: SocialAccount,
    method: string,
    endpoint: string,
    data?: any
  ): Promise<any> {
    const url = `${this.API_BASE}${endpoint}`;

    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${account.access_token}`,
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Handle specific Twitter errors
      if (response.status === 429) {
        const retryAfter = response.headers.get('x-rate-limit-reset');
        throw new Error(`Rate limit exceeded. Retry after ${retryAfter || '15 minutes'}`);
      }

      if (response.status === 401) {
        throw new Error('Twitter authentication failed. Token may be expired or invalid.');
      }

      if (response.status === 403) {
        throw new Error('Twitter API access forbidden. Check account permissions.');
      }

      const errorMessage = errorData.detail || errorData.error || `Twitter API error: ${response.status}`;
      throw new Error(errorMessage);
    }

    return await response.json();
  }

  /**
   * Test connection to Twitter account using V2 publisher
   */
  async testConnection(account: SocialAccount): Promise<any> {
    try {
      this.logger.info('Testing Twitter connection with V2', {
        accountId: account.account_id,
      });

      // Use V2 publisher for connection testing
      const result = await this.publisherV2.testConnection(account);

      this.logger.info('Twitter connection test successful with V2', {
        username: result.accountInfo?.username,
        name: result.accountInfo?.name,
      });

      return result;

    } catch (error) {
      this.logger.error('Twitter connection test failed with V2', error);
      throw error;
    }
  }

  /**
   * Detect media type from URL
   */
  private detectMediaType(url: string): 'IMAGE' | 'VIDEO' | 'GIF' {
    const urlLower = url.toLowerCase();

    if (urlLower.includes('.mp4') || urlLower.includes('.mov') || urlLower.includes('.avi')) {
      return 'VIDEO';
    } else if (urlLower.includes('.gif')) {
      return 'GIF';
    } else {
      return 'IMAGE';
    }
  }

  /**
   * Get account information
   */
  async getAccountInfo(account: SocialAccount): Promise<any> {
    try {
      const response = await this.makeTwitterRequest(
        account,
        'GET',
        `/users/${account.account_id}?user.fields=public_metrics,verified`
      );

      return {
        id: response.data.id,
        username: response.data.username,
        name: response.data.name,
        verified: response.data.verified,
        followers_count: response.data.public_metrics?.followers_count,
        following_count: response.data.public_metrics?.following_count,
        tweet_count: response.data.public_metrics?.tweet_count,
      };

    } catch (error) {
      this.logger.error('Failed to get Twitter account info', error);
      throw error;
    }
  }

  /**
   * Delete a tweet
   */
  async deletePost(account: SocialAccount, postId: string): Promise<boolean> {
    try {
      this.logger.info('Deleting Twitter post', { postId });

      await this.makeTwitterRequest(account, 'DELETE', `/tweets/${postId}`);

      this.logger.info('Twitter post deleted successfully', { postId });
      return true;

    } catch (error) {
      this.logger.error('Failed to delete Twitter post', error, { postId });
      throw error;
    }
  }

  /**
   * Get post analytics
   */
  async getPostAnalytics(account: SocialAccount, postId: string): Promise<any> {
    try {
      const response = await this.makeTwitterRequest(
        account,
        'GET',
        `/tweets/${postId}?tweet.fields=public_metrics,created_at`
      );

      return {
        id: response.data.id,
        text: response.data.text,
        created_at: response.data.created_at,
        metrics: response.data.public_metrics,
      };

    } catch (error) {
      this.logger.error('Failed to get Twitter post analytics', error);
      throw error;
    }
  }

  /**
   * Validate Twitter credentials
   */
  async validateCredentials(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/users/me`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      return response.ok;

    } catch (error) {
      this.logger.error('Failed to validate Twitter credentials', error);
      return false;
    }
  }
}
