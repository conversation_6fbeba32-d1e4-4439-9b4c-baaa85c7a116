#!/usr/bin/env node

/**
 * Manual Social Media Integration Verification
 * Comprehensive code analysis and structure verification
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Manual Social Media Integration Verification\n');

let totalChecks = 0;
let passedChecks = 0;
let results = [];

function verify(checkName, condition, details = '') {
  totalChecks++;
  const status = condition ? '✅' : '❌';
  const result = `${status} ${checkName}`;
  
  console.log(result);
  results.push(result);
  
  if (details) {
    console.log(`   ${details}`);
    results.push(`   ${details}`);
  }
  
  if (condition) passedChecks++;
  return condition;
}

function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, filePath));
}

function fileContains(filePath, searchText) {
  if (!fileExists(filePath)) return false;
  const content = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
  return content.includes(searchText);
}

function analyzeFile(filePath) {
  if (!fileExists(filePath)) return { exists: false, content: '', lines: 0 };
  const content = fs.readFileSync(path.join(__dirname, filePath), 'utf8');
  return {
    exists: true,
    content,
    lines: content.split('\n').length,
    size: content.length
  };
}

console.log('📁 Phase 1: File Structure Verification\n');

// Core API files
const apiFiles = [
  'src/app/api/social/connect/route.ts',
  'src/app/api/social/callback/[platform]/route.ts', 
  'src/app/api/social/disconnect/route.ts'
];

apiFiles.forEach(file => {
  const analysis = analyzeFile(file);
  verify(`API file exists: ${file}`, analysis.exists, `Lines: ${analysis.lines}, Size: ${analysis.size} chars`);
});

// Component files
const componentFiles = [
  'src/components/social/social-connection-manager.tsx'
];

componentFiles.forEach(file => {
  const analysis = analyzeFile(file);
  verify(`Component file exists: ${file}`, analysis.exists, `Lines: ${analysis.lines}, Size: ${analysis.size} chars`);
});

// Page files
const pageFiles = [
  'src/app/social/page.tsx',
  'src/app/test-social/page.tsx'
];

pageFiles.forEach(file => {
  const analysis = analyzeFile(file);
  verify(`Page file exists: ${file}`, analysis.exists, `Lines: ${analysis.lines}, Size: ${analysis.size} chars`);
});

console.log('\n🔗 Phase 2: API Implementation Analysis\n');

// Analyze connect API
const connectApi = analyzeFile('src/app/api/social/connect/route.ts');
if (connectApi.exists) {
  verify('Connect API has GET method', connectApi.content.includes('export async function GET'));
  verify('Connect API has POST method', connectApi.content.includes('export async function POST'));
  verify('Connect API has authentication', connectApi.content.includes('auth.getUser'));
  verify('Connect API has platform validation', connectApi.content.includes('z.enum') || connectApi.content.includes('connectSchema'));
  verify('Connect API supports Twitter', connectApi.content.includes('TWITTER'));
  verify('Connect API supports Facebook', connectApi.content.includes('FACEBOOK'));
  verify('Connect API supports LinkedIn', connectApi.content.includes('LINKEDIN'));
  verify('Connect API supports Instagram', connectApi.content.includes('INSTAGRAM'));
  verify('Connect API has OAuth URL generation', connectApi.content.includes('authUrl'));
  verify('Connect API has error handling', connectApi.content.includes('try {') && connectApi.content.includes('catch'));
}

// Analyze callback API
const callbackApi = analyzeFile('src/app/api/social/callback/[platform]/route.ts');
if (callbackApi.exists) {
  verify('Callback API has GET method', callbackApi.content.includes('export async function GET'));
  verify('Callback API has platform handling', callbackApi.content.includes('params.platform'));
  verify('Callback API has Twitter handler', callbackApi.content.includes('handleTwitterCallback'));
  verify('Callback API has Facebook handler', callbackApi.content.includes('handleFacebookCallback'));
  verify('Callback API has LinkedIn handler', callbackApi.content.includes('handleLinkedInCallback'));
  verify('Callback API has authentication', callbackApi.content.includes('auth.getUser'));
  verify('Callback API has error handling', callbackApi.content.includes('try {') && callbackApi.content.includes('catch'));
  verify('Callback API has redirect handling', callbackApi.content.includes('NextResponse.redirect'));
}

// Analyze disconnect API
const disconnectApi = analyzeFile('src/app/api/social/disconnect/route.ts');
if (disconnectApi.exists) {
  verify('Disconnect API has POST method', disconnectApi.content.includes('export async function POST'));
  verify('Disconnect API has authentication', disconnectApi.content.includes('auth.getUser'));
  verify('Disconnect API has validation', disconnectApi.content.includes('disconnectSchema') || disconnectApi.content.includes('z.object'));
  verify('Disconnect API has user verification', disconnectApi.content.includes('user_id'));
  verify('Disconnect API has activity logging', disconnectApi.content.includes('activities'));
}

console.log('\n🎨 Phase 3: Component Analysis\n');

// Analyze SocialConnectionManager
const connectionManager = analyzeFile('src/components/social/social-connection-manager.tsx');
if (connectionManager.exists) {
  verify('Component has React imports', connectionManager.content.includes('import React'));
  verify('Component has state management', connectionManager.content.includes('useState'));
  verify('Component has useEffect', connectionManager.content.includes('useEffect'));
  verify('Component has loadConnectionStatus', connectionManager.content.includes('loadConnectionStatus'));
  verify('Component has connectPlatform', connectionManager.content.includes('connectPlatform'));
  verify('Component has disconnectAccount', connectionManager.content.includes('disconnectAccount'));
  verify('Component has platform icons', connectionManager.content.includes('getPlatformIcon'));
  verify('Component has platform colors', connectionManager.content.includes('getPlatformColor'));
  verify('Component has status indicators', connectionManager.content.includes('getStatusIcon'));
  verify('Component has error handling', connectionManager.content.includes('toast.error'));
  verify('Component has TypeScript interfaces', connectionManager.content.includes('interface'));
  verify('Component exports default', connectionManager.content.includes('export default'));
}

console.log('\n📱 Phase 4: Page Implementation Analysis\n');

// Analyze social page
const socialPage = analyzeFile('src/app/social/page.tsx');
if (socialPage.exists) {
  verify('Social page imports SocialConnectionManager', socialPage.content.includes('SocialConnectionManager'));
  verify('Social page has authentication check', socialPage.content.includes('checkAuth'));
  verify('Social page has stats loading', socialPage.content.includes('loadStats') || socialPage.content.includes('stats'));
  verify('Social page has callback handling', socialPage.content.includes('handleCallbackParams'));
  verify('Social page has useEffect', socialPage.content.includes('useEffect'));
  verify('Social page has useState', socialPage.content.includes('useState'));
  verify('Social page has responsive design', socialPage.content.includes('responsive') || socialPage.content.includes('grid'));
}

// Analyze test page
const testPage = analyzeFile('src/app/test-social/page.tsx');
if (testPage.exists) {
  verify('Test page has authentication check', testPage.content.includes('checkAuth'));
  verify('Test page has API testing functions', testPage.content.includes('testSocialConnectionAPI') || testPage.content.includes('testTwitterConnectionFlow'));
  verify('Test page has comprehensive testing', testPage.content.includes('runFullSocialTest') || testPage.content.includes('comprehensive'));
  verify('Test page has error handling', testPage.content.includes('toast.error'));
}

console.log('\n🔐 Phase 5: OAuth Implementation Analysis\n');

if (connectApi.exists) {
  // Twitter OAuth
  verify('Twitter OAuth implementation', 
    connectApi.content.includes('TwitterService') || connectApi.content.includes('oauth_token'));
  
  // Facebook OAuth
  verify('Facebook OAuth implementation',
    connectApi.content.includes('facebook.com/v18.0/dialog/oauth'));
  
  // LinkedIn OAuth
  verify('LinkedIn OAuth implementation',
    connectApi.content.includes('linkedin.com/oauth/v2/authorization'));
  
  // Instagram OAuth
  verify('Instagram OAuth implementation',
    connectApi.content.includes('instagram') && connectApi.content.includes('FACEBOOK'));
}

if (callbackApi.exists) {
  // Token exchange implementations
  verify('Facebook token exchange', callbackApi.content.includes('oauth/access_token'));
  verify('LinkedIn token exchange', callbackApi.content.includes('linkedin.com/oauth/v2/accessToken'));
  verify('Profile fetching', callbackApi.content.includes('graph.facebook.com') || callbackApi.content.includes('api.linkedin.com'));
}

console.log('\n🔒 Phase 6: Security Implementation Analysis\n');

// Check authentication patterns
const authPatterns = [
  'auth.getUser',
  'user_id',
  'createClient',
  'supabase.auth'
];

authPatterns.forEach(pattern => {
  const hasInConnect = connectApi.exists && connectApi.content.includes(pattern);
  const hasInCallback = callbackApi.exists && callbackApi.content.includes(pattern);
  const hasInDisconnect = disconnectApi.exists && disconnectApi.content.includes(pattern);
  
  verify(`Security pattern '${pattern}' implemented`, hasInConnect || hasInCallback || hasInDisconnect);
});

console.log('\n📊 Phase 7: Error Handling Analysis\n');

// Check error handling patterns
const errorPatterns = [
  'try {',
  'catch',
  'NextResponse.json',
  'status: 401',
  'status: 400',
  'status: 500'
];

errorPatterns.forEach(pattern => {
  const hasInApis = (connectApi.exists && connectApi.content.includes(pattern)) ||
                   (callbackApi.exists && callbackApi.content.includes(pattern)) ||
                   (disconnectApi.exists && disconnectApi.content.includes(pattern));
  
  verify(`Error handling pattern '${pattern}' implemented`, hasInApis);
});

console.log('\n🌐 Phase 8: Platform Support Analysis\n');

// Check platform support
const platforms = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK', 'SNAPCHAT'];
platforms.forEach(platform => {
  const supported = connectApi.exists && connectApi.content.includes(platform);
  verify(`Platform ${platform} supported`, supported);
});

console.log('\n📘 Phase 9: TypeScript Implementation Analysis\n');

// Check TypeScript usage
const tsPatterns = [
  'interface',
  'type',
  'useState<',
  ': string',
  ': boolean'
];

tsPatterns.forEach(pattern => {
  const hasInFiles = (connectApi.exists && connectApi.content.includes(pattern)) ||
                    (connectionManager.exists && connectionManager.content.includes(pattern)) ||
                    (socialPage.exists && socialPage.content.includes(pattern));
  
  verify(`TypeScript pattern '${pattern}' used`, hasInFiles);
});

console.log('\n🔗 Phase 10: Integration Features Analysis\n');

// Check integration features
if (connectApi.exists) {
  verify('Connection status tracking', connectApi.content.includes('status') && connectApi.content.includes('expired'));
  verify('Activity logging', connectApi.content.includes('activities'));
  verify('Token expiry handling', connectApi.content.includes('expires_at'));
}

if (connectionManager.exists) {
  verify('Real-time updates', connectionManager.content.includes('loadConnectionStatus'));
  verify('Platform management', connectionManager.content.includes('connectPlatform') && connectionManager.content.includes('disconnectAccount'));
}

// Final Results
console.log('\n' + '='.repeat(80));
console.log('🎯 MANUAL SOCIAL MEDIA INTEGRATION VERIFICATION RESULTS');
console.log('='.repeat(80));
console.log(`📊 Checks Passed: ${passedChecks}/${totalChecks}`);
console.log(`📈 Success Rate: ${Math.round((passedChecks / totalChecks) * 100)}%`);

if (passedChecks >= Math.floor(totalChecks * 0.95)) {
  console.log('\n🎉 SOCIAL MEDIA INTEGRATION IMPLEMENTATION EXCELLENT!');
  console.log('✅ All critical components verified');
  console.log('✅ API implementation complete');
  console.log('✅ Security measures in place');
  console.log('✅ Components properly structured');
  console.log('✅ Error handling comprehensive');
  console.log('✅ Platform support complete');
  console.log('✅ TypeScript implementation solid');
  console.log('\n🚀 CODE STRUCTURE READY FOR PRODUCTION!');
} else if (passedChecks >= Math.floor(totalChecks * 0.85)) {
  console.log('\n⚠️ SOCIAL MEDIA INTEGRATION MOSTLY COMPLETE');
  console.log('✅ Core implementation solid');
  console.log('⚠️ Some minor improvements possible');
} else {
  console.log('\n❌ SOCIAL MEDIA INTEGRATION NEEDS WORK');
  console.log('❌ Multiple issues in implementation');
}

console.log('\n📋 Detailed Verification Results:');
console.log('='.repeat(80));
results.forEach(result => console.log(result));

console.log('\n📁 Implementation Summary:');
console.log(`• API Files: ${apiFiles.filter(f => fileExists(f)).length}/${apiFiles.length} complete`);
console.log(`• Component Files: ${componentFiles.filter(f => fileExists(f)).length}/${componentFiles.length} complete`);
console.log(`• Page Files: ${pageFiles.filter(f => fileExists(f)).length}/${pageFiles.length} complete`);
console.log(`• Platform Support: ${platforms.filter(p => connectApi.exists && connectApi.content.includes(p)).length}/${platforms.length} platforms`);

process.exit(passedChecks >= Math.floor(totalChecks * 0.95) ? 0 : 1);
