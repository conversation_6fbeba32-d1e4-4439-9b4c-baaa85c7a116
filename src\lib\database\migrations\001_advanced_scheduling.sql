-- =====================================================
-- eWasl Advanced Scheduling System - Database Migration
-- Version: 1.0
-- Date: 2025-01-27
-- =====================================================

-- Add recurring support to existing posts table
ALTER TABLE posts ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS recurring_pattern JSONB;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS parent_post_id TEXT;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS recurring_end_date TIMESTAMP;
ALTER TABLE posts ADD COLUMN IF NOT EXISTS optimal_time_score DECIMAL(3,2);

-- Create recurring posts management table
CREATE TABLE IF NOT EXISTS recurring_posts (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  media_url TEXT,
  platforms TEXT[] NOT NULL,
  
  -- Pattern Configuration
  pattern_type TEXT NOT NULL CHECK (pattern_type IN ('daily', 'weekly', 'monthly', 'custom')),
  pattern_config JSONB NOT NULL,
  
  -- Scheduling Configuration
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP,
  timezone TEXT DEFAULT 'UTC',
  
  -- Status and Statistics
  is_active BOOLEAN DEFAULT TRUE,
  posts_generated INTEGER DEFAULT 0,
  last_generated_at TIMESTAMP,
  next_generation_at TIMESTAMP,
  
  -- Metadata
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Foreign Key Constraints
  CONSTRAINT fk_recurring_posts_user 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create bulk operations tracking table
CREATE TABLE IF NOT EXISTS bulk_operations (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  operation_type TEXT NOT NULL CHECK (operation_type IN ('import', 'schedule', 'update', 'delete')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  
  -- Progress Tracking
  total_items INTEGER NOT NULL,
  processed_items INTEGER DEFAULT 0,
  successful_items INTEGER DEFAULT 0,
  failed_items INTEGER DEFAULT 0,
  
  -- Configuration and Data
  source_file_url TEXT,
  operation_config JSONB,
  
  -- Results and Logging
  error_log JSONB DEFAULT '[]'::jsonb,
  result_summary JSONB,
  processing_log JSONB DEFAULT '[]'::jsonb,
  
  -- Timing
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  estimated_completion_at TIMESTAMP,
  
  -- Metadata
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Foreign Key Constraints
  CONSTRAINT fk_bulk_operations_user 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create optimal times analysis table
CREATE TABLE IF NOT EXISTS optimal_times (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  platform TEXT NOT NULL,
  
  -- Time Analysis
  hour_of_day INTEGER NOT NULL CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday
  engagement_score DECIMAL(5,2) NOT NULL DEFAULT 0,
  post_count INTEGER NOT NULL DEFAULT 0,
  
  -- Performance Metrics
  avg_likes DECIMAL(10,2) DEFAULT 0,
  avg_comments DECIMAL(10,2) DEFAULT 0,
  avg_shares DECIMAL(10,2) DEFAULT 0,
  avg_clicks DECIMAL(10,2) DEFAULT 0,
  avg_impressions DECIMAL(10,2) DEFAULT 0,
  
  -- Analysis Metadata
  last_analyzed_at TIMESTAMP DEFAULT NOW(),
  sample_size INTEGER DEFAULT 0,
  confidence_level DECIMAL(3,2) DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Foreign Key Constraints
  CONSTRAINT fk_optimal_times_user 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
  -- Unique constraint to prevent duplicates
  UNIQUE(user_id, platform, hour_of_day, day_of_week)
);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Posts table indexes
CREATE INDEX IF NOT EXISTS idx_posts_recurring ON posts(is_recurring, parent_post_id);
CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON posts(scheduled_at) WHERE status = 'SCHEDULED';
CREATE INDEX IF NOT EXISTS idx_posts_user_status ON posts(user_id, status);
CREATE INDEX IF NOT EXISTS idx_posts_parent_id ON posts(parent_post_id) WHERE parent_post_id IS NOT NULL;

-- Recurring posts indexes
CREATE INDEX IF NOT EXISTS idx_recurring_posts_user_active ON recurring_posts(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_posts_next_generation ON recurring_posts(next_generation_at) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_recurring_posts_pattern_type ON recurring_posts(pattern_type, is_active);

-- Bulk operations indexes
CREATE INDEX IF NOT EXISTS idx_bulk_operations_user_status ON bulk_operations(user_id, status);
CREATE INDEX IF NOT EXISTS idx_bulk_operations_status_created ON bulk_operations(status, created_at);
CREATE INDEX IF NOT EXISTS idx_bulk_operations_type_status ON bulk_operations(operation_type, status);

-- Optimal times indexes
CREATE INDEX IF NOT EXISTS idx_optimal_times_user_platform ON optimal_times(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_optimal_times_platform_score ON optimal_times(platform, engagement_score DESC);
CREATE INDEX IF NOT EXISTS idx_optimal_times_analysis_date ON optimal_times(last_analyzed_at);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update recurring_posts updated_at timestamp
CREATE OR REPLACE FUNCTION update_recurring_posts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_recurring_posts_updated_at
  BEFORE UPDATE ON recurring_posts
  FOR EACH ROW
  EXECUTE FUNCTION update_recurring_posts_updated_at();

-- Update bulk_operations updated_at timestamp
CREATE OR REPLACE FUNCTION update_bulk_operations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_bulk_operations_updated_at
  BEFORE UPDATE ON bulk_operations
  FOR EACH ROW
  EXECUTE FUNCTION update_bulk_operations_updated_at();
-- =====================================================
-- SAMPLE DATA FOR TESTING (Optional)
-- =====================================================

-- Insert sample pattern configurations for reference
INSERT INTO recurring_posts (id, user_id, title, content, platforms, pattern_type, pattern_config, start_date, is_active)
VALUES 
  ('sample-daily', 'sample-user', 'Daily Sample', 'Sample daily post', ARRAY['TWITTER'], 'daily', '{"interval": 1, "time": "09:00"}', NOW(), FALSE),
  ('sample-weekly', 'sample-user', 'Weekly Sample', 'Sample weekly post', ARRAY['FACEBOOK'], 'weekly', '{"days": ["monday", "wednesday", "friday"], "time": "14:00"}', NOW(), FALSE),
  ('sample-monthly', 'sample-user', 'Monthly Sample', 'Sample monthly post', ARRAY['LINKEDIN'], 'monthly', '{"dayOfMonth": 15, "time": "10:00"}', NOW(), FALSE)
ON CONFLICT (id) DO NOTHING;