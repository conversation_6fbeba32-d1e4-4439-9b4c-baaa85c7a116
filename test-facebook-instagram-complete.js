#!/usr/bin/env node

/**
 * Complete Facebook & Instagram OAuth Integration Test
 * Tests both platforms following the same robust pattern as LinkedIn
 */

const baseUrl = 'http://localhost:3001';

async function testPlatform(platform, platformName) {
  console.log(`\n🔍 TESTING ${platformName.toUpperCase()} OAUTH INTEGRATION`);
  console.log('='.repeat(50));

  try {
    // Step 1: Test OAuth configuration
    console.log(`\n📋 Step 1: Testing ${platformName} OAuth Configuration...`);
    const configResponse = await fetch(`${baseUrl}/api/social/oauth-status`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ platform, action: 'check-credentials' })
    });
    const configData = await configResponse.json();
    
    if (!configData.success) {
      throw new Error(`${platformName} OAuth configuration failed: ${configData.message}`);
    }
    console.log(`✅ ${platformName} OAuth Configuration: PASSED`);
    console.log(`   - Client ID: ${configData.hasClientId ? 'Present' : 'Missing'}`);
    console.log(`   - Client Secret: ${configData.hasClientSecret ? 'Present' : 'Missing'}`);
    console.log(`   - Scopes: ${configData.scopes.join(', ')}`);

    // Step 2: Test authorization URL generation
    console.log(`\n🔗 Step 2: Testing ${platformName} Authorization URL Generation...`);
    const urlResponse = await fetch(`${baseUrl}/api/social/oauth-status`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ platform, action: 'test-auth-url' })
    });
    const urlData = await urlResponse.json();
    
    if (!urlData.success) {
      throw new Error(`${platformName} URL generation failed: ${urlData.message}`);
    }
    console.log(`✅ ${platformName} Authorization URL Generation: PASSED`);
    console.log(`   - URL: ${urlData.authUrl.substring(0, 80)}...`);

    // Step 3: Test callback with simulated response
    console.log(`\n🔄 Step 3: Testing ${platformName} OAuth Callback (Simulated)...`);
    console.log('   Note: This will fail at token exchange (expected with fake code)');
    
    const callbackResponse = await fetch(
      `${baseUrl}/api/${platform}/callback?code=fake-test-code&state=test-state`,
      { redirect: 'manual' }
    );
    
    console.log(`   - Response Status: ${callbackResponse.status}`);
    console.log(`   - Response Type: ${callbackResponse.status === 307 ? 'Redirect (Expected)' : 'Other'}`);
    
    if (callbackResponse.status === 307) {
      const location = callbackResponse.headers.get('location');
      console.log(`   - Redirect Location: ${location}`);
      
      if (location && location.includes('/auth/error?error=token_exchange_failed')) {
        console.log(`✅ ${platformName} OAuth Callback: PASSED (Expected token exchange failure)`);
      } else if (location && location.includes('/auth/error?error=user_not_found')) {
        console.log(`❌ ${platformName} OAuth Callback: FAILED (User not found - RLS issue)`);
        throw new Error('User lookup failed in callback - RLS policy blocking access');
      } else {
        console.log(`⚠️  ${platformName} OAuth Callback: Unexpected redirect location`);
      }
    }

    return {
      platform: platformName,
      success: true,
      configuration: 'WORKING',
      authUrl: 'WORKING',
      callback: 'WORKING',
      testUrl: `${baseUrl}/test-${platform}`
    };

  } catch (error) {
    console.error(`\n❌ ${platformName.toUpperCase()} OAUTH INTEGRATION TEST FAILED`);
    console.error('='.repeat(50));
    console.error(`Error: ${error.message}`);
    return {
      platform: platformName,
      success: false,
      error: error.message
    };
  }
}

async function testBothPlatforms() {
  console.log('🚀 FACEBOOK & INSTAGRAM OAUTH INTEGRATION TESTS');
  console.log('================================================\n');

  try {
    // Test user lookup (service role client)
    console.log('👤 Testing User Lookup (Service Role Client)...');
    const userResponse = await fetch(`${baseUrl}/api/test-user-lookup`);
    const userData = await userResponse.json();
    
    if (!userData.success) {
      throw new Error(`User lookup failed: ${userData.message}`);
    }
    console.log('✅ User Lookup: PASSED');
    console.log(`   - User ID: ${userData.userData.id}`);
    console.log(`   - Email: ${userData.userData.email}`);
    console.log(`   - Name: ${userData.userData.name}`);

    // Test Facebook OAuth
    const facebookResult = await testPlatform('facebook', 'Facebook');
    
    // Test Instagram OAuth
    const instagramResult = await testPlatform('instagram', 'Instagram');

    // Summary
    console.log('\n🎉 FACEBOOK & INSTAGRAM OAUTH INTEGRATION TEST RESULTS');
    console.log('======================================================');
    console.log('✅ Service Role User Lookup: WORKING');
    console.log(`${facebookResult.success ? '✅' : '❌'} Facebook OAuth Integration: ${facebookResult.success ? 'WORKING' : 'FAILED'}`);
    console.log(`${instagramResult.success ? '✅' : '❌'} Instagram OAuth Integration: ${instagramResult.success ? 'WORKING' : 'FAILED'}`);

    if (facebookResult.success && instagramResult.success) {
      console.log('\n🚀 BOTH INTEGRATIONS STATUS: READY FOR PRODUCTION TESTING!');
      console.log('\nNext Steps:');
      console.log('1. Provide the actual Facebook App Secret to replace placeholder');
      console.log('2. Test Facebook OAuth:');
      console.log(`   - Visit: ${facebookResult.testUrl}`);
      console.log('   - Click "Generate Authorization URL"');
      console.log('   - Click "Start Facebook OAuth"');
      console.log('   - Complete Facebook authorization');
      console.log('3. Test Instagram OAuth (Business Account Required):');
      console.log(`   - Visit: ${instagramResult.testUrl}`);
      console.log('   - Ensure you have a business Instagram account');
      console.log('   - Click "Generate Authorization URL"');
      console.log('   - Click "Start Instagram OAuth"');
      console.log('   - Complete Instagram business account authorization');
      console.log('\n📋 Important Notes:');
      console.log('- Facebook OAuth works with personal and business accounts');
      console.log('- Instagram OAuth requires business accounts only (personal accounts will be rejected)');
      console.log('- Both integrations use the service role client to bypass RLS issues');
      console.log('- All redirects are configured for localhost:3001');
    } else {
      console.log('\n❌ SOME INTEGRATIONS FAILED - Please check the errors above');
      if (!facebookResult.success) {
        console.log(`Facebook Error: ${facebookResult.error}`);
      }
      if (!instagramResult.success) {
        console.log(`Instagram Error: ${instagramResult.error}`);
      }
    }

  } catch (error) {
    console.error('\n❌ OAUTH INTEGRATION TESTS FAILED');
    console.error('==================================');
    console.error(`Error: ${error.message}`);
    console.error('\nPlease check the server logs and fix the issues before proceeding.');
    process.exit(1);
  }
}

// Run the tests
testBothPlatforms();
