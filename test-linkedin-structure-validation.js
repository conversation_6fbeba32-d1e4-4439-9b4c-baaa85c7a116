#!/usr/bin/env node

/**
 * LinkedIn Integration Structure Validation
 * Validates that all LinkedIn integration components are properly implemented
 */

const fs = require('fs');
const path = require('path');

async function validateLinkedInIntegrationStructure() {
  console.log('🔗 LINKEDIN INTEGRATION STRUCTURE VALIDATION');
  console.log('=' .repeat(80));
  console.log(`Started at: ${new Date().toISOString()}\n`);

  const validation = {
    files: { total: 0, existing: 0, missing: [] },
    imports: { total: 0, valid: 0, invalid: [] },
    exports: { total: 0, valid: 0, invalid: [] },
    types: { total: 0, valid: 0, invalid: [] },
    overall: { score: 0, status: 'UNKNOWN' }
  };

  // 1. FILE EXISTENCE VALIDATION
  console.log('📁 VALIDATING FILE EXISTENCE...\n');

  const requiredFiles = [
    {
      path: 'src/lib/social/business-accounts/linkedin-companies-manager.ts',
      description: 'LinkedIn Companies Manager',
      critical: true
    },
    {
      path: 'src/lib/social/business-accounts/business-account-types.ts',
      description: 'Business Account Types',
      critical: true
    },
    {
      path: 'src/lib/social/business-accounts/page-selection-service.ts',
      description: 'Page Selection Service',
      critical: true
    },
    {
      path: 'src/components/social/linkedin-company-selector.tsx',
      description: 'LinkedIn Company Selector UI',
      critical: true
    },
    {
      path: 'src/components/social/business-account-card.tsx',
      description: 'Business Account Card UI',
      critical: false
    },
    {
      path: 'src/components/social/account-configuration-modal.tsx',
      description: 'Account Configuration Modal',
      critical: false
    },
    {
      path: 'src/app/api/social/business-accounts/route.ts',
      description: 'Business Accounts API',
      critical: true
    },
    {
      path: 'src/app/api/social/business-accounts/select/route.ts',
      description: 'Business Account Selection API',
      critical: true
    },
    {
      path: 'src/app/api/social/business-accounts/refresh/route.ts',
      description: 'Business Account Refresh API',
      critical: true
    }
  ];

  requiredFiles.forEach(file => {
    validation.files.total++;
    if (fs.existsSync(file.path)) {
      console.log(`✅ ${file.description}: ${file.path}`);
      validation.files.existing++;
    } else {
      console.log(`❌ ${file.description}: ${file.path} - MISSING`);
      validation.files.missing.push(file);
    }
  });

  console.log(`\n📊 Files: ${validation.files.existing}/${validation.files.total} present`);

  // 2. CONTENT VALIDATION
  console.log('\n📝 VALIDATING FILE CONTENTS...\n');

  // Check LinkedIn Companies Manager
  if (fs.existsSync('src/lib/social/business-accounts/linkedin-companies-manager.ts')) {
    const content = fs.readFileSync('src/lib/social/business-accounts/linkedin-companies-manager.ts', 'utf8');
    
    const requiredMethods = [
      'fetchUserOrganizations',
      'storeUserOrganizations',
      'getStoredOrganizations',
      'updateSelectedOrganization',
      'validateOrganizationPermissions',
      'refreshOrganizations'
    ];

    console.log('🔗 LinkedIn Companies Manager:');
    requiredMethods.forEach(method => {
      validation.exports.total++;
      if (content.includes(`async ${method}`) || content.includes(`${method}(`)) {
        console.log(`  ✅ ${method}() method present`);
        validation.exports.valid++;
      } else {
        console.log(`  ❌ ${method}() method missing`);
        validation.exports.invalid.push(`LinkedInCompaniesManager.${method}`);
      }
    });
  }

  // Check Page Selection Service LinkedIn integration
  if (fs.existsSync('src/lib/social/business-accounts/page-selection-service.ts')) {
    const content = fs.readFileSync('src/lib/social/business-accounts/page-selection-service.ts', 'utf8');
    
    console.log('\n🔧 Page Selection Service LinkedIn Integration:');
    
    const linkedinChecks = [
      { check: 'LinkedInCompaniesManager import', pattern: 'LinkedInCompaniesManager' },
      { check: 'LinkedIn case in getBusinessAccountConfig', pattern: "case 'LINKEDIN':" },
      { check: 'getLinkedInBusinessConfig method', pattern: 'getLinkedInBusinessConfig' },
      { check: 'LinkedIn case in selectBusinessAccount', pattern: 'linkedinCompaniesManager.updateSelectedOrganization' },
      { check: 'LinkedIn case in refreshBusinessAccounts', pattern: 'linkedinCompaniesManager.refreshOrganizations' }
    ];

    linkedinChecks.forEach(check => {
      validation.imports.total++;
      if (content.includes(check.pattern)) {
        console.log(`  ✅ ${check.check}`);
        validation.imports.valid++;
      } else {
        console.log(`  ❌ ${check.check} missing`);
        validation.imports.invalid.push(check.check);
      }
    });
  }

  // Check Business Account Types
  if (fs.existsSync('src/lib/social/business-accounts/business-account-types.ts')) {
    const content = fs.readFileSync('src/lib/social/business-accounts/business-account-types.ts', 'utf8');
    
    console.log('\n📋 Business Account Types:');
    
    const typeChecks = [
      'LinkedInCompanyPage',
      'LinkedInOrganization',
      'PLATFORM_BUSINESS_REQUIREMENTS',
      'LINKEDIN'
    ];

    typeChecks.forEach(type => {
      validation.types.total++;
      if (content.includes(type)) {
        console.log(`  ✅ ${type} type/interface present`);
        validation.types.valid++;
      } else {
        console.log(`  ❌ ${type} type/interface missing`);
        validation.types.invalid.push(type);
      }
    });
  }

  // Check UI Components
  if (fs.existsSync('src/components/social/linkedin-company-selector.tsx')) {
    const content = fs.readFileSync('src/components/social/linkedin-company-selector.tsx', 'utf8');
    
    console.log('\n🎨 LinkedIn Company Selector UI:');
    
    const uiChecks = [
      { check: 'LinkedInCompanySelector component', pattern: 'export function LinkedInCompanySelector' },
      { check: 'Business accounts API call', pattern: '/api/social/business-accounts' },
      { check: 'Refresh functionality', pattern: '/api/social/business-accounts/refresh' },
      { check: 'Selection functionality', pattern: '/api/social/business-accounts/select' },
      { check: 'Arabic RTL support', pattern: 'شركات لينكد إن' }
    ];

    uiChecks.forEach(check => {
      if (content.includes(check.pattern)) {
        console.log(`  ✅ ${check.check}`);
      } else {
        console.log(`  ❌ ${check.check} missing`);
      }
    });
  }

  // 3. CALCULATE OVERALL SCORE
  const fileScore = (validation.files.existing / validation.files.total) * 100;
  const importScore = validation.imports.total > 0 ? (validation.imports.valid / validation.imports.total) * 100 : 100;
  const exportScore = validation.exports.total > 0 ? (validation.exports.valid / validation.exports.total) * 100 : 100;
  const typeScore = validation.types.total > 0 ? (validation.types.valid / validation.types.total) * 100 : 100;

  validation.overall.score = (fileScore * 0.4 + importScore * 0.2 + exportScore * 0.3 + typeScore * 0.1);

  if (validation.overall.score >= 90) {
    validation.overall.status = 'EXCELLENT';
  } else if (validation.overall.score >= 80) {
    validation.overall.status = 'GOOD';
  } else if (validation.overall.score >= 70) {
    validation.overall.status = 'FAIR';
  } else {
    validation.overall.status = 'POOR';
  }

  // 4. GENERATE FINAL REPORT
  console.log('\n📊 LINKEDIN INTEGRATION VALIDATION SUMMARY');
  console.log('=' .repeat(80));
  console.log(`📁 File Structure: ${fileScore.toFixed(1)}% (${validation.files.existing}/${validation.files.total})`);
  console.log(`📥 Imports: ${importScore.toFixed(1)}% (${validation.imports.valid}/${validation.imports.total})`);
  console.log(`📤 Exports: ${exportScore.toFixed(1)}% (${validation.exports.valid}/${validation.exports.total})`);
  console.log(`📋 Types: ${typeScore.toFixed(1)}% (${validation.types.valid}/${validation.types.total})`);
  console.log(`\n🎯 OVERALL SCORE: ${validation.overall.score.toFixed(1)}% - ${validation.overall.status}`);

  // 5. RECOMMENDATIONS
  console.log('\n💡 RECOMMENDATIONS:');

  if (validation.files.missing.length > 0) {
    console.log('📁 Missing Files:');
    validation.files.missing.forEach(file => {
      const priority = file.critical ? 'CRITICAL' : 'OPTIONAL';
      console.log(`  ❌ ${file.description} (${priority})`);
    });
  }

  if (validation.imports.invalid.length > 0) {
    console.log('📥 Missing Imports/Integrations:');
    validation.imports.invalid.forEach(item => {
      console.log(`  ❌ ${item}`);
    });
  }

  if (validation.exports.invalid.length > 0) {
    console.log('📤 Missing Methods:');
    validation.exports.invalid.forEach(method => {
      console.log(`  ❌ ${method}`);
    });
  }

  if (validation.overall.score >= 90) {
    console.log('🎉 LinkedIn integration is excellently implemented!');
    console.log('✅ Ready for production testing');
  } else if (validation.overall.score >= 80) {
    console.log('✅ LinkedIn integration is well implemented');
    console.log('⚠️  Minor improvements recommended');
  } else if (validation.overall.score >= 70) {
    console.log('⚠️  LinkedIn integration needs some improvements');
    console.log('🔧 Address missing components before production');
  } else {
    console.log('❌ LinkedIn integration needs significant work');
    console.log('🚨 Critical components missing');
  }

  console.log('\n🏁 VALIDATION COMPLETE');
  console.log(`Finished at: ${new Date().toISOString()}`);

  return validation;
}

if (require.main === module) {
  validateLinkedInIntegrationStructure().then(validation => {
    process.exit(validation.overall.score >= 80 ? 0 : 1);
  }).catch(error => {
    console.error('❌ VALIDATION ERROR:', error);
    process.exit(1);
  });
}

module.exports = { validateLinkedInIntegrationStructure };
