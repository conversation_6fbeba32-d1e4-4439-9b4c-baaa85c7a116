{"spec": {"name": "ewasl-social-scheduler", "region": "nyc", "services": [{"name": "web", "dockerfile_path": "Dockerfile", "source_dir": "ewasl-app", "image": {"registry_type": "DOCKER_HUB", "registry": "library", "repository": "node", "tag": "20-alpine"}, "http_port": 3000, "instance_count": 1, "instance_size_slug": "basic-xs", "routes": [{"path": "/"}], "health_check": {"http_path": "/api/health", "initial_delay_seconds": 10, "period_seconds": 60}, "envs": [{"key": "NODE_ENV", "value": "production", "scope": "RUN_AND_BUILD_TIME"}, {"key": "NEXT_PUBLIC_API_URL", "value": "https://app-92d1f7b6-85f2-47e2-8a69-d823b1586159-api.ondigitalocean.app", "scope": "RUN_AND_BUILD_TIME"}, {"key": "NEXT_PUBLIC_APP_URL", "value": "https://app.ewasl.com", "scope": "RUN_AND_BUILD_TIME"}, {"key": "NEXT_PUBLIC_APP_VERSION", "value": "1.0.0", "scope": "RUN_AND_BUILD_TIME"}, {"key": "NEXT_TELEMETRY_DISABLED", "value": "1", "scope": "RUN_AND_BUILD_TIME"}, {"key": "DATABASE_URL", "value": "${db.DATABASE_URL}", "scope": "RUN_AND_BUILD_TIME"}]}], "databases": [{"name": "db", "engine": "PG", "version": "14"}], "domains": [{"domain": "app.ewasl.com", "type": "PRIMARY"}]}}