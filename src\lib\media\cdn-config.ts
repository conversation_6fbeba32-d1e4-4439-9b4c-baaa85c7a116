/**
 * CDN Configuration and Provider Management
 * Supports multiple CDN providers with automatic failover
 */

export interface CDNConfig {
  provider: 'aws' | 'cloudflare' | 'vercel' | 'digitalocean';
  bucket: string;
  region: string;
  cdnDomain: string;
  accessKey?: string;
  secretKey?: string;
  maxFileSize: number;
  allowedFormats: string[];
  cacheTTL: number;
}

export interface CDNUploadResult {
  success: boolean;
  cdnUrl: string;
  originalUrl: string;
  fileId: string;
  metadata: MediaMetadata;
  error?: string;
}

export interface MediaMetadata {
  filename: string;
  size: number;
  mimeType: string;
  dimensions?: {
    width: number;
    height: number;
  };
  duration?: number; // for videos
  uploadedAt: Date;
  optimizedVersions?: OptimizedVersion[];
}

export interface OptimizedVersion {
  format: string;
  quality: string;
  size: number;
  url: string;
  dimensions: {
    width: number;
    height: number;
  };
}

export class CDNManager {
  private config: CDNConfig;
  private providers: Map<string, CDNProvider> = new Map();

  constructor(config: CDNConfig) {
    this.config = config;
    this.initializeProviders();
  }

  private initializeProviders(): void {
    // Initialize AWS S3 + CloudFront
    if (this.config.provider === 'aws') {
      this.providers.set('aws', new AWSCDNProvider(this.config));
    }

    // Initialize Cloudflare R2 + CDN
    if (this.config.provider === 'cloudflare') {
      this.providers.set('cloudflare', new CloudflareCDNProvider(this.config));
    }

    // Initialize Vercel Blob Storage
    if (this.config.provider === 'vercel') {
      this.providers.set('vercel', new VercelCDNProvider(this.config));
    }

    // Initialize DigitalOcean Spaces
    if (this.config.provider === 'digitalocean') {
      this.providers.set('digitalocean', new DigitalOceanCDNProvider(this.config));
    }
  }

  /**
   * Upload file to CDN with automatic optimization
   */
  async uploadFile(
    file: File | Buffer,
    filename: string,
    options?: UploadOptions
  ): Promise<CDNUploadResult> {
    try {
      console.log('Uploading file to CDN:', {
        filename,
        size: file instanceof File ? file.size : file.length,
        provider: this.config.provider
      });

      // Validate file
      const validation = this.validateFile(file, filename);
      if (!validation.isValid) {
        throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
      }

      // Get primary provider
      const provider = this.providers.get(this.config.provider);
      if (!provider) {
        throw new Error(`CDN provider not configured: ${this.config.provider}`);
      }

      // Generate unique file ID
      const fileId = this.generateFileId(filename);
      const cdnPath = this.generateCDNPath(fileId, filename);

      // Upload to CDN
      const uploadResult = await provider.upload(file, cdnPath, options);

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'CDN upload failed');
      }

      // Generate CDN URL
      const cdnUrl = this.generateCDNUrl(cdnPath);

      // Extract metadata
      const metadata = await this.extractMetadata(file, filename);

      console.log('File uploaded successfully to CDN:', {
        fileId,
        cdnUrl,
        size: metadata.size
      });

      return {
        success: true,
        cdnUrl,
        originalUrl: uploadResult.url,
        fileId,
        metadata,
      };

    } catch (error) {
      console.error('CDN upload error:', error);
      return {
        success: false,
        cdnUrl: '',
        originalUrl: '',
        fileId: '',
        metadata: {} as MediaMetadata,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Delete file from CDN
   */
  async deleteFile(fileId: string): Promise<boolean> {
    try {
      const provider = this.providers.get(this.config.provider);
      if (!provider) {
        throw new Error(`CDN provider not configured: ${this.config.provider}`);
      }

      const result = await provider.delete(fileId);
      console.log('File deleted from CDN:', { fileId, success: result });
      
      return result;

    } catch (error) {
      console.error('CDN delete error:', error);
      return false;
    }
  }

  /**
   * Generate signed URL for private access
   */
  async generateSignedUrl(
    fileId: string, 
    expiresIn: number = 3600
  ): Promise<string> {
    try {
      const provider = this.providers.get(this.config.provider);
      if (!provider) {
        throw new Error(`CDN provider not configured: ${this.config.provider}`);
      }

      return await provider.generateSignedUrl(fileId, expiresIn);

    } catch (error) {
      console.error('Signed URL generation error:', error);
      throw error;
    }
  }

  /**
   * Get file metadata from CDN
   */
  async getFileMetadata(fileId: string): Promise<MediaMetadata | null> {
    try {
      const provider = this.providers.get(this.config.provider);
      if (!provider) {
        throw new Error(`CDN provider not configured: ${this.config.provider}`);
      }

      return await provider.getMetadata(fileId);

    } catch (error) {
      console.error('Metadata retrieval error:', error);
      return null;
    }
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File | Buffer, filename: string): ValidationResult {
    const errors: string[] = [];
    const size = file instanceof File ? file.size : file.length;

    // Check file size
    if (size > this.config.maxFileSize) {
      errors.push(`File size exceeds limit: ${size} > ${this.config.maxFileSize}`);
    }

    // Check file format
    const extension = filename.split('.').pop()?.toLowerCase();
    if (extension && !this.config.allowedFormats.includes(extension)) {
      errors.push(`File format not allowed: ${extension}`);
    }

    // Check filename
    if (!filename || filename.length > 255) {
      errors.push('Invalid filename');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate unique file ID
   */
  private generateFileId(filename: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const extension = filename.split('.').pop();
    
    return `${timestamp}-${random}.${extension}`;
  }

  /**
   * Generate CDN path for file
   */
  private generateCDNPath(fileId: string, originalFilename: string): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `media/${year}/${month}/${day}/${fileId}`;
  }

  /**
   * Generate CDN URL
   */
  private generateCDNUrl(cdnPath: string): string {
    return `https://${this.config.cdnDomain}/${cdnPath}`;
  }

  /**
   * Extract file metadata
   */
  private async extractMetadata(file: File | Buffer, filename: string): Promise<MediaMetadata> {
    const size = file instanceof File ? file.size : file.length;
    const mimeType = file instanceof File ? file.type : this.getMimeTypeFromFilename(filename);

    const metadata: MediaMetadata = {
      filename,
      size,
      mimeType,
      uploadedAt: new Date(),
    };

    // Extract dimensions for images
    if (mimeType.startsWith('image/')) {
      try {
        metadata.dimensions = await this.extractImageDimensions(file);
      } catch (error) {
        console.warn('Failed to extract image dimensions:', error);
      }
    }

    // Extract duration for videos
    if (mimeType.startsWith('video/')) {
      try {
        metadata.duration = await this.extractVideoDuration(file);
      } catch (error) {
        console.warn('Failed to extract video duration:', error);
      }
    }

    return metadata;
  }

  /**
   * Extract image dimensions
   */
  private async extractImageDimensions(file: File | Buffer): Promise<{ width: number; height: number }> {
    // This would use a library like 'sharp' or 'image-size'
    // For now, return placeholder
    return { width: 0, height: 0 };
  }

  /**
   * Extract video duration
   */
  private async extractVideoDuration(file: File | Buffer): Promise<number> {
    // This would use a library like 'ffprobe' or 'node-ffmpeg'
    // For now, return placeholder
    return 0;
  }

  /**
   * Get MIME type from filename
   */
  private getMimeTypeFromFilename(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'mp4': 'video/mp4',
      'mov': 'video/quicktime',
      'avi': 'video/x-msvideo',
      'webm': 'video/webm',
    };

    return mimeTypes[extension || ''] || 'application/octet-stream';
  }
}

// Provider interfaces
export interface CDNProvider {
  upload(file: File | Buffer, path: string, options?: UploadOptions): Promise<UploadResult>;
  delete(fileId: string): Promise<boolean>;
  generateSignedUrl(fileId: string, expiresIn: number): Promise<string>;
  getMetadata(fileId: string): Promise<MediaMetadata | null>;
}

export interface UploadOptions {
  contentType?: string;
  cacheControl?: string;
  metadata?: Record<string, string>;
  isPublic?: boolean;
}

export interface UploadResult {
  success: boolean;
  url: string;
  error?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// AWS CDN Provider implementation
export class AWSCDNProvider implements CDNProvider {
  private config: CDNConfig;

  constructor(config: CDNConfig) {
    this.config = config;
  }

  async upload(file: File | Buffer, path: string, options?: UploadOptions): Promise<UploadResult> {
    // AWS S3 upload implementation
    // This would use AWS SDK
    return { success: true, url: `https://${this.config.bucket}.s3.amazonaws.com/${path}` };
  }

  async delete(fileId: string): Promise<boolean> {
    // AWS S3 delete implementation
    return true;
  }

  async generateSignedUrl(fileId: string, expiresIn: number): Promise<string> {
    // AWS S3 signed URL generation
    return `https://${this.config.bucket}.s3.amazonaws.com/${fileId}?signed=true`;
  }

  async getMetadata(fileId: string): Promise<MediaMetadata | null> {
    // AWS S3 metadata retrieval
    return null;
  }
}

// Cloudflare CDN Provider implementation
export class CloudflareCDNProvider implements CDNProvider {
  private config: CDNConfig;

  constructor(config: CDNConfig) {
    this.config = config;
  }

  async upload(file: File | Buffer, path: string, options?: UploadOptions): Promise<UploadResult> {
    // Cloudflare R2 upload implementation
    return { success: true, url: `https://${this.config.cdnDomain}/${path}` };
  }

  async delete(fileId: string): Promise<boolean> {
    return true;
  }

  async generateSignedUrl(fileId: string, expiresIn: number): Promise<string> {
    return `https://${this.config.cdnDomain}/${fileId}?signed=true`;
  }

  async getMetadata(fileId: string): Promise<MediaMetadata | null> {
    return null;
  }
}

// Vercel CDN Provider implementation
export class VercelCDNProvider implements CDNProvider {
  private config: CDNConfig;

  constructor(config: CDNConfig) {
    this.config = config;
  }

  async upload(file: File | Buffer, path: string, options?: UploadOptions): Promise<UploadResult> {
    // Vercel Blob upload implementation
    return { success: true, url: `https://${this.config.cdnDomain}/${path}` };
  }

  async delete(fileId: string): Promise<boolean> {
    return true;
  }

  async generateSignedUrl(fileId: string, expiresIn: number): Promise<string> {
    return `https://${this.config.cdnDomain}/${fileId}?signed=true`;
  }

  async getMetadata(fileId: string): Promise<MediaMetadata | null> {
    return null;
  }
}

// DigitalOcean CDN Provider implementation
export class DigitalOceanCDNProvider implements CDNProvider {
  private config: CDNConfig;

  constructor(config: CDNConfig) {
    this.config = config;
  }

  async upload(file: File | Buffer, path: string, options?: UploadOptions): Promise<UploadResult> {
    // DigitalOcean Spaces upload implementation
    return { success: true, url: `https://${this.config.bucket}.${this.config.region}.digitaloceanspaces.com/${path}` };
  }

  async delete(fileId: string): Promise<boolean> {
    return true;
  }

  async generateSignedUrl(fileId: string, expiresIn: number): Promise<string> {
    return `https://${this.config.bucket}.${this.config.region}.digitaloceanspaces.com/${fileId}?signed=true`;
  }

  async getMetadata(fileId: string): Promise<MediaMetadata | null> {
    return null;
  }
}

export default CDNManager;
