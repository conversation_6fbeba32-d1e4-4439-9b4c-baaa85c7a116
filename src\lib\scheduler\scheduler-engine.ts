import { createClient } from '@/lib/supabase/server';
import { SocialMediaPublisher } from './social-media-publisher';
import { QueueManager } from './queue-manager';
import { SchedulerLogger } from './scheduler-logger';

export interface ScheduledPost {
  id: string;
  content: string;
  media_url?: string;
  platforms: string[];
  scheduled_at: string;
  user_id: string;
  status: 'SCHEDULED' | 'PROCESSING' | 'PUBLISHED' | 'FAILED' | 'CANCELLED';
  retry_count: number;
  max_retries: number;
  error_message?: string;
  published_urls?: Record<string, string>;
  created_at: string;
  updated_at: string;
}

export interface PublishResult {
  platform: string;
  success: boolean;
  url?: string;
  error?: string;
  retry_after?: number;
}

export class SchedulerEngine {
  private publisher: SocialMediaPublisher;
  private queueManager: QueueManager;
  private logger: SchedulerLogger;
  private isRunning: boolean = false;
  private processingInterval?: NodeJS.Timeout;

  constructor() {
    this.publisher = new SocialMediaPublisher();
    this.queueManager = new QueueManager();
    this.logger = new SchedulerLogger();
  }

  /**
   * Start the scheduler engine
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Scheduler engine is already running');
      return;
    }

    this.isRunning = true;
    this.logger.info('Starting scheduler engine...');

    // Initialize queue manager
    await this.queueManager.initialize();

    // Start processing scheduled posts
    this.processingInterval = setInterval(async () => {
      await this.processScheduledPosts();
    }, 30000); // Check every 30 seconds

    this.logger.info('Scheduler engine started successfully');
  }

  /**
   * Stop the scheduler engine
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Scheduler engine is not running');
      return;
    }

    this.isRunning = false;
    this.logger.info('Stopping scheduler engine...');

    // Clear processing interval
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }

    // Cleanup queue manager
    await this.queueManager.cleanup();

    this.logger.info('Scheduler engine stopped successfully');
  }

  /**
   * Process scheduled posts that are due for publishing
   */
  private async processScheduledPosts(): Promise<void> {
    try {
      const supabase = createClient();
      const now = new Date().toISOString();

      // Get posts that are due for publishing
      const { data: duePosts, error } = await supabase
        .from('posts')
        .select('*')
        .eq('status', 'SCHEDULED')
        .lte('scheduled_at', now)
        .order('scheduled_at', { ascending: true })
        .limit(50); // Process up to 50 posts at a time

      if (error) {
        this.logger.error('Failed to fetch scheduled posts:', error);
        return;
      }

      if (!duePosts || duePosts.length === 0) {
        return; // No posts to process
      }

      this.logger.info(`Processing ${duePosts.length} scheduled posts`);

      // Process each post
      for (const post of duePosts) {
        await this.processPost(post);
      }

    } catch (error) {
      this.logger.error('Error in processScheduledPosts:', error);
    }
  }

  /**
   * Process a single post
   */
  private async processPost(post: ScheduledPost): Promise<void> {
    const supabase = createClient();

    try {
      this.logger.info(`Processing post ${post.id} for platforms: ${post.platforms.join(', ')}`);

      // Update post status to PROCESSING
      await supabase
        .from('posts')
        .update({
          status: 'PROCESSING',
          updated_at: new Date().toISOString(),
        })
        .eq('id', post.id);

      // Add to processing queue
      await this.queueManager.addJob('publish-post', {
        postId: post.id,
        userId: post.user_id,
        content: post.content,
        mediaUrl: post.media_url,
        platforms: post.platforms,
        retryCount: post.retry_count || 0,
        maxRetries: post.max_retries || 3,
      });

      this.logger.info(`Post ${post.id} added to processing queue`);

    } catch (error) {
      this.logger.error(`Failed to process post ${post.id}:`, error);

      // Update post status to FAILED
      await supabase
        .from('posts')
        .update({
          status: 'FAILED',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          updated_at: new Date().toISOString(),
        })
        .eq('id', post.id);
    }
  }

  /**
   * Publish a post to social media platforms
   */
  async publishPost(jobData: any): Promise<void> {
    const { postId, userId, content, mediaUrl, platforms, retryCount, maxRetries } = jobData;
    const supabase = createClient();

    try {
      this.logger.info(`Publishing post ${postId} to platforms: ${platforms.join(', ')}`);

      // Get user's social accounts
      const { data: socialAccounts, error: accountsError } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .in('platform', platforms)
        .eq('is_active', true);

      if (accountsError) {
        throw new Error(`Failed to fetch social accounts: ${accountsError.message}`);
      }

      if (!socialAccounts || socialAccounts.length === 0) {
        throw new Error('No active social accounts found for the specified platforms');
      }

      // Publish to each platform
      const results: PublishResult[] = [];
      const publishedUrls: Record<string, string> = {};
      let hasFailures = false;

      for (const account of socialAccounts) {
        try {
          const result = await this.publisher.publishToAccount(account, {
            content,
            mediaUrl,
          });

          results.push(result);

          if (result.success && result.url) {
            publishedUrls[account.platform] = result.url;
          } else {
            hasFailures = true;
          }

        } catch (error) {
          this.logger.error(`Failed to publish to ${account.platform}:`, error);
          results.push({
            platform: account.platform,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
          hasFailures = true;
        }
      }

      // Update post status based on results
      const allSuccessful = results.every(r => r.success);
      const allFailed = results.every(r => !r.success);

      let newStatus: string;
      if (allSuccessful) {
        newStatus = 'PUBLISHED';
      } else if (allFailed && retryCount >= maxRetries) {
        newStatus = 'FAILED';
      } else {
        // Partial success or retryable failure
        newStatus = hasFailures ? 'FAILED' : 'PUBLISHED';
      }

      // Update post in database
      await supabase
        .from('posts')
        .update({
          status: newStatus,
          published_urls: Object.keys(publishedUrls).length > 0 ? publishedUrls : null,
          published_at: allSuccessful ? new Date().toISOString() : null,
          error_message: allFailed ? results.map(r => r.error).filter(Boolean).join('; ') : null,
          retry_count: retryCount + 1,
          updated_at: new Date().toISOString(),
        })
        .eq('id', postId);

      // Log activity
      await supabase
        .from('activities')
        .insert({
          user_id: userId,
          action: allSuccessful ? 'POST_PUBLISHED' : 'POST_PUBLISH_FAILED',
          metadata: {
            postId,
            platforms,
            results,
            publishedUrls,
          },
        });

      // Schedule retry if needed
      if (hasFailures && retryCount < maxRetries) {
        const retryDelay = Math.min(300000 * Math.pow(2, retryCount), 3600000); // Exponential backoff, max 1 hour
        await this.queueManager.addJob('publish-post', {
          ...jobData,
          retryCount: retryCount + 1,
        }, { delay: retryDelay });

        this.logger.info(`Scheduled retry for post ${postId} in ${retryDelay / 1000} seconds`);
      }

      this.logger.info(`Post ${postId} processing completed. Status: ${newStatus}`);

    } catch (error) {
      this.logger.error(`Failed to publish post ${postId}:`, error);

      // Update post status to FAILED
      await supabase
        .from('posts')
        .update({
          status: 'FAILED',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          retry_count: retryCount + 1,
          updated_at: new Date().toISOString(),
        })
        .eq('id', postId);

      // Schedule retry if within limits
      if (retryCount < maxRetries) {
        const retryDelay = Math.min(300000 * Math.pow(2, retryCount), 3600000);
        await this.queueManager.addJob('publish-post', {
          ...jobData,
          retryCount: retryCount + 1,
        }, { delay: retryDelay });
      }
    }
  }

  /**
   * Get scheduler status
   */
  getStatus(): { isRunning: boolean; queueStatus: any } {
    return {
      isRunning: this.isRunning,
      queueStatus: this.queueManager.getStatus(),
    };
  }

  /**
   * Manually trigger processing of a specific post
   */
  async triggerPostProcessing(postId: string): Promise<void> {
    const supabase = createClient();

    const { data: post, error } = await supabase
      .from('posts')
      .select('*')
      .eq('id', postId)
      .single();

    if (error || !post) {
      throw new Error(`Post not found: ${postId}`);
    }

    await this.processPost(post);
  }
}
