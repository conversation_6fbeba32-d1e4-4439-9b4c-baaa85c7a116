import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { BulkScheduler } from '@/lib/scheduling/bulk-scheduler';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/posts/bulk-operations/[id] - Get specific bulk operation status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Fetching bulk operation status:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get bulk operation status
    const bulkScheduler = new BulkScheduler();
    const operation = await bulkScheduler.getBulkOperationStatus(id);

    if (!operation) {
      return NextResponse.json(
        { error: 'Bulk operation not found' },
        { status: 404 }
      );
    }

    // Verify user owns this operation
    if (operation.userId !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Calculate progress percentage
    const progressPercentage = operation.totalItems > 0
      ? Math.round((operation.processedItems / operation.totalItems) * 100)
      : 0;

    // Calculate estimated completion time
    let estimatedCompletion = null;
    if (operation.status === 'processing' && operation.processedItems > 0) {
      const itemsRemaining = operation.totalItems - operation.processedItems;
      const processingRate = operation.processedItems /
        ((new Date().getTime() - (operation.startedAt?.getTime() || 0)) / 1000); // items per second

      if (processingRate > 0) {
        const secondsRemaining = itemsRemaining / processingRate;
        estimatedCompletion = new Date(Date.now() + secondsRemaining * 1000);
      }
    }

    // Get recent processing logs
    const recentLogs = operation.processingLog.slice(-10); // Last 10 log entries

    return NextResponse.json({
      success: true,
      data: {
        operation,
        progress: {
          percentage: progressPercentage,
          processed: operation.processedItems,
          total: operation.totalItems,
          successful: operation.successfulItems,
          failed: operation.failedItems,
          remaining: operation.totalItems - operation.processedItems,
        },
        timing: {
          startedAt: operation.startedAt,
          completedAt: operation.completedAt,
          estimatedCompletion,
          duration: operation.completedAt && operation.startedAt
            ? operation.completedAt.getTime() - operation.startedAt.getTime()
            : null,
        },
        logs: {
          recent: recentLogs,
          totalEntries: operation.processingLog.length,
          errors: operation.errorLog,
        },
      },
    });

  } catch (error: any) {
    console.error('Error fetching bulk operation:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch bulk operation',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/posts/bulk-operations/[id] - Update bulk operation (pause/resume/cancel)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Updating bulk operation:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action } = body;

    if (!action || !['cancel', 'retry'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Use "cancel" or "retry"' },
        { status: 400 }
      );
    }

    const bulkScheduler = new BulkScheduler();

    // Verify operation exists and user owns it
    const operation = await bulkScheduler.getBulkOperationStatus(id);
    if (!operation || operation.userId !== user.id) {
      return NextResponse.json(
        { error: 'Bulk operation not found or access denied' },
        { status: 404 }
      );
    }

    switch (action) {
      case 'cancel':
        if (!['pending', 'processing'].includes(operation.status)) {
          return NextResponse.json(
            { error: 'Can only cancel pending or processing operations' },
            { status: 400 }
          );
        }

        await bulkScheduler.cancelBulkOperation(id, user.id);

        return NextResponse.json({
          success: true,
          message: 'تم إلغاء العملية المجمعة بنجاح',
        });

      case 'retry':
        if (operation.status !== 'failed') {
          return NextResponse.json(
            { error: 'Can only retry failed operations' },
            { status: 400 }
          );
        }

        // Reset operation status and retry
        await supabase
          .from('bulk_operations')
          .update({
            status: 'pending',
            processed_items: 0,
            successful_items: 0,
            failed_items: 0,
            error_log: [],
            processing_log: [],
            started_at: null,
            completed_at: null,
          })
          .eq('id', id);

        return NextResponse.json({
          success: true,
          message: 'تم إعادة تعيين العملية للمحاولة مرة أخرى',
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Error updating bulk operation:', error);
    return NextResponse.json(
      {
        error: 'Failed to update bulk operation',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/posts/bulk-operations/[id] - Delete bulk operation record
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id } = await params;
    console.log('Deleting bulk operation:', id);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify operation exists and user owns it
    const { data: operation, error: fetchError } = await supabase
      .from('bulk_operations')
      .select('status')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !operation) {
      return NextResponse.json(
        { error: 'Bulk operation not found' },
        { status: 404 }
      );
    }

    // Don't allow deletion of processing operations
    if (operation.status === 'processing') {
      return NextResponse.json(
        { error: 'Cannot delete processing operations. Cancel first.' },
        { status: 400 }
      );
    }

    // Delete the operation
    const { error: deleteError } = await supabase
      .from('bulk_operations')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (deleteError) {
      throw deleteError;
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'BULK_OPERATION_DELETED',
        metadata: {
          operationId: id,
          status: operation.status,
        },
      });

    return NextResponse.json({
      success: true,
      message: 'تم حذف سجل العملية المجمعة',
    });

  } catch (error: any) {
    console.error('Error deleting bulk operation:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete bulk operation',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
