import { NextRequest, NextResponse } from 'next/server';
import { getStripeInstance } from '@/lib/stripe/config';
import { SubscriptionService } from '@/lib/stripe/subscription-service';

// Force dynamic rendering for webhooks
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const stripe = getStripeInstance();
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      console.error('Missing Stripe signature');
      return NextResponse.json(
        { error: 'Missing Stripe signature' },
        { status: 400 }
      );
    }

    if (!process.env.STRIPE_WEBHOOK_SECRET) {
      console.error('Missing STRIPE_WEBHOOK_SECRET environment variable');
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      );
    }

    // Verify webhook signature
    let event;
    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    console.log(`Received webhook event: ${event.type} (${event.id})`);

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Process the webhook event
    try {
      await subscriptionService.handleWebhook(event);
      console.log(`Successfully processed webhook event: ${event.type}`);
    } catch (processingError) {
      console.error(`Failed to process webhook event ${event.type}:`, processingError);

      // Return 500 to tell Stripe to retry the webhook
      return NextResponse.json(
        {
          error: 'Webhook processing failed',
          eventType: event.type,
          eventId: event.id
        },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      received: true,
      eventType: event.type,
      eventId: event.id,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Webhook handler error:', error);

    return NextResponse.json(
      {
        error: 'Webhook handler failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Stripe-Signature',
    },
  });
}


