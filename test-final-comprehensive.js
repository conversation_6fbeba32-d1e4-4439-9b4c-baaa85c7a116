#!/usr/bin/env node

/**
 * Final Comprehensive Testing Suite
 * Complete assessment of OAuth flows and social media integrations
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-Final-Tester/1.0',
        ...options.headers
      },
      timeout: 15000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function runFinalComprehensiveAssessment() {
  console.log('🎯 FINAL COMPREHENSIVE OAUTH & SOCIAL MEDIA ASSESSMENT');
  console.log('=' .repeat(80));
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`Assessment started at: ${new Date().toISOString()}\n`);
  
  const assessment = {
    systemHealth: { tests: [], score: 0 },
    oauthIntegration: { tests: [], score: 0 },
    socialMediaProviders: { tests: [], score: 0 },
    enhancedFeatures: { tests: [], score: 0 },
    overallScore: 0,
    recommendations: []
  };
  
  // 1. SYSTEM HEALTH ASSESSMENT
  console.log('🏥 SYSTEM HEALTH ASSESSMENT...\n');
  
  const healthEndpoints = [
    { name: 'Main Health', url: '/api/health', weight: 3 },
    { name: 'System Health', url: '/api/system/health', weight: 2 },
    { name: 'Billing Health', url: '/api/billing/health', weight: 1 }
  ];
  
  for (const endpoint of healthEndpoints) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint.url}`);
      const passed = response.status === 200;
      
      assessment.systemHealth.tests.push({
        name: endpoint.name,
        passed,
        weight: endpoint.weight,
        status: response.status
      });
      
      if (passed) assessment.systemHealth.score += endpoint.weight;
      
      console.log(`  ${passed ? '✅' : '❌'} ${endpoint.name}: ${response.status}`);
    } catch (error) {
      assessment.systemHealth.tests.push({
        name: endpoint.name,
        passed: false,
        weight: endpoint.weight,
        error: error.message
      });
      console.log(`  ❌ ${endpoint.name}: Error - ${error.message}`);
    }
  }
  
  const maxHealthScore = healthEndpoints.reduce((sum, e) => sum + e.weight, 0);
  assessment.systemHealth.percentage = (assessment.systemHealth.score / maxHealthScore) * 100;
  
  // 2. OAUTH INTEGRATION ASSESSMENT
  console.log('\n🔐 OAUTH INTEGRATION ASSESSMENT...\n');
  
  const platforms = ['twitter', 'facebook', 'instagram', 'linkedin'];
  
  for (const platform of platforms) {
    try {
      const callbackUrl = `${BASE_URL}/api/social/callback/${platform}`;
      const response = await makeRequest(callbackUrl);
      
      // 307 redirects are expected for OAuth callbacks
      const passed = [307, 302, 400, 401].includes(response.status);
      
      assessment.oauthIntegration.tests.push({
        name: `${platform} callback`,
        passed,
        status: response.status,
        weight: 1
      });
      
      if (passed) assessment.oauthIntegration.score += 1;
      
      console.log(`  ${passed ? '✅' : '❌'} ${platform.toUpperCase()} callback: ${response.status}`);
    } catch (error) {
      assessment.oauthIntegration.tests.push({
        name: `${platform} callback`,
        passed: false,
        error: error.message,
        weight: 1
      });
      console.log(`  ❌ ${platform.toUpperCase()} callback: Error - ${error.message}`);
    }
  }
  
  assessment.oauthIntegration.percentage = (assessment.oauthIntegration.score / platforms.length) * 100;
  
  // 3. SOCIAL MEDIA PROVIDERS ASSESSMENT
  console.log('\n📱 SOCIAL MEDIA PROVIDERS ASSESSMENT...\n');
  
  const providerTests = [
    { name: 'Social Accounts', url: '/api/social/accounts', weight: 3 },
    { name: 'Enhanced Test', url: '/api/social/enhanced/test', weight: 2 },
    { name: 'Facebook Connectivity', url: '/api/test/facebook-connectivity', weight: 2 },
    { name: 'LinkedIn Posting', url: '/api/test/linkedin-posting', weight: 2 }
  ];
  
  for (const test of providerTests) {
    try {
      const response = await makeRequest(`${BASE_URL}${test.url}`);
      const passed = response.status === 200;
      
      assessment.socialMediaProviders.tests.push({
        name: test.name,
        passed,
        weight: test.weight,
        status: response.status
      });
      
      if (passed) assessment.socialMediaProviders.score += test.weight;
      
      console.log(`  ${passed ? '✅' : '❌'} ${test.name}: ${response.status}`);
    } catch (error) {
      assessment.socialMediaProviders.tests.push({
        name: test.name,
        passed: false,
        weight: test.weight,
        error: error.message
      });
      console.log(`  ❌ ${test.name}: Error - ${error.message}`);
    }
  }
  
  const maxProviderScore = providerTests.reduce((sum, t) => sum + t.weight, 0);
  assessment.socialMediaProviders.percentage = (assessment.socialMediaProviders.score / maxProviderScore) * 100;
  
  // 4. ENHANCED FEATURES ASSESSMENT
  console.log('\n🚀 ENHANCED FEATURES ASSESSMENT...\n');
  
  const enhancedTests = [
    { name: 'Enhanced Connect', url: '/api/social/enhanced/connect', method: 'POST', weight: 2 },
    { name: 'Enhanced Publish', url: '/api/social/enhanced/publish', method: 'POST', weight: 2 }
  ];
  
  for (const test of enhancedTests) {
    try {
      const response = await makeRequest(`${BASE_URL}${test.url}`, {
        method: test.method,
        body: { platform: 'twitter' }
      });
      
      // 200 or 400 (validation error) are both acceptable
      const passed = [200, 400].includes(response.status);
      
      assessment.enhancedFeatures.tests.push({
        name: test.name,
        passed,
        weight: test.weight,
        status: response.status
      });
      
      if (passed) assessment.enhancedFeatures.score += test.weight;
      
      console.log(`  ${passed ? '✅' : '❌'} ${test.name}: ${response.status}`);
    } catch (error) {
      assessment.enhancedFeatures.tests.push({
        name: test.name,
        passed: false,
        weight: test.weight,
        error: error.message
      });
      console.log(`  ❌ ${test.name}: Error - ${error.message}`);
    }
  }
  
  const maxEnhancedScore = enhancedTests.reduce((sum, t) => sum + t.weight, 0);
  assessment.enhancedFeatures.percentage = (assessment.enhancedFeatures.score / maxEnhancedScore) * 100;
  
  // 5. CALCULATE OVERALL SCORE
  const weights = {
    systemHealth: 0.25,
    oauthIntegration: 0.30,
    socialMediaProviders: 0.30,
    enhancedFeatures: 0.15
  };
  
  assessment.overallScore = 
    (assessment.systemHealth.percentage * weights.systemHealth) +
    (assessment.oauthIntegration.percentage * weights.oauthIntegration) +
    (assessment.socialMediaProviders.percentage * weights.socialMediaProviders) +
    (assessment.enhancedFeatures.percentage * weights.enhancedFeatures);
  
  // 6. GENERATE RECOMMENDATIONS
  if (assessment.systemHealth.percentage < 80) {
    assessment.recommendations.push('🏥 System health needs attention - check failed health endpoints');
  }
  
  if (assessment.oauthIntegration.percentage < 90) {
    assessment.recommendations.push('🔐 OAuth integration needs review - some callback endpoints may be misconfigured');
  }
  
  if (assessment.socialMediaProviders.percentage < 80) {
    assessment.recommendations.push('📱 Social media providers need attention - check connectivity and authentication');
  }
  
  if (assessment.enhancedFeatures.percentage < 80) {
    assessment.recommendations.push('🚀 Enhanced features need review - check provider implementations');
  }
  
  if (assessment.overallScore >= 90) {
    assessment.recommendations.push('🎉 Excellent! System is ready for production deployment');
  } else if (assessment.overallScore >= 75) {
    assessment.recommendations.push('✅ Good! System is mostly ready with minor issues to address');
  } else if (assessment.overallScore >= 60) {
    assessment.recommendations.push('⚠️ Fair! System needs significant improvements before production');
  } else {
    assessment.recommendations.push('❌ Poor! System requires major fixes before deployment');
  }
  
  // 7. GENERATE FINAL REPORT
  console.log('\n🎯 FINAL ASSESSMENT REPORT');
  console.log('=' .repeat(80));
  console.log(`🏥 System Health: ${assessment.systemHealth.percentage.toFixed(1)}%`);
  console.log(`🔐 OAuth Integration: ${assessment.oauthIntegration.percentage.toFixed(1)}%`);
  console.log(`📱 Social Media Providers: ${assessment.socialMediaProviders.percentage.toFixed(1)}%`);
  console.log(`🚀 Enhanced Features: ${assessment.enhancedFeatures.percentage.toFixed(1)}%`);
  console.log(`\n📊 OVERALL SCORE: ${assessment.overallScore.toFixed(1)}%`);
  
  const grade = assessment.overallScore >= 90 ? 'A' :
                assessment.overallScore >= 80 ? 'B' :
                assessment.overallScore >= 70 ? 'C' :
                assessment.overallScore >= 60 ? 'D' : 'F';
  
  console.log(`🎓 GRADE: ${grade}`);
  
  console.log('\n💡 RECOMMENDATIONS:');
  assessment.recommendations.forEach(rec => console.log(`  ${rec}`));
  
  console.log('\n🏁 ASSESSMENT COMPLETE');
  console.log(`Finished at: ${new Date().toISOString()}`);
  
  return assessment;
}

if (require.main === module) {
  runFinalComprehensiveAssessment().then(assessment => {
    process.exit(assessment.overallScore >= 70 ? 0 : 1);
  }).catch(error => {
    console.error('❌ CRITICAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = { runFinalComprehensiveAssessment };
