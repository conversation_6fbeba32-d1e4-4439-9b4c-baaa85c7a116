// Email validation utilities for eWasl
// Prevents bounce backs and improves deliverability

export interface EmailValidationResult {
  isValid: boolean;
  error?: string;
  suggestion?: string;
}

// Common disposable email domains to block
const DISPOSABLE_DOMAINS = [
  '10minutemail.com',
  'tempmail.org',
  'guerrillamail.com',
  'mailinator.com',
  'yopmail.com',
  'temp-mail.org',
  'throwaway.email',
  'getnada.com',
  'maildrop.cc',
  'sharklasers.com'
];

// Common typos in popular email domains
const DOMAIN_SUGGESTIONS: Record<string, string> = {
  'gmai.com': 'gmail.com',
  'gmial.com': 'gmail.com',
  'gmail.co': 'gmail.com',
  'yahooo.com': 'yahoo.com',
  'yaho.com': 'yahoo.com',
  'hotmial.com': 'hotmail.com',
  'hotmai.com': 'hotmail.com',
  'outlok.com': 'outlook.com',
  'outloo.com': 'outlook.com'
};

export function validateEmail(email: string): EmailValidationResult {
  // Basic format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!email || email.trim() === '') {
    return {
      isValid: false,
      error: 'البريد الإلكتروني مطلوب'
    };
  }

  const trimmedEmail = email.trim().toLowerCase();

  if (!emailRegex.test(trimmedEmail)) {
    return {
      isValid: false,
      error: 'تنسيق البريد الإلكتروني غير صحيح'
    };
  }

  // Extract domain
  const domain = trimmedEmail.split('@')[1];

  // Check for disposable email domains
  if (DISPOSABLE_DOMAINS.includes(domain)) {
    return {
      isValid: false,
      error: 'البريد الإلكتروني المؤقت غير مسموح. يرجى استخدام بريد إلكتروني دائم'
    };
  }

  // Check for common typos and suggest corrections
  if (DOMAIN_SUGGESTIONS[domain]) {
    return {
      isValid: false,
      error: 'يبدو أن هناك خطأ في النطاق',
      suggestion: trimmedEmail.replace(domain, DOMAIN_SUGGESTIONS[domain])
    };
  }

  // Additional validation rules
  const localPart = trimmedEmail.split('@')[0];

  // Check local part length
  if (localPart.length > 64) {
    return {
      isValid: false,
      error: 'الجزء المحلي من البريد الإلكتروني طويل جداً'
    };
  }

  // Check domain length
  if (domain.length > 253) {
    return {
      isValid: false,
      error: 'نطاق البريد الإلكتروني طويل جداً'
    };
  }

  // Check for consecutive dots
  if (trimmedEmail.includes('..')) {
    return {
      isValid: false,
      error: 'البريد الإلكتروني يحتوي على نقاط متتالية'
    };
  }

  // Check for valid characters
  const validCharsRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!validCharsRegex.test(trimmedEmail)) {
    return {
      isValid: false,
      error: 'البريد الإلكتروني يحتوي على أحرف غير صالحة'
    };
  }

  return {
    isValid: true
  };
}

// Real-time email validation for forms
export function validateEmailRealTime(email: string): EmailValidationResult {
  const result = validateEmail(email);

  // For real-time validation, be less strict initially
  if (!email || email.trim() === '') {
    return { isValid: true }; // Don't show error for empty field
  }

  return result;
}

// Server-side email validation for registration
export function validateEmailForRegistration(email: string): EmailValidationResult {
  const result = validateEmail(email);

  if (!result.isValid) {
    return result;
  }

  // Additional server-side checks
  const trimmedEmail = email.trim().toLowerCase();
  const domain = trimmedEmail.split('@')[1];

  // Check for test/development emails
  const testPatterns = [
    /test.*@/,
    /demo.*@/,
    /fake.*@/,
    /example.*@/,
    /@test\./,
    /@example\./,
    /@localhost/,
    /@.*\.test$/,
    /@.*\.local$/
  ];

  for (const pattern of testPatterns) {
    if (pattern.test(trimmedEmail)) {
      return {
        isValid: false,
        error: 'عناوين البريد الإلكتروني التجريبية غير مسموحة في الإنتاج'
      };
    }
  }

  return { isValid: true };
}

// Clean existing invalid emails from database
export async function cleanInvalidEmails() {
  // This function would be used to clean up existing invalid emails
  // Implementation would depend on your specific needs
  console.log('Email cleanup function - implement based on your requirements');
}
