'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  ArrowRight,
  Users,
  Building,
  Globe,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { AccountManagementDashboard } from '@/components/social/account-management-dashboard';
import { createClient } from '@/lib/supabase/client';

interface SetupStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isRequired: boolean;
  action?: () => void;
}

export default function AccountSelectionPage() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [setupSteps, setSetupSteps] = useState<SetupStep[]>([]);
  const [overallProgress, setOverallProgress] = useState(0);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkAuthAndLoadData();
  }, []);

  const checkAuthAndLoadData = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        router.push('/auth/signin');
        return;
      }

      setUser(user);
      await loadSetupProgress(user.id);
      
    } catch (error) {
      console.error('Error checking auth:', error);
      router.push('/auth/signin');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSetupProgress = async (userId: string) => {
    try {
      // Check platform configurations
      const platforms = ['facebook', 'linkedin', 'instagram', 'twitter'];
      const configPromises = platforms.map(async (platform) => {
        try {
          const response = await fetch(`/api/social/business-accounts?platform=${platform}&userId=${userId}`);
          const data = await response.json();
          
          return {
            platform,
            isConnected: !data.requiresReconnection,
            isConfigured: data.isConfigured,
            hasBusinessAccounts: data.hasBusinessAccounts,
            selectedAccount: data.selectedAccount
          };
        } catch (error) {
          return {
            platform,
            isConnected: false,
            isConfigured: false,
            hasBusinessAccounts: false,
            selectedAccount: null
          };
        }
      });

      const platformStatuses = await Promise.all(configPromises);

      // Create setup steps based on platform statuses
      const steps: SetupStep[] = [
        {
          id: 'connect-facebook',
          title: 'ربط حساب فيسبوك',
          description: 'اربط حساب فيسبوك الخاص بك للوصول إلى صفحاتك التجارية',
          isCompleted: platformStatuses.find(p => p.platform === 'facebook')?.isConnected || false,
          isRequired: true,
          action: () => connectPlatform('facebook')
        },
        {
          id: 'select-facebook-page',
          title: 'اختيار صفحة فيسبوك',
          description: 'اختر الصفحة التجارية التي تريد النشر عليها',
          isCompleted: platformStatuses.find(p => p.platform === 'facebook')?.selectedAccount != null,
          isRequired: true
        },
        {
          id: 'connect-linkedin',
          title: 'ربط حساب لينكد إن',
          description: 'اربط حساب لينكد إن للوصول إلى شركاتك',
          isCompleted: platformStatuses.find(p => p.platform === 'linkedin')?.isConnected || false,
          isRequired: false,
          action: () => connectPlatform('linkedin')
        },
        {
          id: 'select-linkedin-company',
          title: 'اختيار شركة لينكد إن',
          description: 'اختر الشركة التي تريد النشر باسمها',
          isCompleted: platformStatuses.find(p => p.platform === 'linkedin')?.selectedAccount != null,
          isRequired: false
        },
        {
          id: 'connect-instagram',
          title: 'ربط حساب إنستغرام',
          description: 'اربط حساب إنستغرام التجاري الخاص بك',
          isCompleted: platformStatuses.find(p => p.platform === 'instagram')?.isConnected || false,
          isRequired: false,
          action: () => connectPlatform('instagram')
        },
        {
          id: 'connect-twitter',
          title: 'ربط حساب تويتر',
          description: 'اربط حساب تويتر الخاص بك',
          isCompleted: platformStatuses.find(p => p.platform === 'twitter')?.isConnected || false,
          isRequired: false,
          action: () => connectPlatform('twitter')
        }
      ];

      setSetupSteps(steps);

      // Calculate overall progress
      const completedSteps = steps.filter(step => step.isCompleted).length;
      const progress = (completedSteps / steps.length) * 100;
      setOverallProgress(progress);

    } catch (error) {
      console.error('Error loading setup progress:', error);
      toast.error('فشل في تحميل حالة الإعداد');
    }
  };

  const connectPlatform = async (platform: string) => {
    try {
      const response = await fetch('/api/social/connect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ platform })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to connect ${platform}`);
      }

      if (data.authUrl) {
        window.location.href = data.authUrl;
      }

    } catch (error: any) {
      console.error(`Error connecting ${platform}:`, error);
      toast.error(`فشل في ربط ${platform}`);
    }
  };

  const refreshSetup = async () => {
    if (user) {
      await loadSetupProgress(user.id);
      toast.success('تم تحديث حالة الإعداد');
    }
  };

  const proceedToDashboard = () => {
    router.push('/dashboard');
  };

  const proceedToPosts = () => {
    router.push('/posts');
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
          <span className="mr-3 text-lg">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  const requiredStepsCompleted = setupSteps.filter(step => step.isRequired && step.isCompleted).length;
  const totalRequiredSteps = setupSteps.filter(step => step.isRequired).length;
  const canProceed = requiredStepsCompleted === totalRequiredSteps;

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold">إعداد الحسابات التجارية</h1>
            <p className="text-muted-foreground">
              اربط وأعد حساباتك التجارية للبدء في إدارة منصات التواصل الاجتماعي
            </p>
          </div>
          <Button onClick={refreshSetup} variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            تحديث
          </Button>
        </div>

        {/* Progress Overview */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold">تقدم الإعداد</h3>
                <p className="text-sm text-muted-foreground">
                  {setupSteps.filter(s => s.isCompleted).length} من {setupSteps.length} خطوات مكتملة
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{Math.round(overallProgress)}%</div>
                <div className="text-sm text-muted-foreground">مكتمل</div>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Setup Steps */}
      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              خطوات الإعداد
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {setupSteps.map((step, index) => (
                <div key={step.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {step.isCompleted ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
                    )}
                    <div>
                      <h3 className="font-medium flex items-center gap-2">
                        {step.title}
                        {step.isRequired && (
                          <Badge variant="destructive" className="text-xs">مطلوب</Badge>
                        )}
                      </h3>
                      <p className="text-sm text-muted-foreground">{step.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {step.isCompleted ? (
                      <Badge className="bg-green-500">مكتمل</Badge>
                    ) : step.action ? (
                      <Button onClick={step.action} size="sm">
                        ربط الآن
                      </Button>
                    ) : (
                      <Badge variant="secondary">في الانتظار</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Account Management Dashboard */}
      {user && (
        <div className="mb-8">
          <AccountManagementDashboard userId={user.id} />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        {canProceed ? (
          <>
            <Button onClick={proceedToDashboard} className="flex items-center gap-2" size="lg">
              <ArrowRight className="w-5 h-5" />
              الانتقال إلى لوحة التحكم
            </Button>
            <Button onClick={proceedToPosts} variant="outline" className="flex items-center gap-2" size="lg">
              <Zap className="w-5 h-5" />
              إنشاء منشور جديد
            </Button>
          </>
        ) : (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              يجب إكمال الخطوات المطلوبة ({requiredStepsCompleted}/{totalRequiredSteps}) قبل المتابعة.
              اربط حساب فيسبوك واختر صفحة تجارية للبدء.
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Quick Stats */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Globe className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {setupSteps.filter(s => s.isCompleted).length}
            </div>
            <div className="text-sm text-muted-foreground">منصات متصلة</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Building className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {setupSteps.filter(s => s.isCompleted && !s.isRequired).length}
            </div>
            <div className="text-sm text-muted-foreground">منصات اختيارية</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Users className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {Math.round(overallProgress)}%
            </div>
            <div className="text-sm text-muted-foreground">نسبة الإكمال</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
