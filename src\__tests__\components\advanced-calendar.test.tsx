/**
 * Tests for Advanced Calendar Component
 * Tests the AdvancedCalendar component functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { AdvancedCalendar } from '@/components/scheduling/advanced-calendar';

// Mock react-big-calendar
jest.mock('react-big-calendar', () => ({
  Calendar: ({ events, onSelectEvent, onSelectSlot, components }: any) => (
    <div data-testid="calendar">
      <div data-testid="calendar-toolbar">
        {components?.toolbar && React.createElement(components.toolbar, {
          label: 'January 2024',
          onNavigate: jest.fn(),
          onView: jest.fn(),
        })}
      </div>
      <div data-testid="calendar-events">
        {events.map((event: any, index: number) => (
          <div
            key={index}
            data-testid={`calendar-event-${index}`}
            onClick={() => onSelectEvent(event)}
          >
            {components?.event && React.createElement(components.event, { event })}
          </div>
        ))}
      </div>
      <div
        data-testid="calendar-slot"
        onClick={() => onSelectSlot({ start: new Date('2024-01-15T10:00:00Z') })}
      >
        Click to select slot
      </div>
    </div>
  ),
  momentLocalizer: () => ({}),
  Views: {
    MONTH: 'month',
    WEEK: 'week',
    DAY: 'day',
  },
}));

jest.mock('moment', () => () => ({
  format: jest.fn().mockReturnValue('Jan 15'),
}));

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('AdvancedCalendar Component', () => {
  const mockPosts = [
    {
      id: 'post-1',
      title: 'Test Post 1',
      start: new Date('2024-01-15T09:00:00Z'),
      end: new Date('2024-01-15T09:30:00Z'),
      status: 'SCHEDULED' as const,
      platforms: ['TWITTER', 'FACEBOOK'],
      content: 'Test content 1',
      isRecurring: false,
    },
    {
      id: 'post-2',
      title: 'Test Post 2',
      start: new Date('2024-01-16T14:00:00Z'),
      end: new Date('2024-01-16T14:30:00Z'),
      status: 'PUBLISHED' as const,
      platforms: ['LINKEDIN'],
      content: 'Test content 2',
      isRecurring: true,
      parentRecurringId: 'recurring-1',
      engagement: {
        likes: 25,
        comments: 5,
        shares: 3,
      },
    },
    {
      id: 'post-3',
      title: 'Test Post 3',
      start: new Date('2024-01-17T11:00:00Z'),
      end: new Date('2024-01-17T11:30:00Z'),
      status: 'FAILED' as const,
      platforms: ['TWITTER'],
      content: 'Test content 3',
      isRecurring: false,
    },
  ];

  const defaultProps = {
    posts: mockPosts,
    onPostMove: jest.fn().mockResolvedValue(undefined),
    onPostClick: jest.fn(),
    onDateSelect: jest.fn(),
    onPostEdit: jest.fn(),
    onPostDelete: jest.fn().mockResolvedValue(undefined),
    onPostDuplicate: jest.fn(),
    loading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render calendar with posts', () => {
    render(<AdvancedCalendar {...defaultProps} />);

    expect(screen.getByTestId('calendar')).toBeInTheDocument();
    expect(screen.getByTestId('calendar-events')).toBeInTheDocument();
    expect(screen.getAllByTestId(/calendar-event-/)).toHaveLength(3);
  });

  it('should display statistics correctly', () => {
    render(<AdvancedCalendar {...defaultProps} />);

    // Check statistics cards
    expect(screen.getByText('3')).toBeInTheDocument(); // Total posts
    expect(screen.getByText('1')).toBeInTheDocument(); // Published posts
    expect(screen.getByText('1')).toBeInTheDocument(); // Scheduled posts
    expect(screen.getByText('1')).toBeInTheDocument(); // Failed posts
    expect(screen.getByText('1')).toBeInTheDocument(); // Recurring posts

    expect(screen.getByText('إجمالي المنشورات')).toBeInTheDocument();
    expect(screen.getByText('منشورة')).toBeInTheDocument();
    expect(screen.getByText('مجدولة')).toBeInTheDocument();
    expect(screen.getByText('فشلت')).toBeInTheDocument();
    expect(screen.getByText('متكررة')).toBeInTheDocument();
  });

  it('should handle post click events', () => {
    render(<AdvancedCalendar {...defaultProps} />);

    const firstEvent = screen.getByTestId('calendar-event-0');
    fireEvent.click(firstEvent);

    expect(defaultProps.onPostClick).toHaveBeenCalledWith(mockPosts[0]);
  });

  it('should handle date selection', () => {
    render(<AdvancedCalendar {...defaultProps} />);

    const calendarSlot = screen.getByTestId('calendar-slot');
    fireEvent.click(calendarSlot);

    expect(defaultProps.onDateSelect).toHaveBeenCalledWith(new Date('2024-01-15T10:00:00Z'));
  });

  it('should show loading state', () => {
    render(<AdvancedCalendar {...defaultProps} loading={true} />);

    expect(screen.getByText('جاري تحميل التقويم...')).toBeInTheDocument();
    expect(screen.queryByTestId('calendar')).not.toBeInTheDocument();
  });

  it('should handle view changes', () => {
    render(<AdvancedCalendar {...defaultProps} />);

    // Check if view buttons are rendered
    expect(screen.getByText('شهر')).toBeInTheDocument();
    expect(screen.getByText('أسبوع')).toBeInTheDocument();
    expect(screen.getByText('يوم')).toBeInTheDocument();

    // Click week view button
    fireEvent.click(screen.getByText('أسبوع'));
    // Note: The actual view change would be tested in integration tests
  });

  it('should render event component with platform badges', () => {
    const EventComponent = ({ event }: { event: any }) => (
      <div data-testid="event-component">
        <div>{event.title}</div>
        <div data-testid="platforms">
          {event.platforms.map((platform: string) => (
            <span key={platform} data-testid={`platform-${platform}`}>
              {platform.slice(0, 2)}
            </span>
          ))}
        </div>
        {event.isRecurring && <span data-testid="recurring-indicator">🔄</span>}
        {event.engagement && (
          <div data-testid="engagement">
            <span>👍 {event.engagement.likes}</span>
            <span>💬 {event.engagement.comments}</span>
            <span>🔄 {event.engagement.shares}</span>
          </div>
        )}
      </div>
    );

    // Mock the event component
    const mockCalendar = jest.requireMock('react-big-calendar').Calendar;
    mockCalendar.mockImplementation(({ events, components }: any) => (
      <div data-testid="calendar">
        {events.map((event: any, index: number) => (
          <div key={index} data-testid={`calendar-event-${index}`}>
            <EventComponent event={event} />
          </div>
        ))}
      </div>
    ));

    render(<AdvancedCalendar {...defaultProps} />);

    // Check platform badges
    expect(screen.getByTestId('platform-TWITTER')).toBeInTheDocument();
    expect(screen.getByTestId('platform-FACEBOOK')).toBeInTheDocument();
    expect(screen.getByTestId('platform-LINKEDIN')).toBeInTheDocument();

    // Check recurring indicator
    expect(screen.getByTestId('recurring-indicator')).toBeInTheDocument();

    // Check engagement metrics
    expect(screen.getByTestId('engagement')).toBeInTheDocument();
    expect(screen.getByText('👍 25')).toBeInTheDocument();
    expect(screen.getByText('💬 5')).toBeInTheDocument();
    expect(screen.getByText('🔄 3')).toBeInTheDocument();
  });

  it('should handle empty posts array', () => {
    render(<AdvancedCalendar {...defaultProps} posts={[]} />);

    expect(screen.getByTestId('calendar')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument(); // All statistics should be 0
  });

  it('should handle post move operations', async () => {
    const { toast } = require('sonner');
    
    render(<AdvancedCalendar {...defaultProps} />);

    // Simulate drag and drop (this would be more complex in real implementation)
    // For now, we'll test the callback directly
    const newDate = new Date('2024-01-20T10:00:00Z');
    
    // Call the onPostMove handler directly
    await defaultProps.onPostMove('post-1', newDate);

    expect(defaultProps.onPostMove).toHaveBeenCalledWith('post-1', newDate);
  });

  it('should handle post move errors', async () => {
    const { toast } = require('sonner');
    const onPostMoveError = jest.fn().mockRejectedValue(new Error('Move failed'));
    
    render(<AdvancedCalendar {...defaultProps} onPostMove={onPostMoveError} />);

    // Test error handling in post move
    try {
      await onPostMoveError('post-1', new Date());
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
    }
  });

  it('should apply correct styling based on post status', () => {
    // This would test the eventStyleGetter function
    // In a real implementation, we'd need to access the function and test it directly
    render(<AdvancedCalendar {...defaultProps} />);

    // The styling logic would be tested by checking computed styles
    // or by testing the eventStyleGetter function in isolation
    expect(screen.getByTestId('calendar')).toBeInTheDocument();
  });
});
