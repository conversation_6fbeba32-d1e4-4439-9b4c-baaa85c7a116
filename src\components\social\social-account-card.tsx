import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, RefreshCw, Trash2, Users, CheckCircle, AlertCircle } from "lucide-react";
import { formatDate } from "@/lib/utils";

interface SocialAccountCardProps {
  account: {
    id: string;
    platform: string;
    accountName: string;
    expiresAt?: Date;
    createdAt: Date;
    isConnected?: boolean;
    isActive?: boolean;
    followers?: number;
    avatar?: string;
  };
  onRefresh?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export function SocialAccountCard({
  account,
  onRefresh,
  onDelete,
}: SocialAccountCardProps) {
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "𝕏";
      case "FACEBOOK":
        return "f";
      case "INSTAGRAM":
        return "📷";
      case "LINKEDIN":
        return "in";
      case "TIKTOK":
        return "TT";
      default:
        return "?";
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "bg-black text-white";
      case "FACEBOOK":
        return "bg-blue-600 text-white";
      case "INSTAGRAM":
        return "bg-pink-600 text-white";
      case "LINKEDIN":
        return "bg-blue-700 text-white";
      case "TIKTOK":
        return "bg-black text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "تويتر";
      case "FACEBOOK":
        return "فيسبوك";
      case "INSTAGRAM":
        return "انستغرام";
      case "LINKEDIN":
        return "لينكد إن";
      case "TIKTOK":
        return "تيك توك";
      default:
        return platform;
    }
  };

  const formatFollowers = (count?: number) => {
    if (!count) return "0";
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div
              className={`w-12 h-12 rounded-xl flex items-center justify-center mr-3 ${getPlatformColor(
                account.platform
              )} shadow-lg`}
            >
              {getPlatformIcon(account.platform)}
            </div>
            <div>
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg">{account.accountName}</CardTitle>
                {account.isActive ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <AlertCircle className="w-4 h-4 text-yellow-500" />
                )}
              </div>
              <CardDescription className="flex items-center gap-2">
                {getPlatformName(account.platform)}
                <Badge variant={account.isActive ? "default" : "secondary"} className="text-xs">
                  {account.isActive ? "نشط" : "غير نشط"}
                </Badge>
              </CardDescription>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">القائمة</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {onRefresh && (
                <DropdownMenuItem onClick={() => onRefresh(account.id)}>
                  <RefreshCw className="ml-2 h-4 w-4" />
                  تحديث الرمز
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onDelete && (
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onDelete(account.id)}
                >
                  <Trash2 className="ml-2 h-4 w-4" />
                  إزالة الحساب
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {account.followers && (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">المتابعون</span>
              </div>
              <span className="font-semibold text-gray-900">{formatFollowers(account.followers)}</span>
            </div>
          )}
          <div className="text-sm space-y-1">
            <p>
              <span className="text-muted-foreground">تم الربط في: </span>
              <span className="font-medium">{formatDate(account.createdAt)}</span>
            </p>
            {account.expiresAt && (
              <p>
                <span className="text-muted-foreground">ينتهي في: </span>
                <span className="font-medium">{formatDate(account.expiresAt)}</span>
              </p>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-3">
        <div className="flex gap-2 w-full">
          {onRefresh && (
            <Button variant="outline" className="flex-1" onClick={() => onRefresh(account.id)}>
              <RefreshCw className="ml-2 h-4 w-4" />
              تحديث
            </Button>
          )}
          <Button variant="outline" className="flex-1">
            عرض الإحصائيات
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
