import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Create a live LinkedIn test post
 * POST /api/test/linkedin-live-post
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Creating live LinkedIn test post...');

    const body = await request.json();
    const { 
      accountName = 'burak ozan',
      content = '🧪 Testing eWasl Social Scheduler LinkedIn integration! 🚀\n\nThis is a live test of our LinkedIn posting functionality.\n\n#eWasl #LinkedIn #SocialMediaManagement #Test',
      dryRun = false 
    } = body;

    const results = {
      timestamp: new Date().toISOString(),
      accountName,
      content,
      dryRun,
      steps: {} as any
    };

    // Step 1: Get LinkedIn account data
    try {
      console.log('Step 1: Retrieving LinkedIn account data...');
      const supabase = createServiceRoleClient();
      const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
      
      const { data: account, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'LINKEDIN')
        .eq('account_name', accountName)
        .single();

      if (error || !account) {
        throw new Error(`LinkedIn account "${accountName}" not found`);
      }

      results.steps.accountRetrieval = {
        success: true,
        account: {
          id: account.id,
          account_name: account.account_name,
          account_id: account.account_id,
          expires_at: account.expires_at,
          hasAccessToken: !!account.access_token
        }
      };

      console.log(`✅ Found LinkedIn account: ${account.account_name} (${account.account_id})`);

      // Step 2: Validate access token
      try {
        console.log('Step 2: Validating LinkedIn access token...');
        
        const response = await fetch(`https://api.linkedin.com/v2/userinfo`, {
          headers: {
            'Authorization': `Bearer ${account.access_token}`,
            'Accept': 'application/json',
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Token validation failed: ${response.status} - ${errorText}`);
        }

        const userData = await response.json();
        results.steps.tokenValidation = {
          success: true,
          userInfo: {
            name: userData.name,
            email: userData.email
          }
        };

        console.log(`✅ Token validation successful for: ${userData.name}`);

        // Step 3: Prepare LinkedIn post data
        console.log('Step 3: Preparing LinkedIn post data...');
        
        const postData = {
          author: `urn:li:person:${account.account_id}`,
          lifecycleState: 'PUBLISHED',
          specificContent: {
            'com.linkedin.ugc.ShareContent': {
              shareCommentary: {
                text: content
              },
              shareMediaCategory: 'NONE'
            }
          },
          visibility: {
            'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
          }
        };

        results.steps.postDataPreparation = {
          success: true,
          postData: {
            author: postData.author,
            contentLength: content.length,
            mediaCategory: postData.specificContent['com.linkedin.ugc.ShareContent'].shareMediaCategory
          }
        };

        console.log('✅ LinkedIn post data prepared successfully');

        if (dryRun) {
          // Dry run - don't actually post
          results.steps.posting = {
            success: true,
            dryRun: true,
            message: 'Dry run completed - no actual post created'
          };

          return NextResponse.json({
            success: true,
            message: '✅ LinkedIn posting dry run completed successfully',
            results,
            recommendation: 'All checks passed - ready for live posting!'
          });
        }

        // Step 4: Create actual LinkedIn post
        console.log('Step 4: Creating LinkedIn post...');
        
        const postResponse = await fetch('https://api.linkedin.com/v2/ugcPosts', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${account.access_token}`,
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0'
          },
          body: JSON.stringify(postData)
        });

        if (!postResponse.ok) {
          const errorText = await postResponse.text();
          throw new Error(`LinkedIn posting failed: ${postResponse.status} - ${errorText}`);
        }

        const postResult = await postResponse.json();
        
        results.steps.posting = {
          success: true,
          postId: postResult.id,
          linkedinPostUrl: `https://www.linkedin.com/feed/update/${postResult.id}/`,
          apiResponse: {
            id: postResult.id,
            status: 'PUBLISHED'
          }
        };

        console.log(`🎉 LinkedIn post created successfully! Post ID: ${postResult.id}`);

        // Step 5: Verify post creation
        console.log('Step 5: Verifying post creation...');
        
        // Wait a moment for LinkedIn to process the post
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Try to fetch the post to verify it exists
        try {
          const verifyResponse = await fetch(`https://api.linkedin.com/v2/ugcPosts/${postResult.id}`, {
            headers: {
              'Authorization': `Bearer ${account.access_token}`,
              'Accept': 'application/json'
            }
          });

          if (verifyResponse.ok) {
            const verifyData = await verifyResponse.json();
            results.steps.verification = {
              success: true,
              postExists: true,
              postStatus: verifyData.lifecycleState || 'UNKNOWN'
            };
            console.log('✅ Post verification successful');
          } else {
            results.steps.verification = {
              success: false,
              error: `Verification failed: ${verifyResponse.status}`,
              note: 'Post may still be successful - LinkedIn API verification can be delayed'
            };
          }
        } catch (verifyError: any) {
          results.steps.verification = {
            success: false,
            error: verifyError.message,
            note: 'Post may still be successful - verification is optional'
          };
        }

        return NextResponse.json({
          success: true,
          message: '🎉 LinkedIn test post created successfully!',
          results,
          postUrl: `https://www.linkedin.com/feed/update/${postResult.id}/`,
          instructions: {
            verification: 'Check the LinkedIn profile to verify the post appears correctly',
            cleanup: 'You can delete the test post from LinkedIn if desired',
            postId: postResult.id
          }
        });

      } catch (tokenError: any) {
        results.steps.tokenValidation = {
          success: false,
          error: tokenError.message
        };
        throw tokenError;
      }

    } catch (accountError: any) {
      results.steps.accountRetrieval = {
        success: false,
        error: accountError.message
      };
      throw accountError;
    }

  } catch (error: any) {
    console.error('❌ LinkedIn live posting test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      results,
      details: 'LinkedIn live posting test failed'
    }, { status: 500 });
  }
}

/**
 * Get test status and instructions
 * GET /api/test/linkedin-live-post
 */
export async function GET() {
  return NextResponse.json({
    message: '🧪 LinkedIn Live Posting Test Endpoint',
    description: 'Create a live test post on LinkedIn to verify posting functionality',
    usage: {
      method: 'POST',
      body: {
        accountName: 'burak ozan (default)',
        content: 'Custom test content (optional)',
        dryRun: 'true/false (default: false)'
      }
    },
    safety: {
      dryRun: 'Set dryRun: true to test without creating actual posts',
      cleanup: 'Test posts can be manually deleted from LinkedIn after verification'
    },
    accounts: {
      available: ['burak ozan', 'Taha Osama'],
      recommended: 'burak ozan (Account ID: kEYfMd6P10)'
    }
  });
}
