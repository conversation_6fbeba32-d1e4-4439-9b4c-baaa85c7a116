import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for workspace creation
const createWorkspaceSchema = z.object({
  name: z.string().min(2).max(100),
  slug: z.string().min(2).max(50).regex(/^[a-z0-9-]+$/),
  description: z.string().optional(),
  timezone: z.string().default('UTC'),
  language: z.enum(['ar', 'en']).default('ar'),
  industry: z.string().optional(),
  company_size: z.enum(['1-10', '11-50', '51-200', '201-1000', '1000+']).optional(),
  website_url: z.string().url().optional(),
});

// Validation schema for workspace updates
const updateWorkspaceSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  description: z.string().optional(),
  timezone: z.string().optional(),
  language: z.enum(['ar', 'en']).optional(),
  industry: z.string().optional(),
  company_size: z.enum(['1-10', '11-50', '51-200', '201-1000', '1000+']).optional(),
  website_url: z.string().url().optional(),
  settings: z.record(z.any()).optional(),
});

// GET - Get user's workspaces
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching user workspaces...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's workspaces with member details
    const { data: workspaces, error: workspacesError } = await supabase
      .from('workspace_members')
      .select(`
        role,
        status,
        joined_at,
        workspaces (
          id,
          name,
          slug,
          description,
          plan_type,
          subscription_status,
          limits,
          avatar_url,
          timezone,
          language,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .order('joined_at', { ascending: false });

    if (workspacesError) {
      console.error('Error fetching workspaces:', workspacesError);
      return NextResponse.json(
        { error: 'Failed to fetch workspaces' },
        { status: 500 }
      );
    }

    // Transform the data to include member role
    const userWorkspaces = workspaces?.map(member => ({
      ...member.workspaces,
      user_role: member.role,
      user_status: member.status,
      joined_at: member.joined_at
    })) || [];

    // Get workspace statistics for each workspace
    const workspacesWithStats = await Promise.all(
      userWorkspaces.map(async (workspace) => {
        // Get member count
        const { count: memberCount } = await supabase
          .from('workspace_members')
          .select('*', { count: 'exact', head: true })
          .eq('workspace_id', (workspace as any).id)
          .eq('status', 'active');

        // Get posts count
        const { count: postsCount } = await supabase
          .from('posts')
          .select('*', { count: 'exact', head: true })
          .eq('workspace_id', (workspace as any).id);

        // Get social accounts count
        const { count: socialAccountsCount } = await supabase
          .from('social_accounts')
          .select('*', { count: 'exact', head: true })
          .eq('workspace_id', (workspace as any).id);

        return {
          ...workspace,
          stats: {
            members: memberCount || 0,
            posts: postsCount || 0,
            social_accounts: socialAccountsCount || 0
          }
        };
      })
    );

    console.log(`Found ${workspacesWithStats.length} workspaces for user ${user.id}`);

    return NextResponse.json({
      success: true,
      workspaces: workspacesWithStats
    }, { status: 200 });

  } catch (error) {
    console.error('Workspaces fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new workspace
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Creating new workspace...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createWorkspaceSchema.parse(body);

    console.log('Creating workspace:', validatedData);

    // Check if slug is already taken
    const { data: existingWorkspace } = await supabase
      .from('workspaces')
      .select('id')
      .eq('slug', validatedData.slug)
      .single();

    if (existingWorkspace) {
      return NextResponse.json(
        { error: 'Workspace slug already exists' },
        { status: 400 }
      );
    }

    // Create workspace
    const { data: workspace, error: workspaceError } = await supabase
      .from('workspaces')
      .insert({
        name: validatedData.name,
        slug: validatedData.slug,
        description: validatedData.description,
        timezone: validatedData.timezone,
        language: validatedData.language,
        industry: validatedData.industry,
        company_size: validatedData.company_size,
        website_url: validatedData.website_url,
        plan_type: 'FREE',
        limits: {
          posts_per_month: 5,
          social_accounts: 2,
          team_members: 1,
          scheduled_posts: 10
        }
      })
      .select()
      .single();

    if (workspaceError) {
      console.error('Error creating workspace:', workspaceError);
      return NextResponse.json(
        { error: 'Failed to create workspace' },
        { status: 500 }
      );
    }

    // Add creator as workspace owner
    const { error: memberError } = await supabase
      .from('workspace_members')
      .insert({
        workspace_id: workspace.id,
        user_id: user.id,
        role: 'OWNER',
        status: 'active',
        joined_at: new Date().toISOString()
      });

    if (memberError) {
      console.error('Error adding workspace owner:', memberError);
      // Try to clean up the workspace
      await supabase.from('workspaces').delete().eq('id', workspace.id);
      return NextResponse.json(
        { error: 'Failed to set workspace ownership' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: workspace.id,
        action: 'WORKSPACE_CREATED',
        details: `Created workspace "${workspace.name}"`,
        created_at: new Date().toISOString(),
      });

    console.log('Workspace created successfully:', workspace.id);

    return NextResponse.json({
      success: true,
      workspace: {
        ...workspace,
        user_role: 'OWNER',
        user_status: 'active',
        stats: {
          members: 1,
          posts: 0,
          social_accounts: 0
        }
      },
      message: 'Workspace created successfully'
    }, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid workspace data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Workspace creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update workspace
export async function PUT(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Updating workspace...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get workspace ID from query params
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('id');

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateWorkspaceSchema.parse(body);

    console.log('Updating workspace:', workspaceId, validatedData);

    // Check if user has permission to update workspace
    const { data: member } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!member || !['OWNER', 'ADMIN'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update workspace' },
        { status: 403 }
      );
    }

    // Update workspace
    const { data: workspace, error: updateError } = await supabase
      .from('workspaces')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', workspaceId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating workspace:', updateError);
      return NextResponse.json(
        { error: 'Failed to update workspace' },
        { status: 500 }
      );
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        workspace_id: workspaceId,
        action: 'WORKSPACE_UPDATED',
        details: `Updated workspace settings`,
        created_at: new Date().toISOString(),
      });

    console.log('Workspace updated successfully:', workspaceId);

    return NextResponse.json({
      success: true,
      workspace,
      message: 'Workspace updated successfully'
    }, { status: 200 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid workspace data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Workspace update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
