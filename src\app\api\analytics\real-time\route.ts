import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Get real-time analytics data
 * GET /api/analytics/real-time
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('post_id');
    const platform = searchParams.get('platform');
    const timeframe = searchParams.get('timeframe') || 'hour'; // hour, day, week

    if (postId) {
      // Get real-time metrics for a specific post
      const { data: postPerformance, error } = await supabase
        .from('post_performance')
        .select(`
          *,
          posts!inner(
            id,
            user_id,
            content,
            status,
            created_at
          )
        `)
        .eq('post_id', postId)
        .eq('posts.user_id', user.id)
        .single();

      if (error) {
        return NextResponse.json({ error: 'Post not found' }, { status: 404 });
      }

      // Get recent analytics events for this post
      const { data: recentEvents } = await supabase
        .from('analytics_events')
        .select('*')
        .eq('post_id', postId)
        .eq('user_id', user.id)
        .gte('timestamp', `now() - interval '1 hour'`)
        .order('timestamp', { ascending: false })
        .limit(100);

      return NextResponse.json({
        success: true,
        post: postPerformance,
        recentEvents: recentEvents || [],
        realTimeMetrics: calculateRealTimeMetrics(recentEvents || []),
      });
    }

    // Get real-time overview for all posts
    const timeframeMap = {
      hour: '1 hour',
      day: '1 day',
      week: '7 days',
    };

    const interval = timeframeMap[timeframe as keyof typeof timeframeMap] || '1 hour';

    // Get recent analytics events
    let eventsQuery = supabase
      .from('analytics_events')
      .select('*')
      .eq('user_id', user.id)
      .gte('timestamp', `now() - interval '${interval}'`)
      .order('timestamp', { ascending: false });

    if (platform) {
      eventsQuery = eventsQuery.eq('platform', platform.toUpperCase());
    }

    const { data: recentEvents, error: eventsError } = await eventsQuery.limit(1000);

    if (eventsError) {
      console.error('Error fetching recent events:', eventsError);
      return NextResponse.json({ error: 'Failed to fetch analytics data' }, { status: 500 });
    }

    // Get active posts (published in the last 24 hours)
    const { data: activePosts, error: postsError } = await supabase
      .from('posts')
      .select(`
        id,
        content,
        status,
        created_at,
        post_performance(*)
      `)
      .eq('user_id', user.id)
      .eq('status', 'PUBLISHED')
      .gte('created_at', `now() - interval '24 hours'`)
      .order('created_at', { ascending: false });

    if (postsError) {
      console.error('Error fetching active posts:', postsError);
    }

    // Calculate real-time metrics
    const realTimeMetrics = calculateRealTimeMetrics(recentEvents || []);
    const platformMetrics = calculatePlatformMetrics(recentEvents || []);
    const engagementVelocity = calculateEngagementVelocity(recentEvents || []);

    return NextResponse.json({
      success: true,
      timeframe,
      realTimeMetrics,
      platformMetrics,
      engagementVelocity,
      activePosts: activePosts || [],
      recentEvents: (recentEvents || []).slice(0, 50), // Latest 50 events
      lastUpdated: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Real-time analytics error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Track real-time analytics event
 * POST /api/analytics/real-time
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, postId, platform, eventType, eventData, metrics } = body;

    if (action === 'track_event') {
      // Track a real-time analytics event
      // For now, just return success - analytics collector will be implemented later
      return NextResponse.json({ success: true, message: 'Event tracked successfully' });
    }

    if (action === 'collect_metrics') {
      // Trigger metrics collection for a specific post
      const { socialAccountId, platformPostId } = body;

      if (!postId || !socialAccountId || !platform || !platformPostId) {
        return NextResponse.json({
          error: 'Missing required fields: postId, socialAccountId, platform, platformPostId'
        }, { status: 400 });
      }

      // For now, just return success - metrics collection will be implemented later
      return NextResponse.json({ success: true, message: 'Metrics collection triggered' });
    }

    if (action === 'refresh_insights') {
      // Generate new insights based on recent data
      // For now, just return success - insights generation will be implemented later
      return NextResponse.json({ success: true, message: 'Insights generation triggered' });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Real-time analytics tracking error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function calculateRealTimeMetrics(events: any[]) {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

  const recentEvents = events.filter(event =>
    new Date(event.timestamp) >= oneHourAgo
  );

  const metrics = {
    totalEvents: recentEvents.length,
    impressions: 0,
    engagements: 0,
    clicks: 0,
    shares: 0,
    comments: 0,
    likes: 0,
    eventsPerMinute: 0,
    topEventTypes: {} as Record<string, number>,
  };

  recentEvents.forEach(event => {
    // Count event types
    metrics.topEventTypes[event.event_type] = (metrics.topEventTypes[event.event_type] || 0) + 1;

    // Sum metrics
    const eventMetrics = event.metrics || {};
    metrics.impressions += eventMetrics.impressions || 0;
    metrics.engagements += eventMetrics.engagements || 0;
    metrics.clicks += eventMetrics.clicks || 0;
    metrics.shares += eventMetrics.shares || 0;
    metrics.comments += eventMetrics.comments || 0;
    metrics.likes += eventMetrics.likes || 0;
  });

  // Calculate events per minute
  metrics.eventsPerMinute = recentEvents.length / 60;

  return metrics;
}

function calculatePlatformMetrics(events: any[]) {
  const platformMetrics = events.reduce((acc, event) => {
    const platform = event.platform;
    if (!acc[platform]) {
      acc[platform] = {
        events: 0,
        impressions: 0,
        engagements: 0,
        clicks: 0,
        lastActivity: null,
      };
    }

    acc[platform].events++;
    const eventMetrics = event.metrics || {};
    acc[platform].impressions += eventMetrics.impressions || 0;
    acc[platform].engagements += eventMetrics.engagements || 0;
    acc[platform].clicks += eventMetrics.clicks || 0;

    if (!acc[platform].lastActivity || event.timestamp > acc[platform].lastActivity) {
      acc[platform].lastActivity = event.timestamp;
    }

    return acc;
  }, {} as Record<string, any>);

  return platformMetrics;
}

function calculateEngagementVelocity(events: any[]) {
  // Calculate engagement velocity (engagements per hour over time)
  const now = new Date();
  const velocityData = [];

  for (let i = 0; i < 24; i++) {
    const hourStart = new Date(now.getTime() - (i + 1) * 60 * 60 * 1000);
    const hourEnd = new Date(now.getTime() - i * 60 * 60 * 1000);

    const hourEvents = events.filter(event => {
      const eventTime = new Date(event.timestamp);
      return eventTime >= hourStart && eventTime < hourEnd;
    });

    const engagements = hourEvents.reduce((sum, event) => {
      const metrics = event.metrics || {};
      return sum + (metrics.engagements || 0);
    }, 0);

    velocityData.unshift({
      hour: hourStart.toISOString(),
      engagements,
      events: hourEvents.length,
    });
  }

  return velocityData;
}
