import { NextRequest, NextResponse } from 'next/server';
import { getOAuthConfig } from '@/lib/social/oauth-config';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    console.log('Instagram OAuth callback received:', {
      code: code ? 'present' : 'missing',
      state,
      error,
      errorDescription,
      timestamp: new Date().toISOString()
    });

    // Handle OAuth errors
    if (error) {
      console.error('Instagram OAuth error:', { error, errorDescription });
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=oauth_error&message=${encodeURIComponent(errorDescription || error)}&platform=instagram`
      );
    }

    // Validate required parameters
    if (!code) {
      console.error('Instagram OAuth callback missing authorization code');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=missing_code&platform=instagram`
      );
    }

    // Get Instagram OAuth configuration
    const config = getOAuthConfig('instagram');
    if (!config || !config.enabled) {
      console.error('Instagram OAuth not configured or disabled');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=platform_not_configured&platform=instagram`
      );
    }

    console.log('Instagram OAuth config loaded:', {
      clientId: config.clientId ? 'present' : 'missing',
      redirectUri: config.redirectUri,
      scopes: config.scope
    });

    // Exchange authorization code for access token
    console.log('Exchanging authorization code for access token...');
    const tokenParams = new URLSearchParams({
      client_id: config.clientId,
      client_secret: config.clientSecret,
      code: code,
      redirect_uri: config.redirectUri,
    });

    const tokenResponse = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Instagram token exchange failed:', {
        status: tokenResponse.status,
        statusText: tokenResponse.statusText,
        error: errorText
      });
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=token_exchange_failed&platform=instagram`
      );
    }

    const tokenData = await tokenResponse.json();
    console.log('Instagram token exchange successful:', {
      hasAccessToken: !!tokenData.access_token,
      tokenType: tokenData.token_type,
      expiresIn: tokenData.expires_in
    });

    // Fetch user profile information
    console.log('Fetching Instagram user profile...');
    const profileResponse = await fetch(
      `${config.userInfoUrl}?fields=id,name,email&access_token=${tokenData.access_token}`
    );

    if (!profileResponse.ok) {
      const errorText = await profileResponse.text();
      console.error('Instagram profile fetch failed:', {
        status: profileResponse.status,
        statusText: profileResponse.statusText,
        error: errorText
      });
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=profile_fetch_failed&platform=instagram`
      );
    }

    const profileData = await profileResponse.json();
    console.log('Instagram profile fetched successfully:', {
      id: profileData.id,
      name: profileData.name,
      email: profileData.email
    });

    // Check if this is a business account by trying to get Instagram business accounts
    console.log('Checking for Instagram business accounts...');
    const businessAccountsResponse = await fetch(
      `https://graph.facebook.com/v19.0/me/accounts?fields=instagram_business_account&access_token=${tokenData.access_token}`
    );

    let instagramBusinessAccount = null;
    if (businessAccountsResponse.ok) {
      const businessAccountsData = await businessAccountsResponse.json();
      console.log('Business accounts response:', businessAccountsData);
      
      // Find the first page with an Instagram business account
      const pageWithInstagram = businessAccountsData.data?.find(
        (page: any) => page.instagram_business_account
      );
      
      if (pageWithInstagram) {
        instagramBusinessAccount = pageWithInstagram.instagram_business_account;
        console.log('Found Instagram business account:', instagramBusinessAccount);
      } else {
        console.warn('No Instagram business account found');
        return NextResponse.redirect(
          `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=no_business_account&platform=instagram&message=${encodeURIComponent('Instagram Graph API requires a business account. Please connect your Instagram account to a Facebook Page and convert it to a business account.')}`
        );
      }
    } else {
      console.error('Failed to fetch business accounts');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=business_account_check_failed&platform=instagram`
      );
    }

    // Store the Instagram account in database
    // Use service role client to bypass RLS policies
    const supabase = createServiceRoleClient();

    // Use an existing user ID for testing
    // In a full implementation, you'd get the actual user ID from session
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037'; // Demo User

    console.log('Looking up user in database...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', demoUserId)
      .single();

    if (userError || !userData) {
      console.error('User lookup failed:', userError);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=user_not_found&platform=instagram`
      );
    }

    console.log('User found:', userData);

    // Check if Instagram account already exists
    const { data: existingAccount } = await supabase
      .from('social_accounts')
      .select('id')
      .eq('user_id', demoUserId)
      .eq('platform', 'INSTAGRAM')
      .eq('account_id', instagramBusinessAccount.id)
      .single();

    if (existingAccount) {
      console.log('Instagram account already exists, updating...');
      const { error: updateError } = await supabase
        .from('social_accounts')
        .update({
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token || null,
          expires_at: tokenData.expires_in
            ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
            : null,
          account_name: profileData.name,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingAccount.id);

      if (updateError) {
        console.error('Failed to update Instagram account:', updateError);
        return NextResponse.redirect(
          `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=account_update_failed&platform=instagram`
        );
      }
    } else {
      console.log('Creating new Instagram account...');
      const { error: insertError } = await supabase
        .from('social_accounts')
        .insert({
          user_id: demoUserId,
          platform: 'INSTAGRAM',
          account_id: instagramBusinessAccount.id,
          account_name: profileData.name,
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token || null,
          expires_at: tokenData.expires_in
            ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
            : null
        });

      if (insertError) {
        console.error('Failed to create Instagram account:', insertError);
        return NextResponse.redirect(
          `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=account_creation_failed&platform=instagram`
        );
      }
    }

    console.log('Instagram OAuth flow completed successfully');

    // Redirect back to social accounts page with success message
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/social?success=instagram_connected&account=${encodeURIComponent(profileData.name)}&type=business`
    );

  } catch (error) {
    console.error('Instagram OAuth callback error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/auth/error?error=internal_error&platform=instagram&message=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    );
  }
}
