// Verification script to ensure ALL Supabase module-level issues are fixed
const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE SUPABASE VERIFICATION');
console.log('=====================================\n');

// Find all API route files
function findApiRoutes(dir, routes = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findApiRoutes(fullPath, routes);
    } else if (file === 'route.ts') {
      routes.push(fullPath);
    }
  });
  
  return routes;
}

// Check for problematic patterns
function checkForIssues(content, filePath) {
  const issues = [];
  
  // Pattern 1: Module-level const supabase = createClient
  if (/const supabase = createClient\(/m.test(content)) {
    issues.push('Module-level const supabase = createClient');
  }
  
  // Pattern 2: Export const supabase = createClient
  if (/export const supabase = createClient\(/m.test(content)) {
    issues.push('Module-level export const supabase = createClient');
  }
  
  // Pattern 3: Let supabase = createClient
  if (/let supabase = createClient\(/m.test(content)) {
    issues.push('Module-level let supabase = createClient');
  }
  
  // Pattern 4: Missing getSupabaseClient function but has createClient import
  if (content.includes('createClient') && 
      !content.includes('function getSupabaseClient()') &&
      !content.includes('const getSupabaseClient =')) {
    issues.push('Has createClient import but no getSupabaseClient function');
  }
  
  // Pattern 5: Function has supabase usage but no client creation
  const exportFunctionMatches = content.match(/export async function (GET|POST|PUT|DELETE|PATCH)\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}/g);
  if (exportFunctionMatches) {
    exportFunctionMatches.forEach(func => {
      if (func.includes('supabase.') && !func.includes('const supabase = getSupabaseClient()')) {
        issues.push('Function uses supabase but missing client creation');
      }
    });
  }
  
  return issues;
}

// Main verification
const apiDir = 'src/app/api';
const routes = findApiRoutes(apiDir);

console.log(`📊 Checking ${routes.length} API route files\n`);

let totalIssues = 0;
let problematicFiles = [];

routes.forEach(routePath => {
  try {
    const content = fs.readFileSync(routePath, 'utf8');
    const issues = checkForIssues(content, routePath);
    
    if (issues.length > 0) {
      problematicFiles.push({ path: routePath, issues });
      totalIssues += issues.length;
      
      console.log(`❌ ${routePath}`);
      issues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
      console.log('');
    }
  } catch (error) {
    console.log(`⚠️  Error reading ${routePath}: ${error.message}`);
  }
});

console.log(`\n📈 VERIFICATION RESULTS:`);
console.log(`Total API routes checked: ${routes.length}`);
console.log(`Files with issues: ${problematicFiles.length}`);
console.log(`Total issues found: ${totalIssues}`);

if (totalIssues === 0) {
  console.log('\n🎉 ALL SUPABASE ISSUES RESOLVED!');
  console.log('✅ No module-level Supabase clients found');
  console.log('✅ All routes use runtime initialization');
  console.log('✅ Ready for successful DigitalOcean deployment');
} else {
  console.log('\n⚠️  ISSUES STILL EXIST:');
  problematicFiles.forEach(file => {
    console.log(`\n📁 ${file.path}:`);
    file.issues.forEach(issue => {
      console.log(`   ❌ ${issue}`);
    });
  });
  
  console.log('\n🔧 These files need manual fixing before deployment');
}

// Additional check for specific patterns that might cause build failures
console.log('\n🔍 CHECKING FOR BUILD-BREAKING PATTERNS...');

let buildBreakers = 0;
routes.forEach(routePath => {
  try {
    const content = fs.readFileSync(routePath, 'utf8');
    
    // Check for the exact pattern that caused the build failure
    if (content.includes('const supabase = createClient(') && 
        content.includes('process.env.NEXT_PUBLIC_SUPABASE_URL') &&
        !content.includes('function getSupabaseClient()')) {
      buildBreakers++;
      console.log(`💥 BUILD BREAKER: ${routePath}`);
    }
  } catch (error) {
    // Ignore read errors for this check
  }
});

if (buildBreakers === 0) {
  console.log('✅ No build-breaking patterns found');
} else {
  console.log(`❌ ${buildBreakers} files will cause build failures`);
}

console.log('\n🚀 Verification complete!');
