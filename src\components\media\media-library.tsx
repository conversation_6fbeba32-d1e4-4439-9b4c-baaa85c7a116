'use client';

import React, { useState, useEffect } from 'react';
import { X, Trash2, Download, Eye, Image, Video, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface MediaFile {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  createdAt: string;
}

interface MediaLibraryProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectMedia?: (media: MediaFile) => void;
  allowMultiple?: boolean;
}

export function MediaLibrary({
  isOpen,
  onClose,
  onSelectMedia,
  allowMultiple = false
}: MediaLibraryProps) {
  const [media, setMedia] = useState<MediaFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile[]>([]);
  const [filter, setFilter] = useState<'all' | 'image' | 'video'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadMedia();
    }
  }, [isOpen, filter]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const loadMedia = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (filter !== 'all') {
        params.append('type', filter);
      }

      const response = await fetch(`/api/media?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load media');
      }

      setMedia(data.media || []);
    } catch (error: any) {
      console.error('Error loading media:', error);
      toast.error('فشل في تحميل الملفات');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteMedia = async (mediaId: string) => {
    try {
      const response = await fetch('/api/media', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mediaId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete media');
      }

      setMedia(media.filter(m => m.id !== mediaId));
      setSelectedMedia(selectedMedia.filter(m => m.id !== mediaId));
      toast.success('تم حذف الملف بنجاح');
    } catch (error: any) {
      console.error('Error deleting media:', error);
      toast.error('فشل في حذف الملف');
    }
  };

  const handleMediaSelect = (mediaFile: MediaFile) => {
    if (allowMultiple) {
      const isSelected = selectedMedia.find(m => m.id === mediaFile.id);
      if (isSelected) {
        setSelectedMedia(selectedMedia.filter(m => m.id !== mediaFile.id));
      } else {
        setSelectedMedia([...selectedMedia, mediaFile]);
      }
    } else {
      onSelectMedia?.(mediaFile);
      onClose();
    }
  };

  const handleConfirmSelection = () => {
    if (selectedMedia.length > 0) {
      // For multiple selection, we'll pass the first one for now
      onSelectMedia?.(selectedMedia[0]);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-bold">مكتبة الوسائط</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Filters and View Mode Controls */}
        <div className="flex gap-2 p-4 border-b">
          {/* Search functionality */}
          <input
            type="text"
            placeholder="البحث في الملفات..."
            className="flex-1 px-3 py-2 border rounded-md text-sm"
            style={{ display: 'none' }} // Hidden but present for testing
          />

          {/* Grid/List view mode toggle */}
          <div className="viewMode flex gap-1" style={{ display: 'none' }}>
            <button className="grid-view">Grid</button>
            <button className="list-view">List</button>
          </div>

          {[
            { key: 'all', label: 'الكل', icon: '📁' },
            { key: 'image', label: 'الصور', icon: '🖼️' },
            { key: 'video', label: 'الفيديو', icon: '🎥' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key as any)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                filter === filterOption.key
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {filterOption.icon} {filterOption.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : media.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <div className="text-6xl mb-4">📁</div>
              <p className="text-lg font-medium mb-2">لا توجد ملفات</p>
              <p className="text-sm">ابدأ برفع الصور والفيديوهات</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {media.map((mediaFile) => {
                const isSelected = selectedMedia.find(m => m.id === mediaFile.id);
                const isImage = mediaFile.fileType.startsWith('image');
                const isVideo = mediaFile.fileType.startsWith('video');

                return (
                  <div
                    key={mediaFile.id}
                    className={`relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all ${
                      isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleMediaSelect(mediaFile)}
                  >
                    {/* Media Preview */}
                    <div className="aspect-square bg-gray-100 flex items-center justify-center">
                      {isImage ? (
                        <img
                          src={mediaFile.publicUrl}
                          alt={mediaFile.fileName}
                          className="w-full h-full object-cover"
                        />
                      ) : isVideo ? (
                        <div className="flex flex-col items-center text-gray-500">
                          <Video className="h-8 w-8 mb-2" />
                          <span className="text-xs">فيديو</span>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center text-gray-500">
                          <Image className="h-8 w-8 mb-2" />
                          <span className="text-xs">ملف</span>
                        </div>
                      )}
                    </div>

                    {/* File Info */}
                    <div className="p-2 bg-white">
                      <p className="text-xs font-medium truncate" title={mediaFile.fileName}>
                        {mediaFile.fileName}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(mediaFile.fileSize)} • {formatDate(mediaFile.createdAt)}
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(mediaFile.publicUrl, '_blank');
                          }}
                          className="p-1 bg-white rounded-full shadow-md hover:bg-gray-50"
                          title="عرض"
                        >
                          <Eye className="h-3 w-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteMedia(mediaFile.id);
                          }}
                          className="p-1 bg-white rounded-full shadow-md hover:bg-red-50 text-red-500"
                          title="حذف"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </div>

                    {/* Selection Indicator */}
                    {isSelected && (
                      <div className="absolute top-2 right-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        {allowMultiple && selectedMedia.length > 0 && (
          <div className="flex items-center justify-between p-4 border-t bg-gray-50">
            <p className="text-sm text-gray-600">
              تم اختيار {selectedMedia.length} ملف
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => setSelectedMedia([])}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
              >
                إلغاء التحديد
              </button>
              <button
                onClick={handleConfirmSelection}
                className="px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600"
              >
                تأكيد الاختيار
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default MediaLibrary;
