# eWasl App Deployment Guide

## Manual Deployment to DigitalOcean App Platform

Since we couldn't install the DigitalOcean CLI, let's deploy manually through the web interface:

### Step 1: Push Your Code to GitHub

```bash
git add .
git commit -m "Prepare for deployment"
git push origin main
```

### Step 2: Configure the App in DigitalOcean App Platform

1. Log in to your DigitalOcean account
2. Go to the App Platform section
3. Select your app (ID: 92d1f7b6-85f2-47e2-8a69-d823b1586159)
4. Click on "Settings" tab
5. Under "App Spec", click "Edit"
6. Replace the content with the contents of your `app-spec.yaml` file
7. Click "Save"

### Step 3: Configure Environment Variables

1. In the App Platform dashboard, go to the "Settings" tab
2. Under "Environment Variables", click "Edit"
3. Add the following environment variables:
   - `DATABASE_URL`: Your PostgreSQL connection string
   - `NEXTAUTH_SECRET`: A secure random string
   - `NEXTAUTH_URL`: https://app.ewasl.com
   - `OPENROUTER_API_KEY`: 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
   - `OPENROUTER_MODEL`: qwen/qwen-4b:free
   - `NODE_ENV`: production
   - `NEXT_TELEMETRY_DISABLED`: 1
4. Click "Save"

### Step 4: Configure Domain Settings

1. In the App Platform dashboard, go to the "Settings" tab
2. Under "Domains", click "Add Domain"
3. Enter "app.ewasl.com"
4. Follow the instructions to verify domain ownership
5. Configure your DNS provider with the required records

### Step 5: Force Rebuild and Deploy

1. In the App Platform dashboard, go to the "Overview" tab
2. Click on "..." (three dots) next to your component
3. Select "Force Rebuild"
4. Monitor the build logs for any errors

## Troubleshooting Common Deployment Issues

### Issue: Build Fails Due to Node.js Version

**Solution**: Make sure your Dockerfile uses Node.js 18:
```dockerfile
FROM node:18-alpine AS base
```

### Issue: React/Next.js Version Compatibility

**Solution**: We've downgraded to stable versions:
- React: 18.2.0
- Next.js: 14.1.0

### Issue: Missing Environment Variables

**Solution**: Double-check all environment variables are set correctly in the DigitalOcean App Platform dashboard.

### Issue: Database Connection Errors

**Solution**: Verify your DATABASE_URL is correct and the database is accessible from DigitalOcean App Platform.

### Issue: DNS Configuration Issues

**Solution**: Make sure your DNS records are correctly set up at your domain registrar:
- CNAME record for "app" pointing to your DigitalOcean app URL
- Allow 24-48 hours for DNS propagation