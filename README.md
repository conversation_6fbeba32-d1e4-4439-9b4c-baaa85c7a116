# eWasl Social Scheduler

A Next.js application for scheduling social media posts with Arabic caption generation using OpenRouter.

## Deployment to DigitalOcean App Platform

### Prerequisites

1. Install the DigitalOcean CLI (doctl):
   - Windows: `scoop install doctl` or download from [DigitalOcean CLI](https://github.com/digitalocean/doctl/releases)
   - macOS: `brew install doctl`
   - Linux: `snap install doctl`

2. Authenticate with DigitalOcean:
   ```
   doctl auth init --access-token ***********************************************************************
   ```

### Deployment Steps

1. Push your code to GitHub:
   ```
   git add .
   git commit -m "Prepare for deployment"
   git push origin main
   ```

2. Deploy using the provided script:
   - Windows: `.\deploy.ps1`
   - macOS/Linux: `chmod +x deploy.sh && ./deploy.sh`

3. Monitor the deployment in the DigitalOcean App Platform dashboard.

### DNS Configuration

To configure the app.ewasl.com subdomain:

1. Go to your domain registrar's DNS settings
2. Add a CNAME record:
   - Name: `app`
   - Value: Your DigitalOcean app URL (e.g., `ewasl-social-scheduler-abcd1234.ondigitalocean.app`)
   - TTL: 3600 (or as recommended)

### Environment Variables

The following environment variables are required:

- `DATABASE_URL`: PostgreSQL connection string
- `NEXTAUTH_SECRET`: Secret for NextAuth.js
- `NEXTAUTH_URL`: URL of your application (https://app.ewasl.com)
- `OPENROUTER_API_KEY`: API key for OpenRouter
- `OPENROUTER_MODEL`: Model to use (qwen/qwen-4b:free)

### Troubleshooting

If you encounter deployment issues:

1. Check the build logs in the DigitalOcean App Platform dashboard
2. Verify that all environment variables are correctly set
3. Ensure your DNS configuration is correct
4. Check that your database is properly configured# Security fixes deployed 05/24/2025 22:26:53
