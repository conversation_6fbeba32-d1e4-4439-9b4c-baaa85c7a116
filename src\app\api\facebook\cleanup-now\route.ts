import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * IMMEDIATE Facebook account cleanup
 * POST /api/facebook/cleanup-now - Remove corrupted Facebook accounts immediately
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧹 IMMEDIATE Facebook cleanup - Removing corrupted accounts...');

    const body = await request.json();
    const { confirm = false } = body;

    if (!confirm) {
      return NextResponse.json({
        error: 'Cleanup requires confirmation. Set confirm: true',
        corruptedAccounts: [
          { name: 'Test Facebook Account', id: 'test-fb-**********.347804' },
          { name: '@eWasl Page', id: 'ewasl_page' }
        ],
        instruction: 'POST with {"confirm": true} to execute cleanup'
      }, { status: 400 });
    }

    const results = {
      timestamp: new Date().toISOString(),
      cleanup: {} as any
    };

    // Get corrupted Facebook accounts
    const supabase = createServiceRoleClient();
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    console.log('Fetching Facebook accounts for Demo User...');
    const { data: facebookAccounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .eq('platform', 'FACEBOOK');

    if (error) {
      throw new Error(`Failed to fetch Facebook accounts: ${error.message}`);
    }

    console.log(`Found ${facebookAccounts?.length || 0} Facebook accounts`);

    // Identify corrupted accounts
    const knownCorruptedAccountIds = [
      'test-fb-**********.347804', // "Test Facebook Account"
      'ewasl_page' // "@eWasl Page"
    ];

    const corruptedAccounts = facebookAccounts?.filter(acc => 
      knownCorruptedAccountIds.includes(acc.account_id)
    ) || [];

    results.cleanup = {
      accountsFound: facebookAccounts?.length || 0,
      corruptedAccountsFound: corruptedAccounts.length,
      accountsToRemove: corruptedAccounts.map(acc => ({
        id: acc.id,
        account_name: acc.account_name,
        account_id: acc.account_id,
        created_at: acc.created_at
      }))
    };

    if (corruptedAccounts.length === 0) {
      return NextResponse.json({
        success: true,
        message: '✅ No corrupted Facebook accounts found to remove',
        results
      });
    }

    // Execute cleanup
    console.log(`Removing ${corruptedAccounts.length} corrupted Facebook accounts...`);
    
    const removalResults = [];
    
    for (const account of corruptedAccounts) {
      try {
        console.log(`Removing account: ${account.account_name} (${account.id})`);
        
        // Remove from database
        const { error: deleteError } = await supabase
          .from('social_accounts')
          .delete()
          .eq('id', account.id);

        if (deleteError) {
          throw new Error(`Failed to delete account ${account.id}: ${deleteError.message}`);
        }

        removalResults.push({
          accountId: account.id,
          accountName: account.account_name,
          accountIdExternal: account.account_id,
          success: true,
          message: 'Account removed successfully'
        });

        console.log(`✅ Removed account: ${account.account_name}`);

      } catch (removeError: any) {
        removalResults.push({
          accountId: account.id,
          accountName: account.account_name,
          accountIdExternal: account.account_id,
          success: false,
          error: removeError.message
        });
        console.error(`❌ Failed to remove account ${account.account_name}:`, removeError);
      }
    }

    results.cleanup.removalResults = removalResults;
    results.cleanup.successfulRemovals = removalResults.filter(r => r.success).length;
    results.cleanup.failedRemovals = removalResults.filter(r => !r.success).length;

    // Verify cleanup
    console.log('Verifying cleanup...');
    const { data: remainingAccounts, error: verifyError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .eq('platform', 'FACEBOOK');

    results.cleanup.verification = {
      remainingFacebookAccounts: remainingAccounts?.length || 0,
      cleanupComplete: (remainingAccounts?.length || 0) === 0,
      verifyError: verifyError?.message
    };

    // Log cleanup activity
    await supabase
      .from('activities')
      .insert({
        user_id: demoUserId,
        action: 'FACEBOOK_ACCOUNTS_IMMEDIATE_CLEANUP',
        metadata: {
          removedAccounts: results.cleanup.successfulRemovals,
          failedRemovals: results.cleanup.failedRemovals,
          cleanupComplete: results.cleanup.verification.cleanupComplete,
          timestamp: new Date().toISOString(),
        },
      });

    console.log(`🎉 Facebook cleanup completed! Removed ${results.cleanup.successfulRemovals} accounts`);

    return NextResponse.json({
      success: true,
      message: `🎉 Facebook cleanup completed! Removed ${results.cleanup.successfulRemovals} corrupted accounts`,
      results,
      nextSteps: {
        phase3: 'Access Facebook Developer Console for app configuration',
        phase4: 'Re-establish Facebook connections with fresh OAuth flow',
        phase5: 'Test Facebook posting functionality'
      }
    });

  } catch (error: any) {
    console.error('❌ Facebook immediate cleanup error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'Facebook immediate cleanup failed'
    }, { status: 500 });
  }
}

/**
 * Check cleanup status
 * GET /api/facebook/cleanup-now
 */
export async function GET() {
  try {
    const supabase = createServiceRoleClient();
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    const { data: facebookAccounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .eq('platform', 'FACEBOOK');

    const knownCorruptedAccountIds = [
      'test-fb-**********.347804',
      'ewasl_page'
    ];

    const corruptedAccounts = facebookAccounts?.filter(acc => 
      knownCorruptedAccountIds.includes(acc.account_id)
    ) || [];

    return NextResponse.json({
      message: '🧹 Facebook cleanup status',
      status: {
        totalFacebookAccounts: facebookAccounts?.length || 0,
        corruptedAccountsRemaining: corruptedAccounts.length,
        cleanupRequired: corruptedAccounts.length > 0,
        cleanupComplete: corruptedAccounts.length === 0
      },
      corruptedAccounts: corruptedAccounts.map(acc => ({
        id: acc.id,
        account_name: acc.account_name,
        account_id: acc.account_id
      })),
      instruction: corruptedAccounts.length > 0 ? 
        'POST with {"confirm": true} to execute cleanup' :
        'Cleanup complete - ready for Phase 3'
    });

  } catch (error) {
    return NextResponse.json({
      error: 'Failed to check cleanup status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
