-- Team Collaboration & Workflow Management Migration
-- Creates comprehensive team collaboration infrastructure

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Organizations Table - Top-level entity for teams/companies
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL, -- URL-friendly identifier
    description TEXT,
    logo_url TEXT,
    website_url TEXT,
    
    -- Subscription and billing
    subscription_plan VARCHAR(50) DEFAULT 'free', -- 'free', 'pro', 'enterprise'
    subscription_status VARCHAR(20) DEFAULT 'active', -- 'active', 'cancelled', 'expired'
    subscription_expires_at TIMESTAMPTZ,
    
    -- Limits based on plan
    max_users INTEGER DEFAULT 3, -- Free plan limit
    max_social_accounts INTEGER DEFAULT 5,
    max_posts_per_month INTEGER DEFAULT 100,
    
    -- Settings
    settings JSONB DEFAULT '{}', -- Organization-wide settings
    timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- <PERSON>ada<PERSON>
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_organizations_slug ON organizations(slug),
    INDEX idx_organizations_created_by ON organizations(created_by),
    INDEX idx_organizations_subscription ON organizations(subscription_plan, subscription_status)
);

-- Organization Members Table - User membership in organizations
CREATE TABLE IF NOT EXISTS organization_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Role and permissions
    role VARCHAR(20) NOT NULL DEFAULT 'member', -- 'owner', 'admin', 'editor', 'viewer', 'member'
    permissions JSONB DEFAULT '{}', -- Custom permissions override
    
    -- Status
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'inactive', 'pending'
    invited_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    invited_at TIMESTAMPTZ,
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(organization_id, user_id),
    
    -- Indexes
    INDEX idx_organization_members_org ON organization_members(organization_id),
    INDEX idx_organization_members_user ON organization_members(user_id),
    INDEX idx_organization_members_role ON organization_members(role),
    INDEX idx_organization_members_status ON organization_members(status)
);

-- Workspaces Table - Project/client-specific workspaces within organizations
CREATE TABLE IF NOT EXISTS workspaces (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    slug VARCHAR(100) NOT NULL, -- Unique within organization
    description TEXT,
    color VARCHAR(7) DEFAULT '#3b82f6', -- Hex color for workspace identification
    
    -- Settings
    settings JSONB DEFAULT '{}', -- Workspace-specific settings
    workflow_settings JSONB DEFAULT '{}', -- Approval workflow configuration
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    archived_at TIMESTAMPTZ,
    
    -- Metadata
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(organization_id, slug),
    
    -- Indexes
    INDEX idx_workspaces_organization ON workspaces(organization_id),
    INDEX idx_workspaces_slug ON workspaces(organization_id, slug),
    INDEX idx_workspaces_active ON workspaces(is_active),
    INDEX idx_workspaces_created_by ON workspaces(created_by)
);

-- Workspace Members Table - User access to specific workspaces
CREATE TABLE IF NOT EXISTS workspace_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Role and permissions
    role VARCHAR(20) NOT NULL DEFAULT 'member', -- 'admin', 'editor', 'reviewer', 'viewer', 'member'
    permissions JSONB DEFAULT '{}', -- Workspace-specific permissions
    
    -- Status
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'inactive'
    
    -- Metadata
    added_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    added_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(workspace_id, user_id),
    
    -- Indexes
    INDEX idx_workspace_members_workspace ON workspace_members(workspace_id),
    INDEX idx_workspace_members_user ON workspace_members(user_id),
    INDEX idx_workspace_members_role ON workspace_members(role)
);

-- Content Approval Workflows Table - Define approval processes
CREATE TABLE IF NOT EXISTS approval_workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Workflow configuration
    steps JSONB NOT NULL DEFAULT '[]', -- Array of approval steps
    is_default BOOLEAN DEFAULT false, -- Default workflow for workspace
    
    -- Conditions
    conditions JSONB DEFAULT '{}', -- When this workflow applies
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_approval_workflows_workspace ON approval_workflows(workspace_id),
    INDEX idx_approval_workflows_default ON approval_workflows(workspace_id, is_default),
    INDEX idx_approval_workflows_active ON approval_workflows(is_active)
);

-- Content Approvals Table - Track approval status for posts
CREATE TABLE IF NOT EXISTS content_approvals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    workflow_id UUID NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
    
    -- Current status
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'changes_requested'
    current_step INTEGER DEFAULT 1, -- Current step in workflow
    
    -- Approval data
    approvals JSONB DEFAULT '[]', -- Array of approval decisions
    comments JSONB DEFAULT '[]', -- Array of comments/feedback
    
    -- Metadata
    submitted_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    submitted_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(post_id), -- One approval process per post
    
    -- Indexes
    INDEX idx_content_approvals_post ON content_approvals(post_id),
    INDEX idx_content_approvals_workflow ON content_approvals(workflow_id),
    INDEX idx_content_approvals_status ON content_approvals(status),
    INDEX idx_content_approvals_submitted_by ON content_approvals(submitted_by)
);

-- Team Comments Table - Comments and feedback on posts
CREATE TABLE IF NOT EXISTS team_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES team_comments(id) ON DELETE CASCADE, -- For threaded comments
    
    -- Comment content
    content TEXT NOT NULL,
    comment_type VARCHAR(20) DEFAULT 'comment', -- 'comment', 'feedback', 'approval', 'rejection'
    
    -- Metadata
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Status
    is_resolved BOOLEAN DEFAULT false,
    resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMPTZ,
    
    -- Indexes
    INDEX idx_team_comments_post ON team_comments(post_id),
    INDEX idx_team_comments_author ON team_comments(author_id),
    INDEX idx_team_comments_parent ON team_comments(parent_comment_id),
    INDEX idx_team_comments_type ON team_comments(comment_type),
    INDEX idx_team_comments_resolved ON team_comments(is_resolved)
);

-- Team Tasks Table - Task assignment and tracking
CREATE TABLE IF NOT EXISTS team_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE, -- Optional: task related to specific post
    
    -- Task details
    title VARCHAR(200) NOT NULL,
    description TEXT,
    task_type VARCHAR(20) DEFAULT 'general', -- 'general', 'content_creation', 'review', 'approval', 'publishing'
    priority VARCHAR(10) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    
    -- Assignment
    assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    assigned_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Status and timing
    status VARCHAR(20) DEFAULT 'todo', -- 'todo', 'in_progress', 'review', 'completed', 'cancelled'
    due_date TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    -- Metadata
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_team_tasks_workspace ON team_tasks(workspace_id),
    INDEX idx_team_tasks_post ON team_tasks(post_id),
    INDEX idx_team_tasks_assigned_to ON team_tasks(assigned_to),
    INDEX idx_team_tasks_status ON team_tasks(status),
    INDEX idx_team_tasks_priority ON team_tasks(priority),
    INDEX idx_team_tasks_due_date ON team_tasks(due_date)
);

-- Team Notifications Table - Real-time collaboration notifications
CREATE TABLE IF NOT EXISTS team_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
    
    -- Notification details
    type VARCHAR(50) NOT NULL, -- 'comment_added', 'approval_requested', 'task_assigned', etc.
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    
    -- Related entities
    related_post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    related_task_id UUID REFERENCES team_tasks(id) ON DELETE CASCADE,
    related_comment_id UUID REFERENCES team_comments(id) ON DELETE CASCADE,
    
    -- Status
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMPTZ,
    
    -- Metadata
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_team_notifications_user ON team_notifications(user_id),
    INDEX idx_team_notifications_org ON team_notifications(organization_id),
    INDEX idx_team_notifications_workspace ON team_notifications(workspace_id),
    INDEX idx_team_notifications_type ON team_notifications(type),
    INDEX idx_team_notifications_read ON team_notifications(is_read),
    INDEX idx_team_notifications_created_at ON team_notifications(created_at)
);

-- Update posts table to include workspace and approval status
ALTER TABLE posts 
ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS approval_status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'pending_approval', 'approved', 'rejected'
ADD COLUMN IF NOT EXISTS assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- Add indexes for new posts columns
CREATE INDEX IF NOT EXISTS idx_posts_workspace ON posts(workspace_id);
CREATE INDEX IF NOT EXISTS idx_posts_approval_status ON posts(approval_status);
CREATE INDEX IF NOT EXISTS idx_posts_assigned_to ON posts(assigned_to);

-- Update social_accounts table to include workspace association
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL;

CREATE INDEX IF NOT EXISTS idx_social_accounts_workspace ON social_accounts(workspace_id);

-- Row Level Security Policies
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for organizations
CREATE POLICY "Users can view organizations they belong to" ON organizations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM organization_members 
            WHERE organization_members.organization_id = organizations.id 
            AND organization_members.user_id = auth.uid()
            AND organization_members.status = 'active'
        )
    );

CREATE POLICY "Organization owners can update their organizations" ON organizations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM organization_members 
            WHERE organization_members.organization_id = organizations.id 
            AND organization_members.user_id = auth.uid()
            AND organization_members.role IN ('owner', 'admin')
        )
    );

-- RLS Policies for organization_members
CREATE POLICY "Users can view organization members" ON organization_members
    FOR SELECT USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM organization_members om 
            WHERE om.organization_id = organization_members.organization_id 
            AND om.user_id = auth.uid()
            AND om.status = 'active'
        )
    );

-- RLS Policies for workspaces
CREATE POLICY "Users can view workspaces they have access to" ON workspaces
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM workspace_members 
            WHERE workspace_members.workspace_id = workspaces.id 
            AND workspace_members.user_id = auth.uid()
            AND workspace_members.status = 'active'
        ) OR
        EXISTS (
            SELECT 1 FROM organization_members 
            WHERE organization_members.organization_id = workspaces.organization_id 
            AND organization_members.user_id = auth.uid()
            AND organization_members.role IN ('owner', 'admin')
        )
    );

-- RLS Policies for team_comments
CREATE POLICY "Users can view comments in their workspaces" ON team_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM posts p
            JOIN workspace_members wm ON wm.workspace_id = p.workspace_id
            WHERE p.id = team_comments.post_id 
            AND wm.user_id = auth.uid()
            AND wm.status = 'active'
        )
    );

CREATE POLICY "Users can create comments in their workspaces" ON team_comments
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM posts p
            JOIN workspace_members wm ON wm.workspace_id = p.workspace_id
            WHERE p.id = team_comments.post_id 
            AND wm.user_id = auth.uid()
            AND wm.status = 'active'
        )
    );

-- RLS Policies for team_notifications
CREATE POLICY "Users can view their own notifications" ON team_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON team_notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Functions for team collaboration
CREATE OR REPLACE FUNCTION get_user_organizations(user_uuid UUID)
RETURNS TABLE(
    organization_id UUID,
    organization_name VARCHAR,
    organization_slug VARCHAR,
    user_role VARCHAR,
    member_status VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.id,
        o.name,
        o.slug,
        om.role,
        om.status
    FROM organizations o
    JOIN organization_members om ON om.organization_id = o.id
    WHERE om.user_id = user_uuid
    AND om.status = 'active'
    ORDER BY o.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check user permissions
CREATE OR REPLACE FUNCTION check_workspace_permission(
    user_uuid UUID,
    workspace_uuid UUID,
    required_permission VARCHAR
) RETURNS BOOLEAN AS $$
DECLARE
    user_role VARCHAR;
    has_permission BOOLEAN := false;
BEGIN
    -- Get user role in workspace
    SELECT wm.role INTO user_role
    FROM workspace_members wm
    WHERE wm.workspace_id = workspace_uuid
    AND wm.user_id = user_uuid
    AND wm.status = 'active';
    
    -- Check permissions based on role
    CASE user_role
        WHEN 'admin' THEN has_permission := true;
        WHEN 'editor' THEN has_permission := required_permission IN ('read', 'write', 'comment');
        WHEN 'reviewer' THEN has_permission := required_permission IN ('read', 'comment', 'approve');
        WHEN 'viewer' THEN has_permission := required_permission = 'read';
        WHEN 'member' THEN has_permission := required_permission IN ('read', 'comment');
        ELSE has_permission := false;
    END CASE;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update triggers to all tables
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_organization_members_updated_at BEFORE UPDATE ON organization_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workspaces_updated_at BEFORE UPDATE ON workspaces FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workspace_members_updated_at BEFORE UPDATE ON workspace_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_approval_workflows_updated_at BEFORE UPDATE ON approval_workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_approvals_updated_at BEFORE UPDATE ON content_approvals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_comments_updated_at BEFORE UPDATE ON team_comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_tasks_updated_at BEFORE UPDATE ON team_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE organizations IS 'Top-level organizations/companies with subscription management';
COMMENT ON TABLE organization_members IS 'User membership and roles within organizations';
COMMENT ON TABLE workspaces IS 'Project/client-specific workspaces within organizations';
COMMENT ON TABLE workspace_members IS 'User access and roles within specific workspaces';
COMMENT ON TABLE approval_workflows IS 'Configurable content approval processes';
COMMENT ON TABLE content_approvals IS 'Approval status tracking for posts';
COMMENT ON TABLE team_comments IS 'Collaborative comments and feedback on posts';
COMMENT ON TABLE team_tasks IS 'Task assignment and tracking system';
COMMENT ON TABLE team_notifications IS 'Real-time collaboration notifications';
