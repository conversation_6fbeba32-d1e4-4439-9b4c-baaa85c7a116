import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import MediaUploadService from '@/lib/media/upload-service';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// File validation schema
const uploadSchema = z.object({
  fileName: z.string().min(1, 'File name is required'),
  fileType: z.string().regex(/^(image|video)\//, 'Invalid file type'),
  fileSize: z.number().max(50 * 1024 * 1024, 'File too large (max 50MB)'),
});

// Allowed file types
const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp'
];

const ALLOWED_VIDEO_TYPES = [
  'video/mp4',
  'video/mpeg',
  'video/quicktime',
  'video/webm'
];

const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES];

// POST - Upload media file with enhanced CDN integration
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Processing enhanced media upload...');

    // Get user from Supabase Auth
    
    const uploadService = new MediaUploadService();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string || 'general';
    const isPublic = formData.get('isPublic') === 'true';
    const generateThumbnail = formData.get('generateThumbnail') === 'true';
    const optimizeForPlatforms = formData.get('optimizeForPlatforms')?.toString().split(',') || [];

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    console.log('File received:', {
      name: file.name,
      type: file.type,
      size: file.size,
      folder,
      isPublic,
      optimizeForPlatforms
    });

    // Validate file
    const validation = uploadSchema.safeParse({
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size
    });

    if (!validation.success) {
      console.log('File validation failed:', validation.error.errors);
      return NextResponse.json(
        {
          error: 'Invalid file',
          details: validation.error.errors.map(err => err.message).join(', ')
        },
        { status: 400 }
      );
    }

    // Check file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'File type not allowed. Supported: images (JPEG, PNG, GIF, WebP) and videos (MP4, WebM)' },
        { status: 400 }
      );
    }

    // Use enhanced CDN upload service
    console.log('Uploading file with enhanced CDN service...');

    const result = await uploadService.uploadFile(file, file.name, {
      userId: user.id,
      folder,
      isPublic,
      generateThumbnail,
      optimizeForPlatforms,
    });

    if (!result.success) {
      console.error('CDN upload error:', result.error);
      return NextResponse.json(
        { error: result.error || 'Failed to upload file' },
        { status: 500 }
      );
    }

    console.log('File uploaded successfully with CDN:', {
      fileId: result.fileId,
      cdnUrl: result.cdnUrl
    });

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'MEDIA_UPLOADED',
        details: `Uploaded ${file.type.startsWith('image') ? 'image' : 'video'}: ${file.name}`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      data: {
        fileId: result.fileId,
        fileName: file.name,
        fileType: file.type,
        fileSize: result.metadata.size,
        cdnUrl: result.cdnUrl,
        metadata: result.metadata,
        optimizedVersions: result.optimizedVersions,
        folder,
        isPublic
      },
      message: 'File uploaded successfully with CDN'
    }, { status: 201 });

  } catch (error) {
    console.error('Media upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - Fetch user's media files
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    const uploadService = new MediaUploadService();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const folder = searchParams.get('folder') || undefined;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Fetching user media files:', {
      userId: user.id,
      folder,
      limit,
      offset
    });

    // Get user's media files
    const mediaFiles = await uploadService.getUserMediaFiles(
      user.id,
      folder,
      limit,
      offset
    );

    return NextResponse.json({
      success: true,
      data: mediaFiles,
      pagination: {
        limit,
        offset,
        total: mediaFiles.length
      }
    });

  } catch (error) {
    console.error('Media files fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch media files' },
      { status: 500 }
    );
  }
}

// DELETE - Delete media file
export async function DELETE(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    
    const uploadService = new MediaUploadService();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const { fileId } = await request.json();

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID required' },
        { status: 400 }
      );
    }

    console.log('Deleting media file:', {
      fileId,
      userId: user.id
    });

    // Delete file
    const success = await uploadService.deleteFile(fileId, user.id);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete file' },
        { status: 500 }
      );
    }

    console.log('File deleted successfully:', fileId);

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('Media delete error:', error);
    return NextResponse.json(
      { error: 'Delete failed' },
      { status: 500 }
    );
  }
}
