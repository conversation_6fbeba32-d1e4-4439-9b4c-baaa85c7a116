import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const submitForApprovalSchema = z.object({
  post_id: z.string().uuid(),
  workflow_id: z.string().uuid().optional(),
  message: z.string().optional(),
});

const approvalDecisionSchema = z.object({
  approval_id: z.string().uuid(),
  decision: z.enum(['approve', 'reject', 'request_changes']),
  comment: z.string().optional(),
  changes_requested: z.array(z.string()).optional(),
});

/**
 * Get approval requests
 * GET /api/teams/approvals?workspace_id=xxx&status=pending
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspace_id');
    const status = searchParams.get('status') || 'pending';
    const assignedToMe = searchParams.get('assigned_to_me') === 'true';

    if (!workspaceId) {
      return NextResponse.json({ error: 'Workspace ID required' }, { status: 400 });
    }

    // Check if user has access to workspace
    const { data: membership } = await supabase
      .from('workspace_members')
      .select('role')
      .eq('workspace_id', workspaceId)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Build query for approvals
    let approvalsQuery = supabase
      .from('content_approvals')
      .select(`
        *,
        post:posts(
          id,
          content,
          media_url,
          scheduled_at,
          status,
          user_id
        ),
        workflow:approval_workflows(
          id,
          name,
          steps
        ),
        submitted_by_user:auth.users!content_approvals_submitted_by_fkey(
          id,
          email,
          raw_user_meta_data
        )
      `)
      .eq('post.workspace_id', workspaceId);

    if (status !== 'all') {
      approvalsQuery = approvalsQuery.eq('status', status);
    }

    // If assigned to me, filter by current step approvers
    if (assignedToMe) {
      // This would require more complex logic to check if user can approve current step
      // For now, we'll show all pending approvals the user can potentially approve
      approvalsQuery = approvalsQuery.eq('status', 'pending');
    }

    const { data: approvals, error } = await approvalsQuery
      .order('submitted_at', { ascending: false });

    if (error) {
      console.error('Error fetching approvals:', error);
      return NextResponse.json({ error: 'Failed to fetch approvals' }, { status: 500 });
    }

    // Filter approvals based on user's role and current step
    const filteredApprovals = (approvals as any[] || []).filter((approval: any) => {
      if (!approval.workflow?.steps || approval.current_step > approval.workflow.steps.length) {
        return false;
      }

      const currentStep = approval.workflow.steps[approval.current_step - 1];
      if (!currentStep) return false;

      // Check if user can approve this step
      const canApprove = currentStep.approver_roles?.includes((membership as any)?.role) ||
                        currentStep.approver_users?.includes(user.id);

      return assignedToMe ? canApprove : true;
    });

    return NextResponse.json({
      success: true,
      approvals: filteredApprovals.map((approval: any) => ({
        ...approval,
        can_approve: approval.workflow?.steps?.[approval.current_step - 1]?.approver_roles?.includes((membership as any)?.role) ||
                    approval.workflow?.steps?.[approval.current_step - 1]?.approver_users?.includes(user.id),
        current_step_info: approval.workflow?.steps?.[approval.current_step - 1],
      })),
    });

  } catch (error) {
    console.error('Approvals fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Submit post for approval
 * POST /api/teams/approvals
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'submit') {
      return await handleSubmitForApproval(supabase, user, body);
    } else if (action === 'decide') {
      return await handleApprovalDecision(supabase, user, body);
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Approval action error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function handleSubmitForApproval(supabase: any, user: any, body: any) {
  const validation = submitForApprovalSchema.safeParse(body);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { post_id, workflow_id, message } = validation.data;

  // Get post and verify ownership/access
  const { data: post, error: postError } = await supabase
    .from('posts')
    .select('*, workspace_id')
    .eq('id', post_id)
    .single();

  if (postError || !post) {
    return NextResponse.json({ error: 'Post not found' }, { status: 404 });
  }

  // Check if user has access to the workspace
  const { data: membership } = await supabase
    .from('workspace_members')
    .select('role')
    .eq('workspace_id', post.workspace_id)
    .eq('user_id', user.id)
    .eq('status', 'active')
    .single();

  if (!membership) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }

  // Get workflow (use provided or default)
  let workflow;
  if (workflow_id) {
    const { data: specificWorkflow } = await supabase
      .from('approval_workflows')
      .select('*')
      .eq('id', workflow_id)
      .eq('workspace_id', post.workspace_id)
      .eq('is_active', true)
      .single();

    workflow = specificWorkflow;
  } else {
    // Get default workflow for workspace
    const { data: defaultWorkflow } = await supabase
      .from('approval_workflows')
      .select('*')
      .eq('workspace_id', post.workspace_id)
      .eq('is_default', true)
      .eq('is_active', true)
      .single();

    workflow = defaultWorkflow;
  }

  if (!workflow) {
    return NextResponse.json({ error: 'No approval workflow found' }, { status: 400 });
  }

  // Check if post already has an approval process
  const { data: existingApproval } = await supabase
    .from('content_approvals')
    .select('id')
    .eq('post_id', post_id)
    .single();

  if (existingApproval) {
    return NextResponse.json({ error: 'Post already submitted for approval' }, { status: 409 });
  }

  // Create approval record
  const { data: approval, error: approvalError } = await supabase
    .from('content_approvals')
    .insert({
      post_id,
      workflow_id: workflow.id,
      status: 'pending',
      current_step: 1,
      submitted_by: user.id,
      comments: message ? [{
        user_id: user.id,
        message,
        timestamp: new Date().toISOString(),
        type: 'submission'
      }] : [],
    })
    .select()
    .single();

  if (approvalError) {
    console.error('Error creating approval:', approvalError);
    return NextResponse.json({ error: 'Failed to submit for approval' }, { status: 500 });
  }

  // Update post status
  await supabase
    .from('posts')
    .update({ approval_status: 'pending_approval' })
    .eq('id', post_id);

  // Create notifications for approvers
  const currentStep = workflow.steps[0];
  if (currentStep?.approver_roles || currentStep?.approver_users) {
    // Get users who can approve this step
    const { data: approvers } = await supabase
      .from('workspace_members')
      .select('user_id')
      .eq('workspace_id', post.workspace_id)
      .in('role', currentStep.approver_roles || [])
      .eq('status', 'active');

    const approverIds = [
      ...(approvers?.map((a: any) => a.user_id) || []),
      ...(currentStep.approver_users || [])
    ];

    // Create notifications
    const notifications = approverIds.map(approverId => ({
      user_id: approverId,
      workspace_id: post.workspace_id,
      type: 'approval_requested',
      title: 'New Approval Request',
      message: `${user.email} submitted a post for approval`,
      related_post_id: post_id,
      created_by: user.id,
    }));

    await supabase
      .from('team_notifications')
      .insert(notifications);
  }

  return NextResponse.json({
    success: true,
    approval,
    message: 'Post submitted for approval successfully',
  });
}

async function handleApprovalDecision(supabase: any, user: any, body: any) {
  const validation = approvalDecisionSchema.safeParse(body);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { approval_id, decision, comment, changes_requested } = validation.data;

  // Get approval with workflow info
  const { data: approval, error: approvalError } = await supabase
    .from('content_approvals')
    .select(`
      *,
      workflow:approval_workflows(*),
      post:posts(workspace_id)
    `)
    .eq('id', approval_id)
    .single();

  if (approvalError || !approval) {
    return NextResponse.json({ error: 'Approval not found' }, { status: 404 });
  }

  // Check if user can approve this step
  const { data: membership } = await supabase
    .from('workspace_members')
    .select('role')
    .eq('workspace_id', approval.post.workspace_id)
    .eq('user_id', user.id)
    .eq('status', 'active')
    .single();

  if (!membership) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }

  const currentStep = approval.workflow.steps[approval.current_step - 1];
  const canApprove = currentStep?.approver_roles?.includes(membership.role) ||
                    currentStep?.approver_users?.includes(user.id);

  if (!canApprove) {
    return NextResponse.json({ error: 'You cannot approve this step' }, { status: 403 });
  }

  // Add approval decision to approvals array
  const newApproval = {
    step: approval.current_step,
    user_id: user.id,
    decision,
    comment,
    changes_requested,
    timestamp: new Date().toISOString(),
  };

  const updatedApprovals = [...(approval.approvals || []), newApproval];

  // Add comment if provided
  const updatedComments = [...(approval.comments || [])];
  if (comment) {
    updatedComments.push({
      user_id: user.id,
      message: comment,
      timestamp: new Date().toISOString(),
      type: 'approval_decision',
      decision,
    });
  }

  let newStatus = approval.status;
  let newStep = approval.current_step;
  let postStatus = 'pending_approval';

  if (decision === 'approve') {
    // Check if this step is complete
    const requiredApprovers = currentStep.required_approvers || 1;
    const stepApprovals = updatedApprovals.filter(a =>
      a.step === approval.current_step && a.decision === 'approve'
    );

    if (stepApprovals.length >= requiredApprovers) {
      // Step is complete, move to next step or complete approval
      if (approval.current_step >= approval.workflow.steps.length) {
        // All steps complete
        newStatus = 'approved';
        postStatus = 'approved';
      } else {
        // Move to next step
        newStep = approval.current_step + 1;
      }
    }
  } else if (decision === 'reject') {
    newStatus = 'rejected';
    postStatus = 'rejected';
  } else if (decision === 'request_changes') {
    newStatus = 'changes_requested';
    postStatus = 'draft';
  }

  // Update approval
  const { error: updateError } = await supabase
    .from('content_approvals')
    .update({
      status: newStatus,
      current_step: newStep,
      approvals: updatedApprovals,
      comments: updatedComments,
      completed_at: ['approved', 'rejected'].includes(newStatus) ? new Date().toISOString() : null,
    })
    .eq('id', approval_id);

  if (updateError) {
    console.error('Error updating approval:', updateError);
    return NextResponse.json({ error: 'Failed to update approval' }, { status: 500 });
  }

  // Update post status
  await supabase
    .from('posts')
    .update({ approval_status: postStatus })
    .eq('id', approval.post_id);

  // Create notification for post author
  await supabase
    .from('team_notifications')
    .insert({
      user_id: approval.submitted_by,
      workspace_id: approval.post.workspace_id,
      type: `approval_${decision}`,
      title: `Approval ${decision === 'approve' ? 'Approved' : decision === 'reject' ? 'Rejected' : 'Changes Requested'}`,
      message: `Your post has been ${decision}d${comment ? `: ${comment}` : ''}`,
      related_post_id: approval.post_id,
      created_by: user.id,
    });

  return NextResponse.json({
    success: true,
    approval: {
      id: approval_id,
      status: newStatus,
      current_step: newStep,
    },
    message: `Approval ${decision}d successfully`,
  });
}
