-- Migration: Scheduler System Tables
-- Description: Create tables for the production scheduler service

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Scheduler logs table for comprehensive logging
CREATE TABLE IF NOT EXISTS scheduler_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    level VARCHAR(10) NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error')),
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    component VARCHAR(100) NOT NULL,
    session_id VARCHAR(100),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Scheduler status table for tracking scheduler health
CREATE TABLE IF NOT EXISTS scheduler_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instance_id VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL CHECK (status IN ('starting', 'running', 'stopping', 'stopped', 'error')),
    last_heartbeat TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    started_at TIMESTAMPTZ DEFAULT NOW(),
    stopped_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Job queue table for background job processing
CREATE TABLE IF NOT EXISTS job_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_type VARCHAR(50) NOT NULL,
    job_data JSONB NOT NULL DEFAULT '{}',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    priority INTEGER DEFAULT 0,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    scheduled_at TIMESTAMPTZ DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    error_details JSONB,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Post publishing history for tracking all publish attempts
CREATE TABLE IF NOT EXISTS post_publish_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,
    social_account_id UUID REFERENCES social_accounts(id) ON DELETE SET NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'processing', 'published', 'failed', 'cancelled')),
    attempt_number INTEGER DEFAULT 1,
    published_url TEXT,
    platform_post_id TEXT,
    error_message TEXT,
    error_details JSONB,
    response_data JSONB,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Platform rate limits tracking
CREATE TABLE IF NOT EXISTS platform_rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    platform VARCHAR(20) NOT NULL,
    social_account_id UUID REFERENCES social_accounts(id) ON DELETE CASCADE,
    endpoint VARCHAR(100) NOT NULL,
    requests_made INTEGER DEFAULT 0,
    requests_limit INTEGER NOT NULL,
    reset_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(platform, social_account_id, endpoint)
);

-- Scheduler metrics for performance monitoring
CREATE TABLE IF NOT EXISTS scheduler_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value NUMERIC NOT NULL,
    metric_type VARCHAR(20) NOT NULL CHECK (metric_type IN ('counter', 'gauge', 'histogram')),
    labels JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_scheduler_logs_timestamp ON scheduler_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_scheduler_logs_level ON scheduler_logs(level);
CREATE INDEX IF NOT EXISTS idx_scheduler_logs_component ON scheduler_logs(component);
CREATE INDEX IF NOT EXISTS idx_scheduler_logs_user_id ON scheduler_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduler_logs_session_id ON scheduler_logs(session_id);

CREATE INDEX IF NOT EXISTS idx_scheduler_status_instance_id ON scheduler_status(instance_id);
CREATE INDEX IF NOT EXISTS idx_scheduler_status_status ON scheduler_status(status);
CREATE INDEX IF NOT EXISTS idx_scheduler_status_last_heartbeat ON scheduler_status(last_heartbeat DESC);

CREATE INDEX IF NOT EXISTS idx_job_queue_status ON job_queue(status);
CREATE INDEX IF NOT EXISTS idx_job_queue_job_type ON job_queue(job_type);
CREATE INDEX IF NOT EXISTS idx_job_queue_scheduled_at ON job_queue(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_job_queue_priority ON job_queue(priority DESC);
CREATE INDEX IF NOT EXISTS idx_job_queue_created_by ON job_queue(created_by);

CREATE INDEX IF NOT EXISTS idx_post_publish_history_post_id ON post_publish_history(post_id);
CREATE INDEX IF NOT EXISTS idx_post_publish_history_platform ON post_publish_history(platform);
CREATE INDEX IF NOT EXISTS idx_post_publish_history_status ON post_publish_history(status);
CREATE INDEX IF NOT EXISTS idx_post_publish_history_social_account_id ON post_publish_history(social_account_id);
CREATE INDEX IF NOT EXISTS idx_post_publish_history_started_at ON post_publish_history(started_at DESC);

CREATE INDEX IF NOT EXISTS idx_platform_rate_limits_platform ON platform_rate_limits(platform);
CREATE INDEX IF NOT EXISTS idx_platform_rate_limits_social_account_id ON platform_rate_limits(social_account_id);
CREATE INDEX IF NOT EXISTS idx_platform_rate_limits_reset_at ON platform_rate_limits(reset_at);

CREATE INDEX IF NOT EXISTS idx_scheduler_metrics_metric_name ON scheduler_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_scheduler_metrics_timestamp ON scheduler_metrics(timestamp DESC);

-- Add RLS policies
ALTER TABLE scheduler_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduler_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_publish_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduler_metrics ENABLE ROW LEVEL SECURITY;

-- Scheduler logs policies
CREATE POLICY "Users can view their own scheduler logs" ON scheduler_logs
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Service role can manage all scheduler logs" ON scheduler_logs
    FOR ALL USING (auth.role() = 'service_role');

-- Scheduler status policies (admin only)
CREATE POLICY "Service role can manage scheduler status" ON scheduler_status
    FOR ALL USING (auth.role() = 'service_role');

-- Job queue policies
CREATE POLICY "Users can view their own jobs" ON job_queue
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Service role can manage all jobs" ON job_queue
    FOR ALL USING (auth.role() = 'service_role');

-- Post publish history policies
CREATE POLICY "Users can view their own publish history" ON post_publish_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM posts 
            WHERE posts.id = post_publish_history.post_id 
            AND posts.user_id = auth.uid()
        )
    );

CREATE POLICY "Service role can manage all publish history" ON post_publish_history
    FOR ALL USING (auth.role() = 'service_role');

-- Platform rate limits policies
CREATE POLICY "Users can view their own rate limits" ON platform_rate_limits
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM social_accounts 
            WHERE social_accounts.id = platform_rate_limits.social_account_id 
            AND social_accounts.user_id = auth.uid()
        )
    );

CREATE POLICY "Service role can manage all rate limits" ON platform_rate_limits
    FOR ALL USING (auth.role() = 'service_role');

-- Scheduler metrics policies (admin only)
CREATE POLICY "Service role can manage scheduler metrics" ON scheduler_metrics
    FOR ALL USING (auth.role() = 'service_role');

-- Add updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_scheduler_status_updated_at 
    BEFORE UPDATE ON scheduler_status 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_job_queue_updated_at 
    BEFORE UPDATE ON job_queue 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_platform_rate_limits_updated_at 
    BEFORE UPDATE ON platform_rate_limits 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add helpful functions
CREATE OR REPLACE FUNCTION get_scheduler_health()
RETURNS TABLE (
    total_jobs BIGINT,
    pending_jobs BIGINT,
    processing_jobs BIGINT,
    failed_jobs BIGINT,
    active_schedulers BIGINT,
    last_activity TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM job_queue) as total_jobs,
        (SELECT COUNT(*) FROM job_queue WHERE status = 'pending') as pending_jobs,
        (SELECT COUNT(*) FROM job_queue WHERE status = 'processing') as processing_jobs,
        (SELECT COUNT(*) FROM job_queue WHERE status = 'failed') as failed_jobs,
        (SELECT COUNT(*) FROM scheduler_status WHERE status = 'running' AND last_heartbeat > NOW() - INTERVAL '5 minutes') as active_schedulers,
        (SELECT MAX(last_heartbeat) FROM scheduler_status) as last_activity;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old logs
CREATE OR REPLACE FUNCTION cleanup_old_scheduler_logs(days_to_keep INTEGER DEFAULT 7)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM scheduler_logs 
    WHERE created_at < NOW() - (days_to_keep || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old job queue entries
CREATE OR REPLACE FUNCTION cleanup_old_jobs(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM job_queue 
    WHERE status IN ('completed', 'failed', 'cancelled') 
    AND created_at < NOW() - (days_to_keep || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE scheduler_logs IS 'Comprehensive logging for scheduler operations';
COMMENT ON TABLE scheduler_status IS 'Track scheduler instance health and status';
COMMENT ON TABLE job_queue IS 'Background job queue for async processing';
COMMENT ON TABLE post_publish_history IS 'Complete history of all post publishing attempts';
COMMENT ON TABLE platform_rate_limits IS 'Track API rate limits for each platform';
COMMENT ON TABLE scheduler_metrics IS 'Performance metrics and monitoring data';

COMMENT ON FUNCTION get_scheduler_health() IS 'Get overall scheduler system health metrics';
COMMENT ON FUNCTION cleanup_old_scheduler_logs(INTEGER) IS 'Remove old scheduler logs to manage storage';
COMMENT ON FUNCTION cleanup_old_jobs(INTEGER) IS 'Remove old completed/failed jobs to manage storage';
