import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Checking scheduler health...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const healthChecks = {
      database: false,
      postsTable: false,
      jobQueueTable: false,
      socialAccountsTable: false,
      schedulerStatusTable: false,
      publishHistoryTable: false,
    };

    let overallHealth = true;

    // Check database connection
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .eq('id', user.id)
        .single();

      healthChecks.database = !error && data;
      if (!healthChecks.database) overallHealth = false;
    } catch (error) {
      console.error('Database connection check failed:', error);
      healthChecks.database = false;
      overallHealth = false;
    }

    // Check posts table
    try {
      const { data, error } = await supabase
        .from('posts')
        .select('id')
        .limit(1);

      healthChecks.postsTable = !error;
      if (!healthChecks.postsTable) overallHealth = false;
    } catch (error) {
      console.error('Posts table check failed:', error);
      healthChecks.postsTable = false;
      overallHealth = false;
    }

    // Check job_queue table
    try {
      const { data, error } = await supabase
        .from('job_queue')
        .select('id')
        .limit(1);

      healthChecks.jobQueueTable = !error;
      if (!healthChecks.jobQueueTable) overallHealth = false;
    } catch (error) {
      console.error('Job queue table check failed:', error);
      healthChecks.jobQueueTable = false;
      overallHealth = false;
    }

    // Check social_accounts table
    try {
      const { data, error } = await supabase
        .from('social_accounts')
        .select('id')
        .limit(1);

      healthChecks.socialAccountsTable = !error;
      if (!healthChecks.socialAccountsTable) overallHealth = false;
    } catch (error) {
      console.error('Social accounts table check failed:', error);
      healthChecks.socialAccountsTable = false;
      overallHealth = false;
    }

    // Check scheduler_status table (optional)
    try {
      const { data, error } = await supabase
        .from('scheduler_status')
        .select('id')
        .limit(1);

      healthChecks.schedulerStatusTable = !error;
    } catch (error) {
      console.warn('Scheduler status table check failed (optional):', error);
      healthChecks.schedulerStatusTable = false;
    }

    // Check post_publish_history table (optional)
    try {
      const { data, error } = await supabase
        .from('post_publish_history')
        .select('id')
        .limit(1);

      healthChecks.publishHistoryTable = !error;
    } catch (error) {
      console.warn('Publish history table check failed (optional):', error);
      healthChecks.publishHistoryTable = false;
    }

    // Get basic statistics
    const stats = {
      totalPosts: 0,
      scheduledPosts: 0,
      totalJobs: 0,
      pendingJobs: 0,
      socialAccounts: 0,
    };

    if (healthChecks.postsTable) {
      try {
        const { count: totalCount } = await supabase
          .from('posts')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);

        const { count: scheduledCount } = await supabase
          .from('posts')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id)
          .eq('status', 'SCHEDULED');

        stats.totalPosts = totalCount || 0;
        stats.scheduledPosts = scheduledCount || 0;
      } catch (error) {
        console.error('Error getting post stats:', error);
      }
    }

    if (healthChecks.jobQueueTable) {
      try {
        const { count: totalJobs } = await supabase
          .from('job_queue')
          .select('*', { count: 'exact', head: true })
          .eq('created_by', user.id);

        const { count: pendingJobs } = await supabase
          .from('job_queue')
          .select('*', { count: 'exact', head: true })
          .eq('created_by', user.id)
          .eq('status', 'pending');

        stats.totalJobs = totalJobs || 0;
        stats.pendingJobs = pendingJobs || 0;
      } catch (error) {
        console.error('Error getting job stats:', error);
      }
    }

    if (healthChecks.socialAccountsTable) {
      try {
        const { count: accountCount } = await supabase
          .from('social_accounts')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);

        stats.socialAccounts = accountCount || 0;
      } catch (error) {
        console.error('Error getting social account stats:', error);
      }
    }

    return NextResponse.json({
      success: overallHealth,
      health: overallHealth ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: healthChecks,
      stats,
      message: overallHealth 
        ? 'All scheduler components are healthy' 
        : 'Some scheduler components have issues',
    });

  } catch (error) {
    console.error('Error checking scheduler health:', error);
    return NextResponse.json(
      { 
        success: false,
        health: 'unhealthy',
        error: 'Failed to check scheduler health',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
