'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Users,
  Plus,
  Settings,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  Calendar,
  BarChart3,
  UserPlus,
  Briefcase,
  Shield,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react';
import { toast } from 'sonner';

interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo_url?: string;
  subscription_plan: string;
  member_count: number;
  workspace_count: number;
  user_role: string;
}

interface Workspace {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  member_count: number;
  post_count: number;
  pending_approvals: number;
  user_role: string;
}

interface TeamMember {
  id: string;
  user_id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  status: string;
  added_at: string;
}

interface ApprovalRequest {
  id: string;
  post: {
    id: string;
    content: string;
    scheduled_at: string;
  };
  status: string;
  current_step: number;
  submitted_at: string;
  can_approve: boolean;
  current_step_info: {
    name: string;
    required_approvers: number;
  };
}

export function TeamDashboard() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(null);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [approvals, setApprovals] = useState<ApprovalRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchOrganizations();
  }, []);

  useEffect(() => {
    if (selectedOrg) {
      fetchWorkspaces(selectedOrg.id);
    }
  }, [selectedOrg]);

  useEffect(() => {
    if (selectedWorkspace) {
      fetchWorkspaceData(selectedWorkspace.id);
    }
  }, [selectedWorkspace]);

  const fetchOrganizations = async () => {
    try {
      const response = await fetch('/api/teams/organizations');
      const result = await response.json();

      if (result.success) {
        setOrganizations(result.organizations || []);
        if (result.organizations && result.organizations.length > 0) {
          setSelectedOrg(result.organizations[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toast.error('فشل في تحميل المؤسسات');
    } finally {
      setLoading(false);
    }
  };

  const fetchWorkspaces = async (organizationId: string) => {
    try {
      const response = await fetch(`/api/teams/workspaces?organization_id=${organizationId}`);
      const result = await response.json();

      if (result.success) {
        setWorkspaces(result.workspaces || []);
        if (result.workspaces && result.workspaces.length > 0) {
          setSelectedWorkspace(result.workspaces[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching workspaces:', error);
      toast.error('فشل في تحميل مساحات العمل');
    }
  };

  const fetchWorkspaceData = async (workspaceId: string) => {
    try {
      // Fetch members
      const membersResponse = await fetch(`/api/teams/members?workspace_id=${workspaceId}`);
      const membersResult = await membersResponse.json();

      if (membersResult.success) {
        setMembers(membersResult.members || []);
      }

      // Fetch pending approvals
      const approvalsResponse = await fetch(`/api/teams/approvals?workspace_id=${workspaceId}&status=pending`);
      const approvalsResult = await approvalsResponse.json();

      if (approvalsResult.success) {
        setApprovals(approvalsResult.approvals || []);
      }
    } catch (error) {
      console.error('Error fetching workspace data:', error);
    }
  };

  const handleApprovalDecision = async (approvalId: string, decision: 'approve' | 'reject' | 'request_changes', comment?: string) => {
    try {
      const response = await fetch('/api/teams/approvals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'decide',
          approval_id: approvalId,
          decision,
          comment,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('تم اتخاذ القرار بنجاح');
        // Refresh approvals
        if (selectedWorkspace) {
          fetchWorkspaceData(selectedWorkspace.id);
        }
      } else {
        toast.error(result.error || 'فشل في اتخاذ القرار');
      }
    } catch (error) {
      console.error('Error making approval decision:', error);
      toast.error('خطأ في اتخاذ القرار');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <Users className="h-8 w-8 animate-pulse mx-auto mb-4" />
          <p>جاري تحميل لوحة الفريق...</p>
        </div>
      </div>
    );
  }

  if (!organizations || organizations.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-semibold mb-2">لا توجد مؤسسات</h3>
        <p className="text-gray-600 mb-4">ابدأ بإنشاء مؤسسة للتعاون مع فريقك</p>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          إنشاء مؤسسة
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl" data-testid="team-dashboard">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">لوحة الفريق</h1>
          <p className="text-gray-600">إدارة الفرق والتعاون في المحتوى</p>
        </div>

        <div className="flex gap-2">
          <Select value={selectedOrg?.id} onValueChange={(value) => {
            const org = organizations.find(o => o.id === value);
            setSelectedOrg(org || null);
          }}>
            <SelectTrigger className="w-48" data-testid="organization-selector">
              <SelectValue placeholder="اختر المؤسسة" />
            </SelectTrigger>
            <SelectContent>
              {(organizations || []).map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  <div className="flex items-center gap-2">
                    <span>{org.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {org.user_role}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            إنشاء مؤسسة
          </Button>
        </div>
      </div>

      {/* Organization Info */}
      {selectedOrg && (
        <Card data-testid="organization-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Briefcase className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold" data-testid="organization-name">{selectedOrg.name}</h3>
                  <p className="text-gray-600">{selectedOrg.description}</p>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="text-center">
                  <div className="font-semibold" data-testid="member-count">{selectedOrg.member_count}</div>
                  <div>أعضاء</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold" data-testid="workspace-count">{selectedOrg.workspace_count}</div>
                  <div>مساحات عمل</div>
                </div>
                <Badge variant="outline" data-testid="subscription-plan">{selectedOrg.subscription_plan}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Workspace Selection */}
      {workspaces && workspaces.length > 0 && (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {workspaces.map((workspace) => (
            <Card
              key={workspace.id}
              className={`min-w-64 cursor-pointer transition-all ${
                selectedWorkspace?.id === workspace.id
                  ? 'ring-2 ring-blue-500 bg-blue-50'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setSelectedWorkspace(workspace)}
              data-testid="workspace-card"
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: workspace.color }}
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{workspace.name}</h4>
                    <div className="flex gap-4 text-xs text-gray-600 mt-1">
                      <span data-testid="workspace-members">{workspace.member_count} أعضاء</span>
                      <span data-testid="workspace-posts">{workspace.post_count} منشورات</span>
                      {workspace.pending_approvals > 0 && (
                        <span className="text-orange-600">
                          {workspace.pending_approvals} موافقات معلقة
                        </span>
                      )}
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {workspace.user_role}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Main Content */}
      {selectedWorkspace && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" data-testid="overview-tab">نظرة عامة</TabsTrigger>
            <TabsTrigger value="members" data-testid="members-tab">الأعضاء</TabsTrigger>
            <TabsTrigger value="approvals" data-testid="approvals-tab">الموافقات</TabsTrigger>
            <TabsTrigger value="settings" data-testid="settings-tab">الإعدادات</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Quick Stats */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">أعضاء الفريق</p>
                      <p className="text-2xl font-bold" data-testid="team-members-stat">{members?.length || 0}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">موافقات معلقة</p>
                      <p className="text-2xl font-bold" data-testid="pending-approvals-stat">{approvals?.length || 0}</p>
                    </div>
                    <Clock className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">منشورات هذا الشهر</p>
                      <p className="text-2xl font-bold" data-testid="monthly-posts-stat">{selectedWorkspace.post_count}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>النشاط الأخير</CardTitle>
                <CardDescription>آخر الأنشطة في مساحة العمل</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4" data-testid="activity-feed">
                  {(approvals || []).slice(0, 5).map((approval) => (
                    <div key={approval.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <AlertCircle className="h-5 w-5 text-orange-500" />
                        <div>
                          <p className="font-medium">طلب موافقة جديد</p>
                          <p className="text-sm text-gray-600">
                            {approval.post.content.substring(0, 50)}...
                          </p>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(approval.submitted_at).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="members" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">أعضاء الفريق</h3>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                دعوة عضو
              </Button>
            </div>

            <div className="grid gap-4">
              {(members || []).map((member) => (
                <Card key={member.id} data-testid="member-card">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback>
                            {member.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{member.name}</p>
                          <p className="text-sm text-gray-600">{member.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{member.role}</Badge>
                        <Badge
                          variant={member.status === 'active' ? 'default' : 'secondary'}
                        >
                          {member.status === 'active' ? 'نشط' : 'غير نشط'}
                        </Badge>
                        <Button variant="ghost" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="approvals" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">طلبات الموافقة</h3>
              <Select defaultValue="pending">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">معلقة</SelectItem>
                  <SelectItem value="approved">موافق عليها</SelectItem>
                  <SelectItem value="rejected">مرفوضة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-4" data-testid="approvals-container">
              {(!approvals || approvals.length === 0) ? (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold mb-2">لا توجد طلبات موافقة</h3>
                  <p className="text-gray-600">لا توجد طلبات موافقة معلقة في الوقت الحالي</p>
                </div>
              ) : (
                (approvals || []).map((approval) => (
                <Card key={approval.id} data-testid="approval-card">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">طلب موافقة - الخطوة {approval.current_step}</p>
                          <p className="text-sm text-gray-600">
                            {approval.current_step_info.name}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {approval.status === 'pending' ? 'معلق' : approval.status}
                        </Badge>
                      </div>

                      <div className="bg-gray-50 p-3 rounded-lg">
                        <p className="text-sm">{approval.post.content}</p>
                        {approval.post.scheduled_at && (
                          <p className="text-xs text-gray-500 mt-2">
                            مجدول للنشر: {new Date(approval.post.scheduled_at).toLocaleString('ar-SA')}
                          </p>
                        )}
                      </div>

                      {approval.can_approve && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleApprovalDecision(approval.id, 'approve')}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            موافقة
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleApprovalDecision(approval.id, 'request_changes')}
                          >
                            <MessageSquare className="h-4 w-4 mr-2" />
                            طلب تعديل
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleApprovalDecision(approval.id, 'reject')}
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            رفض
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات مساحة العمل</CardTitle>
                <CardDescription>إدارة إعدادات وصلاحيات مساحة العمل</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="workspace-name">اسم مساحة العمل</Label>
                    <Input
                      id="workspace-name"
                      defaultValue={selectedWorkspace.name}
                      data-testid="workspace-name-input"
                    />
                  </div>
                  <div>
                    <Label htmlFor="workspace-color">لون مساحة العمل</Label>
                    <Input
                      id="workspace-color"
                      type="color"
                      defaultValue={selectedWorkspace.color}
                      data-testid="workspace-color-input"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="workspace-description">الوصف</Label>
                  <Textarea
                    id="workspace-description"
                    defaultValue={selectedWorkspace.description}
                    data-testid="workspace-description-input"
                  />
                </div>
                <Button>حفظ التغييرات</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
