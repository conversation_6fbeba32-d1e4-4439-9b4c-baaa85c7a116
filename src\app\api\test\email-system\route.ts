import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { testType, email } = await request.json();
    const supabase = createClient();

    let result: any = {};

    switch (testType) {
      case 'signup':
        // Test signup with email confirmation
        const uniqueEmail = `test+${Date.now()}@example.com`;
        const { data: signupData, error: signupError } = await supabase.auth.signUp({
          email: uniqueEmail,
          password: 'TestPassword123!',
          options: {
            data: {
              name: 'Test User',
              role: 'USER'
            }
          }
        });

        result = {
          success: !signupError,
          message: signupError ? signupError.message : `Signup successful for ${uniqueEmail}`,
          data: signupData,
          error: signupError,
          emailSent: !signupData?.session, // If no session, email confirmation is required
          testEmail: uniqueEmail
        };
        break;

      case 'password-reset':
        // Test password reset email
        const { error: resetError } = await supabase.auth.resetPasswordForEmail(email || '<EMAIL>', {
          redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
        });

        result = {
          success: !resetError,
          message: resetError ? resetError.message : `Password reset email sent to ${email}`,
          error: resetError,
          testEmail: email
        };
        break;

      case 'email-change':
        // Test email change (requires authenticated user)
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          result = {
            success: false,
            message: 'User must be authenticated to test email change',
            error: { message: 'No authenticated user' }
          };
        } else {
          const newEmail = `newemail+${Date.now()}@example.com`;
          const { error: updateError } = await supabase.auth.updateUser({
            email: newEmail
          });

          result = {
            success: !updateError,
            message: updateError ? updateError.message : `Email change request sent to ${newEmail}`,
            error: updateError,
            oldEmail: user.email,
            newEmail: newEmail
          };
        }
        break;

      case 'config-check':
        // Check if email system is configured
        try {
          // Try to send a password reset to a test email to check if SMTP is working
          const { error: configError } = await supabase.auth.resetPasswordForEmail('<EMAIL>', {
            redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
          });

          if (configError) {
            if (configError.message.includes('rate limit') || configError.message.includes('too many')) {
              result = {
                success: true,
                message: 'Email system is working (rate limited)',
                warning: 'Rate limited - this indicates the email system is functional',
                error: configError
              };
            } else if (configError.message.includes('invalid') || configError.message.includes('not found')) {
              result = {
                success: true,
                message: 'Email system is working (user not found is expected)',
                info: 'This error is expected for non-existent users',
                error: configError
              };
            } else {
              result = {
                success: false,
                message: 'Email system configuration issue',
                error: configError
              };
            }
          } else {
            result = {
              success: true,
              message: 'Email system is configured and working'
            };
          }
        } catch (error: any) {
          result = {
            success: false,
            message: 'Email system test failed',
            error: error
          };
        }
        break;

      default:
        result = {
          success: false,
          message: 'Invalid test type',
          error: { message: 'Unknown test type' }
        };
    }

    return NextResponse.json({
      success: result.success,
      testType,
      timestamp: new Date().toISOString(),
      ...result
    });

  } catch (error: any) {
    console.error('Email test error:', error);
    return NextResponse.json({
      success: false,
      message: 'Email test failed',
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Email System Test API',
    availableTests: [
      'signup - Test signup with email confirmation',
      'password-reset - Test password reset email',
      'email-change - Test email change confirmation',
      'config-check - Check email system configuration'
    ],
    usage: 'POST with { testType: "signup|password-reset|email-change|config-check", email?: "<EMAIL>" }'
  });
}
