'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Upload, 
  Image, 
  Video, 
  FileText, 
  Music,
  Trash2, 
  Download, 
  Eye,
  RefreshCw,
  Filter,
  Search,
  Grid,
  List,
  Plus,
  X,
  CheckCircle,
  AlertCircle,
  Clock,
  Facebook,
  Linkedin,
  Instagram,
  Twitter
} from 'lucide-react';
import { toast } from 'sonner';

interface MediaFile {
  id: string;
  originalName: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  mediaType: 'image' | 'video' | 'document' | 'audio';
  url: string;
  thumbnailUrl?: string;
  metadata: any;
  platformVersions: any[];
  uploadedAt: string;
  processedAt?: string;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  error?: string;
}

interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  error?: string;
  result?: any;
}

export function MediaManagementDashboard() {
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, UploadProgress>>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());

  // Upload form state
  const [uploadFormData, setUploadFormData] = useState({
    description: '',
    tags: '',
    targetPlatforms: [] as string[],
    autoOptimize: true
  });

  useEffect(() => {
    loadMediaFiles();
  }, [filterType]);

  const loadMediaFiles = async () => {
    try {
      setIsLoading(true);
      
      const params = new URLSearchParams();
      if (filterType !== 'all') {
        params.append('mediaType', filterType);
      }
      params.append('limit', '50');
      params.append('sortBy', 'uploadedAt');
      params.append('sortOrder', 'desc');

      const response = await fetch(`/api/media/enhanced?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load media files');
      }

      setMediaFiles(data.data?.media || []);

    } catch (error: any) {
      console.error('Error loading media files:', error);
      toast.error('فشل في تحميل ملفات الوسائط');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    
    for (const file of fileArray) {
      const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Initialize upload progress
      setUploadProgress(prev => ({
        ...prev,
        [uploadId]: {
          file,
          progress: 0,
          status: 'uploading'
        }
      }));

      try {
        // Prepare form data
        const formData = new FormData();
        formData.append('file', file);
        formData.append('description', uploadFormData.description);
        formData.append('tags', uploadFormData.tags);
        formData.append('targetPlatforms', uploadFormData.targetPlatforms.join(','));
        formData.append('autoOptimize', uploadFormData.autoOptimize.toString());

        // Upload file
        const response = await fetch('/api/media/enhanced', {
          method: 'POST',
          body: formData
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Upload failed');
        }

        // Update progress to processing
        setUploadProgress(prev => ({
          ...prev,
          [uploadId]: {
            ...prev[uploadId],
            progress: 50,
            status: 'processing'
          }
        }));

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Update progress to complete
        setUploadProgress(prev => ({
          ...prev,
          [uploadId]: {
            ...prev[uploadId],
            progress: 100,
            status: 'complete',
            result: data.data
          }
        }));

        toast.success(`تم رفع ${file.name} بنجاح`);

        // Reload media files
        await loadMediaFiles();

        // Remove from progress after delay
        setTimeout(() => {
          setUploadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[uploadId];
            return newProgress;
          });
        }, 3000);

      } catch (error: any) {
        console.error('Upload error:', error);
        
        setUploadProgress(prev => ({
          ...prev,
          [uploadId]: {
            ...prev[uploadId],
            status: 'error',
            error: error.message
          }
        }));

        toast.error(`فشل في رفع ${file.name}: ${error.message}`);
      }
    }
  }, [uploadFormData]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  const handleDeleteMedia = async (mediaId: string) => {
    try {
      const response = await fetch(`/api/media/enhanced?mediaId=${mediaId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Delete failed');
      }

      toast.success('تم حذف الملف بنجاح');
      await loadMediaFiles();

    } catch (error: any) {
      console.error('Delete error:', error);
      toast.error(`فشل في حذف الملف: ${error.message}`);
    }
  };

  const handlePlatformToggle = (platform: string) => {
    setUploadFormData(prev => ({
      ...prev,
      targetPlatforms: prev.targetPlatforms.includes(platform)
        ? prev.targetPlatforms.filter(p => p !== platform)
        : [...prev.targetPlatforms, platform]
    }));
  };

  const getMediaIcon = (mediaType: string) => {
    switch (mediaType) {
      case 'image':
        return <Image className="w-5 h-5 text-blue-500" />;
      case 'video':
        return <Video className="w-5 h-5 text-purple-500" />;
      case 'document':
        return <FileText className="w-5 h-5 text-green-500" />;
      case 'audio':
        return <Music className="w-5 h-5 text-orange-500" />;
      default:
        return <FileText className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <RefreshCw className="w-4 h-4 text-gray-500 animate-spin" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return <Facebook className="w-4 h-4 text-blue-600" />;
      case 'linkedin':
        return <Linkedin className="w-4 h-4 text-blue-700" />;
      case 'instagram':
        return <Instagram className="w-4 h-4 text-pink-600" />;
      case 'twitter':
        return <Twitter className="w-4 h-4 text-blue-400" />;
      default:
        return null;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredMediaFiles = mediaFiles.filter(file => {
    if (searchQuery) {
      return file.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
             file.metadata?.description?.toLowerCase().includes(searchQuery.toLowerCase());
    }
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            رفع ملفات الوسائط
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Upload Form */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="description">وصف الملف</Label>
              <Textarea
                id="description"
                placeholder="وصف اختياري للملف..."
                value={uploadFormData.description}
                onChange={(e) => setUploadFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tags">العلامات</Label>
                <Input
                  id="tags"
                  placeholder="علامة1, علامة2, علامة3"
                  value={uploadFormData.tags}
                  onChange={(e) => setUploadFormData(prev => ({ ...prev, tags: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label>المنصات المستهدفة</Label>
                <div className="flex gap-2 flex-wrap">
                  {['facebook', 'linkedin', 'instagram', 'twitter'].map(platform => (
                    <div key={platform} className="flex items-center space-x-2">
                      <Checkbox
                        checked={uploadFormData.targetPlatforms.includes(platform)}
                        onCheckedChange={() => handlePlatformToggle(platform)}
                      />
                      <div className="flex items-center gap-1">
                        {getPlatformIcon(platform)}
                        <span className="text-sm capitalize">{platform}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={uploadFormData.autoOptimize}
                  onCheckedChange={(checked) => setUploadFormData(prev => ({ ...prev, autoOptimize: !!checked }))}
                />
                <Label>تحسين تلقائي للمنصات</Label>
              </div>
            </div>
          </div>

          {/* Drop Zone */}
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              اسحب الملفات هنا أو انقر للاختيار
            </p>
            <p className="text-sm text-gray-500 mb-4">
              يدعم الصور والفيديوهات والمستندات (حتى 100 ميجابايت)
            </p>
            <input
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.doc,.docx,.txt"
              onChange={handleFileInputChange}
              className="hidden"
              id="file-upload"
            />
            <Button asChild>
              <label htmlFor="file-upload" className="cursor-pointer">
                <Plus className="w-4 h-4 ml-2" />
                اختيار الملفات
              </label>
            </Button>
          </div>

          {/* Upload Progress */}
          {Object.keys(uploadProgress).length > 0 && (
            <div className="space-y-2">
              <Label>حالة الرفع</Label>
              {Object.entries(uploadProgress).map(([uploadId, progress]) => (
                <div key={uploadId} className="flex items-center gap-3 p-3 border rounded">
                  {getMediaIcon(progress.file.type.split('/')[0])}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">{progress.file.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {formatFileSize(progress.file.size)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all ${
                          progress.status === 'error' ? 'bg-red-500' :
                          progress.status === 'complete' ? 'bg-green-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${progress.progress}%` }}
                      />
                    </div>
                    {progress.error && (
                      <p className="text-xs text-red-500 mt-1">{progress.error}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(progress.status)}
                    {progress.status === 'complete' && (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Media Library */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Image className="w-5 h-5" />
              مكتبة الوسائط
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                onClick={loadMediaFiles}
                variant="outline"
                size="sm"
                disabled={isLoading}
              >
                {isLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
              <Button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                variant="outline"
                size="sm"
              >
                {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters and Search */}
          <div className="flex gap-4 items-center">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الملفات</SelectItem>
                  <SelectItem value="image">الصور</SelectItem>
                  <SelectItem value="video">الفيديوهات</SelectItem>
                  <SelectItem value="document">المستندات</SelectItem>
                  <SelectItem value="audio">الصوتيات</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="البحث في الملفات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Media Grid/List */}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
              <span className="mr-3">جاري التحميل...</span>
            </div>
          ) : filteredMediaFiles.length === 0 ? (
            <div className="text-center py-12">
              <Image className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">لا توجد ملفات وسائط</p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredMediaFiles.map((file) => (
                <div key={file.id} className="border rounded-lg overflow-hidden">
                  <div className="aspect-video bg-gray-100 flex items-center justify-center">
                    {file.mediaType === 'image' ? (
                      <img
                        src={file.thumbnailUrl || file.url}
                        alt={file.originalName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex flex-col items-center gap-2">
                        {getMediaIcon(file.mediaType)}
                        <span className="text-xs text-muted-foreground">
                          {file.mediaType.toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-medium truncate">{file.originalName}</h3>
                      {getStatusIcon(file.status)}
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                      <span>{formatFileSize(file.fileSize)}</span>
                      <span>{formatDate(file.uploadedAt)}</span>
                    </div>
                    {file.platformVersions.length > 0 && (
                      <div className="flex gap-1 mb-2">
                        {file.platformVersions.map((version, index) => (
                          <div key={index} title={version.platform}>
                            {getPlatformIcon(version.platform)}
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="flex gap-1">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Download className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleDeleteMedia(file.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredMediaFiles.map((file) => (
                <div key={file.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                    {getMediaIcon(file.mediaType)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium">{file.originalName}</h3>
                      {getStatusIcon(file.status)}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>{formatFileSize(file.fileSize)}</span>
                      <span>{formatDate(file.uploadedAt)}</span>
                      {file.platformVersions.length > 0 && (
                        <div className="flex gap-1">
                          {file.platformVersions.map((version, index) => (
                            <div key={index} title={version.platform}>
                              {getPlatformIcon(version.platform)}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteMedia(file.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
