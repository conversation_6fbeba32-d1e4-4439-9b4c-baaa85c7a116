'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  Upload, 
  Image, 
  Video, 
  File, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Download,
  Trash2,
  Eye,
  Copy,
  FolderPlus,
  Settings
} from 'lucide-react';

interface MediaFile {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  cdnUrl: string;
  folder: string;
  isPublic: boolean;
  metadata: any;
  optimizedVersions?: OptimizedVersion[];
  createdAt: string;
}

interface OptimizedVersion {
  platform: string;
  format: string;
  quality: string;
  url: string;
  dimensions: {
    width: number;
    height: number;
  };
  size: number;
}

interface UploadProgress {
  id: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

interface MediaLibraryProps {
  userId: string;
  onFileSelect?: (file: MediaFile) => void;
  allowMultiSelect?: boolean;
  allowedTypes?: string[];
  maxFileSize?: number;
  className?: string;
}

export default function MediaLibrary({
  userId,
  onFileSelect,
  allowMultiSelect = false,
  allowedTypes = ['image/*', 'video/*'],
  maxFileSize = 50 * 1024 * 1024, // 50MB
  className = ''
}: MediaLibraryProps) {
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch media files
  const fetchMediaFiles = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (selectedFolder !== 'all') {
        params.append('folder', selectedFolder);
      }

      const response = await fetch(`/api/media/upload?${params}`);
      const data = await response.json();

      if (data.success) {
        setMediaFiles(data.data || []);
      } else {
        setError(data.error || 'Failed to fetch media files');
      }
    } catch (err) {
      setError('Failed to fetch media files');
      console.error('Media fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [selectedFolder]);

  // Upload files
  const uploadFiles = useCallback(async (files: File[]) => {
    const uploads: UploadProgress[] = files.map(file => ({
      id: `upload_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      fileName: file.name,
      progress: 0,
      status: 'uploading'
    }));

    setUploadProgress(prev => [...prev, ...uploads]);

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const uploadId = uploads[i].id;

      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('folder', selectedFolder === 'all' ? 'general' : selectedFolder);
        formData.append('isPublic', 'true');
        formData.append('generateThumbnail', 'true');
        formData.append('optimizeForPlatforms', 'instagram,facebook,twitter,linkedin');

        // Update progress to processing
        setUploadProgress(prev => 
          prev.map(up => 
            up.id === uploadId 
              ? { ...up, progress: 50, status: 'processing' }
              : up
          )
        );

        const response = await fetch('/api/media/upload', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          // Update progress to completed
          setUploadProgress(prev => 
            prev.map(up => 
              up.id === uploadId 
                ? { ...up, progress: 100, status: 'completed' }
                : up
            )
          );

          // Refresh media files
          setTimeout(() => {
            fetchMediaFiles();
            // Remove completed upload from progress
            setUploadProgress(prev => prev.filter(up => up.id !== uploadId));
          }, 1000);

        } else {
          // Update progress to error
          setUploadProgress(prev => 
            prev.map(up => 
              up.id === uploadId 
                ? { ...up, status: 'error', error: data.error }
                : up
            )
          );
        }

      } catch (err) {
        setUploadProgress(prev => 
          prev.map(up => 
            up.id === uploadId 
              ? { ...up, status: 'error', error: 'Upload failed' }
              : up
          )
        );
      }
    }
  }, [selectedFolder, fetchMediaFiles]);

  // Dropzone configuration
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: uploadFiles,
    accept: allowedTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: maxFileSize,
    multiple: true
  });

  // Delete file
  const deleteFile = useCallback(async (fileId: string) => {
    try {
      const response = await fetch('/api/media/upload', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fileId }),
      });

      const data = await response.json();

      if (data.success) {
        setMediaFiles(prev => prev.filter(file => file.id !== fileId));
        setSelectedFiles(prev => prev.filter(id => id !== fileId));
      } else {
        setError(data.error || 'Failed to delete file');
      }
    } catch (err) {
      setError('Failed to delete file');
      console.error('Delete error:', err);
    }
  }, []);

  // Copy URL to clipboard
  const copyUrl = useCallback(async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      // Show success message (you could add a toast here)
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  }, []);

  // Filter files based on search and folder
  const filteredFiles = mediaFiles.filter(file => {
    const matchesSearch = file.fileName.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFolder = selectedFolder === 'all' || file.folder === selectedFolder;
    return matchesSearch && matchesFolder;
  });

  // Get unique folders
  const folders = Array.from(new Set(mediaFiles.map(file => file.folder)));

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file icon
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (fileType.startsWith('video/')) return <Video className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  useEffect(() => {
    fetchMediaFiles();
  }, [fetchMediaFiles]);

  return (
    <div className={`media-library ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Media Library
        </h2>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
          </button>
          
          <button className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700">
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
        
        <select
          value={selectedFolder}
          onChange={(e) => setSelectedFolder(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
        >
          <option value="all">All Folders</option>
          {folders.map(folder => (
            <option key={folder} value={folder}>
              {folder.charAt(0).toUpperCase() + folder.slice(1)}
            </option>
          ))}
        </select>
      </div>

      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragActive
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
      >
        <input {...getInputProps()} />
        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
        </p>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          or click to browse files
        </p>
        <p className="text-sm text-gray-400">
          Supports: Images, Videos • Max size: {formatFileSize(maxFileSize)}
        </p>
      </div>

      {/* Upload Progress */}
      {uploadProgress.length > 0 && (
        <div className="mt-6 space-y-2">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Uploading Files
          </h3>
          {uploadProgress.map(upload => (
            <div key={upload.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {upload.fileName}
                </span>
                <span className="text-sm text-gray-500">
                  {upload.status === 'error' ? 'Error' : `${upload.progress}%`}
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all ${
                    upload.status === 'error' 
                      ? 'bg-red-500' 
                      : upload.status === 'completed'
                      ? 'bg-green-500'
                      : 'bg-blue-500'
                  }`}
                  style={{ width: `${upload.progress}%` }}
                />
              </div>
              {upload.error && (
                <p className="text-sm text-red-500 mt-1">{upload.error}</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Media Grid/List */}
      {loading ? (
        <div className="mt-6 text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-gray-500 dark:text-gray-400 mt-2">Loading media files...</p>
        </div>
      ) : filteredFiles.length === 0 ? (
        <div className="mt-6 text-center py-12">
          <Image className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">
            {searchQuery || selectedFolder !== 'all' 
              ? 'No files match your search criteria' 
              : 'No media files yet. Upload some files to get started!'
            }
          </p>
        </div>
      ) : (
        <div className={`mt-6 ${
          viewMode === 'grid' 
            ? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4' 
            : 'space-y-2'
        }`}>
          {filteredFiles.map(file => (
            <MediaFileCard
              key={file.id}
              file={file}
              viewMode={viewMode}
              isSelected={selectedFiles.includes(file.id)}
              onSelect={() => {
                if (allowMultiSelect) {
                  setSelectedFiles(prev => 
                    prev.includes(file.id)
                      ? prev.filter(id => id !== file.id)
                      : [...prev, file.id]
                  );
                } else {
                  setSelectedFiles([file.id]);
                  onFileSelect?.(file);
                }
              }}
              onDelete={() => deleteFile(file.id)}
              onCopyUrl={() => copyUrl(file.cdnUrl)}
              formatFileSize={formatFileSize}
              getFileIcon={getFileIcon}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// Media File Card Component
interface MediaFileCardProps {
  file: MediaFile;
  viewMode: 'grid' | 'list';
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onCopyUrl: () => void;
  formatFileSize: (bytes: number) => string;
  getFileIcon: (fileType: string) => React.ReactNode;
}

function MediaFileCard({
  file,
  viewMode,
  isSelected,
  onSelect,
  onDelete,
  onCopyUrl,
  formatFileSize,
  getFileIcon
}: MediaFileCardProps) {
  const isImage = file.fileType.startsWith('image/');
  const isVideo = file.fileType.startsWith('video/');

  if (viewMode === 'grid') {
    return (
      <div
        className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
          isSelected 
            ? 'border-blue-500 ring-2 ring-blue-200' 
            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
        }`}
        onClick={onSelect}
      >
        <div className="aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
          {isImage ? (
            <img
              src={file.cdnUrl}
              alt={file.fileName}
              className="w-full h-full object-cover"
              loading="lazy"
            />
          ) : isVideo ? (
            <div className="relative w-full h-full bg-gray-900 flex items-center justify-center">
              <Video className="w-8 h-8 text-white" />
              <video
                src={file.cdnUrl}
                className="absolute inset-0 w-full h-full object-cover opacity-50"
                muted
                preload="metadata"
              />
            </div>
          ) : (
            <div className="flex flex-col items-center">
              {getFileIcon(file.fileType)}
              <span className="text-xs text-gray-500 mt-1">
                {file.fileType.split('/')[1]?.toUpperCase()}
              </span>
            </div>
          )}
        </div>

        {/* Overlay with actions */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCopyUrl();
              }}
              className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors"
              title="Copy URL"
            >
              <Copy className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                window.open(file.cdnUrl, '_blank');
              }}
              className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors"
              title="View"
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors text-red-500"
              title="Delete"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* File info */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
          <p className="text-white text-xs font-medium truncate">
            {file.fileName}
          </p>
          <p className="text-gray-300 text-xs">
            {formatFileSize(file.fileSize)}
          </p>
        </div>
      </div>
    );
  }

  // List view
  return (
    <div
      className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer transition-all ${
        isSelected 
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
      }`}
      onClick={onSelect}
    >
      <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 flex items-center justify-center flex-shrink-0">
        {isImage ? (
          <img
            src={file.cdnUrl}
            alt={file.fileName}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        ) : (
          getFileIcon(file.fileType)
        )}
      </div>

      <div className="flex-1 min-w-0">
        <p className="font-medium text-gray-900 dark:text-white truncate">
          {file.fileName}
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {formatFileSize(file.fileSize)} • {file.folder}
        </p>
      </div>

      <div className="flex items-center gap-2">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onCopyUrl();
          }}
          className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          title="Copy URL"
        >
          <Copy className="w-4 h-4" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            window.open(file.cdnUrl, '_blank');
          }}
          className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          title="View"
        >
          <Eye className="w-4 h-4" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          className="p-2 text-gray-400 hover:text-red-500"
          title="Delete"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
