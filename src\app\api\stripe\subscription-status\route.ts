import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { UsageTracker } from '@/lib/stripe/usage-tracker';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user subscription details
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        name,
        subscription_status,
        trial_ends_at,
        current_plan_id,
        subscription_plans!inner(
          id,
          name,
          name_ar,
          description,
          description_ar,
          price_monthly,
          price_yearly,
          max_social_accounts,
          max_users,
          max_posts_per_month,
          features,
          plan_type
        )
      `)
      .eq('id', user.id)
      .single();

    if (userError) {
      console.error('Failed to get user data:', userError);
      return NextResponse.json(
        { error: 'Failed to get user data' },
        { status: 500 }
      );
    }

    // Get active subscription details
    const { data: subscription, error: subError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    // Get usage data
    const usageTracker = new UsageTracker();
    const [currentUsage, planLimits, limitStatus] = await Promise.all([
      usageTracker.getCurrentUsage(user.id),
      usageTracker.getUserPlanLimits(user.id),
      usageTracker.checkLimitExceeded(user.id)
    ]);

    // Get social accounts count
    const { count: socialAccountsCount } = await supabase
      .from('social_accounts')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    // Get team members count
    const { count: teamMembersCount } = await supabase
      .from('team_members')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('status', 'active');

    return NextResponse.json({
      success: true,
      user: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        subscriptionStatus: userData.subscription_status,
        trialEndsAt: userData.trial_ends_at,
      },
      plan: userData.subscription_plans,
      subscription: subscription || null,
      usage: {
        current: currentUsage,
        limits: planLimits,
        limitStatus,
        socialAccounts: socialAccountsCount || 0,
        teamMembers: (teamMembersCount || 0) + 1, // +1 for owner
      },
      features: userData.subscription_plans?.features || {},
    });

  } catch (error) {
    console.error('Subscription status error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get subscription status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
