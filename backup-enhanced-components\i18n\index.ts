import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export type Locale = 'ar' | 'en';

export const locales: Locale[] = ['ar', 'en'];
export const defaultLocale: Locale = 'ar';

// RTL languages
export const rtlLocales: Locale[] = ['ar'];

/**
 * Check if a locale is RTL
 */
export function isRTL(locale: Locale): boolean {
  return rtlLocales.includes(locale);
}

/**
 * Get text direction for a locale
 */
export function getDirection(locale: Locale): 'ltr' | 'rtl' {
  return isRTL(locale) ? 'rtl' : 'ltr';
}

/**
 * Get text alignment based on locale
 */
export function getTextAlign(locale: Locale): 'left' | 'right' {
  return isRTL(locale) ? 'right' : 'left';
}

/**
 * Get flex direction with RTL support
 */
export function getFlexDirection(
  locale: Locale, 
  defaultDirection: 'row' | 'row-reverse' = 'row'
): 'row' | 'row-reverse' {
  if (defaultDirection === 'row') {
    return isRTL(locale) ? 'row-reverse' : 'row';
  } else {
    return isRTL(locale) ? 'row' : 'row-reverse';
  }
}

/**
 * Get margin/padding classes with RTL support
 */
export function getSpacingClass(
  locale: Locale, 
  side: 'start' | 'end', 
  size: number,
  type: 'margin' | 'padding' = 'margin'
): string {
  const prefix = type === 'margin' ? 'm' : 'p';
  const direction = side === 'start' 
    ? (isRTL(locale) ? 'r' : 'l')
    : (isRTL(locale) ? 'l' : 'r');
  
  return `${prefix}${direction}-${size}`;
}

/**
 * Hook to get current locale
 */
export function useLocale(): Locale {
  const router = useRouter();
  return (router.locale as Locale) || defaultLocale;
}

/**
 * Hook to get translations for current locale
 */
export function useTranslations(namespace?: string) {
  const locale = useLocale();
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadTranslations() {
      try {
        setLoading(true);
        const module = await import(`./locales/${locale}.json`);
        const data = namespace ? module.default[namespace] || {} : module.default;
        setTranslations(data);
      } catch (error) {
        console.error(`Failed to load translations for ${locale}:`, error);
        setTranslations({});
      } finally {
        setLoading(false);
      }
    }

    loadTranslations();
  }, [locale, namespace]);

  const t = (key: string, fallback?: string): string => {
    const keys = key.split('.');
    let value = translations;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return fallback || key;
      }
    }
    
    return typeof value === 'string' ? value : fallback || key;
  };

  return { t, loading, locale };
}

/**
 * Format date according to locale
 */
export function formatDate(date: Date | string, locale: Locale): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (locale === 'ar') {
    return dateObj.toLocaleDateString('ar-SA', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  }
  
  return dateObj.toLocaleDateString('en-US', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });
}

/**
 * Format time according to locale
 */
export function formatTime(date: Date | string, locale: Locale): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (locale === 'ar') {
    return dateObj.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }
  
  return dateObj.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
}

/**
 * Format numbers according to locale
 */
export function formatNumber(number: number, locale: Locale): string {
  if (locale === 'ar') {
    return number.toLocaleString('ar-SA');
  }
  
  return number.toLocaleString('en-US');
}

/**
 * Get CSS classes for RTL support
 */
export function getRTLClasses(locale: Locale): string {
  const direction = getDirection(locale);
  const textAlign = getTextAlign(locale);
  
  return `dir-${direction} text-${textAlign}`;
}

/**
 * Language switcher utility
 */
export function switchLocale(router: any, newLocale: Locale) {
  const { pathname, asPath, query } = router;
  router.push({ pathname, query }, asPath, { locale: newLocale });
}

/**
 * Get localized URL
 */
export function getLocalizedUrl(path: string, locale: Locale): string {
  if (locale === defaultLocale) {
    return path;
  }
  return `/${locale}${path}`;
}

/**
 * Validate if string contains Arabic characters
 */
export function containsArabic(text: string): boolean {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
}

/**
 * Auto-detect text direction based on content
 */
export function detectTextDirection(text: string): 'ltr' | 'rtl' {
  return containsArabic(text) ? 'rtl' : 'ltr';
}

/**
 * Get appropriate font family for locale
 */
export function getFontFamily(locale: Locale): string {
  if (locale === 'ar') {
    return 'var(--font-arabic), "Noto Sans Arabic", "Cairo", "Amiri", sans-serif';
  }
  return 'var(--font-inter), Inter, -apple-system, BlinkMacSystemFont, sans-serif';
}

export default {
  locales,
  defaultLocale,
  isRTL,
  getDirection,
  getTextAlign,
  getFlexDirection,
  getSpacingClass,
  formatDate,
  formatTime,
  formatNumber,
  getRTLClasses,
  switchLocale,
  getLocalizedUrl,
  containsArabic,
  detectTextDirection,
  getFontFamily,
};
