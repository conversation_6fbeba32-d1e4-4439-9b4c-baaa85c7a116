// @ts-ignore - OpenAI types may not be available
import { OpenAI } from 'openai';

// AI Content Generation Service
export interface ContentGenerationRequest {
  prompt: string;
  platform: 'twitter' | 'facebook' | 'instagram' | 'linkedin' | 'snapchat';
  tone: 'professional' | 'casual' | 'friendly' | 'formal' | 'humorous' | 'inspiring';
  language: 'ar' | 'en';
  includeHashtags: boolean;
  includeEmojis: boolean;
  maxLength?: number;
  keywords?: string[];
  targetAudience?: string;
}

export interface ContentGenerationResponse {
  content: string;
  hashtags: string[];
  emojis: string[];
  suggestions: string[];
  characterCount: number;
  platformOptimized: boolean;
}

export interface HashtagSuggestion {
  tag: string;
  popularity: 'high' | 'medium' | 'low';
  relevance: number;
  category: string;
}

export class AIContentGenerator {
  private openai: OpenAI | null;
  private openRouterApiKey: string;

  constructor() {
    // Only initialize OpenAI if API key is available
    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (openaiApiKey) {
      this.openai = new OpenAI({
        apiKey: openaiApiKey,
      });
    } else {
      this.openai = null;
    }
    this.openRouterApiKey = process.env.OPENROUTER_API_KEY || '';
  }

  /**
   * Generate content using AI based on user requirements
   */
  async generateContent(request: ContentGenerationRequest): Promise<ContentGenerationResponse> {
    try {
      const systemPrompt = this.buildSystemPrompt(request);
      const userPrompt = this.buildUserPrompt(request);

      // Use OpenRouter for Arabic content (cost-effective) or as fallback
      if (request.language === 'ar' || !this.openai) {
        return await this.generateWithOpenRouter(systemPrompt, userPrompt, request);
      }

      // Use OpenAI for English content if available
      try {
        return await this.generateWithOpenAI(systemPrompt, userPrompt, request);
      } catch (openaiError) {
        console.warn('OpenAI failed, falling back to OpenRouter:', openaiError);
        return await this.generateWithOpenRouter(systemPrompt, userPrompt, request);
      }

    } catch (error) {
      console.error('Content generation error:', error);
      throw new Error('Failed to generate content');
    }
  }

  /**
   * Generate content using OpenAI
   */
  private async generateWithOpenAI(
    systemPrompt: string,
    userPrompt: string,
    request: ContentGenerationRequest
  ): Promise<ContentGenerationResponse> {
    if (!this.openai) {
      throw new Error('OpenAI API key not configured. Using OpenRouter fallback.');
    }

    const completion = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 500,
    });

    const response = completion.choices[0]?.message?.content || '';
    return this.parseAIResponse(response, request);
  }

  /**
   * Generate content using OpenRouter (cost-effective for Arabic)
   */
  private async generateWithOpenRouter(
    systemPrompt: string,
    userPrompt: string,
    request: ContentGenerationRequest
  ): Promise<ContentGenerationResponse> {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || '',
        'X-Title': 'eWasl Social Scheduler',
      } as HeadersInit,
      body: JSON.stringify({
        model: 'qwen/qwen-2.5-72b-instruct',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 500,
      }),
    });

    if (!response.ok) {
      throw new Error('OpenRouter API request failed');
    }

    const data = await response.json();
    const aiResponse = data.choices[0]?.message?.content || '';
    return this.parseAIResponse(aiResponse, request);
  }

  /**
   * Build system prompt based on platform and requirements
   */
  private buildSystemPrompt(request: ContentGenerationRequest): string {
    const platformLimits = {
      twitter: 280,
      facebook: 2000,
      instagram: 2200,
      linkedin: 3000,
      snapchat: 250,
    };

    const language = request.language === 'ar' ? 'Arabic' : 'English';
    const limit = request.maxLength || platformLimits[request.platform];

    return `You are an expert social media content creator specializing in ${language} content for ${request.platform}.

REQUIREMENTS:
- Language: ${language}
- Platform: ${request.platform}
- Tone: ${request.tone}
- Character limit: ${limit}
- Include hashtags: ${request.includeHashtags}
- Include emojis: ${request.includeEmojis}
- Target audience: ${request.targetAudience || 'general'}

PLATFORM-SPECIFIC GUIDELINES:
${this.getPlatformGuidelines(request.platform)}

RESPONSE FORMAT:
Provide your response in this exact JSON format:
{
  "content": "main post content",
  "hashtags": ["hashtag1", "hashtag2"],
  "emojis": ["emoji1", "emoji2"],
  "suggestions": ["alternative version 1", "alternative version 2"]
}

Focus on creating engaging, authentic content that resonates with the target audience while following platform best practices.`;
  }

  /**
   * Build user prompt with specific requirements
   */
  private buildUserPrompt(request: ContentGenerationRequest): string {
    let prompt = `Create social media content based on: "${request.prompt}"`;

    if (request.keywords && request.keywords.length > 0) {
      prompt += `\n\nInclude these keywords: ${request.keywords.join(', ')}`;
    }

    if (request.targetAudience) {
      prompt += `\n\nTarget audience: ${request.targetAudience}`;
    }

    return prompt;
  }

  /**
   * Get platform-specific guidelines
   */
  private getPlatformGuidelines(platform: string): string {
    const guidelines = {
      twitter: `
- Keep it concise and punchy
- Use trending hashtags (2-3 max)
- Encourage engagement with questions
- Use threads for longer content`,

      facebook: `
- Tell a story or share insights
- Use 1-2 relevant hashtags
- Encourage comments and shares
- Include call-to-action`,

      instagram: `
- Visual storytelling focus
- Use 5-10 relevant hashtags
- Include emojis for personality
- Write engaging captions`,

      linkedin: `
- Professional and insightful tone
- Share industry knowledge
- Use 3-5 professional hashtags
- Encourage professional discussion`,

      snapchat: `
- Casual and authentic
- Short and snappy
- Use trending slang appropriately
- Focus on immediate engagement`,
    };

    return guidelines[platform as keyof typeof guidelines] || '';
  }

  /**
   * Parse AI response and extract structured data
   */
  private parseAIResponse(response: string, request: ContentGenerationRequest): ContentGenerationResponse {
    try {
      // Try to parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          content: parsed.content || response,
          hashtags: parsed.hashtags || [],
          emojis: parsed.emojis || [],
          suggestions: parsed.suggestions || [],
          characterCount: (parsed.content || response).length,
          platformOptimized: this.isPlatformOptimized(parsed.content || response, request.platform),
        };
      }

      // Fallback: extract content manually
      return this.extractContentManually(response, request);

    } catch (error) {
      console.error('Error parsing AI response:', error);
      return this.extractContentManually(response, request);
    }
  }

  /**
   * Extract content manually if JSON parsing fails
   */
  private extractContentManually(response: string, request: ContentGenerationRequest): ContentGenerationResponse {
    // Extract hashtags
    const hashtagMatches = response.match(/#[\w\u0600-\u06FF]+/g) || [];
    const hashtags = hashtagMatches.map(tag => tag.substring(1));

    // Extract emojis
    const emojiMatches = response.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || [];

    // Clean content (remove hashtags for main content)
    const cleanContent = response.replace(/#[\w\u0600-\u06FF]+/g, '').trim();

    return {
      content: cleanContent,
      hashtags,
      emojis: emojiMatches,
      suggestions: [],
      characterCount: cleanContent.length,
      platformOptimized: this.isPlatformOptimized(cleanContent, request.platform),
    };
  }

  /**
   * Check if content is optimized for platform
   */
  private isPlatformOptimized(content: string, platform: string): boolean {
    const platformLimits = {
      twitter: 280,
      facebook: 2000,
      instagram: 2200,
      linkedin: 3000,
      snapchat: 250,
    };

    const limit = platformLimits[platform as keyof typeof platformLimits];
    return content.length <= limit && content.length > 10;
  }

  /**
   * Generate hashtag suggestions
   */
  async generateHashtagSuggestions(
    content: string,
    platform: string,
    language: 'ar' | 'en' = 'ar'
  ): Promise<HashtagSuggestion[]> {
    try {
      const prompt = `Generate relevant hashtags for this ${language === 'ar' ? 'Arabic' : 'English'} social media content on ${platform}: "${content}"

Provide 10-15 hashtags with popularity and relevance scores. Format as JSON:
[
  {
    "tag": "hashtag",
    "popularity": "high|medium|low",
    "relevance": 0.9,
    "category": "category"
  }
]`;

      let response: string;

      if (language === 'ar') {
        // Use OpenRouter for Arabic
        const apiResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.openRouterApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'qwen/qwen-2.5-72b-instruct',
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.5,
            max_tokens: 300,
          }),
        });

        const data = await apiResponse.json();
        response = data.choices[0]?.message?.content || '[]';
      } else {
        // Use OpenAI for English if available, otherwise use OpenRouter
        if (this.openai) {
          const completion = await this.openai.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.5,
            max_tokens: 300,
          });
          response = completion.choices[0]?.message?.content || '[]';
        } else {
          // Fallback to OpenRouter for English
          const apiResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.openRouterApiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'qwen/qwen-2.5-72b-instruct',
              messages: [{ role: 'user', content: prompt }],
              temperature: 0.5,
              max_tokens: 300,
            }),
          });
          const data = await apiResponse.json();
          response = data.choices[0]?.message?.content || '[]';
        }
      }

      // Parse JSON response
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return [];

    } catch (error) {
      console.error('Hashtag generation error:', error);
      return [];
    }
  }

  /**
   * Optimize content for specific platform
   */
  async optimizeForPlatform(
    content: string,
    fromPlatform: string,
    toPlatform: string,
    language: 'ar' | 'en' = 'ar'
  ): Promise<string> {
    try {
      const prompt = `Optimize this ${language === 'ar' ? 'Arabic' : 'English'} social media content from ${fromPlatform} for ${toPlatform}:

Original content: "${content}"

Adapt the content to fit ${toPlatform}'s best practices, character limits, and audience expectations while maintaining the core message.`;

      let response: string;

      if (language === 'ar') {
        const apiResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.openRouterApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'qwen/qwen-2.5-72b-instruct',
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.7,
            max_tokens: 400,
          }),
        });

        const data = await apiResponse.json();
        response = data.choices[0]?.message?.content || content;
      } else {
        // Use OpenAI for English if available, otherwise use OpenRouter
        if (this.openai) {
          const completion = await this.openai.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.7,
            max_tokens: 400,
          });
          response = completion.choices[0]?.message?.content || content;
        } else {
          // Fallback to OpenRouter for English
          const apiResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.openRouterApiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'qwen/qwen-2.5-72b-instruct',
              messages: [{ role: 'user', content: prompt }],
              temperature: 0.7,
              max_tokens: 400,
            }),
          });
          const data = await apiResponse.json();
          response = data.choices[0]?.message?.content || content;
        }
      }

      return response.trim();

    } catch (error) {
      console.error('Content optimization error:', error);
      return content;
    }
  }
}

// Export singleton instance
export const aiContentGenerator = new AIContentGenerator();
