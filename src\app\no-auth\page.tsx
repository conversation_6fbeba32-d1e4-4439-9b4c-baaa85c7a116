"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";

export default function NoAuthPage() {
  const router = useRouter();

  useEffect(() => {
    // Automatically redirect to dashboard after 1 second
    const timer = setTimeout(() => {
      router.push("/dashboard");
    }, 1000);

    return () => clearTimeout(timer);
  }, [router]);

  const goToDashboard = () => {
    router.push("/dashboard");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <span className="text-3xl font-bold text-white">✅</span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-3">
            Authentication Removed
          </h1>
          <p className="text-gray-600 text-lg">تم إزالة تسجيل الدخول - وصول مباشر للوحة التحكم</p>
          <p className="text-sm text-gray-500 mt-2">No Login Required</p>
        </div>

        {/* Access Panel */}
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-10 border border-white/50">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">🎉 Direct Access Enabled</h2>
            <p className="text-gray-600 text-lg">You can now access the dashboard without logging in</p>
          </div>

          <div className="space-y-6">
            {/* Auto Redirect Notice */}
            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-6 border border-emerald-100">
              <h4 className="font-bold text-emerald-900 mb-4 text-lg">⚡ Auto Redirect</h4>
              <p className="text-emerald-800">You will be automatically redirected to the dashboard in 1 second...</p>
            </div>

            {/* Manual Access Button */}
            <div className="text-center">
              <Button
                onClick={goToDashboard}
                size="lg"
                className="h-16 px-12 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-lg"
              >
                🎯 Go to Dashboard Now
              </Button>
            </div>

            {/* Success Information */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
              <h4 className="font-bold text-green-900 mb-4 text-lg">✅ Authentication Status</h4>
              <div className="space-y-2 text-sm text-green-800">
                <p>• <strong>Login Screen:</strong> Completely removed</p>
                <p>• <strong>Dashboard Access:</strong> Direct access enabled</p>
                <p>• <strong>All Routes:</strong> Accessible without authentication</p>
                <p>• <strong>Status:</strong> Ready for immediate use</p>
              </div>
            </div>

            {/* Available Routes */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
              <h4 className="font-bold text-blue-900 mb-4 text-lg">🌐 Available Routes</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <p>• <a href="/dashboard" className="text-blue-600 hover:underline font-medium">/dashboard</a> - Main Dashboard</p>
                  <p>• <a href="/posts" className="text-blue-600 hover:underline font-medium">/posts</a> - Posts Management</p>
                  <p>• <a href="/social" className="text-blue-600 hover:underline font-medium">/social</a> - Social Accounts</p>
                </div>
                <div className="space-y-1">
                  <p>• <a href="/schedule" className="text-blue-600 hover:underline font-medium">/schedule</a> - Content Scheduler</p>
                  <p>• <a href="/settings" className="text-blue-600 hover:underline font-medium">/settings</a> - Settings</p>
                  <p>• <a href="/analytics" className="text-blue-600 hover:underline font-medium">/analytics</a> - Analytics</p>
                </div>
              </div>
            </div>

            {/* Success Message */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100 text-center">
              <h4 className="font-bold text-purple-900 mb-2 text-lg">🎉 Success!</h4>
              <p className="text-purple-800 text-sm">
                The login screen has been completely removed. You now have direct access to all dashboard features.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
