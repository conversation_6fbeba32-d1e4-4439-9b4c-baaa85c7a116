'use client';

import React, { useState } from 'react';
import { Templates<PERSON>rowser } from '@/components/templates/templates-browser';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Copy, Download, FileText, Sparkles } from 'lucide-react';
import { toast } from 'sonner';

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  platform_compatibility: string[];
  language: string;
  content_body: string;
  variables: any[];
  hashtags: string[];
  tone: string;
  industry_tags: string[];
  usage_count: number;
  rating: number;
  is_featured: boolean;
  created_at: string;
}

export default function TemplatesPage() {
  const [selectedContent, setSelectedContent] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  const handleTemplateSelect = (template: Template, processedContent: string) => {
    setSelectedContent(processedContent);
    setSelectedTemplate(template);
    toast.success('تم تحديد القالب بنجاح');
  };

  const copyContent = () => {
    if (selectedContent) {
      navigator.clipboard.writeText(selectedContent);
      toast.success('تم نسخ المحتوى إلى الحافظة');
    }
  };

  const downloadContent = () => {
    if (selectedContent) {
      const blob = new Blob([selectedContent], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${selectedTemplate?.name || 'template'}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('تم تحميل المحتوى');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            مكتبة القوالب
          </h1>
          <p className="text-gray-600">
            استكشف مجموعة واسعة من القوالب الجاهزة لإنشاء محتوى احترافي لوسائل التواصل الاجتماعي
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Templates Browser */}
          <div className="lg:col-span-2">
            <TemplatesBrowser
              onTemplateSelect={handleTemplateSelect}
              language="ar"
            />
          </div>

          {/* Selected Content Preview */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  المحتوى المحدد
                </CardTitle>
                <CardDescription>
                  معاينة المحتوى الذي تم إنشاؤه من القالب
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedTemplate ? (
                  <>
                    <div className="space-y-2">
                      <h3 className="font-semibold">{selectedTemplate.name}</h3>
                      <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
                      
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline">{selectedTemplate.category}</Badge>
                        <Badge variant="outline">{selectedTemplate.tone}</Badge>
                        {selectedTemplate.is_featured && (
                          <Badge variant="secondary">
                            <Sparkles className="h-3 w-3 mr-1" />
                            مميز
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <span>المنصات:</span>
                        {selectedTemplate.platform_compatibility.map((platform) => (
                          <Badge key={platform} variant="outline" className="text-xs">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <Textarea
                        value={selectedContent}
                        onChange={(e) => setSelectedContent(e.target.value)}
                        placeholder="سيظهر المحتوى المحدد هنا..."
                        className="min-h-[200px] text-sm"
                        dir="rtl"
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copyContent}
                        disabled={!selectedContent}
                        className="flex-1"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        نسخ
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={downloadContent}
                        disabled={!selectedContent}
                        className="flex-1"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        تحميل
                      </Button>
                    </div>

                    <div className="text-xs text-gray-500 space-y-1">
                      <div>الأحرف: {selectedContent.length}</div>
                      <div>الكلمات: {selectedContent.trim().split(/\s+/).filter(w => w.length > 0).length}</div>
                      <div>الهاشتاقات: {(selectedContent.match(/#[\w\u0600-\u06FF]+/g) || []).length}</div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>اختر قالباً لمعاينة المحتوى</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">إحصائيات سريعة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span>إجمالي القوالب</span>
                    <span className="font-semibold">5+</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الفئات المتاحة</span>
                    <span className="font-semibold">10</span>
                  </div>
                  <div className="flex justify-between">
                    <span>المنصات المدعومة</span>
                    <span className="font-semibold">5</span>
                  </div>
                  <div className="flex justify-between">
                    <span>اللغات</span>
                    <span className="font-semibold">العربية</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
