// Configuration validation to prevent build errors
// Ensures all required environment variables are available

export interface ConfigValidation {
  isValid: boolean;
  missingVars: string[];
  warnings: string[];
}

// Required environment variables for production
const REQUIRED_ENV_VARS = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
];

// Optional environment variables (will show warnings if missing)
const OPTIONAL_ENV_VARS = [
  'STRIPE_SECRET_KEY',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
  'TWITTER_API_KEY',
  'TWITTER_API_SECRET',
  'FACEBOOK_APP_ID',
  'FACEBOOK_APP_SECRET',
  'LINKEDIN_CLIENT_ID',
  'LINKEDIN_CLIENT_SECRET',
  'SENDGRID_API_KEY',
  'NEXTAUTH_SECRET',
];

export function validateConfiguration(): ConfigValidation {
  const missingVars: string[] = [];
  const warnings: string[] = [];

  // Check required variables
  for (const varName of REQUIRED_ENV_VARS) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  }

  // Check optional variables
  for (const varName of OPTIONAL_ENV_VARS) {
    if (!process.env[varName]) {
      warnings.push(varName);
    }
  }

  return {
    isValid: missingVars.length === 0,
    missingVars,
    warnings,
  };
}

// Safe environment variable getter with fallbacks
export function getEnvVar(name: string, fallback?: string): string {
  const value = process.env[name];

  if (!value) {
    if (fallback !== undefined) {
      return fallback;
    }

    // In development, return a placeholder
    if (process.env.NODE_ENV === 'development') {
      console.warn(`⚠️  Environment variable ${name} is not set, using placeholder`);
      return `placeholder_${name.toLowerCase()}`;
    }

    throw new Error(`Required environment variable ${name} is not set`);
  }

  return value;
}

// Safe configuration getters for each service
export const config = {
  supabase: {
    url: () => getEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
    anonKey: () => getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
    serviceRoleKey: () => getEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
  },

  stripe: {
    secretKey: () => getEnvVar('STRIPE_SECRET_KEY', ''),
    publishableKey: () => getEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY', ''),
    webhookSecret: () => getEnvVar('STRIPE_WEBHOOK_SECRET', ''),
  },

  twitter: {
    apiKey: () => getEnvVar('TWITTER_API_KEY', ''),
    apiSecret: () => getEnvVar('TWITTER_API_SECRET', ''),
    bearerToken: () => getEnvVar('TWITTER_BEARER_TOKEN', ''),
  },

  facebook: {
    appId: () => getEnvVar('FACEBOOK_APP_ID', ''),
    appSecret: () => getEnvVar('FACEBOOK_APP_SECRET', ''),
  },

  linkedin: {
    clientId: () => getEnvVar('LINKEDIN_CLIENT_ID', ''),
    clientSecret: () => getEnvVar('LINKEDIN_CLIENT_SECRET', ''),
  },

  email: {
    sendgridApiKey: () => getEnvVar('SENDGRID_API_KEY', ''),
    fromEmail: () => getEnvVar('SENDGRID_FROM_EMAIL', '<EMAIL>'),
    fromName: () => getEnvVar('SENDGRID_FROM_NAME', 'eWasl'),
  },

  app: {
    url: () => getEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000'),
    nextAuthSecret: () => getEnvVar('NEXTAUTH_SECRET', ''),
  },
};

// Validate configuration on module load (only in production)
if (process.env.NODE_ENV === 'production') {
  const validation = validateConfiguration();

  if (!validation.isValid) {
    console.error('❌ Configuration validation failed!');
    console.error('Missing required environment variables:', validation.missingVars);

    // Don't throw in production build, just log the error
    // The actual error will be thrown when the service is used
  }

  if (validation.warnings.length > 0) {
    console.warn('⚠️  Optional environment variables not set:', validation.warnings);
    console.warn('Some features may not work correctly');
  }
}

// validateConfiguration is already exported above
