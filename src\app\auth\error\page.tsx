'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const [error, setError] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [details, setDetails] = useState<string>('');

  useEffect(() => {
    setError(searchParams.get('error') || 'unknown_error');
    setDescription(searchParams.get('description') || '');
    setDetails(searchParams.get('details') || '');
  }, [searchParams]);

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case 'access_denied':
        return 'You denied access to your account. Please try again and grant the necessary permissions.';
      case 'missing_code':
        return 'Authorization code was missing. Please try the connection process again.';
      case 'oauth_not_configured':
        return 'OAuth is not properly configured for this platform. Please contact support.';
      case 'token_exchange_failed':
        return 'Failed to exchange authorization code for access token. Please try again.';
      case 'profile_fetch_failed':
        return 'Failed to fetch your profile information. Please try again.';
      case 'database_error':
        return 'Failed to save your account information. Please try again or contact support.';
      case 'user_not_found':
        return 'User account not found. Please ensure you are logged in before connecting social accounts.';
      case 'callback_error':
        return 'An error occurred during the authorization process. Please try again.';
      default:
        return 'An unexpected error occurred during account connection. Please try again.';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {/* Error Icon */}
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>

            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Connection Failed
            </h2>

            <p className="mt-2 text-sm text-gray-600">
              {getErrorMessage(error)}
            </p>

            {description && (
              <div className="mt-4 p-3 bg-red-50 rounded-md">
                <p className="text-sm text-red-800">
                  <strong>Details:</strong> {description}
                </p>
              </div>
            )}

            {details && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <p className="text-xs text-gray-600">
                  <strong>Technical Details:</strong> {details}
                </p>
              </div>
            )}

            <div className="mt-6 space-y-3">
              <button
                onClick={() => window.history.back()}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Try Again
              </button>

              <button
                onClick={() => window.close()}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Close Window
              </button>

              <a
                href="/social"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Go to Social Accounts
              </a>
            </div>

            <div className="mt-6 text-xs text-gray-500">
              <p>Error Code: {error}</p>
              <p className="mt-1">If this problem persists, please contact support.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
              <p className="mt-4 text-sm text-gray-600">Loading...</p>
            </div>
          </div>
        </div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}
