import { SchedulerIntegration } from './scheduler-integration';
import { SchedulerLogger } from './scheduler-logger';

/**
 * Background service that manages the post scheduler
 * This service ensures posts are processed even if the main application is idle
 */
export class BackgroundSchedulerService {
  private static instance: BackgroundSchedulerService | null = null;
  private schedulerIntegration: SchedulerIntegration;
  private logger: SchedulerLogger;
  private isRunning: boolean = false;
  private processingInterval: NodeJS.Timeout | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.schedulerIntegration = new SchedulerIntegration();
    this.logger = new SchedulerLogger('background-service');
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): BackgroundSchedulerService {
    if (!BackgroundSchedulerService.instance) {
      BackgroundSchedulerService.instance = new BackgroundSchedulerService();
    }
    return BackgroundSchedulerService.instance;
  }

  /**
   * Start the background service
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Background service is already running');
      return;
    }

    try {
      this.logger.info('Starting background scheduler service...');

      // Initialize the scheduler integration
      await this.schedulerIntegration.initialize();

      // Start processing scheduled posts every 30 seconds
      this.processingInterval = setInterval(async () => {
        await this.processScheduledPosts();
      }, 30000); // 30 seconds

      // Start health check every 5 minutes
      this.healthCheckInterval = setInterval(async () => {
        await this.performHealthCheck();
      }, 300000); // 5 minutes

      this.isRunning = true;
      this.logger.info('Background scheduler service started successfully');

      // Perform initial health check
      await this.performHealthCheck();

    } catch (error) {
      this.logger.error('Failed to start background service', error);
      throw error;
    }
  }

  /**
   * Stop the background service
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Background service is not running');
      return;
    }

    try {
      this.logger.info('Stopping background scheduler service...');

      // Clear intervals
      if (this.processingInterval) {
        clearInterval(this.processingInterval);
        this.processingInterval = null;
      }

      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      // Shutdown scheduler integration
      await this.schedulerIntegration.shutdown();

      this.isRunning = false;
      this.logger.info('Background scheduler service stopped successfully');

    } catch (error) {
      this.logger.error('Error stopping background service', error);
      throw error;
    }
  }

  /**
   * Process scheduled posts by calling the API endpoint
   */
  private async processScheduledPosts(): Promise<void> {
    try {
      // Call the internal processing API
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/scheduler/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Processing API returned ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.processed > 0) {
        this.logger.info('Processed scheduled posts', {
          processed: result.processed,
          successful: result.successful,
          failed: result.failed,
        });
      }

    } catch (error) {
      this.logger.error('Error processing scheduled posts', error);
    }
  }

  /**
   * Perform health check on the scheduler system
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const status = this.schedulerIntegration.getStatus();
      
      if (!status.isInitialized) {
        this.logger.warn('Scheduler integration is not initialized, attempting to restart...');
        await this.schedulerIntegration.initialize();
      }

      // Log health status
      this.logger.info('Health check completed', {
        isInitialized: status.isInitialized,
        schedulerStatus: status.schedulerStatus,
        queueStatus: status.queueStatus,
      });

    } catch (error) {
      this.logger.error('Health check failed', error);
      
      // Attempt to restart the scheduler integration
      try {
        await this.schedulerIntegration.shutdown();
        await this.schedulerIntegration.initialize();
        this.logger.info('Scheduler integration restarted after health check failure');
      } catch (restartError) {
        this.logger.error('Failed to restart scheduler integration', restartError);
      }
    }
  }

  /**
   * Get service status
   */
  public getStatus(): {
    isRunning: boolean;
    schedulerStatus: any;
    uptime: number;
  } {
    return {
      isRunning: this.isRunning,
      schedulerStatus: this.schedulerIntegration.getStatus(),
      uptime: this.isRunning ? Date.now() : 0,
    };
  }

  /**
   * Manually trigger post processing
   */
  public async triggerProcessing(): Promise<void> {
    if (!this.isRunning) {
      throw new Error('Background service is not running');
    }

    await this.processScheduledPosts();
  }

  /**
   * Get job statistics
   */
  public async getJobStatistics(): Promise<any> {
    return await this.schedulerIntegration.getJobStatistics();
  }
}

// Export singleton instance
export const backgroundScheduler = BackgroundSchedulerService.getInstance();

// Auto-start in production environment
if (process.env.NODE_ENV === 'production' && typeof window === 'undefined') {
  // Only start in server-side environment
  backgroundScheduler.start().catch(error => {
    console.error('Failed to auto-start background scheduler:', error);
  });
}
