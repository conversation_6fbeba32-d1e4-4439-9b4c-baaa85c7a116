import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Analyze Facebook token issues
 * GET /api/test/facebook-token-analysis
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Analyzing Facebook token issues...');

    const results = {
      timestamp: new Date().toISOString(),
      analysis: {} as any
    };

    // Get Facebook accounts with detailed token information
    try {
      console.log('Retrieving Facebook accounts with token details...');
      const supabase = createServiceRoleClient();
      const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
      
      const { data: facebookAccounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'FACEBOOK');

      if (error || !facebookAccounts?.length) {
        throw new Error('No Facebook accounts found');
      }

      results.analysis.accounts = [];

      for (const account of facebookAccounts) {
        const tokenAnalysis = {
          accountName: account.account_name,
          accountId: account.account_id,
          createdAt: account.created_at,
          expiresAt: account.expires_at,
          hasAccessToken: !!account.access_token,
          tokenLength: account.access_token?.length || 0,
          tokenPrefix: account.access_token?.substring(0, 20) + '...' || 'N/A',
          tokenFormat: 'unknown',
          isExpired: false,
          daysUntilExpiry: null,
          tokenType: 'unknown'
        };

        // Analyze token format
        if (account.access_token) {
          // Facebook tokens typically start with specific patterns
          if (account.access_token.startsWith('EAA')) {
            tokenAnalysis.tokenFormat = 'Facebook App Token (EAA...)';
            tokenAnalysis.tokenType = 'app_token';
          } else if (account.access_token.startsWith('EAAG')) {
            tokenAnalysis.tokenFormat = 'Facebook User Token (EAAG...)';
            tokenAnalysis.tokenType = 'user_token';
          } else if (account.access_token.startsWith('EAAP')) {
            tokenAnalysis.tokenFormat = 'Facebook Page Token (EAAP...)';
            tokenAnalysis.tokenType = 'page_token';
          } else {
            tokenAnalysis.tokenFormat = 'Unknown Facebook Token Format';
            tokenAnalysis.tokenType = 'unknown';
          }

          // Check expiry
          if (account.expires_at) {
            const expiryDate = new Date(account.expires_at);
            const now = new Date();
            tokenAnalysis.isExpired = expiryDate < now;
            tokenAnalysis.daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
          }
        }

        // Test token validity with Facebook's debug endpoint
        if (account.access_token) {
          try {
            console.log(`Testing token validity for ${account.account_name}...`);
            
            // Use Facebook's debug_token endpoint to check token validity
            const debugResponse = await fetch(`https://graph.facebook.com/v19.0/debug_token?input_token=${account.access_token}&access_token=${account.access_token}`);
            
            if (debugResponse.ok) {
              const debugData = await debugResponse.json();
              tokenAnalysis.debugInfo = {
                isValid: debugData.data?.is_valid || false,
                appId: debugData.data?.app_id,
                userId: debugData.data?.user_id,
                expiresAt: debugData.data?.expires_at,
                scopes: debugData.data?.scopes || [],
                type: debugData.data?.type
              };
            } else {
              const errorText = await debugResponse.text();
              tokenAnalysis.debugInfo = {
                error: `Debug API error: ${debugResponse.status} - ${errorText}`,
                isValid: false
              };
            }
          } catch (debugError: any) {
            tokenAnalysis.debugInfo = {
              error: debugError.message,
              isValid: false
            };
          }
        }

        results.analysis.accounts.push(tokenAnalysis);
      }

      // Generate recommendations
      results.analysis.recommendations = [];

      for (const account of results.analysis.accounts) {
        if (!account.hasAccessToken) {
          results.analysis.recommendations.push({
            account: account.accountName,
            issue: 'No access token',
            solution: 'Re-authenticate the Facebook account'
          });
        } else if (account.isExpired) {
          results.analysis.recommendations.push({
            account: account.accountName,
            issue: 'Token expired',
            solution: 'Refresh the Facebook access token'
          });
        } else if (account.debugInfo && !account.debugInfo.isValid) {
          results.analysis.recommendations.push({
            account: account.accountName,
            issue: 'Invalid token',
            solution: 'Re-authenticate the Facebook account or check app permissions'
          });
        } else if (account.tokenType === 'unknown') {
          results.analysis.recommendations.push({
            account: account.accountName,
            issue: 'Unknown token format',
            solution: 'Verify token format and re-authenticate if necessary'
          });
        }
      }

      // Overall assessment
      const validTokens = results.analysis.accounts.filter(acc => 
        acc.hasAccessToken && 
        !acc.isExpired && 
        acc.debugInfo?.isValid !== false
      ).length;

      results.analysis.summary = {
        totalAccounts: results.analysis.accounts.length,
        accountsWithTokens: results.analysis.accounts.filter(acc => acc.hasAccessToken).length,
        validTokens,
        expiredTokens: results.analysis.accounts.filter(acc => acc.isExpired).length,
        needsReauthentication: results.analysis.accounts.length - validTokens,
        overallStatus: validTokens > 0 ? 'PARTIAL_WORKING' : 'NEEDS_REAUTHENTICATION'
      };

      console.log('Facebook token analysis completed:', results.analysis.summary);

      return NextResponse.json({
        success: true,
        message: '🔍 Facebook token analysis completed',
        results,
        nextSteps: validTokens > 0 ? 
          'Some Facebook accounts are working. Test posting with valid accounts.' :
          'All Facebook accounts need re-authentication. Please re-connect Facebook accounts.'
      });

    } catch (error: any) {
      throw error;
    }

  } catch (error) {
    console.error('❌ Facebook token analysis error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Facebook token analysis failed'
    }, { status: 500 });
  }
}
