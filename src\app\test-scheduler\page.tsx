'use client';

import { useState, useEffect } from 'react';

export default function TestSchedulerPage() {
  const [schedulerStatus, setSchedulerStatus] = useState(null);
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [testResults, setTestResults] = useState({});

  useEffect(() => {
    testSchedulerSystem();
  }, []);

  const testSchedulerSystem = async () => {
    try {
      console.log('Testing scheduler system...');
      setLoading(true);
      setError('');

      const results = {};

      // Test 1: Check scheduler status
      try {
        const statusResponse = await fetch('/api/scheduler/status');
        const statusData = await statusResponse.json();
        results.schedulerStatus = statusData.success ? '✅ Working' : '❌ Failed';
        setSchedulerStatus(statusData);
      } catch (err) {
        results.schedulerStatus = '❌ API Error';
        console.error('Scheduler status test failed:', err);
      }

      // Test 2: Check job queue
      try {
        const jobsResponse = await fetch('/api/scheduler/jobs');
        const jobsData = await jobsResponse.json();
        results.jobQueue = jobsData.success ? `✅ Working (${jobsData.jobs?.length || 0} jobs)` : '❌ Failed';
        setJobs(jobsData.jobs || []);
      } catch (err) {
        results.jobQueue = '❌ API Error';
        console.error('Job queue test failed:', err);
      }

      // Test 3: Test post scheduling
      try {
        const testPost = {
          content: 'Test scheduled post from eWasl scheduler system 🚀',
          scheduledAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes from now
          platforms: ['linkedin'],
        };

        const scheduleResponse = await fetch('/api/posts/schedule', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testPost),
        });

        const scheduleData = await scheduleResponse.json();
        results.postScheduling = scheduleData.success ? '✅ Working' : '❌ Failed';
      } catch (err) {
        results.postScheduling = '❌ API Error';
        console.error('Post scheduling test failed:', err);
      }

      // Test 4: Check database tables
      try {
        const tablesResponse = await fetch('/api/scheduler/health');
        const tablesData = await tablesResponse.json();
        results.databaseTables = tablesData.success ? '✅ All tables exist' : '❌ Missing tables';
      } catch (err) {
        results.databaseTables = '❌ API Error';
        console.error('Database tables test failed:', err);
      }

      setTestResults(results);

    } catch (err) {
      console.error('Scheduler test failed:', err);
      setError('Scheduler integration test failed: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const createTestJob = async () => {
    try {
      const response = await fetch('/api/scheduler/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobType: 'publish-post',
          jobData: {
            postId: 'test-' + Date.now(),
            content: 'Test job created at ' + new Date().toLocaleString(),
            platforms: ['linkedin'],
          },
          scheduledAt: new Date(Date.now() + 2 * 60 * 1000).toISOString(), // 2 minutes from now
          priority: 5,
        }),
      });

      const data = await response.json();
      if (data.success) {
        alert('Test job created successfully!');
        testSchedulerSystem(); // Refresh data
      } else {
        alert('Failed to create test job: ' + data.error);
      }
    } catch (err) {
      alert('Error creating test job: ' + err.message);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            ⏰ Background Post Scheduler Test
          </h1>
          <p className="text-xl text-gray-600">
            Testing eWasl's background post scheduling system
          </p>
        </div>

        {/* Test Results */}
        <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
          
          {loading && (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span>Testing scheduler system...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-red-700">
                <strong>Error:</strong> {error}
              </div>
            </div>
          )}

          {!loading && !error && Object.keys(testResults).length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Scheduler Status API:</span>
                <span className="font-mono">{testResults.schedulerStatus}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Job Queue System:</span>
                <span className="font-mono">{testResults.jobQueue}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Post Scheduling:</span>
                <span className="font-mono">{testResults.postScheduling}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Database Tables:</span>
                <span className="font-mono">{testResults.databaseTables}</span>
              </div>
            </div>
          )}
        </div>

        {/* Scheduler Status */}
        {schedulerStatus && (
          <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Scheduler Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900">Engine Status</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {schedulerStatus.status?.engine?.isRunning ? '🟢 Running' : '🔴 Stopped'}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900">Queue Manager</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {schedulerStatus.status?.queue?.isInitialized ? '🟢 Initialized' : '🔴 Not Initialized'}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-900">Active Jobs</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {schedulerStatus.status?.queue?.activeJobs || 0} jobs
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Job Queue */}
        <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Job Queue</h2>
            <button
              onClick={createTestJob}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Create Test Job
            </button>
          </div>
          
          {jobs.length === 0 ? (
            <p className="text-gray-500 text-center py-8">No jobs in queue</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Job Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Scheduled At
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Attempts
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {jobs.slice(0, 10).map((job) => (
                    <tr key={job.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {job.job_type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          job.status === 'completed' ? 'bg-green-100 text-green-800' :
                          job.status === 'failed' ? 'bg-red-100 text-red-800' :
                          job.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {job.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(job.scheduled_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {job.priority}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {job.attempts}/{job.max_attempts}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* System Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-6">
          <h3 className="font-medium text-blue-800 mb-2">Scheduler System Information</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>Engine:</strong> Background post scheduler with queue management</p>
            <p><strong>Processing:</strong> Every 30 seconds for due posts</p>
            <p><strong>Platforms:</strong> LinkedIn, Facebook, Instagram, Twitter/X</p>
            <p><strong>Features:</strong> Retry logic, priority queuing, recurring posts</p>
            <p><strong>Monitoring:</strong> Real-time status and job tracking</p>
            <p><strong>Database:</strong> Supabase with job queue and post tables</p>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/"
            className="text-blue-600 hover:text-blue-500 text-sm font-medium mr-4"
          >
            ← Back to Home
          </a>
          <a
            href="/test-stripe"
            className="text-blue-600 hover:text-blue-500 text-sm font-medium"
          >
            Test Stripe Integration →
          </a>
        </div>
      </div>
    </div>
  );
}
