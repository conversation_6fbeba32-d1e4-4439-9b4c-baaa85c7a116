"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export function MockEngagementChart() {
  const mockData = [
    { date: "01/05", likes: 120, comments: 85, shares: 32 },
    { date: "02/05", likes: 150, comments: 90, shares: 45 },
    { date: "03/05", likes: 200, comments: 110, shares: 60 },
    { date: "04/05", likes: 180, comments: 95, shares: 55 },
    { date: "05/05", likes: 250, comments: 130, shares: 70 },
    { date: "06/05", likes: 280, comments: 150, shares: 90 },
    { date: "07/05", likes: 300, comments: 170, shares: 100 },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>تفاعل المستخدمين</CardTitle>
        <CardDescription>
          تحليل تفاعل المستخدمين مع منشوراتك خلال الأسبوع الماضي
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="likes" className="space-y-4">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="likes">الإعجابات</TabsTrigger>
            <TabsTrigger value="comments">التعليقات</TabsTrigger>
            <TabsTrigger value="shares">المشاركات</TabsTrigger>
          </TabsList>
          <TabsContent value="likes" className="space-y-4">
            <div className="h-[300px] border rounded-md p-4">
              <div className="h-full flex flex-col justify-center items-center">
                <p className="text-lg font-medium">رسم بياني للإعجابات</p>
                <p className="text-muted-foreground">
                  (هذا عرض تجريبي - يرجى تثبيت مكتبة recharts للرسوم البيانية الكاملة)
                </p>
                <div className="mt-4 w-full">
                  <div className="flex justify-between">
                    {mockData.map((item, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div 
                          className="bg-primary w-8" 
                          style={{ height: `${item.likes / 3}px` }}
                        ></div>
                        <span className="text-xs mt-1">{item.date}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="comments" className="space-y-4">
            <div className="h-[300px] border rounded-md p-4">
              <div className="h-full flex flex-col justify-center items-center">
                <p className="text-lg font-medium">رسم بياني للتعليقات</p>
                <p className="text-muted-foreground">
                  (هذا عرض تجريبي - يرجى تثبيت مكتبة recharts للرسوم البيانية الكاملة)
                </p>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="shares" className="space-y-4">
            <div className="h-[300px] border rounded-md p-4">
              <div className="h-full flex flex-col justify-center items-center">
                <p className="text-lg font-medium">رسم بياني للمشاركات</p>
                <p className="text-muted-foreground">
                  (هذا عرض تجريبي - يرجى تثبيت مكتبة recharts للرسوم البيانية الكاملة)
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

export function MockPlatformComparison() {
  const mockData = [
    { platform: "تويتر", value: 35 },
    { platform: "فيسبوك", value: 25 },
    { platform: "انستغرام", value: 30 },
    { platform: "لينكد إن", value: 10 },
  ];

  const colors = ["#1DA1F2", "#4267B2", "#E1306C", "#0A66C2"];

  return (
    <Card>
      <CardHeader>
        <CardTitle>مقارنة المنصات</CardTitle>
        <CardDescription>
          توزيع التفاعل عبر منصات التواصل الاجتماعي المختلفة
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] flex items-center justify-center">
          <div className="relative w-48 h-48">
            {/* Simple mock pie chart */}
            <div className="absolute inset-0 rounded-full border-8 border-[#1DA1F2]" style={{ clipPath: 'polygon(50% 50%, 0 0, 35% 0, 50% 15%, 65% 0, 100% 0, 100% 35%, 85% 50%, 100% 65%, 100% 100%, 65% 100%, 50% 85%, 35% 100%, 0 100%, 0 65%, 15% 50%, 0 35%)' }}></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <p className="text-lg font-medium">مخطط دائري</p>
            </div>
          </div>
          <div className="ml-8">
            <div className="space-y-2">
              {mockData.map((item, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-4 h-4 mr-2" style={{ backgroundColor: colors[index] }}></div>
                  <span>{item.platform}: {item.value}%</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
