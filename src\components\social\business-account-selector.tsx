'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Building2, Users, Image, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface BusinessAccount {
  id: string;
  name: string;
  username?: string;
  category?: string;
  picture?: string;
  profileImage?: string;
  fanCount?: number;
  followersCount?: number;
  type: 'page' | 'business' | 'organization' | 'personal';
  accessToken?: string;
  pageId?: string;
  pageName?: string;
}

interface BusinessAccountSelectorProps {
  platform: string;
  accessToken: string;
  onAccountSelected: (account: BusinessAccount) => void;
  onCancel: () => void;
}

export function BusinessAccountSelector({
  platform,
  accessToken,
  onAccountSelected,
  onCancel
}: BusinessAccountSelectorProps) {
  const [accounts, setAccounts] = useState<BusinessAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null);

  useEffect(() => {
    fetchBusinessAccounts();
  }, [platform, accessToken]);

  const fetchBusinessAccounts = async () => {
    try {
      setLoading(true);
      console.log(`🏢 Fetching business accounts for ${platform}...`);

      const response = await fetch('/api/social/business-accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform,
          accessToken,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch business accounts');
      }

      console.log(`✅ Found ${data.accounts.length} business accounts`);
      setAccounts(data.accounts);

      if (data.accounts.length === 0) {
        toast.warning(`لم يتم العثور على حسابات تجارية لـ ${platform}`);
      }
    } catch (error: any) {
      console.error('💥 Business accounts fetch error:', error);
      toast.error(`خطأ في جلب الحسابات التجارية: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleAccountSelect = (account: BusinessAccount) => {
    setSelectedAccount(account.id);
    onAccountSelected(account);
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'FACEBOOK': return '📘';
      case 'INSTAGRAM': return '📷';
      case 'LINKEDIN': return '💼';
      case 'TWITTER': return '🐦';
      default: return '🔗';
    }
  };

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case 'FACEBOOK': return 'فيسبوك';
      case 'INSTAGRAM': return 'إنستغرام';
      case 'LINKEDIN': return 'لينكد إن';
      case 'TWITTER': return 'تويتر';
      default: return platform;
    }
  };

  const getAccountTypeLabel = (type: string) => {
    switch (type) {
      case 'page': return 'صفحة تجارية';
      case 'business': return 'حساب تجاري';
      case 'organization': return 'منظمة';
      case 'personal': return 'حساب شخصي';
      default: return type;
    }
  };

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'page': return 'bg-blue-100 text-blue-800';
      case 'business': return 'bg-green-100 text-green-800';
      case 'organization': return 'bg-purple-100 text-purple-800';
      case 'personal': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getPlatformIcon(platform)}
            اختيار الحساب التجاري - {getPlatformName(platform)}
          </CardTitle>
          <CardDescription>
            جاري البحث عن الحسابات التجارية المتاحة...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="mr-2">جاري التحميل...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getPlatformIcon(platform)}
          اختيار الحساب التجاري - {getPlatformName(platform)}
        </CardTitle>
        <CardDescription>
          اختر الحساب التجاري الذي تريد ربطه بمنصة eWasl
        </CardDescription>
      </CardHeader>
      <CardContent>
        {accounts.length === 0 ? (
          <div className="text-center py-8">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد حسابات تجارية
            </h3>
            <p className="text-gray-600 mb-4">
              لم يتم العثور على حسابات تجارية لهذا المنصة. تأكد من أن لديك صفحات أو حسابات تجارية مرتبطة بحسابك.
            </p>
            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={onCancel}>
                إلغاء
              </Button>
              <Button onClick={fetchBusinessAccounts}>
                إعادة المحاولة
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid gap-4">
              {accounts.map((account) => (
                <Card
                  key={account.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedAccount === account.id
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleAccountSelect(account)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                          {account.picture || account.profileImage ? (
                            <img
                              src={account.picture || account.profileImage}
                              alt={account.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Building2 className="h-6 w-6 text-gray-500" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium">{account.name}</h4>
                          {account.username && (
                            <p className="text-sm text-gray-600">@{account.username}</p>
                          )}
                          {account.category && (
                            <p className="text-sm text-gray-500">{account.category}</p>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={getAccountTypeColor(account.type)}>
                              {getAccountTypeLabel(account.type)}
                            </Badge>
                            {(account.fanCount || account.followersCount) && (
                              <div className="flex items-center gap-1 text-sm text-gray-500">
                                <Users className="h-3 w-3" />
                                {(account.fanCount || account.followersCount)?.toLocaleString()}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      {selectedAccount === account.id && (
                        <CheckCircle className="h-6 w-6 text-blue-500" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <div className="flex gap-2 justify-end pt-4 border-t">
              <Button variant="outline" onClick={onCancel}>
                إلغاء
              </Button>
              <Button 
                disabled={!selectedAccount}
                onClick={() => {
                  const account = accounts.find(a => a.id === selectedAccount);
                  if (account) {
                    handleAccountSelect(account);
                  }
                }}
              >
                ربط الحساب المحدد
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
