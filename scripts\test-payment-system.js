#!/usr/bin/env node

/**
 * Payment System Testing Script
 * Tests all payment-related functionality end-to-end
 */

const https = require('https');
const fs = require('fs');

// Configuration
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'test123456';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-Payment-Test/1.0',
        ...options.headers
      }
    };

    const req = https.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

// Test runner function
async function runTest(name, testFn) {
  console.log(`\n🧪 Running test: ${name}`);
  
  try {
    const result = await testFn();
    if (result.success) {
      console.log(`✅ PASSED: ${name}`);
      testResults.passed++;
      testResults.tests.push({ name, status: 'PASSED', details: result.details });
    } else {
      console.log(`❌ FAILED: ${name} - ${result.error}`);
      testResults.failed++;
      testResults.tests.push({ name, status: 'FAILED', error: result.error, details: result.details });
    }
  } catch (error) {
    console.log(`❌ ERROR: ${name} - ${error.message}`);
    testResults.failed++;
    testResults.tests.push({ name, status: 'ERROR', error: error.message });
  }
}

// Test 1: Health Check
async function testHealthCheck() {
  const response = await makeRequest(`${BASE_URL}/api/billing/health`);
  
  if (response.status !== 200) {
    return { success: false, error: `Health check failed with status ${response.status}` };
  }

  const health = response.data;
  if (health.status !== 'healthy' && health.status !== 'degraded') {
    return { 
      success: false, 
      error: `System unhealthy: ${health.status}`,
      details: health.checks 
    };
  }

  return { 
    success: true, 
    details: {
      status: health.status,
      stripe: health.checks.stripe.status,
      database: health.checks.database.status,
      webhook: health.checks.webhook.status
    }
  };
}

// Test 2: Stripe Configuration
async function testStripeConfiguration() {
  const response = await makeRequest(`${BASE_URL}/api/billing/health`);
  
  if (response.status !== 200) {
    return { success: false, error: 'Cannot access health endpoint' };
  }

  const stripeCheck = response.data.checks.stripe;
  if (stripeCheck.status !== 'healthy') {
    return { 
      success: false, 
      error: 'Stripe not properly configured',
      details: stripeCheck.details 
    };
  }

  // Check if live mode is enabled
  const isLiveMode = stripeCheck.details.live_mode;
  if (!isLiveMode) {
    console.log('⚠️  WARNING: Stripe is in test mode');
  }

  return { 
    success: true, 
    details: {
      live_mode: isLiveMode,
      charges_enabled: stripeCheck.details.charges_enabled,
      payouts_enabled: stripeCheck.details.payouts_enabled
    }
  };
}

// Test 3: Subscription Plans
async function testSubscriptionPlans() {
  const response = await makeRequest(`${BASE_URL}/api/billing/change-plan`);
  
  if (response.status !== 401) {
    return { success: false, error: 'Expected 401 for unauthenticated request' };
  }

  // Test with mock authentication (this would need actual auth token in real scenario)
  return { 
    success: true, 
    details: { message: 'Plan endpoint properly protected' }
  };
}

// Test 4: Usage Tracking
async function testUsageTracking() {
  // This test would require authentication, so we'll test the structure
  const response = await makeRequest(`${BASE_URL}/api/billing/subscription`);
  
  if (response.status !== 401) {
    return { success: false, error: 'Expected 401 for unauthenticated request' };
  }

  return { 
    success: true, 
    details: { message: 'Usage tracking endpoint properly protected' }
  };
}

// Test 5: Webhook Endpoint
async function testWebhookEndpoint() {
  const response = await makeRequest(`${BASE_URL}/api/stripe/webhook`, {
    method: 'POST',
    body: { test: 'invalid' }
  });
  
  // Should fail due to invalid signature
  if (response.status !== 400) {
    return { success: false, error: `Expected 400 for invalid webhook, got ${response.status}` };
  }

  return { 
    success: true, 
    details: { message: 'Webhook endpoint properly validates signatures' }
  };
}

// Test 6: Environment Variables
async function testEnvironmentVariables() {
  const requiredVars = [
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
    'STRIPE_WEBHOOK_SECRET'
  ];

  const response = await makeRequest(`${BASE_URL}/api/billing/health`);
  
  if (response.status !== 200) {
    return { success: false, error: 'Cannot access health endpoint' };
  }

  const envCheck = response.data.checks.environment;
  if (envCheck.status !== 'healthy') {
    return { 
      success: false, 
      error: 'Environment variables not properly configured',
      details: envCheck.details 
    };
  }

  return { 
    success: true, 
    details: {
      stripe_mode: envCheck.details.stripe_mode,
      required_vars_configured: envCheck.details.required_vars_configured
    }
  };
}

// Test 7: Database Schema
async function testDatabaseSchema() {
  const response = await makeRequest(`${BASE_URL}/api/billing/health`);
  
  if (response.status !== 200) {
    return { success: false, error: 'Cannot access health endpoint' };
  }

  const dbCheck = response.data.checks.database;
  if (dbCheck.status !== 'healthy') {
    return { 
      success: false, 
      error: 'Database not accessible',
      details: dbCheck.details 
    };
  }

  return { 
    success: true, 
    details: {
      connection: dbCheck.details.connection,
      read_access: dbCheck.details.read_access,
      write_access: dbCheck.details.write_access
    }
  };
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Payment System Tests');
  console.log(`📍 Testing against: ${BASE_URL}`);
  console.log('=' * 50);

  // Run all tests
  await runTest('Health Check', testHealthCheck);
  await runTest('Stripe Configuration', testStripeConfiguration);
  await runTest('Subscription Plans', testSubscriptionPlans);
  await runTest('Usage Tracking', testUsageTracking);
  await runTest('Webhook Endpoint', testWebhookEndpoint);
  await runTest('Environment Variables', testEnvironmentVariables);
  await runTest('Database Schema', testDatabaseSchema);

  // Print summary
  console.log('\n' + '=' * 50);
  console.log('📊 TEST SUMMARY');
  console.log('=' * 50);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);

  // Print detailed results
  console.log('\n📋 DETAILED RESULTS:');
  testResults.tests.forEach(test => {
    const status = test.status === 'PASSED' ? '✅' : '❌';
    console.log(`${status} ${test.name}`);
    if (test.error) {
      console.log(`   Error: ${test.error}`);
    }
    if (test.details) {
      console.log(`   Details: ${JSON.stringify(test.details, null, 2)}`);
    }
  });

  // Save results to file
  const resultsFile = 'payment-system-test-results.json';
  fs.writeFileSync(resultsFile, JSON.stringify({
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL,
    summary: {
      passed: testResults.passed,
      failed: testResults.failed,
      total: testResults.passed + testResults.failed,
      successRate: Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)
    },
    tests: testResults.tests
  }, null, 2));

  console.log(`\n💾 Results saved to: ${resultsFile}`);

  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests, makeRequest };
