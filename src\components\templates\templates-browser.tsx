'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  Star,
  Eye,
  Copy,
  Download,
  Heart,
  TrendingUp,
  Clock,
  Tag,
  Users,
  Zap,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { toast } from 'sonner';

interface TemplateVariable {
  name: string;
  type: string;
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: string[];
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  platform_compatibility: string[];
  language: string;
  content_body: string;
  variables: TemplateVariable[];
  hashtags: string[];
  tone: string;
  industry_tags: string[];
  usage_count: number;
  rating: number;
  is_featured: boolean;
  created_at: string;
  template_categories?: {
    name: string;
    name_ar: string;
    icon: string;
  };
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  icon: string;
  sort_order: number;
}

interface TemplatesBrowserProps {
  onTemplateSelect?: (template: Template, processedContent: string) => void;
  selectedPlatform?: string;
  language?: string;
}

export function TemplatesBrowser({
  onTemplateSelect,
  selectedPlatform = '',
  language = 'ar'
}: TemplatesBrowserProps) {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTone, setSelectedTone] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('created_at');
  const [showFeatured, setShowFeatured] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});
  const [previewContent, setPreviewContent] = useState('');
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 12,
    offset: 0,
    hasMore: false
  });

  const tones = [
    { value: 'professional', label: 'مهني' },
    { value: 'casual', label: 'عادي' },
    { value: 'friendly', label: 'ودود' },
    { value: 'formal', label: 'رسمي' },
    { value: 'humorous', label: 'فكاهي' },
    { value: 'inspiring', label: 'ملهم' },
    { value: 'exciting', label: 'مثير' }
  ];

  const industries = [
    { value: 'technology', label: 'تقنية' },
    { value: 'retail', label: 'تجارة تجزئة' },
    { value: 'ecommerce', label: 'تجارة إلكترونية' },
    { value: 'education', label: 'تعليم' },
    { value: 'healthcare', label: 'رعاية صحية' },
    { value: 'finance', label: 'مالية' },
    { value: 'consulting', label: 'استشارات' },
    { value: 'services', label: 'خدمات' }
  ];

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch('/api/templates/categories');
      const result = await response.json();

      if (result.success) {
        setCategories(result.categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  const fetchTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        language,
        limit: pagination.limit.toString(),
        offset: pagination.offset.toString(),
        sortBy,
        sortOrder: 'desc'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory) params.append('category', selectedCategory);
      if (selectedPlatform) params.append('platform', selectedPlatform);
      if (selectedTone) params.append('tone', selectedTone);
      if (selectedIndustry) params.append('industry', selectedIndustry);
      if (showFeatured) params.append('featured', 'true');

      const response = await fetch(`/api/templates?${params}`);
      const result = await response.json();

      if (result.success) {
        if (pagination.offset === 0) {
          setTemplates(result.templates);
        } else {
          setTemplates(prev => [...prev, ...result.templates]);
        }
        setPagination(prev => ({
          ...prev,
          total: result.pagination.total,
          hasMore: result.pagination.hasMore
        }));
      } else {
        toast.error('فشل في تحميل القوالب');
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('خطأ في تحميل القوالب');
    } finally {
      setLoading(false);
    }
  }, [
    language, pagination.limit, pagination.offset, sortBy, searchTerm,
    selectedCategory, selectedPlatform, selectedTone, selectedIndustry, showFeatured
  ]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    setPagination(prev => ({ ...prev, offset: 0 }));
  }, [searchTerm, selectedCategory, selectedTone, selectedIndustry, showFeatured, sortBy]);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleLoadMore = () => {
    setPagination(prev => ({
      ...prev,
      offset: prev.offset + prev.limit
    }));
  };

  const handleTemplatePreview = (template: Template) => {
    setSelectedTemplate(template);
    setTemplateVariables({});
    setPreviewContent(template.content_body);
  };

  const updatePreview = useCallback(() => {
    if (!selectedTemplate) return;

    let content = selectedTemplate.content_body;
    Object.entries(templateVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      content = content.replace(regex, value || `{{${key}}}`);
    });
    setPreviewContent(content);
  }, [selectedTemplate, templateVariables]);

  useEffect(() => {
    updatePreview();
  }, [updatePreview]);

  const handleUseTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      const response = await fetch('/api/templates/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: selectedTemplate.id,
          variables: templateVariables,
          trackUsage: true
        })
      });

      const result = await response.json();

      if (result.success) {
        onTemplateSelect?.(selectedTemplate, result.content);
        setSelectedTemplate(null);
        toast.success('تم تطبيق القالب بنجاح');
      } else {
        toast.error(result.error || 'فشل في تطبيق القالب');
      }
    } catch (error) {
      console.error('Error processing template:', error);
      toast.error('خطأ في تطبيق القالب');
    }
  };

  const copyTemplate = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('تم نسخ القالب');
  };

  const getPlatformIcon = (platform: string) => {
    const icons: Record<string, string> = {
      facebook: '📘',
      instagram: '📷',
      twitter: '🐦',
      linkedin: '💼',
      snapchat: '👻'
    };
    return icons[platform] || '📱';
  };

  const getToneLabel = (tone: string) => {
    return tones.find(t => t.value === tone)?.label || tone;
  };

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">مكتبة القوالب</h2>
          <p className="text-gray-600">اختر من مجموعة واسعة من القوالب الجاهزة</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="البحث في القوالب..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pr-10"
              />
            </div>

            {/* Category Filter */}
            <select
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option value="">جميع الفئات</option>
              {categories.map((category) => (
                <option key={category.id} value={category.name}>
                  {category.name_ar}
                </option>
              ))}
            </select>

            {/* Tone Filter */}
            <select
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              value={selectedTone}
              onChange={(e) => setSelectedTone(e.target.value)}
            >
              <option value="">جميع النبرات</option>
              {tones.map((tone) => (
                <option key={tone.value} value={tone.value}>
                  {tone.label}
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
            >
              <option value="created_at">الأحدث</option>
              <option value="usage_count">الأكثر استخداماً</option>
              <option value="rating">الأعلى تقييماً</option>
              <option value="name">الاسم</option>
            </select>
          </div>

          <div className="flex items-center gap-4 mt-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={showFeatured}
                onChange={(e) => setShowFeatured(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">القوالب المميزة فقط</span>
            </label>

            <div className="text-sm text-gray-600">
              {pagination.total} قالب
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid/List */}
      {loading && pagination.offset === 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
            : "space-y-4"
          }>
            {templates.map((template) => (
              <Card key={template.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription className="mt-1">
                        {template.description}
                      </CardDescription>
                    </div>
                    {template.is_featured && (
                      <Badge variant="secondary" className="mr-2">
                        <Star className="h-3 w-3 mr-1" />
                        مميز
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Category and Tone */}
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Badge variant="outline">
                        {template.template_categories?.name_ar || template.category}
                      </Badge>
                      <Badge variant="outline">
                        {getToneLabel(template.tone)}
                      </Badge>
                    </div>

                    {/* Platforms */}
                    <div className="flex items-center gap-1">
                      {template.platform_compatibility.map((platform) => (
                        <span key={platform} className="text-lg" title={platform}>
                          {getPlatformIcon(platform)}
                        </span>
                      ))}
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-3">
                        <span className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {template.usage_count}
                        </span>
                        <span className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {template.rating.toFixed(1)}
                        </span>
                      </div>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {new Date(template.created_at).toLocaleDateString('ar')}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTemplatePreview(template)}
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        معاينة
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyTemplate(template.content_body)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          {pagination.hasMore && (
            <div className="text-center">
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={loading}
              >
                {loading ? 'جاري التحميل...' : 'تحميل المزيد'}
              </Button>
            </div>
          )}
        </>
      )}

      {/* Template Preview Dialog */}
      {selectedTemplate && (
        <Dialog open={!!selectedTemplate} onOpenChange={() => setSelectedTemplate(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {selectedTemplate.name}
                {selectedTemplate.is_featured && (
                  <Badge variant="secondary">
                    <Star className="h-3 w-3 mr-1" />
                    مميز
                  </Badge>
                )}
              </DialogTitle>
              <DialogDescription>
                {selectedTemplate.description}
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Variables Input */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">متغيرات القالب</h3>

                {selectedTemplate.variables.length > 0 ? (
                  <div className="space-y-4">
                    {selectedTemplate.variables.map((variable) => (
                      <div key={variable.name}>
                        <Label className="flex items-center gap-1">
                          {variable.label}
                          {variable.required && <span className="text-red-500">*</span>}
                        </Label>

                        {variable.type === 'textarea' ? (
                          <textarea
                            className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                            placeholder={variable.placeholder}
                            value={templateVariables[variable.name] || ''}
                            onChange={(e) => setTemplateVariables(prev => ({
                              ...prev,
                              [variable.name]: e.target.value
                            }))}
                          />
                        ) : variable.type === 'select' ? (
                          <select
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                            value={templateVariables[variable.name] || ''}
                            onChange={(e) => setTemplateVariables(prev => ({
                              ...prev,
                              [variable.name]: e.target.value
                            }))}
                          >
                            <option value="">{variable.placeholder}</option>
                            {variable.options?.map((option) => (
                              <option key={option} value={option}>
                                {option}
                              </option>
                            ))}
                          </select>
                        ) : (
                          <Input
                            type={variable.type}
                            placeholder={variable.placeholder}
                            value={templateVariables[variable.name] || ''}
                            onChange={(e) => setTemplateVariables(prev => ({
                              ...prev,
                              [variable.name]: e.target.value
                            }))}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">هذا القالب لا يحتوي على متغيرات قابلة للتخصيص</p>
                )}

                {/* Template Info */}
                <div className="space-y-3 pt-4 border-t">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      {selectedTemplate.template_categories?.name_ar || selectedTemplate.category}
                    </Badge>
                    <Badge variant="outline">
                      {getToneLabel(selectedTemplate.tone)}
                    </Badge>
                  </div>

                  <div className="flex items-center gap-1">
                    <span className="text-sm text-gray-600 ml-2">المنصات:</span>
                    {selectedTemplate.platform_compatibility.map((platform) => (
                      <span key={platform} className="text-lg" title={platform}>
                        {getPlatformIcon(platform)}
                      </span>
                    ))}
                  </div>

                  {selectedTemplate.hashtags.length > 0 && (
                    <div>
                      <span className="text-sm text-gray-600 block mb-1">الهاشتاقات:</span>
                      <div className="flex flex-wrap gap-1">
                        {selectedTemplate.hashtags.map((hashtag) => (
                          <Badge key={hashtag} variant="secondary" className="text-xs">
                            #{hashtag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Preview */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">معاينة المحتوى</h3>

                <div className="border rounded-lg p-4 bg-gray-50 min-h-[200px]">
                  <pre className="whitespace-pre-wrap text-sm font-sans" dir="rtl">
                    {previewContent}
                  </pre>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleUseTemplate} className="flex-1">
                    <Download className="h-4 w-4 mr-2" />
                    استخدام القالب
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => copyTemplate(previewContent)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    نسخ
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
