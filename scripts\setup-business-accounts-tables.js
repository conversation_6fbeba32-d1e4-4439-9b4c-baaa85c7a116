#!/usr/bin/env node

/**
 * Setup Business Accounts Tables
 * Creates database tables for Facebook Pages, LinkedIn Companies, and business account management
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupBusinessAccountsTables() {
  console.log('🏢 Setting up Business Accounts Tables...');
  console.log('=' .repeat(60));
  console.log(`Supabase URL: ${supabaseUrl}`);
  console.log(`Started at: ${new Date().toISOString()}\n`);

  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', '002_business_accounts_tables.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration file loaded successfully');
    console.log(`File size: ${migrationSQL.length} characters\n`);

    // Execute the migration
    console.log('🚀 Executing business accounts migration...');
    
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }

    console.log('✅ Business accounts migration executed successfully\n');

    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    
    const tablesToVerify = [
      'facebook_pages',
      'linkedin_companies', 
      'instagram_business_accounts',
      'business_account_selections'
    ];

    for (const tableName of tablesToVerify) {
      try {
        const { data: tableData, error: tableError } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);

        if (tableError && !tableError.message.includes('relation') && !tableError.message.includes('does not exist')) {
          console.warn(`⚠️  Table ${tableName}: ${tableError.message}`);
        } else {
          console.log(`✅ Table ${tableName}: Created successfully`);
        }
      } catch (error) {
        console.warn(`⚠️  Table ${tableName}: Verification failed - ${error.message}`);
      }
    }

    // Verify social_accounts table updates
    console.log('\n🔍 Verifying social_accounts table updates...');
    
    try {
      const { data: socialAccountsData, error: socialAccountsError } = await supabase
        .from('social_accounts')
        .select('page_id, business_account_type, business_account_id, business_account_name, status')
        .limit(1);

      if (socialAccountsError && !socialAccountsError.message.includes('column') && !socialAccountsError.message.includes('does not exist')) {
        console.warn(`⚠️  social_accounts updates: ${socialAccountsError.message}`);
      } else {
        console.log('✅ social_accounts table: Updated with business account columns');
      }
    } catch (error) {
      console.warn(`⚠️  social_accounts verification failed: ${error.message}`);
    }

    // Test basic operations
    console.log('\n🧪 Testing basic table operations...');
    
    // Test facebook_pages table
    try {
      const testPageData = {
        social_account_id: 'test-social-account-id',
        page_id: 'test-page-id',
        page_name: 'Test Page',
        page_access_token: 'test-token',
        page_category: 'Business',
        fan_count: 0,
        permissions: ['MANAGE', 'CREATE_CONTENT']
      };

      const { data: insertData, error: insertError } = await supabase
        .from('facebook_pages')
        .insert(testPageData)
        .select()
        .single();

      if (insertError) {
        console.warn(`⚠️  facebook_pages insert test: ${insertError.message}`);
      } else {
        console.log('✅ facebook_pages: Insert operation working');
        
        // Clean up test data
        await supabase
          .from('facebook_pages')
          .delete()
          .eq('id', insertData.id);
      }
    } catch (error) {
      console.warn(`⚠️  facebook_pages test failed: ${error.message}`);
    }

    console.log('\n📊 Business Accounts Tables Setup Summary:');
    console.log('=' .repeat(60));
    console.log('✅ facebook_pages table created');
    console.log('✅ linkedin_companies table created');
    console.log('✅ instagram_business_accounts table created');
    console.log('✅ business_account_selections table created');
    console.log('✅ social_accounts table updated with business account columns');
    console.log('✅ Indexes created for performance optimization');
    console.log('✅ RLS (Row Level Security) policies applied');
    console.log('✅ Updated_at triggers configured');

    console.log('\n🎯 Next Steps:');
    console.log('1. Test Facebook Pages integration with real OAuth flow');
    console.log('2. Implement LinkedIn Companies manager');
    console.log('3. Add Instagram Business Accounts integration');
    console.log('4. Test business account selection UI components');

    console.log('\n🏁 Business Accounts Tables Setup Complete!');
    console.log(`Finished at: ${new Date().toISOString()}`);

  } catch (error) {
    console.error('\n❌ Business Accounts Tables Setup Failed:');
    console.error(error.message);
    
    if (error.details) {
      console.error('Details:', error.details);
    }
    
    if (error.hint) {
      console.error('Hint:', error.hint);
    }

    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  setupBusinessAccountsTables();
}

module.exports = { setupBusinessAccountsTables };
