name: ewasl-social-scheduler
region: nyc
domains:
- domain: app.ewasl.com
  type: PRIMARY
services:
- name: ewasl-nextjs-app
  dockerfile_path: Dockerfile
  source_dir: /
  github:
    branch: main
    repo: TahaOsa/eWasl.com
    deploy_on_push: true
  http_port: 3000
  instance_count: 1
  instance_size_slug: basic-xxs
  routes:
  - path: /
  envs:
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    value: "postgresql://postgres.ajpcbugydftdyhlbddpl:<EMAIL>:5432/postgres"
  - key: NEXT_PUBLIC_SUPABASE_URL
    scope: RUN_AND_BUILD_TIME
    value: "https://ajpcbugydftdyhlbddpl.supabase.co"
  - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
    scope: RUN_AND_BUILD_TIME
    value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dHPGCdWDJV-BEeQTnNhSDcck8PnvRMYKCIo2xn49Yqg"
  - key: SUPABASE_SERVICE_ROLE_KEY
    scope: RUN_AND_BUILD_TIME
    value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc"
  - key: NEXTAUTH_SECRET
    scope: RUN_AND_BUILD_TIME
    value: "ewasl-nextauth-secret-2025-production"
  - key: NEXTAUTH_URL
    scope: RUN_AND_BUILD_TIME
    value: "https://app.ewasl.com"
  - key: OPENROUTER_API_KEY
    scope: RUN_AND_BUILD_TIME
    value: "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"
  - key: OPENROUTER_MODEL
    scope: RUN_AND_BUILD_TIME
    value: "qwen/qwen-4b:free"
  - key: OPENROUTER_REFERER
    scope: RUN_AND_BUILD_TIME
    value: "https://app.ewasl.com"
  - key: NODE_ENV
    scope: RUN_AND_BUILD_TIME
    value: "production"
  - key: NEXT_TELEMETRY_DISABLED
    scope: RUN_AND_BUILD_TIME
    value: "1"
  - key: NEXT_PUBLIC_APP_URL
    scope: RUN_AND_BUILD_TIME
    value: "https://app.ewasl.com"
  - key: NEXT_PUBLIC_APP_VERSION
    scope: RUN_AND_BUILD_TIME
    value: "1.0.0"
  - key: NEXT_PUBLIC_API_URL
    scope: RUN_AND_BUILD_TIME
    value: "https://app.ewasl.com/api"