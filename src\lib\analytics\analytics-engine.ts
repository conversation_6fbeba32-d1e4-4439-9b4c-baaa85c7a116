import { createClient } from '@/lib/supabase/server';
import { SchedulerLogger } from '../scheduler/scheduler-logger';
import { SocialMediaPublisher } from '../scheduler/social-media-publisher';

export interface PostMetrics {
  postId: string;
  platform: string;
  socialAccountId: string;
  platformPostId: string;
  
  // Engagement metrics
  likesCount: number;
  commentsCount: number;
  sharesCount: number;
  savesCount: number;
  clicksCount: number;
  
  // Reach and impressions
  impressions: number;
  reach: number;
  uniqueViews: number;
  
  // Calculated metrics
  engagementRate: number;
  clickThroughRate: number;
  saveRate: number;
  
  // Timing
  collectedAt: Date;
  postPublishedAt: Date;
  
  // Raw platform data
  rawData: Record<string, any>;
}

export interface AccountMetrics {
  socialAccountId: string;
  platform: string;
  
  // Follower metrics
  followersCount: number;
  followingCount: number;
  postsCount: number;
  
  // Engagement metrics
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  avgEngagementRate: number;
  
  // Growth metrics
  followersGrowth: number;
  followersGrowthRate: number;
  
  // Time period
  periodStart: Date;
  periodEnd: Date;
  periodType: 'daily' | 'weekly' | 'monthly';
  
  // Raw platform data
  rawData: Record<string, any>;
}

export interface AnalyticsSummary {
  totalPosts: number;
  totalEngagement: number;
  avgEngagementRate: number;
  totalReach: number;
  totalImpressions: number;
  bestPerformingPlatform: string;
  growthRate: number;
  topPerformingPosts: any[];
  platformBreakdown: Record<string, any>;
}

/**
 * Core analytics engine for collecting and processing social media metrics
 */
export class AnalyticsEngine {
  private logger: SchedulerLogger;
  private publisher: SocialMediaPublisher;

  constructor() {
    this.logger = new SchedulerLogger('analytics-engine');
    this.publisher = new SocialMediaPublisher();
  }

  /**
   * Collect analytics for a specific post
   */
  async collectPostAnalytics(postId: string, userId: string): Promise<PostMetrics[]> {
    try {
      this.logger.info('Collecting post analytics', { postId, userId });

      const supabase = createClient();

      // Get post details and published URLs
      const { data: post, error: postError } = await supabase
        .from('posts')
        .select(`
          id,
          content,
          platforms,
          published_urls,
          published_at,
          user_id
        `)
        .eq('id', postId)
        .eq('user_id', userId)
        .single();

      if (postError || !post) {
        throw new Error(`Post not found: ${postId}`);
      }

      if (!post.published_urls || Object.keys(post.published_urls).length === 0) {
        throw new Error('Post has not been published yet');
      }

      const metrics: PostMetrics[] = [];

      // Collect metrics from each platform
      for (const [platform, url] of Object.entries(post.published_urls)) {
        try {
          const platformMetrics = await this.collectPlatformMetrics(
            platform,
            url as string,
            post,
            userId
          );

          if (platformMetrics) {
            metrics.push(platformMetrics);
            
            // Store in database
            await this.storePostMetrics(platformMetrics);
          }

        } catch (error) {
          this.logger.error(`Failed to collect metrics for ${platform}`, error, { postId, platform });
        }
      }

      this.logger.info('Post analytics collection completed', {
        postId,
        platformsCollected: metrics.length,
      });

      return metrics;

    } catch (error) {
      this.logger.error('Failed to collect post analytics', error, { postId, userId });
      throw error;
    }
  }

  /**
   * Collect analytics for a specific platform
   */
  private async collectPlatformMetrics(
    platform: string,
    publishedUrl: string,
    post: any,
    userId: string
  ): Promise<PostMetrics | null> {
    try {
      const supabase = createClient();

      // Get social account for this platform
      const { data: socialAccount, error: accountError } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('platform', platform)
        .eq('is_active', true)
        .single();

      if (accountError || !socialAccount) {
        this.logger.warn(`No active social account found for ${platform}`, { userId, platform });
        return null;
      }

      // Extract platform post ID from URL
      const platformPostId = this.extractPostIdFromUrl(platform, publishedUrl);
      if (!platformPostId) {
        this.logger.warn(`Could not extract post ID from URL: ${publishedUrl}`);
        return null;
      }

      // Get platform-specific publisher
      const publisher = this.publisher.getPublisher(platform);
      if (!publisher || typeof publisher.getPostAnalytics !== 'function') {
        this.logger.warn(`Analytics not supported for platform: ${platform}`);
        return null;
      }

      // Fetch analytics from platform
      const rawAnalytics = await publisher.getPostAnalytics(socialAccount, platformPostId);

      // Convert to standardized format
      const metrics: PostMetrics = {
        postId: post.id,
        platform,
        socialAccountId: socialAccount.id,
        platformPostId,
        
        // Extract engagement metrics (platform-specific)
        likesCount: this.extractMetric(rawAnalytics, 'likes', platform),
        commentsCount: this.extractMetric(rawAnalytics, 'comments', platform),
        sharesCount: this.extractMetric(rawAnalytics, 'shares', platform),
        savesCount: this.extractMetric(rawAnalytics, 'saves', platform),
        clicksCount: this.extractMetric(rawAnalytics, 'clicks', platform),
        
        // Extract reach and impressions
        impressions: this.extractMetric(rawAnalytics, 'impressions', platform),
        reach: this.extractMetric(rawAnalytics, 'reach', platform),
        uniqueViews: this.extractMetric(rawAnalytics, 'views', platform),
        
        // Calculate derived metrics
        engagementRate: 0, // Will be calculated
        clickThroughRate: 0, // Will be calculated
        saveRate: 0, // Will be calculated
        
        collectedAt: new Date(),
        postPublishedAt: new Date(post.published_at),
        rawData: rawAnalytics,
      };

      // Calculate derived metrics
      metrics.engagementRate = this.calculateEngagementRate(metrics);
      metrics.clickThroughRate = this.calculateClickThroughRate(metrics);
      metrics.saveRate = this.calculateSaveRate(metrics);

      return metrics;

    } catch (error) {
      this.logger.error(`Failed to collect ${platform} metrics`, error);
      return null;
    }
  }

  /**
   * Extract metric value from platform-specific response
   */
  private extractMetric(rawData: any, metricType: string, platform: string): number {
    if (!rawData) return 0;

    switch (platform) {
      case 'TWITTER':
        switch (metricType) {
          case 'likes': return rawData.metrics?.like_count || 0;
          case 'comments': return rawData.metrics?.reply_count || 0;
          case 'shares': return rawData.metrics?.retweet_count || 0;
          case 'impressions': return rawData.metrics?.impression_count || 0;
          case 'clicks': return rawData.metrics?.url_link_clicks || 0;
          default: return 0;
        }

      case 'FACEBOOK':
        switch (metricType) {
          case 'likes': return rawData.metrics?.likes || 0;
          case 'comments': return rawData.metrics?.comments || 0;
          case 'shares': return rawData.metrics?.shares || 0;
          case 'reach': return rawData.metrics?.reach || 0;
          case 'impressions': return rawData.metrics?.impressions || 0;
          default: return 0;
        }

      case 'INSTAGRAM':
        switch (metricType) {
          case 'likes': return rawData.metrics?.likes || 0;
          case 'comments': return rawData.metrics?.comments || 0;
          case 'saves': return rawData.metrics?.saved || 0;
          case 'reach': return rawData.metrics?.reach || 0;
          case 'impressions': return rawData.metrics?.impressions || 0;
          default: return 0;
        }

      case 'LINKEDIN':
        switch (metricType) {
          case 'likes': return rawData.metrics?.likes || 0;
          case 'comments': return rawData.metrics?.comments || 0;
          case 'shares': return rawData.metrics?.shares || 0;
          case 'clicks': return rawData.metrics?.clicks || 0;
          case 'impressions': return rawData.metrics?.impressions || 0;
          default: return 0;
        }

      default:
        return 0;
    }
  }

  /**
   * Extract post ID from published URL
   */
  private extractPostIdFromUrl(platform: string, url: string): string | null {
    try {
      switch (platform) {
        case 'TWITTER':
          const twitterMatch = url.match(/status\/(\d+)/);
          return twitterMatch ? twitterMatch[1] : null;

        case 'FACEBOOK':
          const facebookMatch = url.match(/\/(\d+)$/);
          return facebookMatch ? facebookMatch[1] : null;

        case 'INSTAGRAM':
          const instagramMatch = url.match(/\/p\/([^\/]+)/);
          return instagramMatch ? instagramMatch[1] : null;

        case 'LINKEDIN':
          const linkedinMatch = url.match(/update\/(.*)/);
          return linkedinMatch ? linkedinMatch[1] : null;

        default:
          return null;
      }
    } catch (error) {
      this.logger.error('Failed to extract post ID from URL', error, { platform, url });
      return null;
    }
  }

  /**
   * Calculate engagement rate
   */
  private calculateEngagementRate(metrics: PostMetrics): number {
    if (metrics.impressions === 0) return 0;
    
    const totalEngagement = metrics.likesCount + metrics.commentsCount + metrics.sharesCount;
    return Math.round((totalEngagement / metrics.impressions) * 10000) / 100; // 2 decimal places
  }

  /**
   * Calculate click-through rate
   */
  private calculateClickThroughRate(metrics: PostMetrics): number {
    if (metrics.impressions === 0) return 0;
    
    return Math.round((metrics.clicksCount / metrics.impressions) * 10000) / 100; // 2 decimal places
  }

  /**
   * Calculate save rate
   */
  private calculateSaveRate(metrics: PostMetrics): number {
    if (metrics.impressions === 0) return 0;
    
    return Math.round((metrics.savesCount / metrics.impressions) * 10000) / 100; // 2 decimal places
  }

  /**
   * Store post metrics in database
   */
  private async storePostMetrics(metrics: PostMetrics): Promise<void> {
    try {
      const supabase = createClient();

      const { error } = await supabase
        .from('post_analytics')
        .upsert({
          post_id: metrics.postId,
          platform: metrics.platform,
          social_account_id: metrics.socialAccountId,
          platform_post_id: metrics.platformPostId,
          
          likes_count: metrics.likesCount,
          comments_count: metrics.commentsCount,
          shares_count: metrics.sharesCount,
          saves_count: metrics.savesCount,
          clicks_count: metrics.clicksCount,
          
          impressions: metrics.impressions,
          reach: metrics.reach,
          unique_views: metrics.uniqueViews,
          
          engagement_rate: metrics.engagementRate,
          click_through_rate: metrics.clickThroughRate,
          save_rate: metrics.saveRate,
          
          collected_at: metrics.collectedAt.toISOString(),
          post_published_at: metrics.postPublishedAt.toISOString(),
          raw_data: metrics.rawData,
        }, {
          onConflict: 'post_id,platform',
        });

      if (error) {
        throw error;
      }

      this.logger.info('Post metrics stored successfully', {
        postId: metrics.postId,
        platform: metrics.platform,
      });

    } catch (error) {
      this.logger.error('Failed to store post metrics', error, {
        postId: metrics.postId,
        platform: metrics.platform,
      });
      throw error;
    }
  }

  /**
   * Collect account-level analytics
   */
  async collectAccountAnalytics(
    socialAccountId: string,
    periodType: 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<AccountMetrics | null> {
    try {
      this.logger.info('Collecting account analytics', { socialAccountId, periodType });

      const supabase = createClient();

      // Get social account details
      const { data: socialAccount, error: accountError } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('id', socialAccountId)
        .single();

      if (accountError || !socialAccount) {
        throw new Error(`Social account not found: ${socialAccountId}`);
      }

      // Get platform-specific publisher
      const publisher = this.publisher.getPublisher(socialAccount.platform);
      if (!publisher || typeof publisher.getAccountInfo !== 'function') {
        this.logger.warn(`Account analytics not supported for platform: ${socialAccount.platform}`);
        return null;
      }

      // Fetch account info from platform
      const accountInfo = await publisher.getAccountInfo(socialAccount);

      // Calculate period dates
      const periodEnd = new Date();
      const periodStart = new Date();
      
      switch (periodType) {
        case 'daily':
          periodStart.setDate(periodStart.getDate() - 1);
          break;
        case 'weekly':
          periodStart.setDate(periodStart.getDate() - 7);
          break;
        case 'monthly':
          periodStart.setMonth(periodStart.getMonth() - 1);
          break;
      }

      // Get previous period data for growth calculation
      const { data: previousMetrics } = await supabase
        .from('account_analytics')
        .select('followers_count')
        .eq('social_account_id', socialAccountId)
        .eq('period_type', periodType)
        .order('period_end', { ascending: false })
        .limit(1);

      const previousFollowersCount = previousMetrics?.[0]?.followers_count || 0;
      const followersGrowth = accountInfo.followers_count - previousFollowersCount;
      const followersGrowthRate = previousFollowersCount > 0 
        ? Math.round((followersGrowth / previousFollowersCount) * 10000) / 100
        : 0;

      const metrics: AccountMetrics = {
        socialAccountId,
        platform: socialAccount.platform,
        
        followersCount: accountInfo.followers_count || 0,
        followingCount: accountInfo.following_count || accountInfo.follows_count || 0,
        postsCount: accountInfo.media_count || accountInfo.posts_count || 0,
        
        totalLikes: 0, // Would need to aggregate from posts
        totalComments: 0, // Would need to aggregate from posts
        totalShares: 0, // Would need to aggregate from posts
        avgEngagementRate: 0, // Would need to calculate from recent posts
        
        followersGrowth,
        followersGrowthRate,
        
        periodStart,
        periodEnd,
        periodType,
        
        rawData: accountInfo,
      };

      // Store in database
      await this.storeAccountMetrics(metrics);

      this.logger.info('Account analytics collected successfully', {
        socialAccountId,
        platform: socialAccount.platform,
        followersCount: metrics.followersCount,
      });

      return metrics;

    } catch (error) {
      this.logger.error('Failed to collect account analytics', error, { socialAccountId });
      return null;
    }
  }

  /**
   * Store account metrics in database
   */
  private async storeAccountMetrics(metrics: AccountMetrics): Promise<void> {
    try {
      const supabase = createClient();

      const { error } = await supabase
        .from('account_analytics')
        .upsert({
          social_account_id: metrics.socialAccountId,
          platform: metrics.platform,
          
          followers_count: metrics.followersCount,
          following_count: metrics.followingCount,
          posts_count: metrics.postsCount,
          
          total_likes: metrics.totalLikes,
          total_comments: metrics.totalComments,
          total_shares: metrics.totalShares,
          avg_engagement_rate: metrics.avgEngagementRate,
          
          followers_growth: metrics.followersGrowth,
          followers_growth_rate: metrics.followersGrowthRate,
          
          period_start: metrics.periodStart.toISOString(),
          period_end: metrics.periodEnd.toISOString(),
          period_type: metrics.periodType,
          
          raw_data: metrics.rawData,
        }, {
          onConflict: 'social_account_id,period_start,period_type',
        });

      if (error) {
        throw error;
      }

    } catch (error) {
      this.logger.error('Failed to store account metrics', error);
      throw error;
    }
  }

  /**
   * Get analytics summary for a user
   */
  async getAnalyticsSummary(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<AnalyticsSummary> {
    try {
      const supabase = createClient();

      // Use default date range if not provided (last 30 days)
      const defaultEndDate = endDate || new Date();
      const defaultStartDate = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      // Get summary using database function
      const { data: summaryData, error } = await supabase
        .rpc('get_user_analytics_summary', {
          p_user_id: userId,
          p_start_date: defaultStartDate.toISOString(),
          p_end_date: defaultEndDate.toISOString(),
        });

      if (error) {
        throw error;
      }

      const summary = summaryData?.[0] || {};

      // Get top performing posts
      const { data: topPosts } = await supabase
        .from('post_analytics')
        .select(`
          post_id,
          platform,
          engagement_rate,
          likes_count,
          comments_count,
          shares_count,
          posts (
            id,
            content,
            published_at
          )
        `)
        .eq('posts.user_id', userId)
        .gte('collected_at', defaultStartDate.toISOString())
        .lte('collected_at', defaultEndDate.toISOString())
        .order('engagement_rate', { ascending: false })
        .limit(5);

      // Get platform breakdown
      const { data: platformData } = await supabase
        .from('post_analytics')
        .select(`
          platform,
          engagement_rate,
          likes_count,
          comments_count,
          shares_count,
          impressions,
          reach
        `)
        .eq('posts.user_id', userId)
        .gte('collected_at', defaultStartDate.toISOString())
        .lte('collected_at', defaultEndDate.toISOString());

      const platformBreakdown: Record<string, any> = {};
      platformData?.forEach(item => {
        if (!platformBreakdown[item.platform]) {
          platformBreakdown[item.platform] = {
            posts: 0,
            totalEngagement: 0,
            totalImpressions: 0,
            totalReach: 0,
            avgEngagementRate: 0,
          };
        }

        const platform = platformBreakdown[item.platform];
        platform.posts++;
        platform.totalEngagement += (item.likes_count + item.comments_count + item.shares_count);
        platform.totalImpressions += item.impressions;
        platform.totalReach += item.reach;
        platform.avgEngagementRate = (platform.avgEngagementRate + item.engagement_rate) / platform.posts;
      });

      return {
        totalPosts: summary.total_posts || 0,
        totalEngagement: summary.total_engagement || 0,
        avgEngagementRate: summary.avg_engagement_rate || 0,
        totalReach: summary.total_reach || 0,
        totalImpressions: summary.total_impressions || 0,
        bestPerformingPlatform: summary.best_performing_platform || '',
        growthRate: summary.growth_rate || 0,
        topPerformingPosts: topPosts || [],
        platformBreakdown,
      };

    } catch (error) {
      this.logger.error('Failed to get analytics summary', error, { userId });
      throw error;
    }
  }
}
