'use client';

import { useState } from 'react';
import { getAuthorizationUrl } from '@/lib/social/oauth-config';

export default function TestLinkedInPage() {
  const [authUrl, setAuthUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateAuthUrl = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/social/oauth-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'linkedin',
          action: 'test-auth-url'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setAuthUrl(data.authUrl);
      } else {
        setError('Failed to generate authorization URL');
      }
    } catch (err) {
      setError('Error generating authorization URL');
    } finally {
      setLoading(false);
    }
  };

  const startOAuthFlow = () => {
    if (authUrl) {
      window.open(authUrl, '_blank', 'width=600,height=700');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              LinkedIn OAuth Test
            </h1>
            <p className="text-gray-600 mb-6">
              Test the LinkedIn OAuth integration
            </p>
          </div>

          <div className="space-y-4">
            <button
              onClick={generateAuthUrl}
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Generating...' : 'Generate Authorization URL'}
            </button>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Error
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      {error}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {authUrl && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      Authorization URL Generated
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p className="mb-2">LinkedIn OAuth URL is ready!</p>
                      <button
                        onClick={startOAuthFlow}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        Start LinkedIn OAuth
                      </button>
                    </div>
                    <div className="mt-3">
                      <details className="text-xs">
                        <summary className="cursor-pointer text-green-600 hover:text-green-800">
                          Show URL
                        </summary>
                        <div className="mt-2 p-2 bg-gray-100 rounded text-gray-800 break-all">
                          {authUrl}
                        </div>
                      </details>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="mt-8 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              OAuth Flow Steps
            </h3>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
              <li>Click "Generate Authorization URL" to create the LinkedIn OAuth URL</li>
              <li>Click "Start LinkedIn OAuth" to open LinkedIn authorization page</li>
              <li>Log in to LinkedIn and authorize the application</li>
              <li>LinkedIn will redirect back to our callback handler</li>
              <li>The callback handler will process the authorization code</li>
              <li>User profile and access token will be stored in the database</li>
            </ol>
          </div>

          <div className="mt-6 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Configuration Status
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Client ID:</span>
                <span className="text-green-600 font-medium">✓ Configured</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Client Secret:</span>
                <span className="text-green-600 font-medium">✓ Configured</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Redirect URI:</span>
                <span className="text-green-600 font-medium">✓ Configured</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Scopes:</span>
                <span className="text-green-600 font-medium">✓ 4 scopes</span>
              </div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <a
              href="/api/social/oauth-status"
              target="_blank"
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              View Full OAuth Status →
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
