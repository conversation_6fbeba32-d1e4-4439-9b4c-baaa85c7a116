'use client';

export default function RoutingTestPage() {
  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      textAlign: 'center',
      padding: '2rem'
    }}>
      <div>
        <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
          🎯 Routing Test Success!
        </h1>
        <p style={{ fontSize: '1.5rem', marginBottom: '2rem' }}>
          If you can see this page, Next.js routing is working correctly.
        </p>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          padding: '1rem',
          borderRadius: '0.5rem',
          marginBottom: '2rem'
        }}>
          <p><strong>Page:</strong> /routing-test</p>
          <p><strong>Time:</strong> {new Date().toLocaleString()}</p>
          <p><strong>Status:</strong> ✅ Accessible</p>
        </div>
        <button
          onClick={() => {
            fetch('/api/health')
              .then(r => r.json())
              .then(data => alert(`API Test: ${JSON.stringify(data, null, 2)}`))
              .catch(err => alert(`API Error: ${err.message}`));
          }}
          style={{
            padding: '1rem 2rem',
            background: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            fontSize: '1rem',
            cursor: 'pointer',
            marginRight: '1rem'
          }}
        >
          🧪 Test Health API
        </button>
        <a
          href="/dashboard"
          style={{
            display: 'inline-block',
            padding: '1rem 2rem',
            background: '#2196F3',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '0.5rem',
            fontSize: '1rem'
          }}
        >
          🏠 Back to Dashboard
        </a>
      </div>
    </div>
  );
}
