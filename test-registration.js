const { default: fetch } = require('node-fetch');

async function testRegistration() {
  console.log('Testing user registration API...');

  const testUser = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'TestPassword123',
    confirmPassword: 'TestPassword123'
  };

  try {
    const response = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });

    const data = await response.json();

    console.log('Response status:', response.status);
    console.log('Response data:', data);

    if (response.ok) {
      console.log('✅ Registration API working correctly!');
    } else {
      console.log('❌ Registration failed:', data.message || data.error);
    }
  } catch (error) {
    console.error('❌ Registration test failed:', error.message);
  }
}

testRegistration();
