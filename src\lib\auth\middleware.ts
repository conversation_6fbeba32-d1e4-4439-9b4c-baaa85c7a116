import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * Authentication middleware for API routes
 * Verifies that the user is authenticated before allowing access
 */
export async function withAuth(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  return async function(request: NextRequest) {
    try {
      // Verify user is authenticated with Supabase
      const supabase = createClient();
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Call the original handler with the user
      return await handler(request, user);
    } catch (error) {
      console.error('Authentication middleware error:', error);
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }
  };
}

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string; // Custom error message
}

/**
 * Simple in-memory rate limiter (for production, use Redis)
 */
class InMemoryRateLimiter {
  private requests: Map<string, { count: number; resetTime: number }> = new Map();

  isAllowed(key: string, config: RateLimitConfig): boolean {
    const now = Date.now();
    const record = this.requests.get(key);

    if (!record || now > record.resetTime) {
      // First request or window expired
      this.requests.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      });
      return true;
    }

    if (record.count >= config.maxRequests) {
      return false;
    }

    record.count++;
    return true;
  }

  cleanup() {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

const rateLimiter = new InMemoryRateLimiter();

// Cleanup expired entries every 5 minutes
setInterval(() => rateLimiter.cleanup(), 5 * 60 * 1000);

/**
 * Rate limiting middleware
 */
export function withRateLimit(config: RateLimitConfig) {
  return function(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function(request: NextRequest) {
      try {
        // Use IP address as the rate limit key
        const ip = request.headers.get('x-forwarded-for') ||
                  request.headers.get('x-real-ip') ||
                  'unknown';

        if (!rateLimiter.isAllowed(ip, config)) {
          return NextResponse.json(
            {
              error: config.message || 'Too many requests. Please try again later.',
              retryAfter: Math.ceil(config.windowMs / 1000)
            },
            {
              status: 429,
              headers: {
                'Retry-After': Math.ceil(config.windowMs / 1000).toString()
              }
            }
          );
        }

        return await handler(request);
      } catch (error) {
        console.error('Rate limiting middleware error:', error);
        return await handler(request); // Continue without rate limiting on error
      }
    };
  };
}

/**
 * Input validation middleware using Zod
 */
export function withValidation<T>(
  schema: any, // Zod schema
  handler: (request: NextRequest, data: T) => Promise<NextResponse>
) {
  return async function(request: NextRequest) {
    try {
      const body = await request.json();
      const validatedData = schema.parse(body);

      return await handler(request, validatedData);
    } catch (error: any) {
      if (error.name === 'ZodError') {
        return NextResponse.json(
          {
            error: 'Invalid input data',
            details: error.errors
          },
          { status: 400 }
        );
      }

      console.error('Validation middleware error:', error);
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }
  };
}

/**
 * Error handling middleware
 */
export function withErrorHandling(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async function(request: NextRequest) {
    try {
      return await handler(request);
    } catch (error: any) {
      console.error('API Error:', error);

      // Don't expose internal errors to clients
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Combine multiple middlewares
 */
export function compose(...middlewares: any[]) {
  return function(handler: any) {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler);
  };
}
