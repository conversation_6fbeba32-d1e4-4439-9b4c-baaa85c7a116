import { createClient } from '@/lib/supabase/server';
import { SchedulerEngine } from './scheduler-engine';
import { QueueManager } from './queue-manager';
import { SchedulerLogger } from './scheduler-logger';

/**
 * Integration service that connects the scheduler with the existing post system
 */
export class SchedulerIntegration {
  private schedulerEngine: SchedulerEngine;
  private queueManager: QueueManager;
  private logger: SchedulerLogger;
  private isInitialized: boolean = false;

  constructor() {
    this.schedulerEngine = new SchedulerEngine();
    this.queueManager = new QueueManager();
    this.logger = new SchedulerLogger('scheduler-integration');
  }

  /**
   * Initialize the scheduler integration
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('Scheduler integration already initialized');
      return;
    }

    try {
      this.logger.info('Initializing scheduler integration...');

      // Initialize components
      await this.queueManager.initialize();
      await this.schedulerEngine.start();

      // Set up post status monitoring
      await this.setupPostStatusMonitoring();

      // Set up recurring post generation
      await this.setupRecurringPostGeneration();

      this.isInitialized = true;
      this.logger.info('Scheduler integration initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize scheduler integration', error);
      throw error;
    }
  }

  /**
   * Shutdown the scheduler integration
   */
  async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      this.logger.info('Shutting down scheduler integration...');

      await this.schedulerEngine.stop();
      await this.queueManager.cleanup();

      this.isInitialized = false;
      this.logger.info('Scheduler integration shutdown completed');

    } catch (error) {
      this.logger.error('Error during scheduler integration shutdown', error);
    }
  }

  /**
   * Schedule a post for publishing
   */
  async schedulePost(postId: string, userId: string, scheduledAt: Date): Promise<void> {
    try {
      this.logger.info('Scheduling post for publishing', {
        postId,
        userId,
        scheduledAt: scheduledAt.toISOString(),
      });

      const supabase = createClient();

      // Get post details
      const { data: post, error: postError } = await supabase
        .from('posts')
        .select(`
          id,
          content,
          media_url,
          platforms,
          user_id,
          scheduled_at
        `)
        .eq('id', postId)
        .eq('user_id', userId)
        .single();

      if (postError || !post) {
        throw new Error(`Post not found: ${postId}`);
      }

      // Validate post data
      if (!post.platforms || post.platforms.length === 0) {
        throw new Error('Post must have at least one platform selected');
      }

      // Calculate delay until scheduled time
      const delay = scheduledAt.getTime() - Date.now();

      // Add job to queue
      await this.queueManager.addJob('publish-post', {
        postId: post.id,
        userId: post.user_id,
        content: post.content,
        mediaUrl: post.media_url,
        platforms: post.platforms,
        retryCount: 0,
        maxRetries: 3,
      }, {
        delay: Math.max(0, delay),
        priority: 5,
      });

      this.logger.info('Post scheduled successfully', {
        postId,
        scheduledAt: scheduledAt.toISOString(),
        delay,
      });

    } catch (error) {
      this.logger.error('Failed to schedule post', error, { postId, userId });
      throw error;
    }
  }

  /**
   * Cancel a scheduled post
   */
  async cancelScheduledPost(postId: string, userId: string): Promise<void> {
    try {
      this.logger.info('Cancelling scheduled post', { postId, userId });

      const supabase = createClient();

      // Update post status
      const { error: updateError } = await supabase
        .from('posts')
        .update({
          status: 'CANCELLED',
          updated_at: new Date().toISOString(),
        })
        .eq('id', postId)
        .eq('user_id', userId);

      if (updateError) {
        throw updateError;
      }

      // Cancel any pending jobs for this post
      const { data: jobs } = await supabase
        .from('job_queue')
        .select('id')
        .eq('job_type', 'publish-post')
        .contains('job_data', { postId })
        .in('status', ['pending', 'processing']);

      if (jobs && jobs.length > 0) {
        for (const job of jobs) {
          await supabase
            .from('job_queue')
            .update({
              status: 'cancelled',
              completed_at: new Date().toISOString(),
              error_message: 'Post cancelled by user',
            })
            .eq('id', job.id);
        }
      }

      this.logger.info('Post cancelled successfully', { postId, cancelledJobs: jobs?.length || 0 });

    } catch (error) {
      this.logger.error('Failed to cancel scheduled post', error, { postId, userId });
      throw error;
    }
  }

  /**
   * Reschedule a post
   */
  async reschedulePost(postId: string, userId: string, newScheduledAt: Date): Promise<void> {
    try {
      this.logger.info('Rescheduling post', {
        postId,
        userId,
        newScheduledAt: newScheduledAt.toISOString(),
      });

      // Cancel current schedule
      await this.cancelScheduledPost(postId, userId);

      // Update post scheduled time
      const supabase = createClient();
      await supabase
        .from('posts')
        .update({
          status: 'SCHEDULED',
          scheduled_at: newScheduledAt.toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', postId)
        .eq('user_id', userId);

      // Schedule with new time
      await this.schedulePost(postId, userId, newScheduledAt);

      this.logger.info('Post rescheduled successfully', { postId, newScheduledAt });

    } catch (error) {
      this.logger.error('Failed to reschedule post', error, { postId, userId });
      throw error;
    }
  }

  /**
   * Set up monitoring for post status changes
   */
  private async setupPostStatusMonitoring(): Promise<void> {
    // This would typically use database triggers or webhooks
    // For now, we'll implement periodic checking
    setInterval(async () => {
      await this.checkForNewScheduledPosts();
    }, 60000); // Check every minute

    this.logger.info('Post status monitoring set up');
  }

  /**
   * Check for newly scheduled posts that need to be added to the queue
   */
  private async checkForNewScheduledPosts(): Promise<void> {
    try {
      const supabase = createClient();
      const now = new Date();
      const futureTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Next 24 hours

      // Get posts that are scheduled but not yet in the job queue
      const { data: posts, error } = await supabase
        .from('posts')
        .select('id, user_id, scheduled_at, platforms, content, media_url')
        .eq('status', 'SCHEDULED')
        .gte('scheduled_at', now.toISOString())
        .lte('scheduled_at', futureTime.toISOString());

      if (error) {
        this.logger.error('Error checking for scheduled posts', error);
        return;
      }

      if (!posts || posts.length === 0) {
        return;
      }

      // Check which posts don't have jobs yet
      for (const post of posts) {
        const { data: existingJobs } = await supabase
          .from('job_queue')
          .select('id')
          .eq('job_type', 'publish-post')
          .contains('job_data', { postId: post.id })
          .in('status', ['pending', 'processing']);

        if (!existingJobs || existingJobs.length === 0) {
          // Schedule this post
          await this.schedulePost(post.id, post.user_id, new Date(post.scheduled_at));
        }
      }

    } catch (error) {
      this.logger.error('Error in checkForNewScheduledPosts', error);
    }
  }

  /**
   * Set up recurring post generation
   */
  private async setupRecurringPostGeneration(): Promise<void> {
    // Check for recurring posts that need generation every hour
    setInterval(async () => {
      await this.checkForRecurringPostGeneration();
    }, 60 * 60 * 1000); // Every hour

    this.logger.info('Recurring post generation monitoring set up');
  }

  /**
   * Check for recurring posts that need new instances generated
   */
  private async checkForRecurringPostGeneration(): Promise<void> {
    try {
      const supabase = createClient();

      // Get active recurring posts that need generation
      const { data: recurringPosts, error } = await supabase
        .from('recurring_posts')
        .select('id, user_id, next_generation_at')
        .eq('is_active', true)
        .lte('next_generation_at', new Date().toISOString());

      if (error) {
        this.logger.error('Error checking for recurring posts', error);
        return;
      }

      if (!recurringPosts || recurringPosts.length === 0) {
        return;
      }

      // Generate posts for each recurring post
      for (const recurringPost of recurringPosts) {
        await this.queueManager.addJob('generate-recurring-posts', {
          recurringPostId: recurringPost.id,
          userId: recurringPost.user_id,
          days: 30, // Generate for next 30 days
        }, {
          priority: 3,
        });

        this.logger.info('Queued recurring post generation', {
          recurringPostId: recurringPost.id,
        });
      }

    } catch (error) {
      this.logger.error('Error in checkForRecurringPostGeneration', error);
    }
  }

  /**
   * Get scheduler status
   */
  getStatus(): {
    isInitialized: boolean;
    schedulerStatus: any;
    queueStatus: any;
  } {
    return {
      isInitialized: this.isInitialized,
      schedulerStatus: this.schedulerEngine.getStatus(),
      queueStatus: this.queueManager.getStatus(),
    };
  }

  /**
   * Manually trigger post processing
   */
  async triggerPostProcessing(postId: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Scheduler integration not initialized');
    }

    await this.schedulerEngine.triggerPostProcessing(postId);
  }

  /**
   * Get job statistics
   */
  async getJobStatistics(): Promise<any> {
    const supabase = createClient();

    const { data: stats } = await supabase
      .from('job_queue')
      .select('status, job_type')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    const statistics = {
      total: stats?.length || 0,
      byStatus: {} as Record<string, number>,
      byType: {} as Record<string, number>,
    };

    stats?.forEach(job => {
      statistics.byStatus[job.status] = (statistics.byStatus[job.status] || 0) + 1;
      statistics.byType[job.job_type] = (statistics.byType[job.job_type] || 0) + 1;
    });

    return statistics;
  }
}
