/**
 * Enhanced Facebook Provider
 * Adapted from Postiz with business page selection and improved error handling
 */

import { 
  AuthTokenDetails, 
  PostDetails, 
  PostResponse, 
  SocialProvider,
  GenerateAuthUrlResponse,
  AnalyticsData
} from '../interfaces';
import { SocialAbstract } from '../social-abstract';
import dayjs from 'dayjs';

export class FacebookEnhancedProvider extends SocialAbstract implements SocialProvider {
  identifier = 'facebook';
  name = 'Facebook Page';
  isBetweenSteps = true; // Requires business page selection
  scopes = [
    'pages_show_list',
    'business_management',
    'pages_manage_posts',
    'pages_manage_engagement',
    'pages_read_engagement',
    'read_insights',
  ];

  async refreshToken(refresh_token: string): Promise<AuthTokenDetails> {
    // Facebook tokens are long-lived and don't need traditional refresh
    // This is a placeholder implementation
    return {
      refreshToken: '',
      expiresIn: 0,
      accessToken: '',
      id: '',
      name: '',
      picture: '',
      username: '',
    };
  }

  async generateAuthUrl(): Promise<GenerateAuthUrlResponse> {
    const state = this.makeId(6);
    const codeVerifier = this.makeId(10);
    
    const url = 'https://www.facebook.com/v20.0/dialog/oauth' +
      `?client_id=${process.env.FACEBOOK_APP_ID}` +
      `&redirect_uri=${encodeURIComponent(
        `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback`
      )}` +
      `&state=${state}` +
      `&scope=${this.scopes.join(',')}`;

    return {
      url,
      codeVerifier,
      state,
    };
  }

  async reConnect(
    id: string,
    requiredId: string,
    accessToken: string
  ): Promise<AuthTokenDetails> {
    try {
      const information = await this.fetchPageInformation(accessToken, requiredId);
      
      return {
        id: information.id,
        name: information.name,
        accessToken: information.access_token,
        refreshToken: information.access_token,
        expiresIn: dayjs().add(59, 'days').unix() - dayjs().unix(),
        picture: information.picture,
        username: information.username,
      };
    } catch (error) {
      console.error('[FacebookEnhanced] ReConnect error:', error);
      throw new Error(`Facebook reconnection failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  async authenticate(params: {
    code: string;
    codeVerifier: string;
    refresh?: string;
  }): Promise<AuthTokenDetails> {
    try {
      // Step 1: Exchange code for short-lived token
      const shortTokenResponse = await this.fetch(
        'https://graph.facebook.com/v20.0/oauth/access_token' +
        `?client_id=${process.env.FACEBOOK_APP_ID}` +
        `&redirect_uri=${encodeURIComponent(
          `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback${
            params.refresh ? `?refresh=${params.refresh}` : ''
          }`
        )}` +
        `&client_secret=${process.env.FACEBOOK_APP_SECRET}` +
        `&code=${params.code}`
      );

      const shortTokenData = await shortTokenResponse.json();

      // Step 2: Exchange short-lived token for long-lived token
      const longTokenResponse = await this.fetch(
        'https://graph.facebook.com/v20.0/oauth/access_token' +
        '?grant_type=fb_exchange_token' +
        `&client_id=${process.env.FACEBOOK_APP_ID}` +
        `&client_secret=${process.env.FACEBOOK_APP_SECRET}` +
        `&fb_exchange_token=${shortTokenData.access_token}&fields=access_token,expires_in`
      );

      const { access_token } = await longTokenResponse.json();

      // Step 3: Verify permissions
      const permissionsResponse = await this.fetch(
        `https://graph.facebook.com/v20.0/me/permissions?access_token=${access_token}`
      );

      const { data } = await permissionsResponse.json();
      const permissions = data
        .filter((d: any) => d.status === 'granted')
        .map((p: any) => p.permission);

      this.checkScopes(this.scopes, permissions);

      // Step 4: Get user information
      const userResponse = await this.fetch(
        `https://graph.facebook.com/v20.0/me?fields=id,name,picture&access_token=${access_token}`
      );

      const {
        id,
        name,
        picture: {
          data: { url },
        },
      } = await userResponse.json();

      return {
        id,
        name,
        accessToken: access_token,
        refreshToken: access_token,
        expiresIn: dayjs().add(59, 'days').unix() - dayjs().unix(),
        picture: url,
        username: '',
      };
    } catch (error) {
      console.error('[FacebookEnhanced] Authentication error:', error);
      throw new Error(`Facebook authentication failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  async getBusinessPages(accessToken: string): Promise<Array<{
    id: string;
    name: string;
    username?: string;
    picture?: string;
    type: 'page';
  }>> {
    try {
      const response = await this.fetch(
        `https://graph.facebook.com/v20.0/me/accounts?fields=id,username,name,picture.type(large)&access_token=${accessToken}`
      );

      const { data } = await response.json();
      
      return data.map((page: any) => ({
        id: page.id,
        name: page.name,
        username: page.username,
        picture: page.picture?.data?.url,
        type: 'page' as const,
      }));
    } catch (error) {
      console.error('[FacebookEnhanced] Get business pages error:', error);
      return [];
    }
  }

  async fetchPageInformation(accessToken: string, pageId: string) {
    try {
      const response = await this.fetch(
        `https://graph.facebook.com/v20.0/${pageId}?fields=username,access_token,name,picture.type(large)&access_token=${accessToken}`
      );

      const {
        id,
        name,
        access_token,
        username,
        picture: {
          data: { url },
        },
      } = await response.json();

      return {
        id,
        name,
        access_token,
        picture: url,
        username,
      };
    } catch (error) {
      console.error('[FacebookEnhanced] Fetch page information error:', error);
      throw error;
    }
  }

  async post(
    id: string,
    accessToken: string,
    postDetails: PostDetails[],
    integration: any
  ): Promise<PostResponse[]> {
    try {
      const [firstPost, ...comments] = postDetails;
      let finalId = '';
      let finalUrl = '';

      // Handle video posts
      if (firstPost?.media?.[0]?.url?.includes('mp4')) {
        const videoResponse = await this.fetch(
          `https://graph.facebook.com/v20.0/${id}/videos?access_token=${accessToken}&fields=id,permalink_url`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              file_url: firstPost.media[0].url,
              description: firstPost.message,
              published: true,
            }),
          }
        );

        const { id: videoId, permalink_url } = await videoResponse.json();
        finalUrl = 'https://www.facebook.com/reel/' + videoId;
        finalId = videoId;
      } else {
        // Handle image posts
        const uploadPhotos = !firstPost?.media?.length ? [] : await Promise.all(
          firstPost.media.map(async (media) => {
            const photoResponse = await this.fetch(
              `https://graph.facebook.com/v20.0/${id}/photos?access_token=${accessToken}`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  url: media.url,
                  published: false,
                }),
              }
            );

            const { id: photoId } = await photoResponse.json();
            return { media_fbid: photoId };
          })
        );

        // Create the main post
        const postResponse = await this.fetch(
          `https://graph.facebook.com/v20.0/${id}/feed?access_token=${accessToken}&fields=id,permalink_url`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              ...(uploadPhotos?.length ? { attached_media: uploadPhotos } : {}),
              message: firstPost.message,
              published: true,
            }),
          }
        );

        const { id: postId, permalink_url } = await postResponse.json();
        finalUrl = permalink_url;
        finalId = postId;
      }

      const results: PostResponse[] = [{
        id: firstPost.id,
        postId: finalId,
        releaseURL: finalUrl,
        status: 'posted',
      }];

      // Handle comments
      for (const comment of comments) {
        try {
          const commentResponse = await this.fetch(
            `https://graph.facebook.com/v20.0/${finalId}/comments?access_token=${accessToken}&fields=id,permalink_url`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                ...(comment.media?.length ? { attachment_url: comment.media[0].url } : {}),
                message: comment.message,
              }),
            }
          );

          const commentData = await commentResponse.json();
          
          results.push({
            id: comment.id,
            postId: commentData.id,
            releaseURL: commentData.permalink_url,
            status: 'posted',
          });
        } catch (error) {
          console.error('[FacebookEnhanced] Comment posting error:', error);
          results.push({
            id: comment.id,
            postId: '',
            releaseURL: '',
            status: 'failed',
          });
        }
      }

      return results;
    } catch (error) {
      console.error('[FacebookEnhanced] Post error:', error);
      throw new Error(`Facebook posting failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  async analytics(
    id: string,
    accessToken: string,
    date: number
  ): Promise<AnalyticsData[]> {
    try {
      const until = dayjs().endOf('day').unix();
      const since = dayjs().subtract(date, 'day').unix();

      const response = await this.fetch(
        `https://graph.facebook.com/v20.0/${id}/insights?metric=page_impressions_unique,page_posts_impressions_unique,page_post_engagements,page_daily_follows,page_video_views&access_token=${accessToken}&period=day&since=${since}&until=${until}`
      );

      const { data } = await response.json();

      return data?.map((d: any) => ({
        label: d.name === 'page_impressions_unique' ? 'Page Impressions' :
               d.name === 'page_post_engagements' ? 'Posts Engagement' :
               d.name === 'page_daily_follows' ? 'Page followers' :
               d.name === 'page_video_views' ? 'Videos views' : 'Posts Impressions',
        percentageChange: 5,
        data: d?.values?.map((v: any) => ({
          total: v.value,
          date: dayjs(v.end_time).format('YYYY-MM-DD'),
        })),
      })) || [];
    } catch (error) {
      console.error('[FacebookEnhanced] Analytics error:', error);
      return [];
    }
  }
}
