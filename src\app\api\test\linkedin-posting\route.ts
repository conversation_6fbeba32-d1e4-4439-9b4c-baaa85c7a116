import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Test LinkedIn posting functionality
 * GET /api/test/linkedin-posting
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing LinkedIn posting functionality...');

    const results = {
      timestamp: new Date().toISOString(),
      tests: {} as any,
      summary: {} as any
    };

    // Test 1: Get LinkedIn accounts from database
    try {
      console.log('Test 1: Retrieving LinkedIn accounts...');
      const supabase = createServiceRoleClient();
      const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
      
      const { data: linkedinAccounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'LINKEDIN');

      results.tests.accountRetrieval = {
        success: !error,
        error: error?.message,
        accountsFound: linkedinAccounts?.length || 0,
        accounts: linkedinAccounts?.map(acc => ({
          id: acc.id,
          account_name: acc.account_name,
          account_id: acc.account_id,
          expires_at: acc.expires_at,
          hasAccessToken: !!acc.access_token
        })) || []
      };

      console.log(`✅ Found ${linkedinAccounts?.length || 0} LinkedIn accounts`);
    } catch (error: any) {
      results.tests.accountRetrieval = {
        success: false,
        error: error.message
      };
    }

    // Test 2: Test LinkedIn connection for each account
    if (results.tests.accountRetrieval.success && results.tests.accountRetrieval.accounts.length > 0) {
      results.tests.connectionTests = [];

      for (const account of results.tests.accountRetrieval.accounts) {
        try {
          console.log(`Test 2: Testing connection for ${account.account_name}...`);
          
          // Get full account data
          const supabase = createServiceRoleClient();
          const { data: fullAccount } = await supabase
            .from('social_accounts')
            .select('*')
            .eq('id', account.id)
            .single();

          if (!fullAccount?.access_token) {
            results.tests.connectionTests.push({
              accountName: account.account_name,
              success: false,
              error: 'No access token available'
            });
            continue;
          }

          // Test LinkedIn API connection
          const response = await fetch(`https://api.linkedin.com/v2/userinfo`, {
            headers: {
              'Authorization': `Bearer ${fullAccount.access_token}`,
              'Accept': 'application/json',
            },
          });

          if (response.ok) {
            const userData = await response.json();
            results.tests.connectionTests.push({
              accountName: account.account_name,
              success: true,
              userInfo: {
                name: userData.name,
                email: userData.email
              }
            });
            console.log(`✅ Connection test passed for ${account.account_name}`);
          } else {
            const errorText = await response.text();
            results.tests.connectionTests.push({
              accountName: account.account_name,
              success: false,
              error: `API error: ${response.status} - ${errorText}`
            });
            console.log(`❌ Connection test failed for ${account.account_name}: ${response.status}`);
          }

        } catch (error: any) {
          results.tests.connectionTests.push({
            accountName: account.account_name,
            success: false,
            error: error.message
          });
        }
      }
    }

    // Test 3: Test LinkedIn posting capability (dry run)
    if (results.tests.accountRetrieval.success && results.tests.accountRetrieval.accounts.length > 0) {
      results.tests.postingCapability = [];

      for (const account of results.tests.accountRetrieval.accounts) {
        try {
          console.log(`Test 3: Testing posting capability for ${account.account_name}...`);
          
          // Get full account data
          const supabase = createServiceRoleClient();
          const { data: fullAccount } = await supabase
            .from('social_accounts')
            .select('*')
            .eq('id', account.id)
            .single();

          if (!fullAccount?.access_token) {
            results.tests.postingCapability.push({
              accountName: account.account_name,
              success: false,
              error: 'No access token available'
            });
            continue;
          }

          // Prepare test post data (dry run - not actually posting)
          const testPostData = {
            author: `urn:li:person:${account.account_id}`,
            lifecycleState: 'PUBLISHED',
            specificContent: {
              'com.linkedin.ugc.ShareContent': {
                shareCommentary: {
                  text: '🧪 Test post from eWasl Social Scheduler - LinkedIn Integration Test! 🚀 #eWasl #LinkedIn #SocialMediaManagement'
                },
                shareMediaCategory: 'NONE'
              }
            },
            visibility: {
              'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
            }
          };

          // Validate post data structure (without actually posting)
          const isValidPostData = testPostData.author && 
                                 testPostData.specificContent && 
                                 testPostData.specificContent['com.linkedin.ugc.ShareContent'] &&
                                 testPostData.specificContent['com.linkedin.ugc.ShareContent'].shareCommentary;

          results.tests.postingCapability.push({
            accountName: account.account_name,
            success: isValidPostData,
            postDataValid: isValidPostData,
            authorUrn: testPostData.author,
            contentLength: testPostData.specificContent['com.linkedin.ugc.ShareContent'].shareCommentary.text.length,
            note: 'Dry run - no actual post created'
          });

          console.log(`✅ Posting capability test passed for ${account.account_name} (dry run)`);

        } catch (error: any) {
          results.tests.postingCapability.push({
            accountName: account.account_name,
            success: false,
            error: error.message
          });
        }
      }
    }

    // Generate summary
    const totalAccounts = results.tests.accountRetrieval?.accountsFound || 0;
    const successfulConnections = results.tests.connectionTests?.filter((test: any) => test.success).length || 0;
    const successfulPostingTests = results.tests.postingCapability?.filter((test: any) => test.success).length || 0;

    results.summary = {
      totalLinkedInAccounts: totalAccounts,
      successfulConnections,
      successfulPostingTests,
      overallStatus: totalAccounts > 0 && successfulConnections > 0 && successfulPostingTests > 0 ? 'READY' : 'NEEDS_ATTENTION',
      readyForPosting: successfulConnections > 0 && successfulPostingTests > 0
    };

    console.log('🎯 LinkedIn posting test summary:', results.summary);

    return NextResponse.json({
      success: true,
      message: '🧪 LinkedIn posting functionality test completed',
      results,
      recommendation: results.summary.readyForPosting ? 
        '✅ LinkedIn integration is ready for live posting!' :
        '⚠️ LinkedIn integration needs attention before live posting'
    });

  } catch (error) {
    console.error('❌ LinkedIn posting test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'LinkedIn posting test failed'
    }, { status: 500 });
  }
}

/**
 * Test actual LinkedIn posting (POST request)
 * POST /api/test/linkedin-posting
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { accountId, content, dryRun = true } = body;

    console.log('🧪 Testing LinkedIn posting with actual API call...');

    if (!accountId) {
      return NextResponse.json({
        error: 'Missing accountId parameter'
      }, { status: 400 });
    }

    // Get account data
    const supabase = createServiceRoleClient();
    const { data: account, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('platform', 'LINKEDIN')
      .single();

    if (error || !account) {
      return NextResponse.json({
        error: 'LinkedIn account not found'
      }, { status: 404 });
    }

    const testContent = content || '🧪 Test post from eWasl Social Scheduler! 🚀\n\nTesting LinkedIn integration functionality.\n\n#eWasl #LinkedIn #SocialMediaManagement #Test';

    if (dryRun) {
      // Dry run - validate everything but don't actually post
      return NextResponse.json({
        success: true,
        message: '✅ Dry run completed successfully',
        account: {
          name: account.account_name,
          id: account.account_id
        },
        content: testContent,
        note: 'This was a dry run - no actual post was created'
      });
    } else {
      // Actual posting (use with caution)
      const postData = {
        author: `urn:li:person:${account.account_id}`,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: testContent
            },
            shareMediaCategory: 'NONE'
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
        }
      };

      const response = await fetch('https://api.linkedin.com/v2/ugcPosts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
          'Content-Type': 'application/json',
          'X-Restli-Protocol-Version': '2.0.0'
        },
        body: JSON.stringify(postData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        return NextResponse.json({
          success: false,
          error: `LinkedIn API error: ${response.status} - ${errorText}`
        }, { status: 400 });
      }

      const result = await response.json();
      
      return NextResponse.json({
        success: true,
        message: '🎉 LinkedIn post created successfully!',
        postId: result.id,
        account: {
          name: account.account_name,
          id: account.account_id
        },
        content: testContent
      });
    }

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
