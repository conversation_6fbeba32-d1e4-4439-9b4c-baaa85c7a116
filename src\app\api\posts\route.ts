import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for creating posts
const createPostSchema = z.object({
  content: z.string().min(1, 'Content is required').max(2800, 'Content too long'),
  media_url: z.string().url().optional().or(z.literal('')),
  status: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED']).default('DRAFT'),
  scheduled_at: z.string().datetime().optional(),
  social_account_ids: z.array(z.string()).min(1, 'At least one social account required'),
});

// GET - Fetch user's posts
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching posts...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Fetching posts for user:', user.id);

    // Build query
    let query = supabase
      .from('posts')
      .select(`
        id,
        content,
        media_url,
        status,
        scheduled_at,
        published_at,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Add status filter if provided
    if (status && status !== 'ALL') {
      query = query.eq('status', status);
    }

    const { data: posts, error } = await query;

    if (error) {
      console.error('Error fetching posts:', error);
      return NextResponse.json(
        { error: 'Failed to fetch posts' },
        { status: 500 }
      );
    }

    console.log(`Fetched ${posts?.length || 0} posts`);

    return NextResponse.json({
      posts: posts || [],
      total: posts?.length || 0,
    });

  } catch (error) {
    console.error('Posts fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new post
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Creating new post...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log('Request body received:', { ...body, content: body.content?.substring(0, 50) + '...' });

    // Validate request body
    const validation = createPostSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        { 
          error: 'Invalid input', 
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    const { content, media_url, status, scheduled_at, social_account_ids } = validation.data;

    console.log('Creating post for user:', user.id);

    // TODO: Add usage limits checking in future payment system implementation

    // Create post
    const { data: post, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content,
        media_url: media_url || null,
        status,
        scheduled_at: scheduled_at ? new Date(scheduled_at).toISOString() : null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating post:', createError);
      return NextResponse.json(
        { error: 'Failed to create post' },
        { status: 500 }
      );
    }

    console.log('Post created successfully:', post.id);

    // TODO: Add usage tracking for billing purposes in future payment system implementation

    // If status is PUBLISHED, publish immediately
    if (status === 'PUBLISHED') {
      // TODO: Implement immediate publishing
      console.log('Immediate publishing requested for post:', post.id);
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        post_id: post.id,
        action: status === 'PUBLISHED' ? 'POST_PUBLISHED' : status === 'SCHEDULED' ? 'POST_SCHEDULED' : 'POST_CREATED',
        details: `Post ${status.toLowerCase()}: ${content.substring(0, 50)}...`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      post,
      message: status === 'PUBLISHED' ? 'Post published successfully' : 
               status === 'SCHEDULED' ? 'Post scheduled successfully' : 
               'Post saved as draft',
    }, { status: 201 });

  } catch (error) {
    console.error('Post creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
