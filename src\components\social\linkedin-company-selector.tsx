'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CheckCircle, Users, Globe, Building, RefreshCw, AlertCircle, MapPin } from 'lucide-react';
import { toast } from 'sonner';
import { BusinessAccount } from '@/lib/social/business-accounts/business-account-types';

interface LinkedInCompanySelectorProps {
  userId: string;
  socialAccountId: string;
  onCompanySelected?: (companyId: string) => void;
  className?: string;
}

export function LinkedInCompanySelector({ 
  userId, 
  socialAccountId, 
  onCompanySelected,
  className 
}: LinkedInCompanySelectorProps) {
  const [companies, setCompanies] = useState<BusinessAccount[]>([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCompanies();
  }, [socialAccountId]);

  const loadCompanies = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/social/business-accounts?platform=linkedin&userId=${userId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load LinkedIn Companies');
      }

      setCompanies(data.businessAccounts || []);
      setSelectedCompanyId(data.selectedAccount?.id || null);

    } catch (error: any) {
      console.error('Error loading LinkedIn Companies:', error);
      setError(error.message);
      toast.error('فشل في تحميل شركات لينكد إن');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshCompanies = async () => {
    try {
      setIsRefreshing(true);
      setError(null);

      const response = await fetch('/api/social/business-accounts/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'linkedin',
          userId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to refresh LinkedIn Companies');
      }

      setCompanies(data.businessAccounts || []);
      toast.success('تم تحديث شركات لينكد إن بنجاح');

    } catch (error: any) {
      console.error('Error refreshing LinkedIn Companies:', error);
      setError(error.message);
      toast.error('فشل في تحديث شركات لينكد إن');
    } finally {
      setIsRefreshing(false);
    }
  };

  const selectCompany = async (companyId: string) => {
    try {
      const response = await fetch('/api/social/business-accounts/select', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'linkedin',
          userId,
          businessAccountId: companyId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to select LinkedIn Company');
      }

      setSelectedCompanyId(companyId);
      onCompanySelected?.(companyId);
      toast.success('تم اختيار شركة لينكد إن بنجاح');

    } catch (error: any) {
      console.error('Error selecting LinkedIn Company:', error);
      toast.error('فشل في اختيار شركة لينكد إن');
    }
  };

  const formatFollowerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const formatLocation = (company: BusinessAccount) => {
    const headquarters = company.metadata?.headquarters;
    if (!headquarters) return null;
    
    const parts = [];
    if (headquarters.city) parts.push(headquarters.city);
    if (headquarters.country) parts.push(headquarters.country);
    
    return parts.length > 0 ? parts.join(', ') : null;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-600 rounded"></div>
            شركات لينكد إن
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
            <span className="mr-2 text-muted-foreground">جاري تحميل الشركات...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-600 rounded"></div>
            شركات لينكد إن
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadCompanies} variant="outline">
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (companies.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-600 rounded"></div>
            شركات لينكد إن
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="w-12 h-12 text-yellow-500 mb-4" />
            <p className="text-muted-foreground mb-4">
              لم يتم العثور على شركات لينكد إن قابلة للإدارة
            </p>
            <p className="text-sm text-muted-foreground mb-4">
              تأكد من أن لديك صلاحيات إدارة الشركات في لينكد إن
            </p>
            <Button onClick={refreshCompanies} variant="outline" disabled={isRefreshing}>
              {isRefreshing ? (
                <>
                  <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
                  جاري التحديث...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 ml-2" />
                  تحديث الشركات
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-600 rounded"></div>
            شركات لينكد إن ({companies.length})
          </CardTitle>
          <Button 
            onClick={refreshCompanies} 
            variant="outline" 
            size="sm"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {companies.map((company) => (
            <div
              key={company.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                selectedCompanyId === company.id
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => selectCompany(company.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={company.profilePictureUrl} alt={company.name} />
                    <AvatarFallback>
                      <Building className="w-6 h-6" />
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-sm">{company.name}</h3>
                      {selectedCompanyId === company.id && (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 mt-1">
                      {company.followerCount && company.followerCount > 0 && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Users className="w-3 h-3" />
                          {formatFollowerCount(company.followerCount)}
                        </div>
                      )}
                      
                      {company.category && (
                        <Badge variant="secondary" className="text-xs">
                          {company.category}
                        </Badge>
                      )}
                      
                      {company.metadata?.employeeCount && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Building className="w-3 h-3" />
                          {company.metadata.employeeCount} موظف
                        </div>
                      )}
                      
                      {formatLocation(company) && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <MapPin className="w-3 h-3" />
                          {formatLocation(company)}
                        </div>
                      )}
                      
                      {company.website && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Globe className="w-3 h-3" />
                          موقع إلكتروني
                        </div>
                      )}
                    </div>
                    
                    {company.metadata?.description && (
                      <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                        {company.metadata.description}
                      </p>
                    )}
                  </div>
                </div>
                
                {selectedCompanyId === company.id && (
                  <Badge variant="default" className="bg-green-500">
                    مختارة
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {selectedCompanyId && (
          <div className="mt-4 p-3 bg-green-50 dark:bg-green-950 border border-green-200 rounded-lg">
            <p className="text-sm text-green-700 dark:text-green-300">
              ✅ سيتم نشر المنشورات على الشركة المختارة
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
