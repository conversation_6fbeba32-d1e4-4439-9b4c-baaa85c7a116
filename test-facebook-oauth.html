<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook OAuth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #1877f2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #166fe5;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .loading {
            display: none;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Facebook OAuth Integration Test</h1>
        <p>Test the Facebook OAuth flow for eWasl Social Scheduler</p>
        
        <div class="info">
            <strong>Phase 4: Re-establish Facebook Connection</strong><br>
            Testing Facebook OAuth configuration and flow after cleanup and redirect URI fix.
        </div>

        <h3>Step 1: Test OAuth Configuration</h3>
        <button class="button" onclick="testConfiguration()">Test Facebook Configuration</button>
        <div id="configResult"></div>

        <h3>Step 2: Generate OAuth URL</h3>
        <button class="button" onclick="generateAuthUrl()">Generate Facebook Auth URL</button>
        <div id="authUrlResult"></div>

        <h3>Step 3: Start OAuth Flow</h3>
        <button class="button" onclick="startOAuth()" id="oauthButton" disabled>Start Facebook OAuth</button>
        <div id="oauthResult"></div>

        <div class="loading" id="loading">⏳ Processing...</div>
    </div>

    <script>
        let authUrl = '';

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        async function testConfiguration() {
            showLoading(true);
            const resultDiv = document.getElementById('configResult');
            
            try {
                const response = await fetch('/api/social/oauth-status');
                const data = await response.json();
                
                const facebookConfig = data.platforms.find(p => p.platform === 'facebook');
                
                if (facebookConfig && facebookConfig.enabled && facebookConfig.configured) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Facebook OAuth Configuration: WORKING<br>
                            • Enabled: ${facebookConfig.enabled}<br>
                            • Configured: ${facebookConfig.configured}<br>
                            • Has Credentials: ${facebookConfig.hasCredentials}<br>
                            • Scopes: ${facebookConfig.scopes.join(', ')}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Facebook OAuth Configuration: FAILED<br>
                            Configuration issues detected.
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Configuration Test Failed: ${error.message}
                    </div>
                `;
            }
            
            showLoading(false);
        }

        async function generateAuthUrl() {
            showLoading(true);
            const resultDiv = document.getElementById('authUrlResult');
            
            try {
                const response = await fetch('/api/social/oauth-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        platform: 'facebook',
                        action: 'test-auth-url'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.authUrl) {
                    authUrl = data.authUrl;
                    document.getElementById('oauthButton').disabled = false;
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Facebook OAuth URL Generated Successfully<br>
                            <strong>Auth URL:</strong><br>
                            <pre>${data.authUrl}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ OAuth URL Generation Failed<br>
                            ${data.message || 'Unknown error'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Auth URL Generation Failed: ${error.message}
                    </div>
                `;
            }
            
            showLoading(false);
        }

        function startOAuth() {
            if (authUrl) {
                document.getElementById('oauthResult').innerHTML = `
                    <div class="info">
                        🔄 Redirecting to Facebook OAuth...<br>
                        You will be redirected to Facebook to complete authentication.
                    </div>
                `;
                
                // Redirect to Facebook OAuth
                window.location.href = authUrl;
            } else {
                document.getElementById('oauthResult').innerHTML = `
                    <div class="error">
                        ❌ No auth URL available. Please generate auth URL first.
                    </div>
                `;
            }
        }

        // Auto-test configuration on page load
        window.onload = function() {
            testConfiguration();
        };
    </script>
</body>
</html>
