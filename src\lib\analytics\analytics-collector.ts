import { createClient } from '@/lib/supabase/server';
import { AnalyticsEngine } from './analytics-engine';
import { SchedulerLogger } from '../scheduler/scheduler-logger';

export interface CollectionJob {
  id: string;
  type: 'post' | 'account' | 'hashtag' | 'competitor';
  targetId: string;
  userId: string;
  priority: number;
  scheduledAt: Date;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  attempts: number;
  maxAttempts: number;
  lastError?: string;
}

/**
 * Service for automated analytics data collection
 */
export class AnalyticsCollector {
  private analyticsEngine: AnalyticsEngine;
  private logger: SchedulerLogger;
  private isRunning: boolean = false;
  private collectionInterval?: NodeJS.Timeout;

  constructor() {
    this.analyticsEngine = new AnalyticsEngine();
    this.logger = new SchedulerLogger('analytics-collector');
  }

  /**
   * Start the analytics collection service
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Analytics collector is already running');
      return;
    }

    this.isRunning = true;
    this.logger.info('Starting analytics collector...');

    // Start collection loop
    this.collectionInterval = setInterval(async () => {
      await this.processCollectionJobs();
    }, 60000); // Check every minute

    // Schedule automatic collections
    await this.scheduleAutomaticCollections();

    this.logger.info('Analytics collector started successfully');
  }

  /**
   * Stop the analytics collection service
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    this.logger.info('Stopping analytics collector...');

    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = undefined;
    }

    this.logger.info('Analytics collector stopped');
  }

  /**
   * Schedule automatic analytics collection for published posts
   */
  async scheduleAutomaticCollections(): Promise<void> {
    try {
      const supabase = createClient();

      // Get recently published posts that need analytics collection
      const { data: recentPosts, error } = await supabase
        .from('posts')
        .select('id, user_id, published_at, platforms')
        .eq('status', 'PUBLISHED')
        .gte('published_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
        .is('analytics_collected', false);

      if (error) {
        this.logger.error('Failed to fetch recent posts for analytics', error);
        return;
      }

      if (!recentPosts || recentPosts.length === 0) {
        return;
      }

      // Schedule collection jobs for each post
      for (const post of recentPosts) {
        await this.schedulePostAnalytics(post.id, post.user_id, {
          delay: this.calculateCollectionDelay(new Date(post.published_at)),
          priority: 5,
        });
      }

      this.logger.info(`Scheduled analytics collection for ${recentPosts.length} posts`);

    } catch (error) {
      this.logger.error('Failed to schedule automatic collections', error);
    }
  }

  /**
   * Calculate optimal delay for analytics collection
   */
  private calculateCollectionDelay(publishedAt: Date): number {
    const now = new Date();
    const timeSincePublished = now.getTime() - publishedAt.getTime();
    
    // Collect analytics at different intervals based on post age
    if (timeSincePublished < 60 * 60 * 1000) { // Less than 1 hour
      return 60 * 60 * 1000; // Wait 1 hour
    } else if (timeSincePublished < 24 * 60 * 60 * 1000) { // Less than 24 hours
      return 4 * 60 * 60 * 1000; // Wait 4 hours
    } else {
      return 24 * 60 * 60 * 1000; // Wait 24 hours
    }
  }

  /**
   * Schedule analytics collection for a specific post
   */
  async schedulePostAnalytics(
    postId: string,
    userId: string,
    options: {
      delay?: number;
      priority?: number;
      maxAttempts?: number;
    } = {}
  ): Promise<void> {
    try {
      const supabase = createClient();

      const scheduledAt = new Date(Date.now() + (options.delay || 0));

      // Check if collection job already exists
      const { data: existingJob } = await supabase
        .from('analytics_collection_jobs')
        .select('id')
        .eq('type', 'post')
        .eq('target_id', postId)
        .eq('status', 'pending')
        .single();

      if (existingJob) {
        this.logger.info('Analytics collection job already exists', { postId });
        return;
      }

      // Create collection job
      const { error } = await supabase
        .from('analytics_collection_jobs')
        .insert({
          type: 'post',
          target_id: postId,
          user_id: userId,
          priority: options.priority || 5,
          scheduled_at: scheduledAt.toISOString(),
          max_attempts: options.maxAttempts || 3,
        });

      if (error) {
        throw error;
      }

      this.logger.info('Analytics collection scheduled', {
        postId,
        scheduledAt: scheduledAt.toISOString(),
      });

    } catch (error) {
      this.logger.error('Failed to schedule post analytics', error, { postId, userId });
      throw error;
    }
  }

  /**
   * Schedule analytics collection for a social account
   */
  async scheduleAccountAnalytics(
    socialAccountId: string,
    userId: string,
    periodType: 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<void> {
    try {
      const supabase = createClient();

      // Calculate next collection time based on period type
      let nextCollection = new Date();
      switch (periodType) {
        case 'daily':
          nextCollection.setHours(nextCollection.getHours() + 24);
          break;
        case 'weekly':
          nextCollection.setDate(nextCollection.getDate() + 7);
          break;
        case 'monthly':
          nextCollection.setMonth(nextCollection.getMonth() + 1);
          break;
      }

      const { error } = await supabase
        .from('analytics_collection_jobs')
        .insert({
          type: 'account',
          target_id: socialAccountId,
          user_id: userId,
          priority: 3,
          scheduled_at: nextCollection.toISOString(),
          metadata: { periodType },
        });

      if (error) {
        throw error;
      }

      this.logger.info('Account analytics collection scheduled', {
        socialAccountId,
        periodType,
        nextCollection: nextCollection.toISOString(),
      });

    } catch (error) {
      this.logger.error('Failed to schedule account analytics', error, { socialAccountId });
      throw error;
    }
  }

  /**
   * Process pending collection jobs
   */
  private async processCollectionJobs(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      const supabase = createClient();

      // Get pending jobs that are due for processing
      const { data: jobs, error } = await supabase
        .from('analytics_collection_jobs')
        .select('*')
        .eq('status', 'pending')
        .lte('scheduled_at', new Date().toISOString())
        .order('priority', { ascending: false })
        .order('scheduled_at', { ascending: true })
        .limit(10); // Process up to 10 jobs at a time

      if (error) {
        this.logger.error('Failed to fetch collection jobs', error);
        return;
      }

      if (!jobs || jobs.length === 0) {
        return;
      }

      this.logger.info(`Processing ${jobs.length} analytics collection jobs`);

      // Process each job
      for (const job of jobs) {
        await this.processCollectionJob(job);
      }

    } catch (error) {
      this.logger.error('Error in processCollectionJobs', error);
    }
  }

  /**
   * Process a single collection job
   */
  private async processCollectionJob(job: any): Promise<void> {
    const supabase = createClient();

    try {
      this.logger.info(`Processing ${job.type} analytics job`, {
        jobId: job.id,
        targetId: job.target_id,
      });

      // Update job status to processing
      await supabase
        .from('analytics_collection_jobs')
        .update({
          status: 'processing',
          started_at: new Date().toISOString(),
          attempts: (job.attempts || 0) + 1,
        })
        .eq('id', job.id);

      let success = false;

      // Process based on job type
      switch (job.type) {
        case 'post':
          await this.analyticsEngine.collectPostAnalytics(job.target_id, job.user_id);
          success = true;
          break;

        case 'account':
          const periodType = job.metadata?.periodType || 'daily';
          await this.analyticsEngine.collectAccountAnalytics(job.target_id, periodType);
          success = true;
          break;

        case 'hashtag':
          // TODO: Implement hashtag analytics collection
          this.logger.warn('Hashtag analytics collection not implemented yet');
          success = true; // Mark as success to avoid retries
          break;

        case 'competitor':
          // TODO: Implement competitor analytics collection
          this.logger.warn('Competitor analytics collection not implemented yet');
          success = true; // Mark as success to avoid retries
          break;

        default:
          throw new Error(`Unknown job type: ${job.type}`);
      }

      if (success) {
        // Mark job as completed
        await supabase
          .from('analytics_collection_jobs')
          .update({
            status: 'completed',
            completed_at: new Date().toISOString(),
          })
          .eq('id', job.id);

        // Mark post as analytics collected if it's a post job
        if (job.type === 'post') {
          await supabase
            .from('posts')
            .update({ analytics_collected: true })
            .eq('id', job.target_id);
        }

        this.logger.info('Analytics collection job completed', {
          jobId: job.id,
          type: job.type,
          targetId: job.target_id,
        });
      }

    } catch (error) {
      this.logger.error('Analytics collection job failed', error, {
        jobId: job.id,
        type: job.type,
        targetId: job.target_id,
      });

      const attempts = (job.attempts || 0) + 1;
      const maxAttempts = job.max_attempts || 3;

      if (attempts >= maxAttempts) {
        // Mark as failed
        await supabase
          .from('analytics_collection_jobs')
          .update({
            status: 'failed',
            completed_at: new Date().toISOString(),
            last_error: error instanceof Error ? error.message : 'Unknown error',
          })
          .eq('id', job.id);
      } else {
        // Schedule retry with exponential backoff
        const retryDelay = Math.min(300000 * Math.pow(2, attempts - 1), 3600000); // Max 1 hour
        const retryAt = new Date(Date.now() + retryDelay);

        await supabase
          .from('analytics_collection_jobs')
          .update({
            status: 'pending',
            scheduled_at: retryAt.toISOString(),
            last_error: error instanceof Error ? error.message : 'Unknown error',
          })
          .eq('id', job.id);

        this.logger.info('Analytics collection job scheduled for retry', {
          jobId: job.id,
          attempt: attempts,
          retryAt: retryAt.toISOString(),
        });
      }
    }
  }

  /**
   * Manually trigger analytics collection for a post
   */
  async collectPostAnalyticsNow(postId: string, userId: string): Promise<void> {
    try {
      this.logger.info('Manual analytics collection triggered', { postId, userId });

      await this.analyticsEngine.collectPostAnalytics(postId, userId);

      // Mark post as analytics collected
      const supabase = createClient();
      await supabase
        .from('posts')
        .update({ analytics_collected: true })
        .eq('id', postId)
        .eq('user_id', userId);

      this.logger.info('Manual analytics collection completed', { postId });

    } catch (error) {
      this.logger.error('Manual analytics collection failed', error, { postId, userId });
      throw error;
    }
  }

  /**
   * Get collection job statistics
   */
  async getCollectionStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    totalToday: number;
  }> {
    try {
      const supabase = createClient();
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { data: stats } = await supabase
        .from('analytics_collection_jobs')
        .select('status')
        .gte('created_at', today.toISOString());

      const result = {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        totalToday: stats?.length || 0,
      };

      stats?.forEach(job => {
        result[job.status as keyof typeof result]++;
      });

      return result;

    } catch (error) {
      this.logger.error('Failed to get collection stats', error);
      return {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        totalToday: 0,
      };
    }
  }

  /**
   * Cleanup old completed jobs
   */
  async cleanupOldJobs(daysToKeep: number = 30): Promise<number> {
    try {
      const supabase = createClient();
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

      const { count, error } = await supabase
        .from('analytics_collection_jobs')
        .delete()
        .in('status', ['completed', 'failed'])
        .lt('completed_at', cutoffDate.toISOString());

      if (error) {
        throw error;
      }

      const deletedCount = count || 0;
      if (deletedCount > 0) {
        this.logger.info(`Cleaned up ${deletedCount} old collection jobs`);
      }

      return deletedCount;

    } catch (error) {
      this.logger.error('Failed to cleanup old jobs', error);
      return 0;
    }
  }
}
