'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface BulkImportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (result: any) => void;
}

interface ImportResult {
  operation: any;
  summary: {
    totalItems: number;
    validItems: number;
    invalidItems: number;
    warnings: string[];
  };
  validItems: any[];
  invalidItems: any[];
  hasMoreValidItems: boolean;
  hasMoreInvalidItems: boolean;
}

export function BulkImportModal({ open, onOpenChange, onSuccess }: BulkImportModalProps) {
  const [step, setStep] = useState<'upload' | 'processing' | 'results'>('upload');
  const [isLoading, setIsLoading] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [csvTemplate, setCsvTemplate] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const fetchCsvTemplate = async () => {
    try {
      const response = await fetch('/api/posts/bulk-import');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في جلب قالب CSV');
      }

      setCsvTemplate(result.data);
    } catch (error: any) {
      console.error('Error fetching CSV template:', error);
      toast.error(error.message);
    }
  };

  const downloadTemplate = () => {
    if (!csvTemplate) return;

    const blob = new Blob([csvTemplate.csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', csvTemplate.downloadFilename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    if (!file.name.endsWith('.csv')) {
      toast.error('يجب أن يكون الملف من نوع CSV');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('حجم الملف يجب أن يكون أقل من 10 ميجابايت');
      return;
    }

    setIsLoading(true);
    setStep('processing');

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/posts/bulk-import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في رفع الملف');
      }

      setImportResult(result.data);
      setStep('results');

      if (result.data.summary.validItems > 0) {
        toast.success(result.message);
      } else {
        toast.warning('لا توجد عناصر صالحة في الملف');
      }

    } catch (error: any) {
      console.error('Error uploading file:', error);
      toast.error(error.message);
      setStep('upload');
    } finally {
      setIsLoading(false);
    }
  };

  const processImport = async () => {
    if (!importResult?.operation?.id) return;

    setIsLoading(true);

    try {
      const response = await fetch('/api/posts/bulk-operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operationId: importResult.operation.id,
          action: 'process',
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'فشل في معالجة الاستيراد');
      }

      toast.success('تم بدء معالجة الاستيراد المجمع');
      onSuccess?.(importResult);
      onOpenChange(false);

    } catch (error: any) {
      console.error('Error processing import:', error);
      toast.error(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const resetModal = () => {
    setStep('upload');
    setImportResult(null);
    setIsLoading(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetModal();
    }
    onOpenChange(newOpen);
  };

  // Fetch template when modal opens
  React.useEffect(() => {
    if (open && !csvTemplate) {
      fetchCsvTemplate();
    }
  }, [open, csvTemplate]);

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            استيراد مجمع للمنشورات
          </DialogTitle>
          <DialogDescription>
            قم برفع ملف CSV لاستيراد عدة منشورات دفعة واحدة
          </DialogDescription>
        </DialogHeader>

        {step === 'upload' && (
          <div className="space-y-6">
            {/* Template Download */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">تحميل القالب</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">
                      قم بتحميل قالب CSV لمعرفة التنسيق المطلوب
                    </p>
                    {csvTemplate && (
                      <div className="space-y-2">
                        <div className="text-xs text-muted-foreground">
                          الأعمدة المطلوبة: {csvTemplate.template.headers.join('، ')}
                        </div>
                        <div className="space-y-1">
                          {csvTemplate.template.tips.map((tip: string, index: number) => (
                            <div key={index} className="text-xs text-muted-foreground flex items-center gap-1">
                              <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                              {tip}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    onClick={downloadTemplate}
                    disabled={!csvTemplate}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    تحميل القالب
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">رفع الملف</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div
                    className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-lg font-medium mb-2">اختر ملف CSV أو اسحبه هنا</p>
                    <p className="text-sm text-muted-foreground">
                      الحد الأقصى لحجم الملف: 10 ميجابايت
                    </p>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload(file);
                    }}
                    className="hidden"
                  />

                  <div className="flex justify-center">
                    <Button onClick={() => fileInputRef.current?.click()}>
                      <FileText className="h-4 w-4 mr-2" />
                      اختيار ملف CSV
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {step === 'processing' && (
          <div className="space-y-6">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <RefreshCw className="h-12 w-12 animate-spin text-primary mb-4" />
                <h3 className="text-lg font-semibold mb-2">جاري معالجة الملف...</h3>
                <p className="text-muted-foreground text-center">
                  يتم تحليل الملف والتحقق من صحة البيانات
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {step === 'results' && importResult && (
          <div className="space-y-6">
            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">نتائج التحليل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {importResult.summary.validItems}
                    </div>
                    <div className="text-sm text-green-700">عناصر صالحة</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {importResult.summary.invalidItems}
                    </div>
                    <div className="text-sm text-red-700">عناصر غير صالحة</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {importResult.summary.totalItems}
                    </div>
                    <div className="text-sm text-blue-700">إجمالي العناصر</div>
                  </div>
                </div>

                {importResult.summary.warnings.length > 0 && (
                  <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">تحذيرات</span>
                    </div>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      {importResult.summary.warnings.map((warning, index) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Valid Items Preview */}
            {importResult.validItems.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    العناصر الصالحة ({importResult.summary.validItems})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {importResult.validItems.slice(0, 5).map((item, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="font-medium line-clamp-1">{item.content}</div>
                        <div className="flex items-center gap-2 mt-2">
                          <div className="flex gap-1">
                            {item.platforms.map((platform: string) => (
                              <Badge key={platform} variant="secondary" className="text-xs">
                                {platform}
                              </Badge>
                            ))}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {new Date(item.scheduledAt).toLocaleDateString('ar')}
                          </span>
                        </div>
                      </div>
                    ))}
                    {importResult.hasMoreValidItems && (
                      <p className="text-sm text-muted-foreground text-center">
                        و {importResult.summary.validItems - 5} عنصر آخر...
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Invalid Items */}
            {importResult.invalidItems.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <XCircle className="h-5 w-5 text-red-600" />
                    العناصر غير الصالحة ({importResult.summary.invalidItems})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {importResult.invalidItems.slice(0, 3).map((item, index) => (
                      <div key={index} className="p-3 border border-red-200 rounded-lg bg-red-50">
                        <div className="font-medium text-red-800">السطر {item.item._rowNumber}</div>
                        <div className="text-sm text-red-600 mt-1">
                          {item.errors.join('، ')}
                        </div>
                      </div>
                    ))}
                    {importResult.hasMoreInvalidItems && (
                      <p className="text-sm text-muted-foreground text-center">
                        و {importResult.summary.invalidItems - 3} خطأ آخر...
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-4">
              <Button variant="outline" onClick={() => setStep('upload')}>
                رفع ملف آخر
              </Button>
              {importResult.summary.validItems > 0 && (
                <Button onClick={processImport} disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      بدء الاستيراد ({importResult.summary.validItems} منشور)
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
