/**
 * API Integration Tests for Advanced Content Creation System
 * Tests API endpoints, request/response handling, and error scenarios
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST as generateContentPOST, GET as generateContentGET } from '@/app/api/content/generate/route';
import { POST as templatesPOST, GET as templatesGET } from '@/app/api/content/templates/route';

// Mock Supabase
jest.mock('@/lib/supabase/server', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'test-user-123', email: '<EMAIL>' } },
        error: null
      })
    },
    from: jest.fn().mockReturnThis(),
    insert: jest.fn().mockResolvedValue({ data: null, error: null }),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    order: jest.fn().mockResolvedValue({ data: [], error: null })
  })
}));

// Mock AI Content Generator
jest.mock('@/lib/ai/content-generator', () => ({
  aiContentGenerator: {
    generateContent: jest.fn().mockResolvedValue({
      content: 'Generated test content',
      hashtags: ['test', 'generated'],
      emojis: ['🤖'],
      suggestions: ['Alternative content'],
      characterCount: 22,
      platformOptimized: true
    }),
    generateHashtagSuggestions: jest.fn().mockResolvedValue([
      { tag: 'test', popularity: 'high', relevance: 0.9, category: 'general' },
      { tag: 'content', popularity: 'medium', relevance: 0.8, category: 'media' }
    ]),
    optimizeForPlatform: jest.fn().mockResolvedValue('Optimized content for target platform')
  }
}));

// Mock Content Template Manager
jest.mock('@/lib/templates/content-templates', () => ({
  contentTemplateManager: {
    searchTemplates: jest.fn().mockReturnValue([
      {
        id: 'test-template',
        name: 'Test Template',
        description: 'Test description',
        category: 'promotional',
        platform: ['facebook'],
        language: 'ar',
        content: 'Test content {{variable1}}',
        variables: [
          {
            name: 'variable1',
            type: 'text',
            label: 'Variable 1',
            placeholder: 'Enter value',
            required: true
          }
        ],
        hashtags: ['test'],
        tone: 'friendly',
        industry: ['technology'],
        isPublic: true,
        usageCount: 5,
        rating: 4.0,
        tags: ['test'],
        createdAt: new Date()
      }
    ]),
    getPopularTemplates: jest.fn().mockReturnValue([]),
    getTemplate: jest.fn().mockReturnValue({
      id: 'test-template',
      name: 'Test Template',
      variables: []
    }),
    processTemplate: jest.fn().mockReturnValue('Processed template content'),
    validateTemplateVariables: jest.fn().mockReturnValue({
      isValid: true,
      errors: []
    })
  }
}));

describe('Content Generation API', () => {
  describe('POST /api/content/generate', () => {
    test('should generate content successfully', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'generate',
          prompt: 'Test prompt',
          platform: 'facebook',
          tone: 'friendly',
          language: 'ar',
          includeHashtags: true,
          includeEmojis: true
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.result).toBeDefined();
      expect(data.result.content).toBe('Generated test content');
      expect(data.result.hashtags).toEqual(['test', 'generated']);
    });

    test('should optimize content for platform', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'optimize',
          content: 'Original content',
          fromPlatform: 'facebook',
          toPlatform: 'twitter',
          language: 'ar'
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.optimizedContent).toBe('Optimized content for target platform');
    });

    test('should generate hashtag suggestions', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'hashtags',
          content: 'Test content for hashtags',
          platform: 'instagram',
          language: 'ar'
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.hashtags).toHaveLength(2);
      expect(data.hashtags[0]).toHaveProperty('tag', 'test');
      expect(data.hashtags[0]).toHaveProperty('popularity', 'high');
    });

    test('should validate request data', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'generate',
          // Missing required fields
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid input');
      expect(data.details).toBeDefined();
    });

    test('should handle invalid action', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'invalid-action'
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid action');
    });

    test('should require authentication', async () => {
      // Mock unauthenticated user
      const mockCreateClient = require('@/lib/supabase/server').createClient;
      mockCreateClient.mockReturnValueOnce({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: new Error('Not authenticated')
          })
        }
      });

      const request = new NextRequest('http://localhost:3000/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          action: 'generate',
          prompt: 'Test prompt',
          platform: 'facebook',
          tone: 'friendly',
          language: 'ar'
        })
      });

      const response = await generateContentPOST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('GET /api/content/generate', () => {
    test('should get usage statistics', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/generate?stats=true');

      const response = await generateContentGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.stats).toBeDefined();
      expect(data.stats).toHaveProperty('totalUsage');
      expect(data.stats).toHaveProperty('byAction');
      expect(data.stats).toHaveProperty('byPlatform');
    });

    test('should require stats parameter', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/generate');

      const response = await generateContentGET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid request');
    });
  });
});

describe('Templates API', () => {
  describe('GET /api/content/templates', () => {
    test('should search templates', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates?action=search&category=promotional&limit=10');

      const response = await templatesGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.templates).toBeDefined();
      expect(Array.isArray(data.templates)).toBe(true);
      expect(data.total).toBeDefined();
      expect(data.hasMore).toBeDefined();
    });

    test('should get popular templates', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates?action=popular&limit=5');

      const response = await templatesGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.templates).toBeDefined();
      expect(Array.isArray(data.templates)).toBe(true);
    });

    test('should get categories', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates?action=categories');

      const response = await templatesGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.categories).toBeDefined();
      expect(Array.isArray(data.categories)).toBe(true);
    });

    test('should get user templates', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates?action=my-templates');

      const response = await templatesGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.templates).toBeDefined();
      expect(Array.isArray(data.templates)).toBe(true);
    });

    test('should handle search with filters', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates?action=search&platform=facebook&language=ar&tone=friendly');

      const response = await templatesGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.templates).toBeDefined();
    });
  });

  describe('POST /api/content/templates', () => {
    test('should process template with variables', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates', {
        method: 'POST',
        body: JSON.stringify({
          action: 'process',
          templateId: 'test-template',
          variables: {
            variable1: 'Test value'
          }
        })
      });

      const response = await templatesPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.content).toBe('Processed template content');
    });

    test('should validate template variables', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates', {
        method: 'POST',
        body: JSON.stringify({
          action: 'validate',
          templateId: 'test-template',
          variables: {
            variable1: 'Test value'
          }
        })
      });

      const response = await templatesPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.validation).toBeDefined();
      expect(data.validation.isValid).toBe(true);
    });

    test('should create custom template', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates', {
        method: 'POST',
        body: JSON.stringify({
          action: 'create',
          name: 'Custom Template',
          description: 'Custom template description',
          category: 'promotional',
          platform: ['facebook'],
          language: 'ar',
          content: 'Custom content {{variable1}}',
          variables: [
            {
              name: 'variable1',
              type: 'text',
              label: 'Variable 1',
              placeholder: 'Enter value',
              required: true
            }
          ],
          hashtags: ['custom'],
          tone: 'friendly',
          industry: ['technology'],
          isPublic: false,
          tags: ['custom']
        })
      });

      const response = await templatesPOST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Template created successfully');
      expect(data.templateId).toBeDefined();
    });

    test('should handle template not found', async () => {
      // Mock template not found
      const mockContentTemplateManager = require('@/lib/templates/content-templates').contentTemplateManager;
      mockContentTemplateManager.getTemplate.mockReturnValueOnce(undefined);

      const request = new NextRequest('http://localhost:3000/api/content/templates', {
        method: 'POST',
        body: JSON.stringify({
          action: 'process',
          templateId: 'non-existent-template',
          variables: {}
        })
      });

      const response = await templatesPOST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Template not found');
    });

    test('should validate template variables and return errors', async () => {
      // Mock validation failure
      const mockContentTemplateManager = require('@/lib/templates/content-templates').contentTemplateManager;
      mockContentTemplateManager.validateTemplateVariables.mockReturnValueOnce({
        isValid: false,
        errors: ['Variable 1 is required']
      });

      const request = new NextRequest('http://localhost:3000/api/content/templates', {
        method: 'POST',
        body: JSON.stringify({
          action: 'process',
          templateId: 'test-template',
          variables: {}
        })
      });

      const response = await templatesPOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid variables');
      expect(data.details).toEqual(['Variable 1 is required']);
    });

    test('should handle invalid action', async () => {
      const request = new NextRequest('http://localhost:3000/api/content/templates', {
        method: 'POST',
        body: JSON.stringify({
          action: 'invalid-action'
        })
      });

      const response = await templatesPOST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid action');
    });
  });
});

describe('Error Handling', () => {
  test('should handle AI service errors', async () => {
    // Mock AI service error
    const mockAiContentGenerator = require('@/lib/ai/content-generator').aiContentGenerator;
    mockAiContentGenerator.generateContent.mockRejectedValueOnce(new Error('AI service error'));

    const request = new NextRequest('http://localhost:3000/api/content/generate', {
      method: 'POST',
      body: JSON.stringify({
        action: 'generate',
        prompt: 'Test prompt',
        platform: 'facebook',
        tone: 'friendly',
        language: 'ar'
      })
    });

    const response = await generateContentPOST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to generate content. Please try again.');
  });

  test('should handle database errors', async () => {
    // Mock database error
    const mockCreateClient = require('@/lib/supabase/server').createClient;
    mockCreateClient.mockReturnValueOnce({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: null },
          error: new Error('Database connection error')
        })
      }
    });

    const request = new NextRequest('http://localhost:3000/api/content/generate', {
      method: 'POST',
      body: JSON.stringify({
        action: 'generate',
        prompt: 'Test prompt',
        platform: 'facebook',
        tone: 'friendly',
        language: 'ar'
      })
    });

    const response = await generateContentPOST(request);
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe('Unauthorized');
  });

  test('should handle malformed JSON requests', async () => {
    const request = new NextRequest('http://localhost:3000/api/content/generate', {
      method: 'POST',
      body: 'invalid json'
    });

    const response = await generateContentPOST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Internal server error');
  });
});
