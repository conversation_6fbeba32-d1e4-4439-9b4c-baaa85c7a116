/**
 * DigitalOcean API Client
 * Provides utilities for interacting with DigitalOcean API
 */

interface DigitalOceanConfig {
  apiToken: string;
  baseUrl?: string;
}

interface DigitalOceanResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
}

interface DropletInfo {
  id: number;
  name: string;
  status: string;
  size_slug: string;
  region: {
    name: string;
    slug: string;
  };
  image: {
    name: string;
    slug: string;
  };
  networks: {
    v4: Array<{
      ip_address: string;
      type: string;
    }>;
  };
  created_at: string;
}

interface AppInfo {
  id: string;
  spec: {
    name: string;
    region: string;
  };
  live_url?: string;
  default_ingress?: string;
  created_at: string;
  updated_at: string;
  tier_slug: string;
  last_deployment_created_at?: string;
}

interface AccountInfo {
  email: string;
  uuid: string;
  email_verified: boolean;
  status: string;
  status_message: string;
  droplet_limit: number;
  floating_ip_limit: number;
  volume_limit: number;
  team?: {
    name: string;
    uuid: string;
  };
}

export class DigitalOceanClient {
  private config: DigitalOceanConfig;

  constructor(config: DigitalOceanConfig) {
    this.config = {
      baseUrl: 'https://api.digitalocean.com/v2',
      ...config
    };
  }

  /**
   * Make authenticated request to DigitalOcean API
   */
  private async makeRequest<T = any>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any
  ): Promise<DigitalOceanResponse<T>> {
    try {
      const url = `${this.config.baseUrl}${endpoint}`;
      
      const headers: Record<string, string> = {
        'Authorization': `Bearer ${this.config.apiToken}`,
        'Content-Type': 'application/json',
      };

      const requestOptions: RequestInit = {
        method,
        headers,
      };

      if (body && method !== 'GET') {
        requestOptions.body = JSON.stringify(body);
      }

      const response = await fetch(url, requestOptions);
      const data = await response.json();

      if (response.ok) {
        return {
          success: true,
          data,
          status: response.status
        };
      } else {
        return {
          success: false,
          error: data.message || 'Unknown error',
          status: response.status
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  /**
   * Get account information
   */
  async getAccount(): Promise<DigitalOceanResponse<{ account: AccountInfo }>> {
    return this.makeRequest('/account');
  }

  /**
   * List all droplets
   */
  async getDroplets(): Promise<DigitalOceanResponse<{ droplets: DropletInfo[] }>> {
    return this.makeRequest('/droplets');
  }

  /**
   * List all apps
   */
  async getApps(): Promise<DigitalOceanResponse<{ apps: AppInfo[] }>> {
    return this.makeRequest('/apps');
  }

  /**
   * Get specific app by ID
   */
  async getApp(appId: string): Promise<DigitalOceanResponse<{ app: AppInfo }>> {
    return this.makeRequest(`/apps/${appId}`);
  }

  /**
   * List domains
   */
  async getDomains(): Promise<DigitalOceanResponse<{ domains: any[] }>> {
    return this.makeRequest('/domains');
  }

  /**
   * Get app deployments
   */
  async getAppDeployments(appId: string): Promise<DigitalOceanResponse<{ deployments: any[] }>> {
    return this.makeRequest(`/apps/${appId}/deployments`);
  }

  /**
   * Create a new deployment for an app
   */
  async createAppDeployment(appId: string, forceRebuild: boolean = false): Promise<DigitalOceanResponse<{ deployment: any }>> {
    return this.makeRequest(`/apps/${appId}/deployments`, 'POST', {
      force_rebuild: forceRebuild
    });
  }

  /**
   * Health check - verify API connectivity
   */
  async healthCheck(): Promise<DigitalOceanResponse<{ healthy: boolean; account: AccountInfo }>> {
    const accountResponse = await this.getAccount();
    
    if (accountResponse.success && accountResponse.data) {
      return {
        success: true,
        data: {
          healthy: true,
          account: accountResponse.data.account
        }
      };
    } else {
      return {
        success: false,
        error: accountResponse.error || 'Health check failed'
      };
    }
  }
}

/**
 * Create DigitalOcean client instance
 */
export function createDigitalOceanClient(): DigitalOceanClient {
  const apiToken = process.env.DIGITALOCEAN_API_TOKEN;
  
  if (!apiToken) {
    throw new Error('DIGITALOCEAN_API_TOKEN environment variable is required');
  }

  return new DigitalOceanClient({ apiToken });
}

/**
 * Default export for convenience
 */
export default DigitalOceanClient;
