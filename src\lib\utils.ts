import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string) {
  return new Date(date).toLocaleDateString("ar-SA", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });
}

export function formatTime(date: Date | string) {
  return new Date(date).toLocaleTimeString("ar-SA", {
    hour: "2-digit",
    minute: "2-digit",
  });
}

export function truncateText(text: string, maxLength: number) {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
}

export function getStatusLabel(status: string) {
  switch (status) {
    case "DRAFT":
      return "مسودة";
    case "SCHEDULED":
      return "مجدول";
    case "PUBLISHED":
      return "منشور";
    case "FAILED":
      return "فشل";
    default:
      return status;
  }
}

export function getPlatformLabel(platform: string) {
  switch (platform) {
    case "TWITTER":
      return "تويتر";
    case "FACEBOOK":
      return "فيسبوك";
    case "INSTAGRAM":
      return "انستغرام";
    case "LINKEDIN":
      return "لينكد إن";
    case "TIKTOK":
      return "تيك توك";
    default:
      return platform;
  }
}
