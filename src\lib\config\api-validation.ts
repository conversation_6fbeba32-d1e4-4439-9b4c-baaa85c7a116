/**
 * API Configuration Validation System
 * Validates and tests all social media API connections
 */

export interface APIValidationResult {
  platform: string;
  isConfigured: boolean;
  isValid: boolean;
  error?: string;
  details?: Record<string, any>;
}

export interface APIConfiguration {
  twitter: {
    apiKey: string;
    apiSecret: string;
    bearerToken: string;
  };
  facebook: {
    appId: string;
    appSecret: string;
  };
  linkedin: {
    clientId: string;
    clientSecret: string;
  };
}

/**
 * Get API configuration from environment variables
 */
export function getAPIConfiguration(): APIConfiguration {
  return {
    twitter: {
      apiKey: process.env.TWITTER_API_KEY || '',
      apiSecret: process.env.TWITTER_API_SECRET || '',
      bearerToken: process.env.TWITTER_BEARER_TOKEN || '',
    },
    facebook: {
      appId: process.env.FACEBOOK_APP_ID || '',
      appSecret: process.env.FACEBOOK_APP_SECRET || '',
    },
    linkedin: {
      clientId: process.env.LINKEDIN_CLIENT_ID || '',
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET || '',
    },
  };
}

/**
 * Check if API keys are configured (not placeholder values)
 */
export function validateAPIConfiguration(): APIValidationResult[] {
  const config = getAPIConfiguration();
  const results: APIValidationResult[] = [];

  // Twitter validation
  const twitterConfigured: boolean = !!(
    config.twitter.apiKey &&
    config.twitter.apiSecret &&
    config.twitter.bearerToken &&
    !config.twitter.apiSecret.includes('your_') &&
    !config.twitter.bearerToken.includes('your_')
  );

  results.push({
    platform: 'Twitter',
    isConfigured: twitterConfigured,
    isValid: false, // Will be tested separately
    details: {
      hasApiKey: !!config.twitter.apiKey,
      hasApiSecret: !!config.twitter.apiSecret && !config.twitter.apiSecret.includes('your_'),
      hasBearerToken: !!config.twitter.bearerToken && !config.twitter.bearerToken.includes('your_'),
    }
  });

  // Facebook validation
  const facebookConfigured: boolean = !!(
    config.facebook.appId &&
    config.facebook.appSecret &&
    !config.facebook.appSecret.includes('your_')
  );

  results.push({
    platform: 'Facebook',
    isConfigured: facebookConfigured,
    isValid: false,
    details: {
      hasAppId: !!config.facebook.appId,
      hasAppSecret: !!config.facebook.appSecret && !config.facebook.appSecret.includes('your_'),
    }
  });

  // LinkedIn validation
  const linkedinConfigured: boolean = !!(
    config.linkedin.clientId &&
    config.linkedin.clientSecret &&
    !config.linkedin.clientSecret.includes('your_')
  );

  results.push({
    platform: 'LinkedIn',
    isConfigured: linkedinConfigured,
    isValid: false,
    details: {
      hasClientId: !!config.linkedin.clientId,
      hasClientSecret: !!config.linkedin.clientSecret && !config.linkedin.clientSecret.includes('your_'),
    }
  });

  return results;
}

/**
 * Test Twitter API connection
 */
export async function testTwitterAPI(): Promise<APIValidationResult> {
  try {
    const config = getAPIConfiguration();

    if (!config.twitter.bearerToken || config.twitter.bearerToken.includes('your_')) {
      return {
        platform: 'Twitter',
        isConfigured: false,
        isValid: false,
        error: 'Bearer token not configured'
      };
    }

    // Test Twitter API v2 with bearer token
    const response = await fetch('https://api.twitter.com/2/users/me', {
      headers: {
        'Authorization': `Bearer ${config.twitter.bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return {
        platform: 'Twitter',
        isConfigured: true,
        isValid: true,
        details: {
          userId: data.data?.id,
          username: data.data?.username,
          name: data.data?.name,
        }
      };
    } else {
      const errorData = await response.json().catch(() => ({}));
      return {
        platform: 'Twitter',
        isConfigured: true,
        isValid: false,
        error: `API Error: ${response.status} - ${errorData.title || 'Unknown error'}`,
        details: errorData
      };
    }
  } catch (error) {
    return {
      platform: 'Twitter',
      isConfigured: true,
      isValid: false,
      error: `Connection Error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Test Facebook API connection
 */
export async function testFacebookAPI(): Promise<APIValidationResult> {
  try {
    const config = getAPIConfiguration();

    if (!config.facebook.appId || !config.facebook.appSecret || config.facebook.appSecret.includes('your_')) {
      return {
        platform: 'Facebook',
        isConfigured: false,
        isValid: false,
        error: 'App ID or App Secret not configured'
      };
    }

    // Get app access token
    const tokenResponse = await fetch(
      `https://graph.facebook.com/oauth/access_token?client_id=${config.facebook.appId}&client_secret=${config.facebook.appSecret}&grant_type=client_credentials`
    );

    if (tokenResponse.ok) {
      const tokenData = await tokenResponse.json();

      // Test the token by getting app info
      const appResponse = await fetch(
        `https://graph.facebook.com/${config.facebook.appId}?access_token=${tokenData.access_token}&fields=name,category`
      );

      if (appResponse.ok) {
        const appData = await appResponse.json();
        return {
          platform: 'Facebook',
          isConfigured: true,
          isValid: true,
          details: {
            appId: config.facebook.appId,
            appName: appData.name,
            category: appData.category,
          }
        };
      } else {
        const errorData = await appResponse.json().catch(() => ({}));
        return {
          platform: 'Facebook',
          isConfigured: true,
          isValid: false,
          error: `App API Error: ${appResponse.status} - ${errorData.error?.message || 'Unknown error'}`,
          details: errorData
        };
      }
    } else {
      const errorData = await tokenResponse.json().catch(() => ({}));
      return {
        platform: 'Facebook',
        isConfigured: true,
        isValid: false,
        error: `Token Error: ${tokenResponse.status} - ${errorData.error?.message || 'Unknown error'}`,
        details: errorData
      };
    }
  } catch (error) {
    return {
      platform: 'Facebook',
      isConfigured: true,
      isValid: false,
      error: `Connection Error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Test LinkedIn API connection
 */
export async function testLinkedInAPI(): Promise<APIValidationResult> {
  try {
    const config = getAPIConfiguration();

    if (!config.linkedin.clientId || !config.linkedin.clientSecret || config.linkedin.clientSecret.includes('your_')) {
      return {
        platform: 'LinkedIn',
        isConfigured: false,
        isValid: false,
        error: 'Client ID or Client Secret not configured'
      };
    }

    // For LinkedIn, we can only test if the client credentials are valid format
    // Full API testing requires user OAuth tokens
    const clientIdValid = /^[a-zA-Z0-9]{10,}$/.test(config.linkedin.clientId);
    const clientSecretValid = config.linkedin.clientSecret.length >= 16;

    if (clientIdValid && clientSecretValid) {
      return {
        platform: 'LinkedIn',
        isConfigured: true,
        isValid: true,
        details: {
          clientId: config.linkedin.clientId,
          note: 'Credentials format valid. Full API testing requires user authentication.'
        }
      };
    } else {
      return {
        platform: 'LinkedIn',
        isConfigured: true,
        isValid: false,
        error: 'Invalid credential format',
        details: {
          clientIdValid,
          clientSecretValid,
        }
      };
    }
  } catch (error) {
    return {
      platform: 'LinkedIn',
      isConfigured: true,
      isValid: false,
      error: `Validation Error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Test all API connections
 */
export async function testAllAPIs(): Promise<APIValidationResult[]> {
  const results = await Promise.all([
    testTwitterAPI(),
    testFacebookAPI(),
    testLinkedInAPI(),
  ]);

  return results;
}

/**
 * Generate API configuration report
 */
export async function generateAPIReport(): Promise<{
  summary: {
    totalPlatforms: number;
    configured: number;
    valid: number;
    issues: number;
  };
  results: APIValidationResult[];
  recommendations: string[];
}> {
  const configValidation = validateAPIConfiguration();
  const apiTests = await testAllAPIs();

  // Merge configuration and test results
  const results = configValidation.map(config => {
    const test = apiTests.find(t => t.platform === config.platform);
    return {
      ...config,
      isValid: test?.isValid || false,
      error: test?.error,
      details: { ...config.details, ...test?.details }
    };
  });

  const summary = {
    totalPlatforms: results.length,
    configured: results.filter(r => r.isConfigured).length,
    valid: results.filter(r => r.isValid).length,
    issues: results.filter(r => !r.isValid).length,
  };

  const recommendations: string[] = [];

  results.forEach(result => {
    if (!result.isConfigured) {
      recommendations.push(`Configure ${result.platform} API credentials in environment variables`);
    } else if (!result.isValid) {
      recommendations.push(`Fix ${result.platform} API configuration: ${result.error}`);
    }
  });

  if (summary.valid === 0) {
    recommendations.push('CRITICAL: No social media APIs are working. Publishing will fail.');
  } else if (summary.valid < summary.totalPlatforms) {
    recommendations.push('Some social media APIs are not working. Users may experience limited functionality.');
  }

  return {
    summary,
    results,
    recommendations
  };
}
