const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function fixDatabaseStructure() {
  console.log('🔧 Fixing Database Structure...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing required environment variables');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  console.log('1️⃣ Adding missing column to social_accounts table...');
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE social_accounts 
        ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
        
        -- Update existing records to be active
        UPDATE social_accounts SET is_active = true WHERE is_active IS NULL;
      `
    });

    if (error) {
      console.log(`⚠️  Column addition: ${error.message}`);
    } else {
      console.log('✅ Added is_active column to social_accounts');
    }
  } catch (error) {
    console.log(`⚠️  Column addition failed: ${error.message}`);
  }

  console.log('\n2️⃣ Creating analytics_data table...');
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS analytics_data (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          platform TEXT NOT NULL,
          post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
          metric_type TEXT NOT NULL,
          metric_value NUMERIC DEFAULT 0,
          date_recorded DATE DEFAULT CURRENT_DATE,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_analytics_user_id ON analytics_data(user_id);
        CREATE INDEX IF NOT EXISTS idx_analytics_platform ON analytics_data(platform);
        CREATE INDEX IF NOT EXISTS idx_analytics_date ON analytics_data(date_recorded);
        CREATE INDEX IF NOT EXISTS idx_analytics_post_id ON analytics_data(post_id);
      `
    });

    if (error) {
      console.log(`⚠️  Analytics table creation: ${error.message}`);
    } else {
      console.log('✅ Created analytics_data table');
    }
  } catch (error) {
    console.log(`⚠️  Analytics table creation failed: ${error.message}`);
  }

  console.log('\n3️⃣ Creating oauth_states table...');
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS oauth_states (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          state TEXT UNIQUE NOT NULL,
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          platform TEXT NOT NULL,
          redirect_url TEXT,
          expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '1 hour'),
          created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_oauth_states_state ON oauth_states(state);
        CREATE INDEX IF NOT EXISTS idx_oauth_states_expires_at ON oauth_states(expires_at);
        CREATE INDEX IF NOT EXISTS idx_oauth_states_user_id ON oauth_states(user_id);
      `
    });

    if (error) {
      console.log(`⚠️  OAuth states table creation: ${error.message}`);
    } else {
      console.log('✅ Created oauth_states table');
    }
  } catch (error) {
    console.log(`⚠️  OAuth states table creation failed: ${error.message}`);
  }

  console.log('\n4️⃣ Setting up RLS policies...');
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on all tables
        ALTER TABLE users ENABLE ROW LEVEL SECURITY;
        ALTER TABLE social_accounts ENABLE ROW LEVEL SECURITY;
        ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
        ALTER TABLE post_social_accounts ENABLE ROW LEVEL SECURITY;
        ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
        ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE analytics_data ENABLE ROW LEVEL SECURITY;
        ALTER TABLE content_templates ENABLE ROW LEVEL SECURITY;
        ALTER TABLE oauth_states ENABLE ROW LEVEL SECURITY;

        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view own profile" ON users;
        DROP POLICY IF EXISTS "Users can update own profile" ON users;
        DROP POLICY IF EXISTS "Users can view own social accounts" ON social_accounts;
        DROP POLICY IF EXISTS "Users can manage own social accounts" ON social_accounts;
        DROP POLICY IF EXISTS "Users can view own posts" ON posts;
        DROP POLICY IF EXISTS "Users can manage own posts" ON posts;
        DROP POLICY IF EXISTS "Users can view own analytics" ON analytics_data;
        DROP POLICY IF EXISTS "Users can manage own analytics" ON analytics_data;

        -- Create RLS policies
        CREATE POLICY "Users can view own profile" ON users
          FOR SELECT USING (auth.uid() = id);
        
        CREATE POLICY "Users can update own profile" ON users
          FOR UPDATE USING (auth.uid() = id);

        CREATE POLICY "Users can view own social accounts" ON social_accounts
          FOR SELECT USING (auth.uid() = user_id);
        
        CREATE POLICY "Users can manage own social accounts" ON social_accounts
          FOR ALL USING (auth.uid() = user_id);

        CREATE POLICY "Users can view own posts" ON posts
          FOR SELECT USING (auth.uid() = user_id);
        
        CREATE POLICY "Users can manage own posts" ON posts
          FOR ALL USING (auth.uid() = user_id);

        CREATE POLICY "Users can view own analytics" ON analytics_data
          FOR SELECT USING (auth.uid() = user_id);
        
        CREATE POLICY "Users can manage own analytics" ON analytics_data
          FOR ALL USING (auth.uid() = user_id);
      `
    });

    if (error) {
      console.log(`⚠️  RLS setup: ${error.message}`);
    } else {
      console.log('✅ Set up RLS policies');
    }
  } catch (error) {
    console.log(`⚠️  RLS setup failed: ${error.message}`);
  }

  console.log('\n🎯 Database Structure Fix Complete!');
}

// Run the fix
fixDatabaseStructure().catch(console.error);
