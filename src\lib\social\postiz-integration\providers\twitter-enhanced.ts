/**
 * Enhanced Twitter/X Provider
 * Uses Twitter API v2 with OAuth 2.0 PKCE
 */

import { 
  AuthTokenDetails, 
  PostDetails, 
  PostResponse, 
  SocialProvider,
  GenerateAuthUrlResponse,
  MediaContent
} from '../interfaces';
import { SocialAbstract } from '../social-abstract';
import * as crypto from 'crypto';

export class TwitterEnhancedProvider extends SocialAbstract implements SocialProvider {
  identifier = 'twitter';
  name = 'Twitter/X';
  isBetweenSteps = false;
  scopes = [
    'tweet.read',
    'tweet.write',
    'users.read',
    'media.upload',
    'offline.access',
  ];

  async refreshToken(refresh_token: string): Promise<AuthTokenDetails> {
    try {
      const response = await this.fetch('https://api.twitter.com/2/oauth2/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(
            `${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`
          ).toString('base64')}`,
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token,
        }),
      });

      const {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in,
      } = await response.json();

      // Get user information
      const userResponse = await this.fetch('https://api.twitter.com/2/users/me?user.fields=profile_image_url,username', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const { data: user } = await userResponse.json();

      return {
        id: user.id,
        accessToken,
        refreshToken,
        expiresIn: expires_in,
        name: user.name,
        picture: user.profile_image_url,
        username: user.username,
      };
    } catch (error) {
      console.error('[TwitterEnhanced] Refresh token error:', error);
      throw new Error(`Failed to refresh Twitter token: ${error instanceof Error ? error.message : error}`);
    }
  }

  async generateAuthUrl(): Promise<GenerateAuthUrlResponse> {
    const state = this.makeId(32);
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = this.generateCodeChallenge(codeVerifier);
    
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: process.env.TWITTER_CLIENT_ID!,
      redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/social/callback/twitter`,
      scope: this.scopes.join(' '),
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    });

    const url = `https://twitter.com/i/oauth2/authorize?${params.toString()}`;

    return {
      url,
      codeVerifier,
      state,
    };
  }

  async authenticate(params: {
    code: string;
    codeVerifier: string;
    refresh?: string;
  }): Promise<AuthTokenDetails> {
    try {
      const response = await this.fetch('https://api.twitter.com/2/oauth2/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(
            `${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`
          ).toString('base64')}`,
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code: params.code,
          redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/social/callback/twitter${
            params.refresh ? `?refresh=${params.refresh}` : ''
          }`,
          code_verifier: params.codeVerifier,
        }),
      });

      const {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: expiresIn,
        scope,
      } = await response.json();

      this.checkScopes(this.scopes, scope.split(' '));

      // Get user information
      const userResponse = await this.fetch('https://api.twitter.com/2/users/me?user.fields=profile_image_url,username', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const { data: user } = await userResponse.json();

      return {
        id: user.id,
        accessToken,
        refreshToken,
        expiresIn,
        name: user.name,
        picture: user.profile_image_url,
        username: user.username,
      };
    } catch (error) {
      console.error('[TwitterEnhanced] Authentication error:', error);
      throw new Error(`Twitter authentication failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  async post(
    id: string,
    accessToken: string,
    postDetails: PostDetails[],
    integration: any
  ): Promise<PostResponse[]> {
    try {
      const [firstPost, ...threadPosts] = postDetails;
      const results: PostResponse[] = [];

      // Handle media uploads if present
      let mediaIds: string[] = [];
      if (firstPost.media?.length) {
        mediaIds = await this.uploadMedia(firstPost.media, accessToken);
      }

      // Create the main tweet
      const tweetData: any = {
        text: firstPost.message,
      };

      if (mediaIds.length > 0) {
        tweetData.media = { media_ids: mediaIds };
      }

      const response = await this.fetch('https://api.twitter.com/2/tweets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(tweetData),
      });

      const { data: tweet } = await response.json();
      
      results.push({
        id: firstPost.id,
        postId: tweet.id,
        releaseURL: `https://twitter.com/i/web/status/${tweet.id}`,
        status: 'posted',
      });

      // Handle thread posts
      let replyToId = tweet.id;
      for (const threadPost of threadPosts) {
        try {
          const threadResponse = await this.fetch('https://api.twitter.com/2/tweets', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify({
              text: threadPost.message,
              reply: {
                in_reply_to_tweet_id: replyToId,
              },
            }),
          });

          const { data: threadTweet } = await threadResponse.json();
          replyToId = threadTweet.id;
          
          results.push({
            id: threadPost.id,
            postId: threadTweet.id,
            releaseURL: `https://twitter.com/i/web/status/${threadTweet.id}`,
            status: 'posted',
          });
        } catch (error) {
          console.error('[TwitterEnhanced] Thread post error:', error);
          results.push({
            id: threadPost.id,
            postId: '',
            releaseURL: '',
            status: 'failed',
          });
        }
      }

      return results;
    } catch (error) {
      console.error('[TwitterEnhanced] Post error:', error);
      throw new Error(`Twitter posting failed: ${error instanceof Error ? error.message : error}`);
    }
  }

  private async uploadMedia(media: MediaContent[], accessToken: string): Promise<string[]> {
    const mediaIds: string[] = [];

    for (const item of media) {
      try {
        console.log('[TwitterEnhanced] Uploading media:', item.url);

        // Download media from URL
        const response = await fetch(item.url);
        if (!response.ok) {
          throw new Error(`Failed to fetch media: ${response.statusText}`);
        }

        const buffer = await response.arrayBuffer();
        const contentType = response.headers.get('content-type') || 'image/jpeg';

        // Upload to Twitter using media upload API
        const uploadResponse = await this.fetch('https://upload.twitter.com/1.1/media/upload.json', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
          body: new FormData().append('media', new Blob([buffer], { type: contentType })),
        });

        const uploadResult = await uploadResponse.json();
        if (uploadResult.media_id_string) {
          mediaIds.push(uploadResult.media_id_string);
          console.log('[TwitterEnhanced] Media uploaded successfully:', uploadResult.media_id_string);
        }
      } catch (error) {
        console.error('[TwitterEnhanced] Media upload error:', error);
        // Continue without this media item
      }
    }

    return mediaIds;
  }

  private generateCodeVerifier(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  private generateCodeChallenge(verifier: string): string {
    return crypto.createHash('sha256').update(verifier).digest('base64url');
  }

  async getTweetMetrics(tweetId: string, accessToken: string): Promise<any> {
    try {
      const response = await this.fetch(
        `https://api.twitter.com/2/tweets/${tweetId}?tweet.fields=public_metrics,created_at`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      return await response.json();
    } catch (error) {
      console.error('[TwitterEnhanced] Get tweet metrics error:', error);
      return null;
    }
  }

  async getUserMetrics(userId: string, accessToken: string): Promise<any> {
    try {
      const response = await this.fetch(
        `https://api.twitter.com/2/users/${userId}?user.fields=public_metrics`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      return await response.json();
    } catch (error) {
      console.error('[TwitterEnhanced] Get user metrics error:', error);
      return null;
    }
  }
}
