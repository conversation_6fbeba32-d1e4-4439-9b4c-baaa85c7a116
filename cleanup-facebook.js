// Direct Facebook account cleanup script
// This script will remove the corrupted Facebook accounts from the database

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseServiceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc';

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function cleanupFacebookAccounts() {
  console.log('🧹 Starting Facebook account cleanup...');
  
  try {
    // Target corrupted Facebook account IDs
    const corruptedAccountIds = [
      'a1b6549a-38c9-4f9a-ad56-0bd26a5a24d7', // "@eWasl Page"
      'a26762d6-9d99-46f8-9be5-a80c50855da0'  // "Test Facebook Account"
    ];
    
    console.log(`Targeting ${corruptedAccountIds.length} corrupted Facebook accounts...`);
    
    // Get account details before deletion
    const { data: accountsToDelete, error: fetchError } = await supabase
      .from('social_accounts')
      .select('*')
      .in('id', corruptedAccountIds);
    
    if (fetchError) {
      throw new Error(`Failed to fetch accounts: ${fetchError.message}`);
    }
    
    console.log(`Found ${accountsToDelete?.length || 0} accounts to delete:`);
    accountsToDelete?.forEach(acc => {
      console.log(`- ${acc.account_name} (${acc.platform}) - ID: ${acc.id}`);
    });
    
    // Delete the accounts
    const { error: deleteError } = await supabase
      .from('social_accounts')
      .delete()
      .in('id', corruptedAccountIds);
    
    if (deleteError) {
      throw new Error(`Failed to delete accounts: ${deleteError.message}`);
    }
    
    console.log('✅ Successfully deleted corrupted Facebook accounts');
    
    // Verify deletion
    const { data: remainingAccounts, error: verifyError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', '3ddaeb03-2d95-4fff-abad-2a2c7dd25037')
      .eq('platform', 'FACEBOOK');
    
    if (verifyError) {
      console.warn('Warning: Could not verify deletion:', verifyError.message);
    } else {
      console.log(`✅ Verification: ${remainingAccounts?.length || 0} Facebook accounts remaining`);
      if (remainingAccounts?.length > 0) {
        remainingAccounts.forEach(acc => {
          console.log(`- Remaining: ${acc.account_name} (${acc.id})`);
        });
      }
    }
    
    // Log cleanup activity
    const { error: logError } = await supabase
      .from('activities')
      .insert({
        user_id: '3ddaeb03-2d95-4fff-abad-2a2c7dd25037',
        action: 'FACEBOOK_ACCOUNTS_DIRECT_CLEANUP',
        metadata: {
          deletedAccountIds: corruptedAccountIds,
          deletedCount: accountsToDelete?.length || 0,
          timestamp: new Date().toISOString(),
        },
      });
    
    if (logError) {
      console.warn('Warning: Could not log activity:', logError.message);
    }
    
    console.log('🎉 Facebook account cleanup completed successfully!');
    console.log('Next steps:');
    console.log('1. Access Facebook Developer Console');
    console.log('2. Re-authenticate Facebook accounts');
    console.log('3. Test Facebook posting functionality');
    
  } catch (error) {
    console.error('❌ Facebook cleanup failed:', error.message);
    process.exit(1);
  }
}

// Execute cleanup
cleanupFacebookAccounts();
