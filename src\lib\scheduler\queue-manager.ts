import { SchedulerLogger } from './scheduler-logger';

export interface JobData {
  [key: string]: any;
}

export interface JobOptions {
  delay?: number; // Delay in milliseconds
  priority?: number; // Higher number = higher priority
  attempts?: number; // Number of retry attempts
}

export interface Job {
  id: string;
  type: string;
  data: JobData;
  options: JobOptions;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  error?: string;
  attempts: number;
}

/**
 * In-memory queue manager for job processing
 * In production, this should be replaced with Redis or similar
 */
export class QueueManager {
  private jobs: Map<string, Job> = new Map();
  private processingJobs: Set<string> = new Set();
  private logger: SchedulerLogger;
  private isInitialized: boolean = false;
  private processingInterval?: NodeJS.Timeout;
  private jobIdCounter: number = 0;

  constructor() {
    this.logger = new SchedulerLogger();
  }

  /**
   * Initialize the queue manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('Queue manager is already initialized');
      return;
    }

    this.isInitialized = true;
    this.logger.info('Initializing queue manager...');

    // Start job processing
    this.processingInterval = setInterval(async () => {
      await this.processJobs();
    }, 5000); // Process jobs every 5 seconds

    this.logger.info('Queue manager initialized successfully');
  }

  /**
   * Cleanup the queue manager
   */
  async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    this.logger.info('Cleaning up queue manager...');

    // Stop processing
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }

    // Wait for processing jobs to complete (with timeout)
    const timeout = 30000; // 30 seconds
    const startTime = Date.now();

    while (this.processingJobs.size > 0 && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (this.processingJobs.size > 0) {
      this.logger.warn(`${this.processingJobs.size} jobs still processing after timeout`);
    }

    this.isInitialized = false;
    this.logger.info('Queue manager cleanup completed');
  }

  /**
   * Add a job to the queue
   */
  async addJob(type: string, data: JobData, options: JobOptions = {}): Promise<string> {
    const jobId = this.generateJobId();
    const now = new Date();

    const job: Job = {
      id: jobId,
      type,
      data,
      options: {
        delay: 0,
        priority: 0,
        attempts: 3,
        ...options,
      },
      status: 'pending',
      createdAt: now,
      attempts: 0,
    };

    this.jobs.set(jobId, job);
    this.logger.info(`Job ${jobId} (${type}) added to queue`);

    return jobId;
  }

  /**
   * Process pending jobs
   */
  private async processJobs(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      // Get pending jobs that are ready to process
      const pendingJobs = Array.from(this.jobs.values())
        .filter(job => 
          job.status === 'pending' && 
          !this.processingJobs.has(job.id) &&
          this.isJobReady(job)
        )
        .sort((a, b) => (b.options.priority || 0) - (a.options.priority || 0)) // Higher priority first
        .slice(0, 10); // Process up to 10 jobs concurrently

      for (const job of pendingJobs) {
        await this.processJob(job);
      }

    } catch (error) {
      this.logger.error('Error in processJobs:', error);
    }
  }

  /**
   * Check if a job is ready to be processed
   */
  private isJobReady(job: Job): boolean {
    const delay = job.options.delay || 0;
    const readyTime = new Date(job.createdAt.getTime() + delay);
    return new Date() >= readyTime;
  }

  /**
   * Process a single job
   */
  private async processJob(job: Job): Promise<void> {
    this.processingJobs.add(job.id);
    
    try {
      this.logger.info(`Processing job ${job.id} (${job.type})`);

      // Update job status
      job.status = 'processing';
      job.processedAt = new Date();
      job.attempts++;

      // Process the job based on its type
      await this.executeJob(job);

      // Mark job as completed
      job.status = 'completed';
      job.completedAt = new Date();

      this.logger.info(`Job ${job.id} completed successfully`);

    } catch (error) {
      this.logger.error(`Job ${job.id} failed:`, error);

      job.error = error instanceof Error ? error.message : 'Unknown error';

      // Check if we should retry
      if (job.attempts < (job.options.attempts || 3)) {
        job.status = 'pending';
        // Add exponential backoff delay
        const backoffDelay = Math.min(60000 * Math.pow(2, job.attempts - 1), 600000); // Max 10 minutes
        job.options.delay = backoffDelay;
        job.createdAt = new Date(); // Reset creation time for delay calculation

        this.logger.info(`Job ${job.id} will be retried in ${backoffDelay / 1000} seconds (attempt ${job.attempts})`);
      } else {
        job.status = 'failed';
        job.completedAt = new Date();
        this.logger.error(`Job ${job.id} failed permanently after ${job.attempts} attempts`);
      }

    } finally {
      this.processingJobs.delete(job.id);
    }
  }

  /**
   * Execute a job based on its type
   */
  private async executeJob(job: Job): Promise<void> {
    switch (job.type) {
      case 'publish-post':
        // Import the scheduler engine dynamically to avoid circular dependency
        const { SchedulerEngine } = await import('./scheduler-engine');
        const scheduler = new SchedulerEngine();
        await scheduler.publishPost(job.data);
        break;

      case 'generate-recurring-posts':
        // Import the recurring manager dynamically
        const { RecurringManager } = await import('../scheduling/recurring-manager');
        const recurringManager = new RecurringManager();
        await recurringManager.generateNextBatch(job.data.recurringPostId, job.data.days || 30);
        break;

      case 'process-bulk-import':
        // Import the bulk scheduler dynamically
        const { BulkScheduler } = await import('../scheduling/bulk-scheduler');
        const bulkScheduler = new BulkScheduler();
        await bulkScheduler.processBulkSchedule(job.data.operationId);
        break;

      default:
        throw new Error(`Unknown job type: ${job.type}`);
    }
  }

  /**
   * Get job by ID
   */
  getJob(jobId: string): Job | undefined {
    return this.jobs.get(jobId);
  }

  /**
   * Get jobs by status
   */
  getJobsByStatus(status: Job['status']): Job[] {
    return Array.from(this.jobs.values()).filter(job => job.status === status);
  }

  /**
   * Get queue status
   */
  getStatus(): {
    totalJobs: number;
    pendingJobs: number;
    processingJobs: number;
    completedJobs: number;
    failedJobs: number;
    isInitialized: boolean;
  } {
    const jobs = Array.from(this.jobs.values());
    
    return {
      totalJobs: jobs.length,
      pendingJobs: jobs.filter(j => j.status === 'pending').length,
      processingJobs: jobs.filter(j => j.status === 'processing').length,
      completedJobs: jobs.filter(j => j.status === 'completed').length,
      failedJobs: jobs.filter(j => j.status === 'failed').length,
      isInitialized: this.isInitialized,
    };
  }

  /**
   * Remove completed jobs older than specified time
   */
  cleanupOldJobs(maxAge: number = 24 * 60 * 60 * 1000): number { // Default 24 hours
    const cutoffTime = new Date(Date.now() - maxAge);
    let removedCount = 0;

    for (const [jobId, job] of this.jobs.entries()) {
      if (
        (job.status === 'completed' || job.status === 'failed') &&
        job.completedAt &&
        job.completedAt < cutoffTime
      ) {
        this.jobs.delete(jobId);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      this.logger.info(`Cleaned up ${removedCount} old jobs`);
    }

    return removedCount;
  }

  /**
   * Cancel a pending job
   */
  cancelJob(jobId: string): boolean {
    const job = this.jobs.get(jobId);
    
    if (!job) {
      return false;
    }

    if (job.status === 'pending') {
      job.status = 'failed';
      job.error = 'Job cancelled';
      job.completedAt = new Date();
      this.logger.info(`Job ${jobId} cancelled`);
      return true;
    }

    return false;
  }

  /**
   * Generate unique job ID
   */
  private generateJobId(): string {
    this.jobIdCounter++;
    return `job_${Date.now()}_${this.jobIdCounter}`;
  }

  /**
   * Get recent job history
   */
  getRecentJobs(limit: number = 50): Job[] {
    return Array.from(this.jobs.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }
}
