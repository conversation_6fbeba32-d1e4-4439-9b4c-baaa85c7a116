'use client';

import { useState } from 'react';

export default function TestXPage() {
  const [authUrl, setAuthUrl] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const generateAuthUrl = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/social/oauth-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: 'x',
          action: 'test-auth-url'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setAuthUrl(data.authUrl);
      } else {
        setError(data.error || 'Failed to generate authorization URL');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error generating auth URL:', err);
    } finally {
      setLoading(false);
    }
  };

  const startOAuth = () => {
    if (authUrl) {
      window.location.href = authUrl;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            🐦 X (Twitter) OAuth Test
          </h1>
        </div>

        <div className="bg-white shadow rounded-lg p-6 space-y-6">
          {/* Generate Auth URL */}
          <div>
            <button
              onClick={generateAuthUrl}
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50"
            >
              {loading ? 'Generating...' : 'Generate Authorization URL'}
            </button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-sm text-red-700">
                <strong>Error:</strong> {error}
              </div>
            </div>
          )}

          {/* Auth URL Display */}
          {authUrl && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <h3 className="font-medium text-green-800 mb-2">Authorization URL Generated!</h3>
                <div className="text-sm text-green-700 break-all">
                  {authUrl}
                </div>
              </div>

              <button
                onClick={startOAuth}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                🚀 Start X OAuth
              </button>
            </div>
          )}

          {/* OAuth Flow Steps */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="font-medium text-blue-800 mb-2">OAuth Flow Steps</h3>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Click "Generate Authorization URL"</li>
              <li>Click "Start X OAuth" to begin authorization</li>
              <li>Sign in to your X account</li>
              <li>Authorize the application</li>
              <li>You'll be redirected back with the result</li>
            </ol>
          </div>

          {/* Test Information */}
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <h3 className="font-medium text-gray-800 mb-2">Test Information</h3>
            <div className="text-sm text-gray-700 space-y-1">
              <p><strong>Test User:</strong> Demo User (3ddaeb03-2d95-4fff-abad-2a2c7dd25037)</p>
              <p><strong>Callback URL:</strong> http://127.0.0.1:3001/api/x/callback</p>
              <p><strong>Success Redirect:</strong> http://127.0.0.1:3001/auth/success?platform=x</p>
              <p><strong>Error Redirect:</strong> http://127.0.0.1:3001/auth/error?platform=x</p>
              <p><strong>Required Scopes:</strong> tweet.read, tweet.write, users.read, follows.read, follows.write, offline.access</p>
              <p><strong>OAuth Method:</strong> OAuth 2.0 with PKCE</p>
            </div>
          </div>

          {/* X API Information */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <h3 className="font-medium text-yellow-800 mb-2">X API Requirements</h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <p><strong>API Version:</strong> X API v2</p>
              <p><strong>OAuth Version:</strong> OAuth 2.0 with PKCE</p>
              <p><strong>Client Type:</strong> Confidential Client (Web App)</p>
              <p><strong>Token Validity:</strong> 2 hours (with refresh token if offline.access scope)</p>
              <p><strong>Rate Limits:</strong> Enhanced limits with OAuth 2.0</p>
            </div>
          </div>

          {/* Developer Notes */}
          <div className="bg-purple-50 border border-purple-200 rounded-md p-4">
            <h3 className="font-medium text-purple-800 mb-2">Developer Notes</h3>
            <div className="text-sm text-purple-700 space-y-1">
              <p>• X OAuth uses PKCE for enhanced security</p>
              <p>• Refresh tokens available with offline.access scope</p>
              <p>• Service role client bypasses RLS for testing</p>
              <p>• Enhanced rate limits: 900 requests/15min for user/tweet lookup</p>
              <p>• Supports both personal and business accounts</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/"
            className="text-blue-600 hover:text-blue-500 text-sm font-medium"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  );
}
