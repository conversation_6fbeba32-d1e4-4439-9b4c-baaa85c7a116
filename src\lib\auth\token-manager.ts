import { createClient } from '@/lib/supabase/client';
import { createClient as createServerClient } from '@/lib/supabase/server';

export interface TokenData {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  tokenType?: string;
  scope?: string;
}

export interface SocialAccountData {
  id: string;
  userId: string;
  platform: string;
  accountId: string;
  accountName: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export class TokenManager {
  private supabase: any;

  constructor(isServer = false) {
    this.supabase = isServer ? createServerClient() : createClient();
  }

  /**
   * Store OAuth tokens securely in the database
   */
  async storeTokens(
    userId: string,
    platform: string,
    accountId: string,
    accountName: string,
    tokenData: TokenData,
    profileData?: any
  ): Promise<{ success: boolean; error?: string; accountId?: string }> {
    try {
      const accountData = {
        user_id: userId,
        platform: platform.toUpperCase(),
        account_id: accountId,
        account_name: accountName,
        access_token: tokenData.accessToken,
        refresh_token: tokenData.refreshToken || null,
        expires_at: tokenData.expiresAt?.toISOString() || null,
        updated_at: new Date().toISOString(),
      };

      // Use upsert to handle both new connections and token refreshes
      const { data, error } = await this.supabase
        .from('social_accounts')
        .upsert(accountData, {
          onConflict: 'user_id,platform,account_id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error storing tokens:', error);
        return { success: false, error: error.message };
      }

      // Log the connection activity
      await this.logActivity(userId, platform, 'TOKEN_STORED', {
        accountId,
        accountName,
        hasRefreshToken: !!tokenData.refreshToken,
        expiresAt: tokenData.expiresAt?.toISOString(),
      });

      return { success: true, accountId: data.id };
    } catch (error: any) {
      console.error('Token storage error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Retrieve tokens for a specific account
   */
  async getTokens(userId: string, platform: string, accountId?: string): Promise<SocialAccountData | null> {
    try {
      let query = this.supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('platform', platform.toUpperCase());

      if (accountId) {
        query = query.eq('account_id', accountId);
      }

      const { data, error } = await query.single();

      if (error || !data) {
        return null;
      }

      return {
        id: data.id,
        userId: data.user_id,
        platform: data.platform,
        accountId: data.account_id,
        accountName: data.account_name,
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        expiresAt: data.expires_at ? new Date(data.expires_at) : undefined,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return null;
    }
  }

  /**
   * Check if tokens are expired or will expire soon
   */
  async checkTokenExpiry(userId: string, platform: string): Promise<{
    isExpired: boolean;
    willExpireSoon: boolean;
    daysUntilExpiry: number | null;
  }> {
    const account = await this.getTokens(userId, platform);

    if (!account || !account.expiresAt) {
      return { isExpired: false, willExpireSoon: false, daysUntilExpiry: null };
    }

    const now = new Date();
    const expiryTime = account.expiresAt;
    const timeDiff = expiryTime.getTime() - now.getTime();
    const daysUntilExpiry = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    return {
      isExpired: timeDiff <= 0,
      willExpireSoon: daysUntilExpiry <= 7 && daysUntilExpiry > 0,
      daysUntilExpiry: daysUntilExpiry > 0 ? daysUntilExpiry : null,
    };
  }

  /**
   * Refresh OAuth 2.0 tokens (for Facebook, LinkedIn)
   */
  async refreshTokens(userId: string, platform: string): Promise<{ success: boolean; error?: string }> {
    try {
      const account = await this.getTokens(userId, platform);

      if (!account || !account.refreshToken) {
        return { success: false, error: 'No refresh token available' };
      }

      let newTokenData: TokenData | null = null;

      switch (platform.toUpperCase()) {
        case 'FACEBOOK':
        case 'INSTAGRAM':
          newTokenData = await this.refreshFacebookToken(account.refreshToken);
          break;
        case 'LINKEDIN':
          newTokenData = await this.refreshLinkedInToken(account.refreshToken);
          break;
        default:
          return { success: false, error: 'Token refresh not supported for this platform' };
      }

      if (!newTokenData) {
        return { success: false, error: 'Failed to refresh tokens' };
      }

      // Store the new tokens
      const result = await this.storeTokens(
        userId,
        platform,
        account.accountId,
        account.accountName,
        newTokenData
      );

      if (result.success) {
        await this.logActivity(userId, platform, 'TOKEN_REFRESHED', {
          accountId: account.accountId,
          expiresAt: newTokenData.expiresAt?.toISOString(),
        });
      }

      return result;
    } catch (error: any) {
      console.error('Token refresh error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Refresh Facebook/Instagram tokens
   */
  private async refreshFacebookToken(refreshToken: string): Promise<TokenData | null> {
    try {
      const response = await fetch('https://graph.facebook.com/v18.0/oauth/access_token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'fb_exchange_token',
          client_id: process.env.FACEBOOK_APP_ID!,
          client_secret: process.env.FACEBOOK_APP_SECRET!,
          fb_exchange_token: refreshToken,
        }),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error.message);
      }

      return {
        accessToken: data.access_token,
        tokenType: data.token_type,
        expiresAt: data.expires_in ? new Date(Date.now() + data.expires_in * 1000) : undefined,
      };
    } catch (error) {
      console.error('Facebook token refresh error:', error);
      return null;
    }
  }

  /**
   * Refresh LinkedIn tokens
   */
  private async refreshLinkedInToken(refreshToken: string): Promise<TokenData | null> {
    try {
      const response = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: refreshToken,
          client_id: process.env.LINKEDIN_CLIENT_ID!,
          client_secret: process.env.LINKEDIN_CLIENT_SECRET!,
        }),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error_description || data.error);
      }

      return {
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        tokenType: data.token_type,
        expiresAt: data.expires_in ? new Date(Date.now() + data.expires_in * 1000) : undefined,
      };
    } catch (error) {
      console.error('LinkedIn token refresh error:', error);
      return null;
    }
  }

  /**
   * Revoke tokens and disconnect account - ENHANCED VERSION
   */
  async revokeTokens(userId: string, platform: string, accountId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔐 TokenManager: Starting revocation process', {
        userId,
        platform,
        accountId
      });

      const account = await this.getTokens(userId, platform, accountId);

      if (!account) {
        console.log('❌ TokenManager: Account not found');
        return { success: false, error: 'Account not found' };
      }

      console.log('✅ TokenManager: Account found, proceeding with revocation');

      // ENHANCED: Attempt to revoke tokens with the platform (non-blocking)
      try {
        await this.revokeTokensWithPlatform(platform, account.accessToken);
        console.log('✅ TokenManager: Platform token revocation completed');
      } catch (platformError: any) {
        console.warn('⚠️ TokenManager: Platform token revocation failed (continuing anyway):', platformError.message);
        // Don't fail the entire process for platform revocation issues
      }

      // ENHANCED: Remove from database with better error handling
      console.log('🗑️ TokenManager: Removing account from database');
      const { error } = await this.supabase
        .from('social_accounts')
        .delete()
        .eq('user_id', userId)
        .eq('platform', platform.toUpperCase())
        .eq('account_id', accountId);

      if (error) {
        console.error('❌ TokenManager: Database deletion error:', error);
        return { success: false, error: `Database deletion failed: ${error.message}` };
      }

      console.log('✅ TokenManager: Account removed from database');

      // ENHANCED: Log activity with error handling
      try {
        await this.logActivity(userId, platform, 'ACCOUNT_DISCONNECTED', {
          accountId,
          accountName: account.accountName,
          timestamp: new Date().toISOString()
        });
        console.log('📝 TokenManager: Activity logged');
      } catch (logError: any) {
        console.warn('⚠️ TokenManager: Activity logging failed:', logError.message);
        // Don't fail for logging issues
      }

      console.log('🎉 TokenManager: Revocation process completed successfully');
      return { success: true };
    } catch (error: any) {
      console.error('💥 TokenManager: Token revocation error:', error);
      return { success: false, error: error.message || 'Unknown token revocation error' };
    }
  }

  /**
   * Revoke tokens with the respective platform - ENHANCED VERSION
   */
  private async revokeTokensWithPlatform(platform: string, accessToken: string): Promise<void> {
    console.log(`🔗 TokenManager: Revoking ${platform} tokens...`);

    try {
      switch (platform.toUpperCase()) {
        case 'FACEBOOK':
        case 'INSTAGRAM':
          console.log('📘 Revoking Facebook/Instagram permissions...');
          const fbResponse = await fetch(`https://graph.facebook.com/v18.0/me/permissions?access_token=${accessToken}`, {
            method: 'DELETE',
          });
          if (!fbResponse.ok) {
            console.warn(`⚠️ Facebook revocation response: ${fbResponse.status}`);
          } else {
            console.log('✅ Facebook/Instagram permissions revoked');
          }
          break;

        case 'LINKEDIN':
          console.log('💼 Revoking LinkedIn tokens...');
          const linkedinResponse = await fetch('https://www.linkedin.com/oauth/v2/revoke', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
              token: accessToken,
              client_id: process.env.LINKEDIN_CLIENT_ID!,
              client_secret: process.env.LINKEDIN_CLIENT_SECRET!,
            }),
          });
          if (!linkedinResponse.ok) {
            console.warn(`⚠️ LinkedIn revocation response: ${linkedinResponse.status}`);
          } else {
            console.log('✅ LinkedIn tokens revoked');
          }
          break;

        case 'TWITTER':
        case 'X':
          console.log('🐦 Twitter/X token revocation (automatic on removal)');
          // Twitter OAuth 1.0a doesn't have a standard revoke endpoint
          // Tokens are automatically invalidated when removed from the app
          break;

        default:
          console.warn(`⚠️ Unknown platform for token revocation: ${platform}`);
      }
    } catch (error: any) {
      console.error(`❌ Platform token revocation error for ${platform}:`, error.message);
      // Don't throw - we still want to remove from our database
      throw error; // Re-throw to be caught by caller
    }
  }

  /**
   * Log activity for audit purposes
   */
  private async logActivity(userId: string, platform: string, action: string, metadata: any): Promise<void> {
    try {
      await this.supabase
        .from('activities')
        .insert({
          user_id: userId,
          action,
          metadata: {
            platform,
            ...metadata,
          },
          created_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error('Activity logging error:', error);
      // Don't throw - logging failure shouldn't break the main flow
    }
  }

  /**
   * Get all connected accounts for a user
   */
  async getConnectedAccounts(userId: string): Promise<SocialAccountData[]> {
    try {
      const { data, error } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching connected accounts:', error);
        return [];
      }

      return data.map((account: any) => ({
        id: account.id,
        userId: account.user_id,
        platform: account.platform,
        accountId: account.account_id,
        accountName: account.account_name,
        accessToken: account.access_token,
        refreshToken: account.refresh_token,
        expiresAt: account.expires_at ? new Date(account.expires_at) : undefined,
        createdAt: new Date(account.created_at),
        updatedAt: new Date(account.updated_at),
      }));
    } catch (error) {
      console.error('Error fetching connected accounts:', error);
      return [];
    }
  }
}

// Helper function to create TokenManager instance
export function createTokenManager(isServer = false): TokenManager {
  return new TokenManager(isServer);
}
