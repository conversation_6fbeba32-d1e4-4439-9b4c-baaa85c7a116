import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * Test publishing capabilities for social media platforms
 * GET /api/social/test-publishing?platform=linkedin
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform')?.toUpperCase();

    if (!platform) {
      return NextResponse.json({
        success: false,
        error: 'Platform parameter is required'
      }, { status: 400 });
    }

    // Test if publishing implementation exists for the platform
    const publishingCapabilities = await testPlatformPublishing(platform);

    return NextResponse.json({
      success: true,
      platform,
      capabilities: publishingCapabilities,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('Publishing test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Test publishing with dry run
 * POST /api/social/test-publishing
 */
export async function POST(request: NextRequest) {
  try {
    const { platforms, content, dryRun = true } = await request.json();

    if (!platforms || !Array.isArray(platforms)) {
      return NextResponse.json({
        success: false,
        error: 'Platforms array is required'
      }, { status: 400 });
    }

    const testResults = [];

    for (const platform of platforms) {
      const result = await testPlatformPublishing(platform.toUpperCase());
      testResults.push({
        platform: platform.toUpperCase(),
        ...result
      });
    }

    return NextResponse.json({
      success: true,
      dryRun,
      content: content || 'Test post content',
      results: testResults,
      summary: {
        total: platforms.length,
        ready: testResults.filter(r => r.publisherAvailable && r.providerAvailable).length,
        partial: testResults.filter(r => r.publisherAvailable || r.providerAvailable).length,
        notReady: testResults.filter(r => !r.publisherAvailable && !r.providerAvailable).length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('Publishing test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Test platform publishing capabilities
 */
async function testPlatformPublishing(platform: string) {
  const result = {
    platform,
    publisherAvailable: false,
    providerAvailable: false,
    apiEndpointExists: false,
    environmentConfigured: false,
    issues: [] as string[],
    details: {} as any
  };

  try {
    // Test if enhanced provider exists
    switch (platform) {
      case 'LINKEDIN':
        try {
          const { LinkedInEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/linkedin-enhanced');
          const provider = new LinkedInEnhancedProvider();
          result.providerAvailable = true;
          result.details.providerName = 'LinkedInEnhancedProvider';
          
          // Test environment variables
          if (process.env.LINKEDIN_CLIENT_ID && process.env.LINKEDIN_CLIENT_SECRET) {
            result.environmentConfigured = true;
          } else {
            result.issues.push('Missing LinkedIn environment variables');
          }
        } catch (error) {
          result.issues.push('LinkedIn provider import failed');
        }

        try {
          const { LinkedInPublisherV2 } = await import('@/lib/social/publishers/linkedin-publisher-v2');
          result.publisherAvailable = true;
          result.details.publisherName = 'LinkedInPublisherV2';
        } catch (error) {
          result.issues.push('LinkedIn publisher not available');
        }
        break;

      case 'FACEBOOK':
        try {
          const { FacebookEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/facebook-enhanced');
          const provider = new FacebookEnhancedProvider();
          result.providerAvailable = true;
          result.details.providerName = 'FacebookEnhancedProvider';
          
          if (process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
            result.environmentConfigured = true;
          } else {
            result.issues.push('Missing Facebook environment variables');
          }
        } catch (error) {
          result.issues.push('Facebook provider import failed');
        }

        try {
          const { FacebookPublisherV2 } = await import('@/lib/social/publishers/facebook-publisher-v2');
          result.publisherAvailable = true;
          result.details.publisherName = 'FacebookPublisherV2';
        } catch (error) {
          result.issues.push('Facebook publisher not available');
        }
        break;

      case 'TWITTER':
        try {
          const { TwitterEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/twitter-enhanced');
          const provider = new TwitterEnhancedProvider();
          result.providerAvailable = true;
          result.details.providerName = 'TwitterEnhancedProvider';
          
          if ((process.env.TWITTER_CLIENT_ID || process.env.X_CLIENT_ID) && 
              (process.env.TWITTER_CLIENT_SECRET || process.env.X_CLIENT_SECRET)) {
            result.environmentConfigured = true;
          } else {
            result.issues.push('Missing Twitter/X environment variables');
          }
        } catch (error) {
          result.issues.push('Twitter provider import failed');
        }

        try {
          const { TwitterPublisherV2 } = await import('@/lib/social/publishers/twitter-publisher-v2');
          result.publisherAvailable = true;
          result.details.publisherName = 'TwitterPublisherV2';
        } catch (error) {
          result.issues.push('Twitter publisher not available');
        }
        break;

      case 'INSTAGRAM':
        try {
          const { InstagramEnhancedProvider } = await import('@/lib/social/postiz-integration/providers/instagram-enhanced');
          const provider = new InstagramEnhancedProvider();
          result.providerAvailable = true;
          result.details.providerName = 'InstagramEnhancedProvider';
          
          if (process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
            result.environmentConfigured = true;
          } else {
            result.issues.push('Missing Instagram (Facebook) environment variables');
          }
        } catch (error) {
          result.issues.push('Instagram provider import failed');
        }

        try {
          const { InstagramPublisher } = await import('@/lib/scheduler/publishers/instagram-publisher');
          result.publisherAvailable = true;
          result.details.publisherName = 'InstagramPublisher';
        } catch (error) {
          result.issues.push('Instagram publisher not available');
        }
        break;

      default:
        result.issues.push(`Platform ${platform} is not supported`);
    }

    // Test if API endpoint exists
    try {
      // Check if the publish endpoint can handle this platform
      result.apiEndpointExists = true; // We know it exists since we're in it
    } catch (error) {
      result.issues.push('API endpoint not available');
    }

  } catch (error: any) {
    result.issues.push(`Testing failed: ${error.message}`);
  }

  return result;
}

/**
 * Get all supported platforms and their status
 */
export async function OPTIONS() {
  const supportedPlatforms = ['LINKEDIN', 'FACEBOOK', 'INSTAGRAM', 'TWITTER'];
  
  const platformStatuses = await Promise.all(
    supportedPlatforms.map(async (platform) => {
      const status = await testPlatformPublishing(platform);
      return {
        platform,
        ready: status.publisherAvailable && status.providerAvailable && status.environmentConfigured,
        partial: status.publisherAvailable || status.providerAvailable,
        issues: status.issues.length,
        ...status
      };
    })
  );

  return NextResponse.json({
    supportedPlatforms,
    platformStatuses,
    summary: {
      total: supportedPlatforms.length,
      ready: platformStatuses.filter(p => p.ready).length,
      partial: platformStatuses.filter(p => p.partial && !p.ready).length,
      notReady: platformStatuses.filter(p => !p.partial).length
    },
    timestamp: new Date().toISOString()
  });
}
