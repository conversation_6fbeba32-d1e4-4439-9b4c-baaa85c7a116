/**
 * Enhanced Social Media Connection API
 * Handles OAuth flows with business account selection
 */

import { NextRequest, NextResponse } from 'next/server';
import { integrationManager } from '@/lib/social/postiz-integration/integration-manager';
import { supabaseServiceRole } from '@/lib/supabase/service-role';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const { platform } = await request.json();

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform is required' },
        { status: 400 }
      );
    }

    // Generate auth URL
    const authData = await integrationManager.generateAuthUrl(platform);
    
    if (!authData) {
      return NextResponse.json(
        { error: `Auth URL generation failed for platform: ${platform}` },
        { status: 400 }
      );
    }

    // Store state and code verifier in cookies for security
    const cookieStore = cookies();
    cookieStore.set(`oauth_state_${platform}`, authData.state, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 600, // 10 minutes
    });
    
    cookieStore.set(`oauth_verifier_${platform}`, authData.codeVerifier, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 600, // 10 minutes
    });

    return NextResponse.json({
      success: true,
      authUrl: authData.url,
      platform,
    });
  } catch (error) {
    console.error('[Enhanced Connect API] Error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to initiate OAuth flow',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    if (error) {
      return NextResponse.json(
        { error: `OAuth error: ${error}` },
        { status: 400 }
      );
    }

    if (!platform || !code || !state) {
      return NextResponse.json(
        { error: 'Missing required OAuth parameters' },
        { status: 400 }
      );
    }

    // Verify state and get code verifier from cookies
    const cookieStore = cookies();
    const storedState = cookieStore.get(`oauth_state_${platform}`)?.value;
    const codeVerifier = cookieStore.get(`oauth_verifier_${platform}`)?.value;

    if (!storedState || storedState !== state) {
      return NextResponse.json(
        { error: 'Invalid OAuth state' },
        { status: 400 }
      );
    }

    if (!codeVerifier) {
      return NextResponse.json(
        { error: 'Missing code verifier' },
        { status: 400 }
      );
    }

    // Authenticate with the platform
    const authResult = await integrationManager.authenticate(platform, {
      code,
      codeVerifier,
    });

    // Get user from session (you'll need to implement this based on your auth system)
    const supabase = supabaseServiceRole;
    // This is a placeholder - implement your actual user authentication
    const userId = 'demo-user-id'; // Replace with actual user ID from session

    // Check if this is a business account selection step
    const provider = integrationManager.getProvider(platform);
    if (provider?.isBetweenSteps) {
      // For platforms like Facebook that require business account selection
      const businessAccounts = await integrationManager.getBusinessAccounts(
        platform,
        authResult.accessToken
      );

      return NextResponse.json({
        success: true,
        requiresAccountSelection: true,
        accounts: businessAccounts,
        tempToken: authResult.accessToken, // Temporary token for account selection
        platform,
      });
    }

    // Save the integration directly
    const { data: integration, error: saveError } = await supabase
      .from('social_accounts')
      .upsert({
        user_id: userId,
        platform,
        account_id: authResult.id,
        account_name: authResult.name,
        account_username: authResult.username,
        account_picture: authResult.picture,
        access_token: authResult.accessToken,
        refresh_token: authResult.refreshToken,
        expires_at: authResult.expiresIn 
          ? new Date(Date.now() + authResult.expiresIn * 1000).toISOString()
          : null,
        account_type: 'personal',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id,platform,account_id'
      })
      .select()
      .single();

    if (saveError) {
      throw saveError;
    }

    // Clean up cookies
    cookieStore.delete(`oauth_state_${platform}`);
    cookieStore.delete(`oauth_verifier_${platform}`);

    return NextResponse.json({
      success: true,
      integration: {
        id: integration.id,
        platform: integration.platform,
        accountName: integration.account_name,
        accountUsername: integration.account_username,
        accountPicture: integration.account_picture,
        accountType: integration.account_type,
        isActive: integration.is_active,
      },
    });
  } catch (error) {
    console.error('[Enhanced Connect API] Callback error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to complete OAuth flow',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
