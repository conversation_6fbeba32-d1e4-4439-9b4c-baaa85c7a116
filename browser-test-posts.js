// Browser Console Test Script for Post Management
// Copy and paste this into the browser console after signing in

console.log('🧪 Starting Post Management Browser Tests...\n');

// Test 1: Create a test post
async function testCreatePost() {
  console.log('📝 Testing Post Creation...');
  
  const testPost = {
    content: `Test post created at ${new Date().toLocaleString()} - Browser Test`,
    media_url: '',
    status: 'DRAFT',
    social_account_ids: ['test-account']
  };
  
  try {
    const response = await fetch('/api/posts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPost)
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Post creation successful!');
      console.log('Post data:', data);
      return data.post;
    } else {
      console.log('❌ Post creation failed:', data.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Post creation error:', error.message);
    return null;
  }
}

// Test 2: Fetch posts
async function testFetchPosts() {
  console.log('\n📋 Testing Posts Fetching...');
  
  try {
    const response = await fetch('/api/posts');
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Posts fetching successful!');
      console.log(`Total posts: ${data.posts.length}`);
      console.log('Posts:', data.posts);
      return data.posts;
    } else {
      console.log('❌ Posts fetching failed:', data.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Posts fetching error:', error.message);
    return null;
  }
}

// Test 3: Create scheduled post
async function testScheduledPost() {
  console.log('\n⏰ Testing Scheduled Post Creation...');
  
  const scheduledDate = new Date();
  scheduledDate.setHours(scheduledDate.getHours() + 1);
  
  const testPost = {
    content: `Scheduled post for ${scheduledDate.toLocaleString()} - Browser Test`,
    media_url: '',
    status: 'SCHEDULED',
    scheduled_at: scheduledDate.toISOString(),
    social_account_ids: ['test-account']
  };
  
  try {
    const response = await fetch('/api/posts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPost)
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Scheduled post creation successful!');
      console.log('Scheduled post data:', data);
      return data.post;
    } else {
      console.log('❌ Scheduled post creation failed:', data.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Scheduled post creation error:', error.message);
    return null;
  }
}

// Test 4: Update a post
async function testUpdatePost(postId) {
  if (!postId) {
    console.log('\n🔄 Skipping post update test (no post ID)');
    return false;
  }
  
  console.log('\n🔄 Testing Post Update...');
  
  const updateData = {
    content: `Updated post content at ${new Date().toLocaleString()}`,
    status: 'DRAFT'
  };
  
  try {
    const response = await fetch(`/api/posts/${postId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Post update successful!');
      console.log('Updated post data:', data);
      return true;
    } else {
      console.log('❌ Post update failed:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Post update error:', error.message);
    return false;
  }
}

// Run all tests
async function runAllPostTests() {
  console.log('🚀 Running comprehensive post management tests...\n');
  
  const results = [];
  let createdPostId = null;
  
  // Test 1: Create post
  const createdPost = await testCreatePost();
  results.push(!!createdPost);
  if (createdPost) {
    createdPostId = createdPost.id;
  }
  
  // Test 2: Fetch posts
  const fetchedPosts = await testFetchPosts();
  results.push(!!fetchedPosts);
  
  // Test 3: Create scheduled post
  const scheduledPost = await testScheduledPost();
  results.push(!!scheduledPost);
  
  // Test 4: Update post
  const updateResult = await testUpdatePost(createdPostId);
  results.push(updateResult);
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n' + '═'.repeat(50));
  console.log('🎯 POST MANAGEMENT BROWSER TEST RESULTS');
  console.log('═'.repeat(50));
  console.log(`✅ Tests Passed: ${passed}/${total}`);
  console.log(`📊 Success Rate: ${Math.round((passed / total) * 100)}%`);
  
  if (passed >= 3) {
    console.log('\n🎉 POST MANAGEMENT SYSTEM WORKING!');
    console.log('✅ Browser tests completed successfully');
  } else {
    console.log('\n⚠️  Some tests failed - check authentication');
  }
  
  return passed >= 3;
}

// Auto-run tests
console.log('To run tests, execute: runAllPostTests()');
console.log('Or run individual tests:');
console.log('- testCreatePost()');
console.log('- testFetchPosts()');
console.log('- testScheduledPost()');
console.log('- testUpdatePost(postId)');

// Make functions available globally
window.runAllPostTests = runAllPostTests;
window.testCreatePost = testCreatePost;
window.testFetchPosts = testFetchPosts;
window.testScheduledPost = testScheduledPost;
window.testUpdatePost = testUpdatePost;
