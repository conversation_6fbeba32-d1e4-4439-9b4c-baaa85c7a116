import { SocialAccount, PostContent } from '../social-media-publisher';
import { SchedulerLogger } from '../scheduler-logger';
import LinkedInPublisherV2 from '@/lib/social/publishers/linkedin-publisher-v2';
import ContentFormatter from '@/lib/social/content-formatter';

export interface LinkedInPublishResult {
  postId: string;
  url: string;
}

/**
 * Enhanced LinkedIn publisher using LinkedIn API v2 with real API calls
 * Integrates with the new LinkedInPublisherV2 service
 */
export class LinkedInPublisher {
  private logger: SchedulerLogger;
  private publisherV2: LinkedInPublisherV2;
  private contentFormatter: ContentFormatter;
  private readonly API_BASE = 'https://api.linkedin.com/v2';

  constructor() {
    this.logger = new SchedulerLogger('linkedin-publisher');
    this.publisherV2 = new LinkedInPublisherV2();
    this.contentFormatter = new ContentFormatter();
  }

  /**
   * Publish a post to LinkedIn using enhanced V2 publisher
   */
  async publish(account: SocialAccount, content: PostContent): Promise<LinkedInPublishResult> {
    try {
      this.logger.info('Publishing to LinkedIn with V2 publisher', {
        accountId: account.account_id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
      });

      // Format content for LinkedIn
      const formattedContent = this.contentFormatter.formatForPlatform(content.content, 'linkedin');

      if (!formattedContent.isValid) {
        throw new Error(`Content validation failed: ${formattedContent.warnings.join(', ')}`);
      }

      if (formattedContent.warnings.length > 0) {
        this.logger.warn('Content formatting warnings', { warnings: formattedContent.warnings });
      }

      // Prepare content for V2 publisher
      const publishContent = {
        content: formattedContent.text,
        mediaUrl: content.mediaUrl,
        mediaType: (content.mediaType || (content.mediaUrl ? 'IMAGE' : undefined)) as "IMAGE" | "VIDEO" | "DOCUMENT" | undefined
      };

      // Use V2 publisher for real API calls
      const result = await this.publisherV2.publishPost(account, publishContent);

      if (!result.success) {
        throw new Error(result.error || 'LinkedIn publishing failed');
      }

      this.logger.info('LinkedIn post published successfully with V2', {
        postId: result.postId,
        postUrl: result.url,
      });

      return {
        postId: result.postId!,
        url: result.url!,
      };

    } catch (error) {
      this.logger.error('Failed to publish LinkedIn post with V2', error);
      throw error;
    }
  }

  /**
   * Upload media to LinkedIn
   */
  private async uploadMedia(account: SocialAccount, mediaUrl: string): Promise<string> {
    try {
      this.logger.info('Uploading media to LinkedIn', { mediaUrl });

      // Step 1: Register upload
      const registerResponse = await this.makeLinkedInRequest(
        account,
        'POST',
        '/assets?action=registerUpload',
        {
          registerUploadRequest: {
            recipes: ['urn:li:digitalmediaRecipe:feedshare-image'],
            owner: `urn:li:person:${account.account_id}`,
            serviceRelationships: [
              {
                relationshipType: 'OWNER',
                identifier: 'urn:li:userGeneratedContent',
              },
            ],
          },
        }
      );

      const uploadUrl = registerResponse.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'].uploadUrl;
      const asset = registerResponse.value.asset;

      // Step 2: Download media
      const mediaResponse = await fetch(mediaUrl);
      if (!mediaResponse.ok) {
        throw new Error(`Failed to download media: ${mediaResponse.statusText}`);
      }

      const mediaBuffer = await mediaResponse.arrayBuffer();

      // Step 3: Upload to LinkedIn
      const uploadResponse = await fetch(uploadUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
        },
        body: mediaBuffer,
      });

      if (!uploadResponse.ok) {
        throw new Error(`LinkedIn media upload failed: ${uploadResponse.statusText}`);
      }

      this.logger.info('Media uploaded successfully to LinkedIn', { asset });

      return asset;

    } catch (error) {
      this.logger.error('Failed to upload media to LinkedIn', error);
      throw error;
    }
  }

  /**
   * Make authenticated request to LinkedIn API
   */
  private async makeLinkedInRequest(
    account: SocialAccount,
    method: string,
    endpoint: string,
    data?: any
  ): Promise<any> {
    const url = `${this.API_BASE}${endpoint}`;

    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${account.access_token}`,
        'Content-Type': 'application/json',
        'X-Restli-Protocol-Version': '2.0.0',
      },
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Handle specific LinkedIn errors
      if (response.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }

      if (response.status === 401) {
        throw new Error('LinkedIn authentication failed. Token may be expired or invalid.');
      }

      if (response.status === 403) {
        throw new Error('LinkedIn API access forbidden. Check permissions.');
      }

      const errorMessage = errorData.message || `LinkedIn API error: ${response.status}`;
      throw new Error(errorMessage);
    }

    return await response.json();
  }

  /**
   * Test connection to LinkedIn account using V2 publisher
   */
  async testConnection(account: SocialAccount): Promise<any> {
    try {
      this.logger.info('Testing LinkedIn connection with V2', {
        accountId: account.account_id,
      });

      // Use V2 publisher for connection testing
      const result = await this.publisherV2.testConnection(account);

      this.logger.info('LinkedIn connection test successful with V2', {
        firstName: result.accountInfo?.firstName,
        lastName: result.accountInfo?.lastName,
      });

      return result;

    } catch (error) {
      this.logger.error('LinkedIn connection test failed with V2', error);
      throw error;
    }
  }

  /**
   * Get account information
   */
  async getAccountInfo(account: SocialAccount): Promise<any> {
    try {
      const response = await this.makeLinkedInRequest(
        account,
        'GET',
        `/people/(id:${account.account_id})?projection=(id,firstName,lastName,headline,profilePicture(displayImage~:playableStreams))`
      );

      return {
        id: response.id,
        firstName: response.firstName?.localized?.en_US,
        lastName: response.lastName?.localized?.en_US,
        headline: response.headline?.localized?.en_US,
        profilePicture: response.profilePicture?.displayImage,
      };

    } catch (error) {
      this.logger.error('Failed to get LinkedIn account info', error);
      throw error;
    }
  }

  /**
   * Delete a LinkedIn post
   */
  async deletePost(account: SocialAccount, postId: string): Promise<boolean> {
    try {
      this.logger.info('Deleting LinkedIn post', { postId });

      // LinkedIn uses URN format for post IDs
      const postUrn = postId.startsWith('urn:') ? postId : `urn:li:share:${postId}`;

      await this.makeLinkedInRequest(
        account,
        'DELETE',
        `/ugcPosts/${encodeURIComponent(postUrn)}`
      );

      this.logger.info('LinkedIn post deleted successfully', { postId });
      return true;

    } catch (error) {
      this.logger.error('Failed to delete LinkedIn post', error, { postId });
      throw error;
    }
  }

  /**
   * Get post analytics
   */
  async getPostAnalytics(account: SocialAccount, postId: string): Promise<any> {
    try {
      const postUrn = postId.startsWith('urn:') ? postId : `urn:li:share:${postId}`;

      // Get post details
      const postResponse = await this.makeLinkedInRequest(
        account,
        'GET',
        `/ugcPosts/${encodeURIComponent(postUrn)}`
      );

      // Get post statistics
      const statsResponse = await this.makeLinkedInRequest(
        account,
        'GET',
        `/socialActions/${encodeURIComponent(postUrn)}`
      );

      return {
        id: postResponse.id,
        text: postResponse.specificContent?.['com.linkedin.ugc.ShareContent']?.shareCommentary?.text,
        created_time: postResponse.created?.time,
        metrics: {
          likes: statsResponse.likesSummary?.totalLikes || 0,
          comments: statsResponse.commentsSummary?.totalComments || 0,
          shares: statsResponse.sharesSummary?.totalShares || 0,
        },
      };

    } catch (error) {
      this.logger.error('Failed to get LinkedIn post analytics', error);
      throw error;
    }
  }

  /**
   * Get profile statistics
   */
  async getProfileStats(account: SocialAccount): Promise<any> {
    try {
      const response = await this.makeLinkedInRequest(
        account,
        'GET',
        `/networkSizes/${account.account_id}?edgeType=CompanyFollowedByMember`
      );

      return {
        connections: response.firstDegreeSize,
        followers: response.secondDegreeSize,
      };

    } catch (error) {
      this.logger.error('Failed to get LinkedIn profile stats', error);
      throw error;
    }
  }

  /**
   * Validate LinkedIn credentials
   */
  async validateCredentials(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/me`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0',
        },
      });

      return response.ok;

    } catch (error) {
      this.logger.error('Failed to validate LinkedIn credentials', error);
      return false;
    }
  }

  /**
   * Get user's company pages
   */
  async getCompanyPages(account: SocialAccount): Promise<any[]> {
    try {
      const response = await this.makeLinkedInRequest(
        account,
        'GET',
        `/organizationAcls?q=roleAssignee&role=ADMINISTRATOR&projection=(elements*(organization~(id,name,logoV2(original~:playableStreams))))`
      );

      return response.elements?.map((element: any) => ({
        id: element.organization?.id,
        name: element.organization?.name,
        logo: element.organization?.logoV2?.original,
      })) || [];

    } catch (error) {
      this.logger.error('Failed to get LinkedIn company pages', error);
      throw error;
    }
  }
}
