import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { z } from "zod";

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// Simple validation schema
const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().optional()
}).refine((data) => !data.confirmPassword || data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

async function registerHandler(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('Registration API called');

    // Get request body
    const body = await request.json();
    console.log('Request body received:', { ...body, password: '[HIDDEN]' });

    // Validate request body with Zod schema
    const validation = registerSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    const { name, email, password } = validation.data;
    console.log('Validation passed for email:', email);

    // Create Supabase client
    
    console.log('Supabase client created');

    // Use Supabase Auth to create user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name,
          role: 'USER'
        }
      }
    });

    if (authError) {
      console.error('Supabase auth error:', authError);
      return NextResponse.json(
        { error: authError.message || 'Failed to create user' },
        { status: 400 }
      );
    }

    if (!authData.user) {
      console.error('No user data returned from Supabase');
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

    console.log('User created successfully with Supabase Auth:', authData.user.id);

    return NextResponse.json(
      {
        message: "تم إنشاء المستخدم بنجاح",
        user: {
          id: authData.user.id,
          name: name,
          email: authData.user.email,
          role: 'USER',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export the handler directly for now
export const POST = registerHandler;
