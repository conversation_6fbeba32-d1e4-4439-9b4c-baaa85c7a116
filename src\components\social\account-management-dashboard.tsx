'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Settings, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Users, 
  Building, 
  Globe,
  TrendingUp,
  Activity,
  Calendar,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';
import { UnifiedBusinessAccountSelector } from './unified-business-account-selector';
import { BusinessAccountConfig } from '@/lib/social/business-accounts/business-account-types';

interface AccountManagementDashboardProps {
  userId: string;
  className?: string;
}

interface DashboardStats {
  totalPlatforms: number;
  connectedPlatforms: number;
  configuredPlatforms: number;
  totalBusinessAccounts: number;
  selectedAccounts: number;
  lastUpdated: string;
}

interface PlatformSummary {
  platform: string;
  name: string;
  isConnected: boolean;
  isConfigured: boolean;
  businessAccountsCount: number;
  selectedAccount?: string;
  lastActivity?: string;
  followerCount?: number;
}

export function AccountManagementDashboard({ 
  userId, 
  className 
}: AccountManagementDashboardProps) {
  const [stats, setStats] = useState<DashboardStats>({
    totalPlatforms: 4,
    connectedPlatforms: 0,
    configuredPlatforms: 0,
    totalBusinessAccounts: 0,
    selectedAccounts: 0,
    lastUpdated: new Date().toISOString()
  });

  const [platformSummaries, setPlatformSummaries] = useState<PlatformSummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, [userId]);

  const loadDashboardData = async () => {
    setIsLoading(true);
    
    try {
      // Load configurations for all platforms
      const platforms = ['facebook', 'linkedin', 'instagram', 'twitter'];
      const configPromises = platforms.map(async (platform) => {
        try {
          const response = await fetch(`/api/social/business-accounts?platform=${platform}&userId=${userId}`);
          const data = await response.json();
          
          return {
            platform,
            name: getPlatformName(platform),
            isConnected: !data.requiresReconnection,
            isConfigured: data.isConfigured,
            businessAccountsCount: data.businessAccounts?.length || 0,
            selectedAccount: data.selectedAccount?.name,
            followerCount: data.selectedAccount?.followerCount,
            lastActivity: data.lastUpdated
          };
        } catch (error) {
          return {
            platform,
            name: getPlatformName(platform),
            isConnected: false,
            isConfigured: false,
            businessAccountsCount: 0
          };
        }
      });

      const summaries = await Promise.all(configPromises);
      setPlatformSummaries(summaries);

      // Calculate stats
      const newStats: DashboardStats = {
        totalPlatforms: platforms.length,
        connectedPlatforms: summaries.filter(s => s.isConnected).length,
        configuredPlatforms: summaries.filter(s => s.isConfigured).length,
        totalBusinessAccounts: summaries.reduce((sum, s) => sum + s.businessAccountsCount, 0),
        selectedAccounts: summaries.filter(s => s.selectedAccount).length,
        lastUpdated: new Date().toISOString()
      };

      setStats(newStats);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('فشل في تحميل بيانات لوحة التحكم');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAllPlatforms = async () => {
    setIsRefreshing(true);
    
    try {
      const platforms = ['facebook', 'linkedin'];
      const refreshPromises = platforms.map(async (platform) => {
        try {
          const response = await fetch('/api/social/business-accounts/refresh', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ platform, userId })
          });
          
          if (response.ok) {
            return { platform, success: true };
          } else {
            return { platform, success: false };
          }
        } catch (error) {
          return { platform, success: false };
        }
      });

      const results = await Promise.all(refreshPromises);
      const successCount = results.filter(r => r.success).length;
      
      if (successCount > 0) {
        toast.success(`تم تحديث ${successCount} منصة بنجاح`);
        await loadDashboardData();
      } else {
        toast.error('فشل في تحديث المنصات');
      }

    } catch (error) {
      console.error('Error refreshing platforms:', error);
      toast.error('فشل في تحديث المنصات');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleConfigurationChange = (platform: string, config: BusinessAccountConfig) => {
    // Update platform summary when configuration changes
    setPlatformSummaries(prev => prev.map(summary => 
      summary.platform === platform 
        ? {
            ...summary,
            isConnected: !config.requiresReconnection,
            isConfigured: config.isConfigured,
            businessAccountsCount: config.businessAccounts.length,
            selectedAccount: config.selectedAccount?.name,
            followerCount: config.selectedAccount?.followerCount,
            lastActivity: config.lastUpdated
          }
        : summary
    ));

    // Recalculate stats
    const updatedSummaries = platformSummaries.map(summary => 
      summary.platform === platform 
        ? {
            ...summary,
            isConnected: !config.requiresReconnection,
            isConfigured: config.isConfigured,
            businessAccountsCount: config.businessAccounts.length,
            selectedAccount: config.selectedAccount?.name
          }
        : summary
    );

    const newStats: DashboardStats = {
      totalPlatforms: 4,
      connectedPlatforms: updatedSummaries.filter(s => s.isConnected).length,
      configuredPlatforms: updatedSummaries.filter(s => s.isConfigured).length,
      totalBusinessAccounts: updatedSummaries.reduce((sum, s) => sum + s.businessAccountsCount, 0),
      selectedAccounts: updatedSummaries.filter(s => s.selectedAccount).length,
      lastUpdated: new Date().toISOString()
    };

    setStats(newStats);
  };

  const getPlatformName = (platform: string) => {
    const names: Record<string, string> = {
      facebook: 'فيسبوك',
      linkedin: 'لينكد إن',
      instagram: 'إنستغرام',
      twitter: 'تويتر'
    };
    return names[platform] || platform;
  };

  const getConnectionProgress = () => {
    return (stats.connectedPlatforms / stats.totalPlatforms) * 100;
  };

  const getConfigurationProgress = () => {
    return (stats.configuredPlatforms / stats.totalPlatforms) * 100;
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-6 h-6" />
              لوحة تحكم الحسابات التجارية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
              <span className="mr-2 text-muted-foreground">جاري تحميل البيانات...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة الحسابات التجارية</h1>
          <p className="text-muted-foreground">
            إدارة وتكوين حساباتك التجارية عبر جميع منصات التواصل الاجتماعي
          </p>
        </div>
        <Button 
          onClick={refreshAllPlatforms} 
          disabled={isRefreshing}
          className="flex items-center gap-2"
        >
          {isRefreshing ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              جاري التحديث...
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4" />
              تحديث الكل
            </>
          )}
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">المنصات المتصلة</p>
                <p className="text-2xl font-bold">{stats.connectedPlatforms}/{stats.totalPlatforms}</p>
              </div>
              <Globe className="w-8 h-8 text-blue-500" />
            </div>
            <Progress value={getConnectionProgress()} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">المنصات المكونة</p>
                <p className="text-2xl font-bold">{stats.configuredPlatforms}/{stats.totalPlatforms}</p>
              </div>
              <Settings className="w-8 h-8 text-green-500" />
            </div>
            <Progress value={getConfigurationProgress()} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">الحسابات التجارية</p>
                <p className="text-2xl font-bold">{stats.totalBusinessAccounts}</p>
              </div>
              <Building className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">الحسابات المختارة</p>
                <p className="text-2xl font-bold">{stats.selectedAccounts}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Platform Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            ملخص المنصات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {platformSummaries.map((summary) => (
              <div key={summary.platform} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    summary.isConnected ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <div>
                    <h3 className="font-medium">{summary.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {summary.businessAccountsCount} حساب تجاري
                      {summary.selectedAccount && ` • ${summary.selectedAccount}`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {summary.followerCount && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {summary.followerCount.toLocaleString()}
                    </Badge>
                  )}
                  <Badge variant={summary.isConfigured ? 'default' : 'secondary'}>
                    {summary.isConfigured ? 'مكون' : 'غير مكون'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Account Selector */}
      <UnifiedBusinessAccountSelector
        userId={userId}
        onConfigurationChange={handleConfigurationChange}
        onAccountSelected={(platform, accountId) => {
          toast.success(`تم اختيار حساب ${getPlatformName(platform)} بنجاح`);
        }}
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            إجراءات سريعة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="flex items-center gap-2 h-auto p-4">
              <Calendar className="w-5 h-5" />
              <div className="text-right">
                <div className="font-medium">جدولة منشور</div>
                <div className="text-sm text-muted-foreground">إنشاء منشور جديد</div>
              </div>
            </Button>
            
            <Button variant="outline" className="flex items-center gap-2 h-auto p-4">
              <BarChart3 className="w-5 h-5" />
              <div className="text-right">
                <div className="font-medium">عرض التحليلات</div>
                <div className="text-sm text-muted-foreground">إحصائيات الأداء</div>
              </div>
            </Button>
            
            <Button variant="outline" className="flex items-center gap-2 h-auto p-4">
              <Settings className="w-5 h-5" />
              <div className="text-right">
                <div className="font-medium">إعدادات متقدمة</div>
                <div className="text-sm text-muted-foreground">تخصيص الإعدادات</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Last Updated */}
      <div className="text-center text-sm text-muted-foreground">
        آخر تحديث: {new Date(stats.lastUpdated).toLocaleString('ar-SA')}
      </div>
    </div>
  );
}
