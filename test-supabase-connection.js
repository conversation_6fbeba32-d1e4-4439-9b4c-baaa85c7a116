const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase Connection...\n');

  // Get environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  console.log('📋 Environment Variables:');
  console.log(`NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`);
  console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${supabaseAnonKey ? '✅ Set' : '❌ Missing'}`);
  console.log(`SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceKey ? '✅ Set' : '❌ Missing'}\n`);

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing required Supabase environment variables');
    return;
  }

  // Test anon client
  console.log('🔗 Testing Anon Client Connection...');
  const anonClient = createClient(supabaseUrl, supabaseAnonKey);

  try {
    const { data: anonData, error: anonError } = await anonClient
      .from('users')
      .select('count')
      .limit(1);

    if (anonError) {
      console.log(`⚠️  Anon client error (expected for RLS): ${anonError.message}`);
    } else {
      console.log('✅ Anon client connected successfully');
    }
  } catch (error) {
    console.log(`⚠️  Anon client connection issue: ${error.message}`);
  }

  // Test service role client
  if (supabaseServiceKey) {
    console.log('\n🔗 Testing Service Role Client Connection...');
    const serviceClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    try {
      // Test basic connection
      const { data: serviceData, error: serviceError } = await serviceClient
        .from('users')
        .select('id, email, name')
        .limit(5);

      if (serviceError) {
        console.error(`❌ Service client error: ${serviceError.message}`);
      } else {
        console.log(`✅ Service client connected successfully`);
        console.log(`📊 Found ${serviceData?.length || 0} users in database`);

        if (serviceData && serviceData.length > 0) {
          console.log('👥 Sample users:');
          serviceData.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.email} (${user.name || 'No name'})`);
          });
        }
      }
    } catch (error) {
      console.error(`❌ Service client connection failed: ${error.message}`);
    }
  }

  console.log('\n🎯 Connection Test Complete!');
}

// Run the test
testSupabaseConnection().catch(console.error);