import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dHPGCdWDJV-BEeQTnNhSDcck8PnvRMYKCIo2xn49Yqg';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🧪 Starting eWasl Authentication Flow Tests...\n');

// Test 1: Supabase Connection
async function testSupabaseConnection() {
  console.log('📡 Test 1: Supabase Connection');
  try {
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log('❌ Connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Supabase connection successful');
    console.log(`   Session status: ${data.session ? 'Active' : 'None'}`);
    return true;
  } catch (error) {
    console.log('❌ Connection test failed:', error.message);
    return false;
  }
}

// Test 2: User Registration
async function testUserRegistration() {
  console.log('\n👤 Test 2: User Registration');
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123';
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          name: 'Test User',
          role: 'USER'
        }
      }
    });
    
    if (error) {
      console.log('❌ Registration failed:', error.message);
      return false;
    }
    
    console.log('✅ User registration successful');
    console.log(`   User ID: ${data.user?.id}`);
    console.log(`   Email: ${data.user?.email}`);
    console.log(`   Session: ${data.session ? 'Created' : 'Pending confirmation'}`);
    
    // Store test user for cleanup
    global.testUserId = data.user?.id;
    global.testUserEmail = testEmail;
    global.testUserPassword = testPassword;
    
    return true;
  } catch (error) {
    console.log('❌ Registration test failed:', error.message);
    return false;
  }
}

// Test 3: User Sign In
async function testUserSignIn() {
  console.log('\n🔐 Test 3: User Sign In');
  
  // First, try to create a demo user if it doesn't exist
  const demoEmail = '<EMAIL>';
  const demoPassword = 'demo123456';
  
  try {
    // Try to sign in first
    let { data, error } = await supabase.auth.signInWithPassword({
      email: demoEmail,
      password: demoPassword,
    });
    
    if (error && error.message.includes('Invalid login credentials')) {
      console.log('   Demo user not found, creating...');
      
      // Create demo user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: demoEmail,
        password: demoPassword,
        options: {
          data: {
            name: 'Demo User',
            role: 'USER'
          }
        }
      });
      
      if (signUpError) {
        console.log('❌ Failed to create demo user:', signUpError.message);
        return false;
      }
      
      console.log('✅ Demo user created successfully');
      
      // Try to sign in again
      ({ data, error } = await supabase.auth.signInWithPassword({
        email: demoEmail,
        password: demoPassword,
      }));
    }
    
    if (error) {
      console.log('❌ Sign in failed:', error.message);
      return false;
    }
    
    console.log('✅ User sign in successful');
    console.log(`   User ID: ${data.user?.id}`);
    console.log(`   Email: ${data.user?.email}`);
    console.log(`   Session: ${data.session ? 'Active' : 'None'}`);
    
    // Store session for further tests
    global.currentSession = data.session;
    
    return true;
  } catch (error) {
    console.log('❌ Sign in test failed:', error.message);
    return false;
  }
}

// Test 4: Session Management
async function testSessionManagement() {
  console.log('\n🎫 Test 4: Session Management');
  
  try {
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log('❌ Session retrieval failed:', error.message);
      return false;
    }
    
    if (data.session) {
      console.log('✅ Session management working');
      console.log(`   Session expires: ${new Date(data.session.expires_at * 1000).toLocaleString()}`);
      console.log(`   Access token: ${data.session.access_token.substring(0, 20)}...`);
      return true;
    } else {
      console.log('⚠️  No active session found');
      return false;
    }
  } catch (error) {
    console.log('❌ Session management test failed:', error.message);
    return false;
  }
}

// Test 5: Sign Out
async function testSignOut() {
  console.log('\n🚪 Test 5: Sign Out');
  
  try {
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.log('❌ Sign out failed:', error.message);
      return false;
    }
    
    // Verify session is cleared
    const { data } = await supabase.auth.getSession();
    
    if (data.session) {
      console.log('❌ Session still active after sign out');
      return false;
    }
    
    console.log('✅ Sign out successful');
    console.log('   Session cleared');
    return true;
  } catch (error) {
    console.log('❌ Sign out test failed:', error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const results = [];
  
  results.push(await testSupabaseConnection());
  results.push(await testUserRegistration());
  results.push(await testUserSignIn());
  results.push(await testSessionManagement());
  results.push(await testSignOut());
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n📊 Test Results Summary:');
  console.log('═'.repeat(50));
  console.log(`✅ Passed: ${passed}/${total} tests`);
  console.log(`❌ Failed: ${total - passed}/${total} tests`);
  
  if (passed === total) {
    console.log('\n🎉 All authentication flows working perfectly!');
    console.log('✅ Task 1.2 COMPLETED: User Registration Flow');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the logs above.');
  }
  
  return passed === total;
}

// Execute tests
runAllTests().catch(console.error);
