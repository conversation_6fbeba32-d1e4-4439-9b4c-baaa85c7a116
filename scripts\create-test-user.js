const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

// Supabase configuration
const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAzMzgwNiwiZXhwIjoyMDYzNjA5ODA2fQ.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestUser() {
  console.log('🚀 Creating test user for eWasl...');

  try {
    // Test user credentials
    const testUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        name: 'eWasl Admin',
        role: 'ADMIN'
      },
      {
        email: '<EMAIL>',
        password: 'demo123456',
        name: 'Demo User',
        role: 'USER'
      }
    ];

    // Get all existing users once
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
    if (listError) {
      throw new Error(`Failed to list users: ${listError.message}`);
    }

    for (const testUser of testUsers) {
      console.log('\n📧 Creating user:', testUser.email);

      // Hash the password
      const hashedPassword = await bcrypt.hash(testUser.password, 12);
      console.log('🔐 Password hashed successfully');

      let authUser = existingUsers.users.find(u => u.email === testUser.email);

    if (!authUser) {
      // Create new user in Supabase Auth
      const { data: newAuthUser, error: authError } = await supabase.auth.admin.createUser({
        email: testUser.email,
        password: testUser.password,
        email_confirm: true,
        user_metadata: {
          name: testUser.name,
          role: testUser.role
        }
      });

      if (authError) {
        console.error('❌ Auth user creation failed:', authError.message);
        throw authError;
      }

      authUser = newAuthUser.user;
      console.log('✅ New auth user created:', authUser.id);
    } else {
      console.log('✅ Found existing auth user:', authUser.id);

      // Update password for existing user
      const { error: updateError } = await supabase.auth.admin.updateUserById(authUser.id, {
        password: testUser.password,
        user_metadata: {
          name: testUser.name,
          role: testUser.role
        }
      });

      if (updateError) {
        console.warn('⚠️ Password update failed:', updateError.message);
      } else {
        console.log('✅ Password updated for existing user');
      }
    }

    // Now create/update the profile in public.users table
    const { data: profileData, error: profileError } = await supabase
      .from('users')
      .upsert({
        id: authUser.id,
        email: testUser.email,
        name: testUser.name,
        role: testUser.role,
        email_verified: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      });

    if (profileError) {
      console.error('❌ Profile upsert failed:', profileError.message);
      throw profileError;
    }

    console.log('✅ User profile created/updated successfully');

    // Create some sample social accounts
    const userId = authUser?.id || authUser?.user?.id;
    if (!userId) {
      console.error('❌ Could not determine user ID from auth user:', authUser);
      return;
    }

    const sampleAccounts = [
      {
        user_id: userId,
        platform: 'TWITTER',
        account_id: 'ewasl_official',
        account_name: '@ewasl_official',
        access_token: 'sample_twitter_token',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        user_id: userId,
        platform: 'FACEBOOK',
        account_id: 'ewasl_page',
        account_name: '@eWasl Page',
        access_token: 'sample_facebook_token',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const { data: accountsData, error: accountsError } = await supabase
      .from('social_accounts')
      .insert(sampleAccounts);

    if (accountsError) {
      console.warn('⚠️ Sample social accounts creation failed:', accountsError.message);
    } else {
      console.log('✅ Sample social accounts created');
    }

    // Create some sample posts
    const samplePosts = [
      {
        user_id: userId,
        content: 'أهلاً بكم في منصة eWasl لإدارة وسائل التواصل الاجتماعي!',
        status: 'PUBLISHED',
        published_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        user_id: userId,
        content: 'نحن نعمل على تحسين المنصة لتوفير تجربة أفضل للمستخدمين.',
        status: 'SCHEDULED',
        scheduled_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        user_id: userId,
        content: 'هذا منشور تجريبي لاختبار واجهة المستخدم.',
        status: 'DRAFT',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const { data: postsData, error: postsError } = await supabase
      .from('posts')
      .insert(samplePosts);

    if (postsError) {
      console.warn('⚠️ Sample posts creation failed:', postsError.message);
    } else {
      console.log('✅ Sample posts created');
    }

    }

    console.log('\n🎉 Test users setup completed successfully!');
    console.log('\n📋 Available Test Accounts:');
    testUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name}:`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   🔑 Password: ${user.password}`);
    });
    console.log('\n🌐 Login URL: https://app.ewasl.com/auth/signin');

  } catch (error) {
    console.error('💥 Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createTestUser();
}

module.exports = { createTestUser };
