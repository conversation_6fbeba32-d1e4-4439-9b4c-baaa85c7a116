import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('📊 Publishing stats API endpoint called');

    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters for date filtering
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    console.log(`📈 Calculating stats for last ${days} days`);

    // Get publishing logs for the user
    const { data: publishingLogs, error: logsError } = await supabase
      .from('publishing_logs')
      .select(`
        *,
        publishing_results (*)
      `)
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (logsError) {
      console.error('Error fetching publishing logs:', logsError);
      return NextResponse.json(
        { error: 'Failed to fetch publishing statistics' },
        { status: 500 }
      );
    }

    // Get scheduled posts
    const { data: scheduledPosts, error: scheduledError } = await supabase
      .from('scheduled_posts')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'scheduled')
      .gte('scheduled_at', new Date().toISOString());

    if (scheduledError) {
      console.error('Error fetching scheduled posts:', scheduledError);
      // Don't fail the request for scheduled posts error
    }

    // Calculate statistics
    const logs = publishingLogs || [];
    const scheduled = scheduledPosts || [];

    const totalPosts = logs.length;
    const scheduledPostsCount = scheduled.length;
    
    // Count successful and failed posts
    let successfulPosts = 0;
    let failedPosts = 0;
    let totalEngagement = 0;
    const platformCounts: Record<string, number> = {};

    logs.forEach(log => {
      if (log.status === 'completed') {
        successfulPosts++;
      } else if (log.status === 'failed') {
        failedPosts++;
      }

      // Count platform usage and engagement
      if (log.publishing_results && Array.isArray(log.publishing_results)) {
        log.publishing_results.forEach((result: any) => {
          // Count platform usage
          platformCounts[result.platform] = (platformCounts[result.platform] || 0) + 1;

          // Sum engagement
          if (result.engagement_data) {
            const engagement = result.engagement_data;
            totalEngagement += (engagement.likes || 0) + 
                             (engagement.shares || 0) + 
                             (engagement.comments || 0) + 
                             (engagement.views || 0);
          }
        });
      }
    });

    // Find top platform
    const topPlatform = Object.entries(platformCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || '';

    // Get platform names in Arabic
    const getPlatformName = (platform: string) => {
      const names: Record<string, string> = {
        facebook: 'فيسبوك',
        linkedin: 'لينكد إن',
        instagram: 'إنستغرام',
        twitter: 'تويتر'
      };
      return names[platform] || platform;
    };

    // Calculate engagement by platform
    const platformEngagement: Record<string, number> = {};
    const platformPostCounts: Record<string, number> = {};

    logs.forEach(log => {
      if (log.publishing_results && Array.isArray(log.publishing_results)) {
        log.publishing_results.forEach((result: any) => {
          const platform = result.platform;
          
          if (!platformEngagement[platform]) {
            platformEngagement[platform] = 0;
            platformPostCounts[platform] = 0;
          }

          platformPostCounts[platform]++;

          if (result.engagement_data) {
            const engagement = result.engagement_data;
            platformEngagement[platform] += (engagement.likes || 0) + 
                                          (engagement.shares || 0) + 
                                          (engagement.comments || 0) + 
                                          (engagement.views || 0);
          }
        });
      }
    });

    // Calculate average engagement per platform
    const platformAverages = Object.entries(platformEngagement).map(([platform, engagement]) => ({
      platform: getPlatformName(platform),
      totalEngagement: engagement,
      postCount: platformPostCounts[platform],
      averageEngagement: platformPostCounts[platform] > 0 
        ? Math.round(engagement / platformPostCounts[platform])
        : 0
    }));

    // Get recent activity (last 7 days)
    const recentDate = new Date();
    recentDate.setDate(recentDate.getDate() - 7);

    const recentLogs = logs.filter(log => 
      new Date(log.created_at) >= recentDate
    );

    const recentActivity = recentLogs.map(log => ({
      date: log.created_at,
      status: log.status,
      platforms: log.platforms || [],
      successfulPlatforms: log.successful_platforms || 0,
      failedPlatforms: log.failed_platforms || 0
    }));

    // Calculate success rate
    const successRate = totalPosts > 0 
      ? Math.round((successfulPosts / totalPosts) * 100)
      : 0;

    // Prepare response
    const stats = {
      totalPosts,
      scheduledPosts: scheduledPostsCount,
      successfulPosts,
      failedPosts,
      totalEngagement,
      topPlatform: getPlatformName(topPlatform),
      successRate,
      averageEngagement: totalPosts > 0 ? Math.round(totalEngagement / totalPosts) : 0,
      platformBreakdown: platformAverages,
      recentActivity,
      dateRange: {
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString(),
        days
      }
    };

    console.log('✅ Publishing stats calculated:', {
      totalPosts: stats.totalPosts,
      successfulPosts: stats.successfulPosts,
      scheduledPosts: stats.scheduledPosts,
      topPlatform: stats.topPlatform
    });

    return NextResponse.json({
      success: true,
      stats,
      message: `Statistics calculated for the last ${days} days`
    });

  } catch (error: any) {
    console.error('❌ Publishing stats API error:', error);

    // Log error for debugging
    try {
      
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        await supabase
          .from('error_logs')
          .insert({
            user_id: user.id,
            error_type: 'publishing_stats_error',
            error_message: error.message,
            error_stack: error.stack,
            created_at: new Date().toISOString()
          });
      }
    } catch (logError) {
      console.error('Error logging error:', logError);
    }

    return NextResponse.json(
      { 
        error: 'Failed to calculate publishing statistics',
        message: error.message || 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('📊 Publishing stats refresh endpoint called');

    // Get authenticated user
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { refreshEngagement = false } = body;

    if (refreshEngagement) {
      console.log('🔄 Refreshing engagement data...');

      // Get recent publishing results that need engagement refresh
      const { data: results, error: resultsError } = await supabase
        .from('publishing_results')
        .select(`
          *,
          publishing_logs!inner(user_id)
        `)
        .eq('publishing_logs.user_id', user.id)
        .not('post_id', 'is', null)
        .gte('published_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()); // Last 7 days

      if (resultsError) {
        throw new Error(`Failed to fetch results for refresh: ${resultsError.message}`);
      }

      // TODO: Implement engagement refresh for each platform
      // This would involve calling each platform's API to get updated engagement metrics
      
      console.log(`📈 Found ${results?.length || 0} posts to refresh engagement for`);

      return NextResponse.json({
        success: true,
        message: 'Engagement refresh initiated',
        refreshedPosts: results?.length || 0
      });
    }

    return NextResponse.json({
      success: true,
      message: 'No refresh action specified'
    });

  } catch (error: any) {
    console.error('❌ Publishing stats refresh error:', error);
    return NextResponse.json(
      { error: 'Failed to refresh publishing statistics' },
      { status: 500 }
    );
  }
}
