'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { TeamDashboard } from '@/components/teams/team-dashboard';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, Plus, Briefcase } from 'lucide-react';
import { toast } from 'sonner';

export default function TeamsPage() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [hasOrganizations, setHasOrganizations] = useState(false);

  useEffect(() => {
    checkUserAndOrganizations();
  }, []);

  const checkUserAndOrganizations = async () => {
    try {
      const supabase = createClient();
      
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        // Redirect to login if not authenticated
        window.location.href = '/login';
        return;
      }

      setUser(user);

      // Check if user has any organizations
      const response = await fetch('/api/teams/organizations');
      const result = await response.json();

      if (result.success && result.organizations.length > 0) {
        setHasOrganizations(true);
      }

    } catch (error) {
      console.error('Error checking user and organizations:', error);
      toast.error('خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const createFirstOrganization = async () => {
    try {
      const orgName = prompt('اسم المؤسسة:');
      if (!orgName) return;

      const orgSlug = orgName.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      const response = await fetch('/api/teams/organizations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: orgName,
          slug: orgSlug,
          description: 'مؤسستي الأولى',
          subscription_plan: 'free',
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('تم إنشاء المؤسسة بنجاح');
        setHasOrganizations(true);
        // Refresh the page to load the new organization
        window.location.reload();
      } else {
        toast.error(result.error || 'فشل في إنشاء المؤسسة');
      }
    } catch (error) {
      console.error('Error creating organization:', error);
      toast.error('خطأ في إنشاء المؤسسة');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Users className="h-8 w-8 animate-pulse mx-auto mb-4" />
          <p>جاري تحميل لوحة الفريق...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">تسجيل الدخول مطلوب</h3>
            <p className="text-gray-600 mb-4">
              يرجى تسجيل الدخول للوصول إلى لوحة الفريق
            </p>
            <Button onClick={() => window.location.href = '/login'}>
              تسجيل الدخول
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!hasOrganizations) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Briefcase className="h-8 w-8 text-blue-600" />
              </div>
              
              <h1 className="text-2xl font-bold mb-4">مرحباً بك في لوحة الفريق</h1>
              <p className="text-gray-600 mb-8">
                ابدأ رحلتك في التعاون مع فريقك من خلال إنشاء مؤسستك الأولى. 
                ستتمكن من دعوة أعضاء الفريق، إدارة المحتوى، وتنظيم سير العمل.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold mb-2">إدارة الفريق</h3>
                  <p className="text-sm text-gray-600">
                    دعوة أعضاء الفريق وإدارة الصلاحيات
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Briefcase className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold mb-2">مساحات العمل</h3>
                  <p className="text-sm text-gray-600">
                    تنظيم المشاريع والعملاء في مساحات منفصلة
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Plus className="h-6 w-6 text-orange-600" />
                  </div>
                  <h3 className="font-semibold mb-2">سير الموافقة</h3>
                  <p className="text-sm text-gray-600">
                    إعداد عمليات موافقة المحتوى قبل النشر
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <Button 
                  size="lg" 
                  className="w-full sm:w-auto"
                  onClick={createFirstOrganization}
                >
                  <Plus className="h-5 w-5 mr-2" />
                  إنشاء مؤسستي الأولى
                </Button>
                
                <div className="text-sm text-gray-500">
                  <p>
                    ✨ <strong>مجاني للبدء:</strong> يمكنك إنشاء مؤسسة مجانية تتضمن 3 أعضاء و 5 حسابات اجتماعية
                  </p>
                </div>
              </div>
            </div>

            {/* Features Overview */}
            <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-6 text-right">
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-3">🚀 ميزات الخطة المجانية</h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• حتى 3 أعضاء في الفريق</li>
                    <li>• 5 حسابات اجتماعية</li>
                    <li>• 100 منشور شهرياً</li>
                    <li>• مساحات عمل متعددة</li>
                    <li>• سير موافقة أساسي</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-3">💼 ميزات الخطة الاحترافية</h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• حتى 25 عضو في الفريق</li>
                    <li>• 50 حساب اجتماعي</li>
                    <li>• 1000 منشور شهرياً</li>
                    <li>• تحليلات متقدمة</li>
                    <li>• سير موافقة مخصص</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <TeamDashboard />
      </div>
    </div>
  );
}
