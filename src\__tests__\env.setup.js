// Environment setup for tests
process.env.NODE_ENV = 'test';

// Next.js environment variables
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000';
process.env.NEXT_PUBLIC_SITE_URL = 'http://localhost:3000';

// Supabase test environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
process.env.SUPABASE_JWT_SECRET = 'test-jwt-secret';

// Database test configuration
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/ewasl_test';

// API test configuration
process.env.API_SECRET_KEY = 'test-api-secret-key';
process.env.NEXTAUTH_SECRET = 'test-nextauth-secret';
process.env.NEXTAUTH_URL = 'http://localhost:3000';

// Social media API test keys (mock values)
process.env.TWITTER_API_KEY = 'test-twitter-api-key';
process.env.TWITTER_API_SECRET = 'test-twitter-api-secret';
process.env.FACEBOOK_APP_ID = 'test-facebook-app-id';
process.env.FACEBOOK_APP_SECRET = 'test-facebook-app-secret';
process.env.LINKEDIN_CLIENT_ID = 'test-linkedin-client-id';
process.env.LINKEDIN_CLIENT_SECRET = 'test-linkedin-client-secret';

// Email service test configuration
process.env.SENDGRID_API_KEY = 'test-sendgrid-api-key';
process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';

// Payment service test configuration
process.env.STRIPE_SECRET_KEY = 'sk_test_test-stripe-secret-key';
process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY = 'pk_test_test-stripe-publishable-key';

// AI service test configuration
process.env.OPENAI_API_KEY = 'test-openai-api-key';
process.env.OPENROUTER_API_KEY = 'test-openrouter-api-key';

// File upload test configuration
process.env.CLOUDINARY_CLOUD_NAME = 'test-cloudinary';
process.env.CLOUDINARY_API_KEY = 'test-cloudinary-api-key';
process.env.CLOUDINARY_API_SECRET = 'test-cloudinary-api-secret';

// Analytics test configuration
process.env.GOOGLE_ANALYTICS_ID = 'GA-TEST-123456';

// Feature flags for testing
process.env.ENABLE_TEAM_COLLABORATION = 'true';
process.env.ENABLE_ADVANCED_ANALYTICS = 'true';
process.env.ENABLE_AI_FEATURES = 'true';
process.env.ENABLE_PAYMENT_PROCESSING = 'true';

// Test-specific configuration
process.env.TEST_USER_EMAIL = '<EMAIL>';
process.env.TEST_USER_PASSWORD = 'testpassword123';
process.env.TEST_ORGANIZATION_ID = 'test-org-id';
process.env.TEST_WORKSPACE_ID = 'test-workspace-id';

// Disable telemetry and analytics in tests
process.env.NEXT_TELEMETRY_DISABLED = '1';
process.env.DISABLE_ANALYTICS = 'true';

// Set test timeouts
process.env.TEST_TIMEOUT = '30000';
process.env.API_TIMEOUT = '10000';

// Mock external services in test environment
process.env.MOCK_EXTERNAL_APIS = 'true';
process.env.MOCK_EMAIL_SERVICE = 'true';
process.env.MOCK_PAYMENT_SERVICE = 'true';
process.env.MOCK_AI_SERVICE = 'true';

// Logging configuration for tests
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests
process.env.DISABLE_REQUEST_LOGGING = 'true';

// Rate limiting configuration for tests
process.env.DISABLE_RATE_LIMITING = 'true';
process.env.TEST_MODE_RATE_LIMIT = '1000'; // High limit for tests

// Cache configuration for tests
process.env.DISABLE_CACHING = 'true'; // Disable caching for predictable tests
process.env.CACHE_TTL = '0';

// Security configuration for tests
process.env.DISABLE_CSRF_PROTECTION = 'true'; // For API testing
process.env.ALLOW_INSECURE_CONNECTIONS = 'true'; // For local testing

console.log('Test environment configured successfully');
