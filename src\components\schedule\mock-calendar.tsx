"use client"

import { useState } from "react";
import { format } from "date-fns";
import { arSA } from "date-fns/locale";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatDate, formatTime, getPlatformLabel } from "@/lib/utils";
import Link from "next/link";

interface Post {
  id: string;
  title: string;
  content: string;
  start: Date;
  end: Date;
  status: "DRAFT" | "SCHEDULED" | "PUBLISHED" | "FAILED";
  platforms: string[];
}

export function MockCalendar() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Post | null>(null);

  const mockPosts: Post[] = [
    {
      id: "1",
      title: "منشور تجريبي 1",
      content: "هذا منشور تجريبي للتقويم",
      start: new Date(),
      end: new Date(new Date().getTime() + 60 * 60 * 1000),
      status: "SCHEDULED",
      platforms: ["TWITTER", "FACEBOOK"],
    },
    {
      id: "2",
      title: "منشور تجريبي 2",
      content: "هذا منشور تجريبي آخر للتقويم",
      start: new Date(new Date().getTime() + 24 * 60 * 60 * 1000),
      end: new Date(new Date().getTime() + 25 * 60 * 60 * 1000),
      status: "DRAFT",
      platforms: ["INSTAGRAM"],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "bg-gray-500";
      case "SCHEDULED":
        return "bg-blue-500";
      case "PUBLISHED":
        return "bg-green-500";
      case "FAILED":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "مسودة";
      case "SCHEDULED":
        return "مجدول";
      case "PUBLISHED":
        return "منشور";
      case "FAILED":
        return "فشل";
      default:
        return status;
    }
  };

  const handleEventClick = (event: Post) => {
    setSelectedEvent(event);
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="bg-card rounded-lg border p-4">
        <h3 className="text-lg font-medium mb-4">المنشورات المجدولة</h3>
        <div className="space-y-4">
          {mockPosts.map((post) => (
            <div 
              key={post.id} 
              className="p-4 border rounded-md cursor-pointer hover:bg-accent"
              onClick={() => handleEventClick(post)}
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium">{post.content}</p>
                  <p className="text-sm text-muted-foreground">
                    {format(post.start, "PPP", { locale: arSA })} - {format(post.start, "p", { locale: arSA })}
                  </p>
                </div>
                <Badge variant="default" className={getStatusColor(post.status)}>
                  {getStatusLabel(post.status)}
                </Badge>
              </div>
              <div className="mt-2 flex gap-2">
                {post.platforms.map((platform) => (
                  <Badge key={platform} variant="outline">
                    {getPlatformLabel(platform)}
                  </Badge>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        {selectedEvent && (
          <DialogContent className="sm:max-w-md" dir="rtl">
            <DialogHeader>
              <DialogTitle>تفاصيل المنشور</DialogTitle>
              <DialogDescription>
                معلومات عن المنشور المجدول
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">المحتوى</p>
                <p className="text-sm text-muted-foreground">
                  {selectedEvent.content}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">الحالة</p>
                <Badge variant="default" className={getStatusColor(selectedEvent.status)}>
                  {getStatusLabel(selectedEvent.status)}
                </Badge>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">موعد النشر</p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(selectedEvent.start)} - {formatTime(selectedEvent.start)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">المنصات</p>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedEvent.platforms.map((platform) => (
                    <Badge key={platform} variant="outline">
                      {getPlatformLabel(platform)}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter className="sm:justify-between">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                إغلاق
              </Button>
              <Link href={`/posts/${selectedEvent.id}`}>
                <Button>عرض التفاصيل</Button>
              </Link>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </div>
  );
}
