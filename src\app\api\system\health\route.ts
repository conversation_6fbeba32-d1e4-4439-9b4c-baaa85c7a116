import { NextRequest, NextResponse } from 'next/server';
import { createDigitalOceanClient } from '@/lib/digitalocean/client';
import { healthMonitor } from '@/lib/monitoring/health-monitor';
import { productionConfig } from '@/lib/config/production-config';
import { createEnhancedStripeClient } from '@/lib/stripe/enhanced-client';
import { createClient } from '@supabase/supabase-js';

// Force dynamic rendering for production deployment
export const dynamic = 'force-dynamic';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  details?: any;
  error?: string;
}

interface SystemHealthResponse {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: HealthCheckResult[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
}

/**
 * Test DigitalOcean API connectivity
 */
async function testDigitalOcean(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    const client = createDigitalOceanClient();
    const result = await client.healthCheck();
    const responseTime = Date.now() - startTime;

    if (result.success && result.data) {
      return {
        service: 'DigitalOcean API',
        status: 'healthy',
        responseTime,
        details: {
          account_email: result.data.account.email,
          account_status: result.data.account.status,
          droplet_limit: result.data.account.droplet_limit
        }
      };
    } else {
      return {
        service: 'DigitalOcean API',
        status: 'unhealthy',
        responseTime,
        error: result.error || 'Unknown error'
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      service: 'DigitalOcean API',
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Connection failed'
    };
  }
}

/**
 * Test Stripe API connectivity
 */
async function testStripe(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    const client = createEnhancedStripeClient();
    const result = await client.healthCheck();
    const responseTime = Date.now() - startTime;

    if (result.success && result.data) {
      return {
        service: 'Stripe API',
        status: 'healthy',
        responseTime,
        details: {
          account_email: result.data.account.email,
          charges_enabled: result.data.account.charges_enabled,
          payouts_enabled: result.data.account.payouts_enabled,
          available_balance: result.data.balance.available.map(bal => ({
            currency: bal.currency,
            amount: bal.amount / 100
          }))
        }
      };
    } else {
      return {
        service: 'Stripe API',
        status: 'unhealthy',
        responseTime,
        error: result.error || 'Unknown error'
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      service: 'Stripe API',
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Connection failed'
    };
  }
}

/**
 * Test database connectivity
 */
async function testDatabase(): Promise<HealthCheckResult> {
  const startTime = Date.now();

  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    const responseTime = Date.now() - startTime;

    if (!error) {
      return {
        service: 'Database',
        status: 'healthy',
        responseTime,
        details: {
          connection: 'successful',
          provider: 'Supabase'
        }
      };
    } else {
      return {
        service: 'Database',
        status: 'unhealthy',
        responseTime,
        error: error.message
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      service: 'Database',
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Connection failed'
    };
  }
}

/**
 * GET /api/system/health - Comprehensive system health check
 */
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Starting production system health check...');

    // Get comprehensive health status from monitoring system
    const productionHealthStatus = await healthMonitor.getHealthStatus();

    // Run legacy health checks in parallel for backward compatibility
    const [digitalOceanResult, stripeResult, databaseResult] = await Promise.all([
      testDigitalOcean(),
      testStripe(),
      testDatabase()
    ]);

    const legacyServices = [digitalOceanResult, stripeResult, databaseResult];

    // Combine production monitoring with legacy checks
    const allServices = [
      ...legacyServices,
      ...productionHealthStatus.services.map(service => ({
        service: service.name,
        status: service.status,
        responseTime: service.responseTime,
        details: service.details,
        error: service.error
      }))
    ];

    // Calculate summary
    const summary = {
      total: allServices.length,
      healthy: allServices.filter(s => s.status === 'healthy').length,
      unhealthy: allServices.filter(s => s.status === 'unhealthy').length,
      degraded: allServices.filter(s => s.status === 'degraded').length
    };

    // Use production monitoring overall status as primary, fallback to calculated
    const overall = productionHealthStatus.status;

    // Enhanced response with production monitoring data
    const response = {
      overall,
      timestamp: new Date().toISOString(),
      services: allServices,
      summary,
      // Production monitoring enhancements
      production: {
        environment: productionHealthStatus.environment,
        uptime: Math.round(productionHealthStatus.uptime / 1000), // seconds
        version: productionHealthStatus.version,
        metrics: productionHealthStatus.metrics,
        alerts: productionHealthStatus.alerts,
        config: {
          enableAIOptimization: productionConfig.getFeatureFlags().enableAIOptimization,
          enableMetrics: productionConfig.getFeatureFlags().enableMetrics,
          enableCaching: productionConfig.getFeatureFlags().enableCaching,
          environment: productionConfig.getConfig().environment
        }
      }
    };

    console.log('Health check completed:', {
      overall,
      healthy: summary.healthy,
      total: summary.total
    });

    // Return appropriate HTTP status based on health
    const httpStatus = overall === 'healthy' ? 200 : overall === 'degraded' ? 207 : 503;

    return NextResponse.json(response, { status: httpStatus });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        overall: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check system failure',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/system/health - Test specific service
 */
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    const { service } = await request.json();

    let result: HealthCheckResult;

    switch (service) {
      case 'digitalocean':
        result = await testDigitalOcean();
        break;
      case 'stripe':
        result = await testStripe();
        break;
      case 'database':
        result = await testDatabase();
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid service. Use: digitalocean, stripe, or database' },
          { status: 400 }
        );
    }

    const httpStatus = result.status === 'healthy' ? 200 : 503;
    return NextResponse.json(result, { status: httpStatus });

  } catch (error) {
    console.error('Service test failed:', error);
    
    return NextResponse.json(
      {
        error: 'Service test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
