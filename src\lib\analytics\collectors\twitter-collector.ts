/**
 * Twitter Analytics Collector
 * Collects analytics data from Twitter API v2
 */

import { PlatformMetricsCollector, PostMetrics } from '../data-collector';

export class TwitterMetricsCollector implements PlatformMetricsCollector {
  platform = 'TWITTER';
  private apiKey: string;
  private apiSecret: string;
  private accessToken: string;
  private accessTokenSecret: string;

  constructor(credentials: {
    apiKey: string;
    apiSecret: string;
    accessToken: string;
    accessTokenSecret: string;
  }) {
    this.apiKey = credentials.apiKey;
    this.apiSecret = credentials.apiSecret;
    this.accessToken = credentials.accessToken;
    this.accessTokenSecret = credentials.accessTokenSecret;
  }

  /**
   * Collect metrics for a specific tweet
   */
  async collectPostMetrics(postId: string, tweetId: string): Promise<PostMetrics> {
    try {
      // Twitter API v2 endpoint for tweet metrics
      const response = await this.makeTwitterRequest(
        `https://api.twitter.com/2/tweets/${tweetId}`,
        {
          'tweet.fields': 'public_metrics,non_public_metrics,organic_metrics,promoted_metrics',
          'expansions': 'author_id',
        }
      );

      if (!response.data) {
        throw new Error('No tweet data found');
      }

      const tweet = response.data;
      const publicMetrics = tweet.public_metrics || {};
      const organicMetrics = tweet.organic_metrics || {};
      const nonPublicMetrics = tweet.non_public_metrics || {};

      return {
        impressions: organicMetrics.impression_count || nonPublicMetrics.impression_count || 0,
        reach: organicMetrics.user_profile_clicks || 0,
        likes: publicMetrics.like_count || 0,
        comments: publicMetrics.reply_count || 0,
        shares: publicMetrics.retweet_count || 0,
        clicks: organicMetrics.url_link_clicks || 0,
        retweets: publicMetrics.retweet_count || 0,
        quote_tweets: publicMetrics.quote_count || 0,
        replies: publicMetrics.reply_count || 0,
      };

    } catch (error) {
      console.error('Error collecting Twitter metrics:', error);
      
      // Return mock data for development/testing
      if (process.env.NODE_ENV === 'development') {
        return this.generateMockMetrics();
      }
      
      throw error;
    }
  }

  /**
   * Collect account-level metrics
   */
  async collectAccountMetrics(accountId: string): Promise<any> {
    try {
      const response = await this.makeTwitterRequest(
        `https://api.twitter.com/2/users/${accountId}`,
        {
          'user.fields': 'public_metrics,created_at,description,location,verified',
        }
      );

      if (!response.data) {
        throw new Error('No user data found');
      }

      const user = response.data;
      const metrics = user.public_metrics || {};

      return {
        followers_count: metrics.followers_count || 0,
        following_count: metrics.following_count || 0,
        tweet_count: metrics.tweet_count || 0,
        listed_count: metrics.listed_count || 0,
        verified: user.verified || false,
        created_at: user.created_at,
        description: user.description,
        location: user.location,
      };

    } catch (error) {
      console.error('Error collecting Twitter account metrics:', error);
      
      if (process.env.NODE_ENV === 'development') {
        return {
          followers_count: Math.floor(Math.random() * 10000) + 1000,
          following_count: Math.floor(Math.random() * 1000) + 100,
          tweet_count: Math.floor(Math.random() * 5000) + 500,
          listed_count: Math.floor(Math.random() * 100) + 10,
          verified: false,
        };
      }
      
      throw error;
    }
  }

  /**
   * Collect audience insights
   */
  async collectAudienceInsights(accountId: string): Promise<any> {
    try {
      // Note: Twitter API v2 doesn't provide detailed audience insights in the free tier
      // This would require Twitter Ads API or premium access
      
      // For now, return mock data structure
      return {
        age_groups: {
          '18-24': 25,
          '25-34': 35,
          '35-44': 25,
          '45-54': 10,
          '55+': 5,
        },
        gender_distribution: {
          male: 52,
          female: 46,
          other: 2,
        },
        location_data: {
          'United States': 40,
          'United Kingdom': 15,
          'Canada': 10,
          'Australia': 8,
          'Germany': 7,
          'Other': 20,
        },
        interests: {
          'Technology': 30,
          'Business': 25,
          'Marketing': 20,
          'Design': 15,
          'Other': 10,
        },
        languages: {
          'English': 85,
          'Spanish': 8,
          'French': 4,
          'Other': 3,
        },
        active_hours: {
          '0': 2, '1': 1, '2': 1, '3': 1, '4': 1, '5': 2,
          '6': 4, '7': 6, '8': 8, '9': 9, '10': 8, '11': 7,
          '12': 9, '13': 10, '14': 9, '15': 8, '16': 7, '17': 8,
          '18': 9, '19': 10, '20': 9, '21': 8, '22': 6, '23': 4,
        },
        device_types: {
          mobile: 70,
          desktop: 25,
          tablet: 5,
        },
        total_followers: Math.floor(Math.random() * 10000) + 1000,
        period_start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        period_end: new Date().toISOString(),
      };

    } catch (error) {
      console.error('Error collecting Twitter audience insights:', error);
      throw error;
    }
  }

  /**
   * Make authenticated request to Twitter API
   */
  private async makeTwitterRequest(url: string, params: Record<string, string> = {}): Promise<any> {
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;

    // In a real implementation, you would use OAuth 1.0a or OAuth 2.0 Bearer Token
    // For now, we'll simulate the request structure
    
    if (process.env.NODE_ENV === 'development') {
      // Return mock response for development
      return {
        data: {
          id: '1234567890',
          text: 'Sample tweet text',
          public_metrics: {
            retweet_count: Math.floor(Math.random() * 100),
            like_count: Math.floor(Math.random() * 500),
            reply_count: Math.floor(Math.random() * 50),
            quote_count: Math.floor(Math.random() * 20),
          },
          organic_metrics: {
            impression_count: Math.floor(Math.random() * 10000) + 1000,
            url_link_clicks: Math.floor(Math.random() * 100),
            user_profile_clicks: Math.floor(Math.random() * 50),
          },
        },
      };
    }

    // Real implementation would use Twitter API client
    const response = await fetch(fullUrl, {
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Twitter API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Generate mock metrics for development/testing
   */
  private generateMockMetrics(): PostMetrics {
    return {
      impressions: Math.floor(Math.random() * 10000) + 1000,
      reach: Math.floor(Math.random() * 5000) + 500,
      likes: Math.floor(Math.random() * 500) + 50,
      comments: Math.floor(Math.random() * 100) + 10,
      shares: Math.floor(Math.random() * 50) + 5,
      clicks: Math.floor(Math.random() * 100) + 10,
      retweets: Math.floor(Math.random() * 50) + 5,
      quote_tweets: Math.floor(Math.random() * 20) + 2,
      replies: Math.floor(Math.random() * 100) + 10,
    };
  }

  /**
   * Get trending topics for content inspiration
   */
  async getTrendingTopics(location: string = 'worldwide'): Promise<string[]> {
    try {
      // Twitter API endpoint for trends
      const response = await this.makeTwitterRequest(
        'https://api.twitter.com/1.1/trends/place.json',
        { id: '1' } // 1 = worldwide
      );

      if (response && response[0] && response[0].trends) {
        return response[0].trends
          .slice(0, 10)
          .map((trend: any) => trend.name);
      }

      return [];

    } catch (error) {
      console.error('Error fetching trending topics:', error);
      
      // Return mock trending topics for development
      return [
        '#TechTrends',
        '#DigitalMarketing',
        '#SocialMedia',
        '#Innovation',
        '#Startup',
        '#AI',
        '#MachineLearning',
        '#WebDevelopment',
        '#Design',
        '#Productivity',
      ];
    }
  }

  /**
   * Analyze tweet performance compared to account average
   */
  async analyzeTweetPerformance(tweetId: string, accountId: string): Promise<any> {
    try {
      const tweetMetrics = await this.collectPostMetrics('', tweetId);
      const accountMetrics = await this.collectAccountMetrics(accountId);

      // Calculate performance ratios
      const engagementRate = tweetMetrics.impressions > 0 
        ? ((tweetMetrics.likes + tweetMetrics.comments + tweetMetrics.shares) / tweetMetrics.impressions) * 100
        : 0;

      // Estimate account average (this would be calculated from historical data)
      const estimatedAvgEngagementRate = 2.5; // Industry average

      const performance = {
        engagement_rate: engagementRate,
        vs_account_average: engagementRate / estimatedAvgEngagementRate,
        vs_industry_average: engagementRate / 2.5, // Industry benchmark
        reach_rate: tweetMetrics.impressions > 0 
          ? (tweetMetrics.reach / tweetMetrics.impressions) * 100 
          : 0,
        click_rate: tweetMetrics.impressions > 0 
          ? (tweetMetrics.clicks / tweetMetrics.impressions) * 100 
          : 0,
      };

      return performance;

    } catch (error) {
      console.error('Error analyzing tweet performance:', error);
      throw error;
    }
  }
}
