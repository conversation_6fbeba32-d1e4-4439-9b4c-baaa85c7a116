import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { InsightsEngine } from '@/lib/analytics/insights-engine';
import { TrendDetector } from '@/lib/analytics/trend-detector';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/analytics/insights - Get AI-powered insights and recommendations
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching analytics insights...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const includeContentInsights = searchParams.get('includeContentInsights') !== 'false';
    const includeTrendAnalysis = searchParams.get('includeTrendAnalysis') !== 'false';
    const includeRecommendations = searchParams.get('includeRecommendations') !== 'false';

    // Parse dates
    let parsedStartDate: Date | undefined;
    let parsedEndDate: Date | undefined;

    if (startDate) {
      parsedStartDate = new Date(startDate);
      if (isNaN(parsedStartDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid start date format' },
          { status: 400 }
        );
      }
    }

    if (endDate) {
      parsedEndDate = new Date(endDate);
      if (isNaN(parsedEndDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid end date format' },
          { status: 400 }
        );
      }
    }

    // Initialize engines
    const insightsEngine = new InsightsEngine();
    const trendDetector = new TrendDetector();

    const insights: any = {
      summary: {
        analysisDate: new Date().toISOString(),
        dateRange: {
          startDate: parsedStartDate?.toISOString(),
          endDate: parsedEndDate?.toISOString(),
        },
        dataPoints: 0,
        confidence: 0,
      },
    };

    // Generate content insights
    if (includeContentInsights) {
      try {
        const contentInsights = await insightsEngine.generateContentInsights(
          user.id,
          parsedStartDate,
          parsedEndDate
        );

        insights.contentInsights = contentInsights;
        insights.summary.dataPoints += contentInsights.length;

        // Calculate overall confidence based on data availability
        if (contentInsights.length > 0) {
          insights.summary.confidence += 30;
        }

      } catch (error) {
        console.error('Error generating content insights:', error);
        insights.contentInsights = [];
      }
    }

    // Generate trend analysis
    if (includeTrendAnalysis) {
      try {
        // Get user's active platforms
        const { data: socialAccounts } = await supabase
          .from('social_accounts')
          .select('platform')
          .eq('user_id', user.id)
          .eq('is_active', true);

        const platforms = socialAccounts?.map(account => account.platform) || [];

        const hashtagTrends = await trendDetector.detectHashtagTrends(platforms, 'weekly');
        const trendInsights = await trendDetector.generateTrendInsights(hashtagTrends);
        const contentTrends = await trendDetector.detectContentTrends(user.id, platforms);

        insights.trendAnalysis = {
          hashtagTrends: hashtagTrends.slice(0, 20), // Top 20 hashtag trends
          trendInsights: trendInsights.slice(0, 10), // Top 10 insights
          contentTrends: contentTrends.slice(0, 10), // Top 10 content trends
          platforms,
        };

        insights.summary.dataPoints += hashtagTrends.length + contentTrends.length;

        if (hashtagTrends.length > 0 || contentTrends.length > 0) {
          insights.summary.confidence += 25;
        }

      } catch (error) {
        console.error('Error generating trend analysis:', error);
        insights.trendAnalysis = {
          hashtagTrends: [],
          trendInsights: [],
          contentTrends: [],
          platforms: [],
        };
      }
    }

    // Generate recommendations
    if (includeRecommendations) {
      try {
        const recommendations = await generateRecommendations(
          supabase,
          user.id,
          insights.contentInsights || [],
          insights.trendAnalysis || {}
        );

        insights.recommendations = recommendations;
        insights.summary.confidence += 20;

      } catch (error) {
        console.error('Error generating recommendations:', error);
        insights.recommendations = {
          content: [],
          timing: [],
          hashtags: [],
          platforms: [],
          general: [],
        };
      }
    }

    // Get existing insights from database
    const { data: existingInsights } = await supabase
      .from('content_insights')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    insights.history = existingInsights || [];

    // Calculate final confidence score
    insights.summary.confidence = Math.min(insights.summary.confidence, 100);

    console.log(`Analytics insights generated for user ${user.id}`);

    return NextResponse.json({
      success: true,
      data: insights,
    });

  } catch (error: any) {
    console.error('Error fetching analytics insights:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch analytics insights',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/analytics/insights - Trigger insights generation
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Triggering insights generation...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { 
      startDate, 
      endDate, 
      forceRegenerate = false,
      includeContentInsights = true,
      includeTrendAnalysis = true 
    } = body;

    // Parse dates
    let parsedStartDate: Date | undefined;
    let parsedEndDate: Date | undefined;

    if (startDate) {
      parsedStartDate = new Date(startDate);
      if (isNaN(parsedStartDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid start date format' },
          { status: 400 }
        );
      }
    }

    if (endDate) {
      parsedEndDate = new Date(endDate);
      if (isNaN(parsedEndDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid end date format' },
          { status: 400 }
        );
      }
    }

    // Check if recent insights exist (unless force regenerate)
    if (!forceRegenerate) {
      const { data: recentInsights } = await supabase
        .from('content_insights')
        .select('id, created_at')
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
        .limit(1);

      if (recentInsights && recentInsights.length > 0) {
        return NextResponse.json({
          success: true,
          message: 'تم إنشاء التحليلات مؤخراً. استخدم forceRegenerate=true لإعادة الإنشاء',
          data: {
            lastGenerated: recentInsights[0].created_at,
            canRegenerate: true,
          },
        });
      }
    }

    // Initialize engines
    const insightsEngine = new InsightsEngine();
    const trendDetector = new TrendDetector();

    const results: any = {
      contentInsights: null,
      trendAnalysis: null,
      generatedAt: new Date().toISOString(),
    };

    // Generate content insights
    if (includeContentInsights) {
      try {
        const contentInsights = await insightsEngine.generateContentInsights(
          user.id,
          parsedStartDate,
          parsedEndDate
        );
        results.contentInsights = {
          count: contentInsights.length,
          insights: contentInsights,
        };
      } catch (error) {
        console.error('Error generating content insights:', error);
        results.contentInsights = { error: 'Failed to generate content insights' };
      }
    }

    // Generate trend analysis
    if (includeTrendAnalysis) {
      try {
        // Get user's active platforms
        const { data: socialAccounts } = await supabase
          .from('social_accounts')
          .select('platform')
          .eq('user_id', user.id)
          .eq('is_active', true);

        const platforms = socialAccounts?.map(account => account.platform) || [];

        const hashtagTrends = await trendDetector.detectHashtagTrends(platforms, 'weekly');
        const contentTrends = await trendDetector.detectContentTrends(user.id, platforms);

        results.trendAnalysis = {
          hashtagTrendsCount: hashtagTrends.length,
          contentTrendsCount: contentTrends.length,
          platforms,
        };
      } catch (error) {
        console.error('Error generating trend analysis:', error);
        results.trendAnalysis = { error: 'Failed to generate trend analysis' };
      }
    }

    console.log(`Insights generation completed for user ${user.id}`);

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء التحليلات والرؤى بنجاح',
      data: results,
    });

  } catch (error: any) {
    console.error('Error triggering insights generation:', error);
    return NextResponse.json(
      {
        error: 'Failed to trigger insights generation',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper function to generate recommendations
async function generateRecommendations(
  supabase: any,
  userId: string,
  contentInsights: any[],
  trendAnalysis: any
): Promise<any> {
  const recommendations = {
    content: [] as string[],
    timing: [] as string[],
    hashtags: [] as string[],
    platforms: [] as string[],
    general: [] as string[],
  };

  // Content recommendations based on insights
  if (contentInsights && contentInsights.length > 0) {
    const bestPerformingType = contentInsights.reduce((best, current) => 
      current.performanceScore > best.performanceScore ? current : best
    );

    recommendations.content.push(
      `${bestPerformingType.type} يحقق أفضل أداء - ركز على هذا النوع من المحتوى`
    );

    if (bestPerformingType.avgEngagementRate > 3) {
      recommendations.content.push('معدل التفاعل ممتاز - استمر في نفس الاستراتيجية');
    } else {
      recommendations.content.push('جرب تحسين جودة المحتوى لزيادة التفاعل');
    }

    // Timing recommendations
    if (bestPerformingType.bestPostingTimes.general) {
      const bestHour = bestPerformingType.bestPostingTimes.general[0];
      recommendations.timing.push(`أفضل وقت للنشر هو الساعة ${bestHour}:00`);
    }

    // Hashtag recommendations
    if (bestPerformingType.hashtags.length > 0) {
      recommendations.hashtags.push(
        `استخدم الهاشتاغات الفعالة: ${bestPerformingType.hashtags.slice(0, 3).join(', ')}`
      );
    }
  }

  // Trend-based recommendations
  if (trendAnalysis.hashtagTrends && trendAnalysis.hashtagTrends.length > 0) {
    const topTrend = trendAnalysis.hashtagTrends[0];
    recommendations.hashtags.push(
      `${topTrend.hashtag} يحقق انتشاراً واسعاً - استخدمه في منشوراتك`
    );
  }

  if (trendAnalysis.contentTrends && trendAnalysis.contentTrends.length > 0) {
    const risingTrend = trendAnalysis.contentTrends.find((trend: any) => trend.trend === 'rising');
    if (risingTrend) {
      recommendations.content.push(
        `${risingTrend.contentType} في نمو على ${risingTrend.platform} - استثمر في هذا النوع`
      );
    }
  }

  // Platform recommendations
  if (trendAnalysis.platforms && trendAnalysis.platforms.length > 0) {
    recommendations.platforms.push(
      `تركز على ${trendAnalysis.platforms.length} منصة - فكر في التوسع لمنصات أخرى`
    );
  }

  // General recommendations
  recommendations.general.push('انشر بانتظام للحفاظ على تفاعل الجمهور');
  recommendations.general.push('تفاعل مع تعليقات المتابعين لزيادة الوصول');
  recommendations.general.push('استخدم القصص والمحتوى التفاعلي لزيادة التفاعل');

  return recommendations;
}
