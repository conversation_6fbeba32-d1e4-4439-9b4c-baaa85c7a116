/**
 * Page Selection Service
 * Unified service for managing business account selection across platforms
 */

import { createClient } from '@/lib/supabase/client';
import { FacebookPagesManager } from './facebook-pages-manager';
import { LinkedInCompaniesManager } from './linkedin-companies-manager';
import {
  BusinessAccount,
  BusinessAccountConfig,
  BusinessAccountSelection,
  PLATFORM_BUSINESS_REQUIREMENTS,
  BusinessAccountError,
  BUSINESS_ACCOUNT_ERRORS
} from './business-account-types';

export class PageSelectionService {
  private supabase = createClient();
  private facebookPagesManager = new FacebookPagesManager();
  private linkedinCompaniesManager = new LinkedInCompaniesManager();

  /**
   * Get business account configuration for a platform
   */
  async getBusinessAccountConfig(
    userId: string, 
    platform: string
  ): Promise<BusinessAccountConfig> {
    try {
      console.log(`Getting business account config for ${platform}`);

      // Get social account for this platform
      const { data: socialAccount, error: accountError } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('platform', platform.toUpperCase())
        .eq('status', 'ACTIVE')
        .single();

      if (accountError || !socialAccount) {
        return {
          platform,
          isConfigured: false,
          hasBusinessAccounts: false,
          businessAccounts: [],
          requiresReconnection: true,
          missingPermissions: [],
          lastUpdated: new Date().toISOString(),
        };
      }

      let businessAccounts: BusinessAccount[] = [];
      let selectedAccount: BusinessAccount | undefined;
      let requiresReconnection = false;
      let missingPermissions: string[] = [];

      // Platform-specific business account fetching
      switch (platform.toUpperCase()) {
        case 'FACEBOOK':
          const facebookConfig = await this.getFacebookBusinessConfig(socialAccount.id);
          businessAccounts = facebookConfig.businessAccounts;
          selectedAccount = facebookConfig.selectedAccount;
          requiresReconnection = facebookConfig.requiresReconnection;
          missingPermissions = facebookConfig.missingPermissions;
          break;

        case 'LINKEDIN':
          const linkedinConfig = await this.getLinkedInBusinessConfig(socialAccount.id);
          businessAccounts = linkedinConfig.businessAccounts;
          selectedAccount = linkedinConfig.selectedAccount;
          requiresReconnection = linkedinConfig.requiresReconnection;
          missingPermissions = linkedinConfig.missingPermissions;
          break;

        case 'INSTAGRAM':
          // TODO: Implement Instagram business accounts
          break;

        case 'TWITTER':
          // Twitter doesn't have business pages, use personal account
          businessAccounts = [{
            id: socialAccount.account_id,
            name: socialAccount.account_name,
            platform: 'TWITTER',
            type: 'BUSINESS',
            permissions: ['tweet.write'],
            metadata: {},
            isSelected: true,
          }];
          break;
      }

      return {
        platform,
        isConfigured: businessAccounts.length > 0,
        hasBusinessAccounts: businessAccounts.length > 0,
        businessAccounts,
        selectedAccount,
        requiresReconnection,
        missingPermissions,
        lastUpdated: new Date().toISOString(),
      };

    } catch (error) {
      console.error('Error getting business account config:', error);
      throw error;
    }
  }

  /**
   * Get Facebook business account configuration
   */
  private async getFacebookBusinessConfig(socialAccountId: string): Promise<{
    businessAccounts: BusinessAccount[];
    selectedAccount?: BusinessAccount;
    requiresReconnection: boolean;
    missingPermissions: string[];
  }> {
    try {
      const pages = await this.facebookPagesManager.getStoredPages(socialAccountId);
      
      if (pages.length === 0) {
        return {
          businessAccounts: [],
          requiresReconnection: true,
          missingPermissions: ['pages_manage_posts'],
        };
      }

      // Convert pages to business accounts
      const businessAccounts: BusinessAccount[] = pages.map(page => ({
        id: page.page_id,
        name: page.page_name,
        platform: 'FACEBOOK',
        type: 'PAGE',
        accessToken: page.page_access_token,
        permissions: page.permissions || [],
        metadata: {
          pictureUrl: page.page_picture_url,
          website: page.website,
          isVerified: page.is_verified,
          category: page.page_category,
          fanCount: page.fan_count,
        },
        isSelected: false,
        followerCount: page.fan_count,
        profilePictureUrl: page.page_picture_url,
        website: page.website,
        category: page.page_category,
      }));

      // Get selected page
      const { data: socialAccount } = await this.supabase
        .from('social_accounts')
        .select('page_id')
        .eq('id', socialAccountId)
        .single();

      const selectedPageId = socialAccount?.page_id;
      let selectedAccount: BusinessAccount | undefined;

      if (selectedPageId) {
        selectedAccount = businessAccounts.find(account => account.id === selectedPageId);
        if (selectedAccount) {
          selectedAccount.isSelected = true;
        }
      }

      return {
        businessAccounts,
        selectedAccount,
        requiresReconnection: false,
        missingPermissions: [],
      };

    } catch (error) {
      console.error('Error getting Facebook business config:', error);
      return {
        businessAccounts: [],
        requiresReconnection: true,
        missingPermissions: ['pages_manage_posts'],
      };
    }
  }

  /**
   * Get LinkedIn business account configuration
   */
  private async getLinkedInBusinessConfig(socialAccountId: string): Promise<{
    businessAccounts: BusinessAccount[];
    selectedAccount?: BusinessAccount;
    requiresReconnection: boolean;
    missingPermissions: string[];
  }> {
    try {
      const organizations = await this.linkedinCompaniesManager.getStoredOrganizations(socialAccountId);

      if (organizations.length === 0) {
        return {
          businessAccounts: [],
          requiresReconnection: true,
          missingPermissions: ['w_organization_social'],
        };
      }

      // Convert organizations to business accounts
      const businessAccounts: BusinessAccount[] = organizations.map(org => ({
        id: org.organization_id,
        name: org.organization_name,
        platform: 'LINKEDIN',
        type: 'COMPANY',
        permissions: org.permissions || [],
        metadata: {
          logoUrl: org.logo_url,
          website: org.website,
          industry: org.industry,
          description: org.description,
          employeeCount: org.employee_count,
          headquarters: {
            country: org.headquarters_country,
            city: org.headquarters_city,
          },
        },
        isSelected: false,
        followerCount: org.follower_count,
        profilePictureUrl: org.logo_url,
        website: org.website,
        category: org.industry,
      }));

      // Get selected organization
      const { data: socialAccount } = await this.supabase
        .from('social_accounts')
        .select('business_account_id')
        .eq('id', socialAccountId)
        .single();

      const selectedOrganizationId = socialAccount?.business_account_id;
      let selectedAccount: BusinessAccount | undefined;

      if (selectedOrganizationId) {
        selectedAccount = businessAccounts.find(account => account.id === selectedOrganizationId);
        if (selectedAccount) {
          selectedAccount.isSelected = true;
        }
      }

      return {
        businessAccounts,
        selectedAccount,
        requiresReconnection: false,
        missingPermissions: [],
      };

    } catch (error) {
      console.error('Error getting LinkedIn business config:', error);
      return {
        businessAccounts: [],
        requiresReconnection: true,
        missingPermissions: ['w_organization_social'],
      };
    }
  }

  /**
   * Select a business account for posting
   */
  async selectBusinessAccount(
    userId: string,
    platform: string,
    businessAccountId: string
  ): Promise<void> {
    try {
      console.log(`Selecting business account ${businessAccountId} for ${platform}`);

      // Get social account
      const { data: socialAccount, error: accountError } = await this.supabase
        .from('social_accounts')
        .select('id')
        .eq('user_id', userId)
        .eq('platform', platform.toUpperCase())
        .eq('status', 'ACTIVE')
        .single();

      if (accountError || !socialAccount) {
        throw new Error('Social account not found');
      }

      // Platform-specific selection logic
      switch (platform.toUpperCase()) {
        case 'FACEBOOK':
          await this.facebookPagesManager.updateSelectedPage(
            socialAccount.id,
            businessAccountId
          );
          break;

        case 'LINKEDIN':
          await this.linkedinCompaniesManager.updateSelectedOrganization(
            socialAccount.id,
            businessAccountId
          );
          break;

        case 'INSTAGRAM':
          // TODO: Implement Instagram business account selection
          break;

        case 'TWITTER':
          // Twitter doesn't need selection, already using the connected account
          break;

        default:
          throw new Error(`Platform ${platform} not supported`);
      }

      // Log the selection
      await this.supabase
        .from('activities')
        .insert({
          user_id: userId,
          action: 'BUSINESS_ACCOUNT_SELECTED',
          metadata: {
            platform,
            businessAccountId,
            socialAccountId: socialAccount.id,
            timestamp: new Date().toISOString(),
          },
        });

      console.log('Successfully selected business account');

    } catch (error) {
      console.error('Error selecting business account:', error);
      throw error;
    }
  }

  /**
   * Refresh business accounts for a platform
   */
  async refreshBusinessAccounts(
    userId: string,
    platform: string
  ): Promise<BusinessAccountConfig> {
    try {
      console.log(`Refreshing business accounts for ${platform}`);

      // Get social account with access token
      const { data: socialAccount, error: accountError } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('platform', platform.toUpperCase())
        .eq('status', 'ACTIVE')
        .single();

      if (accountError || !socialAccount) {
        throw new Error('Social account not found');
      }

      // Platform-specific refresh logic
      switch (platform.toUpperCase()) {
        case 'FACEBOOK':
          if (socialAccount.access_token) {
            await this.facebookPagesManager.refreshPageTokens(
              socialAccount.id,
              socialAccount.access_token
            );
          }
          break;

        case 'LINKEDIN':
          if (socialAccount.access_token) {
            await this.linkedinCompaniesManager.refreshOrganizations(
              socialAccount.id,
              socialAccount.access_token
            );
          }
          break;

        case 'INSTAGRAM':
          // TODO: Implement Instagram refresh
          break;
      }

      // Return updated configuration
      return await this.getBusinessAccountConfig(userId, platform);

    } catch (error) {
      console.error('Error refreshing business accounts:', error);
      throw error;
    }
  }

  /**
   * Validate business account permissions
   */
  async validateBusinessAccountPermissions(
    platform: string,
    businessAccountId: string,
    accessToken: string
  ): Promise<boolean> {
    try {
      console.log(`Validating permissions for ${platform} account ${businessAccountId}`);

      switch (platform.toUpperCase()) {
        case 'FACEBOOK':
          return await this.facebookPagesManager.validatePagePermissions(
            accessToken,
            businessAccountId
          );

        case 'LINKEDIN':
          return await this.linkedinCompaniesManager.validateOrganizationPermissions(
            accessToken,
            businessAccountId
          );

        case 'INSTAGRAM':
          // TODO: Implement Instagram validation
          return true;

        case 'TWITTER':
          // TODO: Implement Twitter validation
          return true;

        default:
          return false;
      }

    } catch (error) {
      console.error('Error validating business account permissions:', error);
      return false;
    }
  }

  /**
   * Get posting configuration for a social account
   */
  async getPostingConfiguration(
    userId: string,
    platform: string
  ): Promise<{
    canPost: boolean;
    selectedAccount?: BusinessAccount;
    accessToken?: string;
    errorMessage?: string;
  }> {
    try {
      const config = await this.getBusinessAccountConfig(userId, platform);

      if (!config.isConfigured) {
        return {
          canPost: false,
          errorMessage: 'No business accounts configured',
        };
      }

      if (!config.selectedAccount) {
        return {
          canPost: false,
          errorMessage: 'No business account selected for posting',
        };
      }

      return {
        canPost: true,
        selectedAccount: config.selectedAccount,
        accessToken: config.selectedAccount.accessToken,
      };

    } catch (error) {
      console.error('Error getting posting configuration:', error);
      return {
        canPost: false,
        errorMessage: 'Failed to get posting configuration',
      };
    }
  }
}
