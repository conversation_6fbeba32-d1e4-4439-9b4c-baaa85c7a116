#!/usr/bin/env node

/**
 * Social Media Posting Integration Testing
 * Tests the enhanced providers and posting capabilities
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-Social-Tester/1.0',
        ...options.headers
      },
      timeout: 15000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function testFacebookConnectivity() {
  console.log('📘 TESTING FACEBOOK CONNECTIVITY...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/test/facebook-connectivity`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Facebook connectivity test endpoint working');
      
      if (response.data.tests) {
        console.log('\n📊 Facebook Test Results:');
        
        if (response.data.tests.connectionTests) {
          console.log('🔗 Connection Tests:');
          response.data.tests.connectionTests.forEach(test => {
            const status = test.success ? '✅' : '❌';
            console.log(`  ${status} ${test.accountName}: ${test.success ? 'Connected' : test.error}`);
          });
        }
        
        if (response.data.tests.pagesTests) {
          console.log('\n📄 Pages Tests:');
          response.data.tests.pagesTests.forEach(test => {
            const status = test.success ? '✅' : '❌';
            console.log(`  ${status} ${test.accountName}: ${test.success ? `${test.pagesFound} pages found` : test.error}`);
          });
        }
      }
      
      return { status: 'PASS', data: response.data };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testLinkedInPosting() {
  console.log('\n💼 TESTING LINKEDIN POSTING...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/test/linkedin-posting`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ LinkedIn posting test endpoint working');
      
      if (response.data.tests) {
        console.log('\n📊 LinkedIn Test Results:');
        
        if (response.data.tests.connectionTests) {
          console.log('🔗 Connection Tests:');
          response.data.tests.connectionTests.forEach(test => {
            const status = test.success ? '✅' : '❌';
            console.log(`  ${status} ${test.accountName}: ${test.success ? 'Connected' : test.error}`);
          });
        }
        
        if (response.data.tests.postingTests) {
          console.log('\n📝 Posting Tests:');
          response.data.tests.postingTests.forEach(test => {
            const status = test.success ? '✅' : '❌';
            console.log(`  ${status} ${test.accountName}: ${test.success ? 'Posting ready' : test.error}`);
          });
        }
      }
      
      return { status: 'PASS', data: response.data };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testEnhancedSocialTest() {
  console.log('\n🚀 TESTING ENHANCED SOCIAL INTEGRATION...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/enhanced/test`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Enhanced social test endpoint working');
      
      if (response.data.results) {
        console.log('\n📊 Enhanced Integration Results:');
        
        Object.entries(response.data.results).forEach(([platform, result]) => {
          const status = result.success ? '✅' : '❌';
          console.log(`  ${status} ${platform.toUpperCase()}: ${result.success ? 'Ready' : result.error}`);
          
          if (result.accounts) {
            console.log(`    Accounts: ${result.accounts.length}`);
          }
        });
      }
      
      return { status: 'PASS', data: response.data };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testJobQueueStatus() {
  console.log('\n⏰ TESTING JOB QUEUE STATUS...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/scheduler/status`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Job queue status endpoint working');
      
      if (response.data.queue) {
        console.log('\n📊 Queue Statistics:');
        const stats = response.data.queue;
        console.log(`  Waiting: ${stats.waiting || 0}`);
        console.log(`  Active: ${stats.active || 0}`);
        console.log(`  Completed: ${stats.completed || 0}`);
        console.log(`  Failed: ${stats.failed || 0}`);
        console.log(`  Total: ${stats.total || 0}`);
      }
      
      if (response.data.health) {
        const healthStatus = response.data.health.healthy ? '✅' : '❌';
        console.log(`\n${healthStatus} Queue Health: ${response.data.health.healthy ? 'Healthy' : 'Unhealthy'}`);
      }
      
      return { status: 'PASS', data: response.data };
    } else if (response.status === 401) {
      console.log('⚠️  Authentication required (expected for production)');
      return { status: 'WARN', httpStatus: response.status };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testMediaProcessing() {
  console.log('\n🖼️ TESTING MEDIA PROCESSING...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/test/media-processing`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Media processing test endpoint working');
      
      if (response.data.tests) {
        console.log('\n📊 Media Processing Results:');
        
        Object.entries(response.data.tests).forEach(([testName, result]) => {
          const status = result.success ? '✅' : '❌';
          console.log(`  ${status} ${testName}: ${result.success ? 'Working' : result.error}`);
        });
      }
      
      return { status: 'PASS', data: response.data };
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      if (response.data && response.data.error) {
        console.log(`Error: ${response.data.error}`);
      }
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function runSocialMediaTests() {
  console.log('🧪 COMPREHENSIVE SOCIAL MEDIA INTEGRATION TESTING');
  console.log('=' .repeat(70));
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  const results = [];
  
  // Run all tests
  results.push(await testFacebookConnectivity());
  results.push(await testLinkedInPosting());
  results.push(await testEnhancedSocialTest());
  results.push(await testJobQueueStatus());
  results.push(await testMediaProcessing());
  
  // Calculate summary
  const passed = results.filter(r => r.status === 'PASS').length;
  const warned = results.filter(r => r.status === 'WARN').length;
  const failed = results.filter(r => r.status === 'FAIL').length;
  const total = results.length;
  
  console.log('\n📊 SOCIAL MEDIA TESTING SUMMARY:');
  console.log('=' .repeat(50));
  console.log(`✅ Passed: ${passed}`);
  console.log(`⚠️  Warnings: ${warned}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  console.log('\n🏁 TESTING COMPLETE');
  console.log(`Finished at: ${new Date().toISOString()}`);
  
  return {
    results,
    summary: {
      total,
      passed,
      warned,
      failed,
      successRate: (passed / total) * 100
    }
  };
}

if (require.main === module) {
  runSocialMediaTests().then(results => {
    process.exit(results.summary.successRate >= 60 ? 0 : 1);
  }).catch(error => {
    console.error('❌ CRITICAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = { runSocialMediaTests };
