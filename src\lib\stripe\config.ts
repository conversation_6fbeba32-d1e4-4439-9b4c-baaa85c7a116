import Stripe from 'stripe';
import { loadStripe } from '@stripe/stripe-js';

// Server-side Stripe instance - conditional initialization
export const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-05-28.basil',
      typescript: true,
    })
  : null;

// Helper function to get Stripe instance with error handling
export const getStripeInstance = (): Stripe => {
  if (!stripe) {
    throw new Error('Stripe not configured: STRIPE_SECRET_KEY environment variable is not set');
  }
  return stripe;
};

// Client-side Stripe instance
export const getStripe = () => {
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  if (!publishableKey) {
    console.warn('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable is not set');
    return null;
  }
  return loadStripe(publishableKey);
};

// Subscription plans configuration with exact pricing requirements
export const SUBSCRIPTION_PLANS = {
  FREE: {
    id: 'free',
    name: 'Free',
    nameAr: 'مجاني',
    description: 'Basic social media management for individuals',
    descriptionAr: 'إدارة أساسية لوسائل التواصل الاجتماعي للأفراد',
    priceMonthly: 0,
    priceYearly: 0,
    maxSocialAccounts: 2,
    maxUsers: 1,
    maxPostsPerMonth: 10,
    features: {
      basicScheduling: true,
      basicAnalytics: true,
      arabicSupport: true,
      rtlSupport: true,
      imageUpload: true,
      basicNotifications: true,
    },
    stripePriceIds: {
      monthly: null,
      yearly: null,
    },
  },
  PRO: {
    id: 'pro',
    name: 'Pro',
    nameAr: 'المحترف',
    description: 'Ideal for individuals and startups',
    descriptionAr: 'مثالي للأفراد والشركات الناشئة',
    priceMonthly: 9.00,
    priceYearly: 90.00,
    maxSocialAccounts: 5,
    maxUsers: 2,
    maxPostsPerMonth: null, // Unlimited
    popular: true,
    features: {
      socialAccounts5: true, // إدارة 5 حسابات اجتماعية
      unlimitedPosts: true, // عدد غير محدود من المنشورات
      users2: true, // مستخدمان فقط
      basicAnalytics: true, // تقارير وتحليلات أساسية
      arabicSupport: true, // دعم النصوص باللغة العربية
      postScheduling: true, // إنشاء المنشورات وجدولتها
      basicAiContent: true, // توليد منشورات AI (أساسي)
      calendarView: true, // عرض تقويم بسيط وجدولة ذكية
      imageVideoUpload: true, // تحميل الصور ومقاطع الفيديو
      rtlSupport: true, // دعم RTL وخطوط عربية
      commentReplies: true, // الرد على تعليقات ومتابعة النشاط
      basicNotifications: true, // إشعارات بسيطة ومركز إشعار مصغر
    },
    featuresAr: [
      'إدارة 5 حسابات اجتماعية',
      'عدد غير محدود من المنشورات',
      'مستخدمان فقط',
      'تقارير وتحليلات أساسية',
      'دعم النصوص باللغة العربية',
      'إنشاء المنشورات وجدولتها',
      'توليد منشورات AI (أساسي)',
      'عرض تقويم بسيط وجدولة ذكية',
      'تحميل الصور ومقاطع الفيديو',
      'دعم RTL وخطوط عربية',
      'الرد على تعليقات ومتابعة النشاط',
      'إشعارات بسيطة ومركز إشعار مصغر'
    ],
    stripePriceIds: {
      monthly: 'price_1RUYNWEpEYvJL85Mqc51HFuS', // Real Stripe Pro Monthly Price ID
      yearly: 'price_1RUYNWEpEYvJL85MjMachouR', // Real Stripe Pro Yearly Price ID
    },
    stripeProductId: 'prod_SPN7y03W0tVZHq', // Real Stripe Product ID
    lookupKeys: {
      monthly: 'ewasl_pro_monthly',
      yearly: 'ewasl_pro_yearly',
    },
  },
  BUSINESS: {
    id: 'business',
    name: 'Business',
    nameAr: 'الأعمال',
    description: 'Perfect for growing teams and agencies',
    descriptionAr: 'مناسب للشركات المتنامية والوكالات الصغيرة',
    priceMonthly: 25.00,
    priceYearly: 250.00,
    maxSocialAccounts: 10,
    maxUsers: 5,
    maxPostsPerMonth: null, // Unlimited
    features: {
      allProFeatures: true, // كل ميزات خطة المحترف، بالإضافة إلى
      socialAccounts10: true, // إدارة 10 حسابات اجتماعية
      users5: true, // حتى 5 مستخدمين
      dragDropScheduling: true, // جدولة متقدمة بالسحب والإفلات
      advancedAnalytics: true, // تحليل أداء متقدم (المنشورات، المتابعين، المقارنة بين المنصات)
      contentLibrary: true, // مكتبة محتوى وقوالب جاهزة للنشر
      advancedAiContent: true, // دعم توليد محتوى AI متقدم
      realtimeNotifications: true, // إشعارات فورية وتحليل النشاط في الوقت الحقيقي
      dataExport: true, // تصدير البيانات بصيغ CSV / PDF / Excel
      prioritySupport: true, // دعم مخصص بأولوية عالية
      automatedReports: true, // تقارير أسبوعية تلقائية
    },
    featuresAr: [
      'كل ميزات خطة المحترف، بالإضافة إلى:',
      'إدارة 10 حسابات اجتماعية',
      'حتى 5 مستخدمين',
      'جدولة متقدمة بالسحب والإفلات',
      'تحليل أداء متقدم (المنشورات، المتابعين، المقارنة بين المنصات)',
      'مكتبة محتوى وقوالب جاهزة للنشر',
      'دعم توليد محتوى AI متقدم',
      'إشعارات فورية وتحليل النشاط في الوقت الحقيقي',
      'تصدير البيانات بصيغ CSV / PDF / Excel',
      'دعم مخصص بأولوية عالية',
      'تقارير أسبوعية تلقائية'
    ],
    stripePriceIds: {
      monthly: process.env.STRIPE_PRICE_ID_BUSINESS_MONTHLY,
      yearly: process.env.STRIPE_PRICE_ID_BUSINESS_YEARLY,
    },
  },
  ENTERPRISE: {
    id: 'enterprise',
    name: 'Enterprise',
    nameAr: 'المؤسسات',
    description: 'For large organizations and marketing agencies',
    descriptionAr: 'للشركات الكبرى والوكالات',
    priceMonthly: null, // Custom pricing
    priceYearly: null, // Custom pricing
    maxSocialAccounts: -1, // Unlimited
    maxUsers: -1, // Unlimited
    maxPostsPerMonth: null, // Unlimited
    customPricing: true,
    contactText: 'راسلنا للحصول على عرض مخصص',
    features: {
      unlimitedAccounts: true, // عدد غير محدود من الحسابات
      unlimitedUsers: true, // عدد غير محدود من المستخدمين
      customAnalytics: true, // تحليلات مخصصة وتقارير شاملة
      integrations: true, // دعم تكاملات إضافية (CRM، Zapier، WhatsApp API)
      adminDashboard: true, // لوحة تحكم إدارية موسعة
      directTraining: true, // تدريب ودعم مباشر
      uiCustomization: true, // تخصيص كامل للواجهة والتقارير
      advancedSecurity: true, // طبقة أمان متقدمة (SSO، إدارة أذونات دقيقة)
    },
    featuresAr: [
      'عدد غير محدود من الحسابات',
      'عدد غير محدود من المستخدمين',
      'تحليلات مخصصة وتقارير شاملة',
      'دعم تكاملات إضافية (CRM، Zapier، WhatsApp API)',
      'لوحة تحكم إدارية موسعة',
      'تدريب ودعم مباشر',
      'تخصيص كامل للواجهة والتقارير',
      'طبقة أمان متقدمة (SSO، إدارة أذونات دقيقة)'
    ],
    stripePriceIds: {
      monthly: null, // Custom pricing
      yearly: null, // Custom pricing
    },
  },
} as const;

export type SubscriptionPlan = keyof typeof SUBSCRIPTION_PLANS;

// Helper functions
export const getPlanByPriceId = (priceId: string) => {
  return Object.values(SUBSCRIPTION_PLANS).find(plan =>
    'stripePriceId' in plan && plan.stripePriceId === priceId
  );
};

export const getPlanById = (planId: string) => {
  return SUBSCRIPTION_PLANS[planId as SubscriptionPlan];
};

export const formatPrice = (price: number, currency: string = 'usd') => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency.toUpperCase(),
    minimumFractionDigits: 0,
  }).format(price);
};

// Helper function to get plan by ID
export function getPlanByIdNew(planId: string) {
  return Object.values(SUBSCRIPTION_PLANS).find(plan => plan.id === planId);
}

// Helper function to get plan by Stripe price ID
export function getPlanByStripePriceId(priceId: string) {
  return Object.values(SUBSCRIPTION_PLANS).find(plan =>
    plan.stripePriceIds.monthly === priceId ||
    plan.stripePriceIds.yearly === priceId
  );
}

// Stripe webhook events we handle
export const STRIPE_WEBHOOK_EVENTS = {
  CUSTOMER_SUBSCRIPTION_CREATED: 'customer.subscription.created',
  CUSTOMER_SUBSCRIPTION_UPDATED: 'customer.subscription.updated',
  CUSTOMER_SUBSCRIPTION_DELETED: 'customer.subscription.deleted',
  INVOICE_PAYMENT_SUCCEEDED: 'invoice.payment_succeeded',
  INVOICE_PAYMENT_FAILED: 'invoice.payment_failed',
  CUSTOMER_CREATED: 'customer.created',
  CUSTOMER_UPDATED: 'customer.updated',
} as const;

// Subscription status mapping
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  CANCELED: 'canceled',
  INCOMPLETE: 'incomplete',
  INCOMPLETE_EXPIRED: 'incomplete_expired',
  PAST_DUE: 'past_due',
  TRIALING: 'trialing',
  UNPAID: 'unpaid',
} as const;

// Payment status mapping
export const PAYMENT_STATUS = {
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
  PENDING: 'pending',
  REFUNDED: 'refunded',
  CANCELED: 'canceled',
} as const;

// Billing cycles
export const BILLING_CYCLE = {
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
} as const;

// Feature limits for usage tracking
export const USAGE_LIMITS = {
  FREE: {
    postsPerMonth: 10,
    socialAccounts: 2,
    users: 1,
    aiGenerations: 5,
    reportsPerMonth: 1,
  },
  PRO: {
    postsPerMonth: null, // Unlimited
    socialAccounts: 5,
    users: 2,
    aiGenerations: 50,
    reportsPerMonth: 10,
  },
  BUSINESS: {
    postsPerMonth: null, // Unlimited
    socialAccounts: 10,
    users: 5,
    aiGenerations: 200,
    reportsPerMonth: null, // Unlimited
  },
  ENTERPRISE: {
    postsPerMonth: null, // Unlimited
    socialAccounts: null, // Unlimited
    users: null, // Unlimited
    aiGenerations: null, // Unlimited
    reportsPerMonth: null, // Unlimited
  },
} as const;

// Default trial period (in days)
export const DEFAULT_TRIAL_DAYS = 14;

// Currency configuration
export const SUPPORTED_CURRENCIES = {
  USD: 'usd',
  EUR: 'eur',
  GBP: 'gbp',
  SAR: 'sar', // Saudi Riyal for Arabic markets
} as const;

export const DEFAULT_CURRENCY = SUPPORTED_CURRENCIES.USD;

// Production-ready Stripe functions based on Vercel SaaS Starter
export async function createCheckoutSession({
  userId,
  priceId,
  successUrl,
  cancelUrl,
  customerId,
}: {
  userId: string;
  priceId: string;
  successUrl?: string;
  cancelUrl?: string;
  customerId?: string;
}) {
  const stripeInstance = getStripeInstance();

  const session = await stripeInstance.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: successUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing?success=true&session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: cancelUrl || `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing?canceled=true`,
    client_reference_id: userId,
    customer: customerId || undefined,
    allow_promotion_codes: true,
    subscription_data: {
      trial_period_days: DEFAULT_TRIAL_DAYS,
    },
  });

  return session;
}

export async function createCustomerPortalSession(customerId: string) {
  const stripeInstance = getStripeInstance();

  const session = await stripeInstance.billingPortal.sessions.create({
    customer: customerId,
    return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`,
  });

  return session;
}

export async function getStripePrices() {
  const stripeInstance = getStripeInstance();

  const prices = await stripeInstance.prices.list({
    expand: ['data.product'],
    active: true,
    type: 'recurring',
  });

  return prices.data.map((price) => ({
    id: price.id,
    productId: typeof price.product === 'string' ? price.product : price.product.id,
    unitAmount: price.unit_amount,
    currency: price.currency,
    interval: price.recurring?.interval,
    trialPeriodDays: price.recurring?.trial_period_days,
  }));
}

export async function getStripeProducts() {
  const stripeInstance = getStripeInstance();

  const products = await stripeInstance.products.list({
    active: true,
    expand: ['data.default_price'],
  });

  return products.data.map((product) => ({
    id: product.id,
    name: product.name,
    description: product.description,
    defaultPriceId: typeof product.default_price === 'string'
      ? product.default_price
      : product.default_price?.id,
  }));
}

export async function handleSubscriptionChange(subscription: any) {
  // This will be implemented with Supabase integration
  console.log('Subscription changed:', subscription.id);
  // TODO: Update user subscription in Supabase
}
