import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';
import { FacebookEnhancedProvider } from '@/lib/social/postiz-integration/providers/facebook-enhanced';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Test Facebook API connectivity and posting capability
 * GET /api/test/facebook-connectivity
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook API connectivity...');

    const results = {
      timestamp: new Date().toISOString(),
      tests: {} as any,
      summary: {} as any
    };

    // Test 1: Get Facebook accounts from database
    try {
      console.log('Test 1: Retrieving Facebook accounts...');
      const supabase = createServiceRoleClient();
      const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
      
      const { data: facebookAccounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', demoUserId)
        .eq('platform', 'FACEBOOK');

      results.tests.accountRetrieval = {
        success: !error,
        error: error?.message,
        accountsFound: facebookAccounts?.length || 0,
        accounts: facebookAccounts?.map(acc => ({
          id: acc.id,
          account_name: acc.account_name,
          account_id: acc.account_id,
          created_at: acc.created_at,
          hasAccessToken: !!acc.access_token
        })) || []
      };

      console.log(`✅ Found ${facebookAccounts?.length || 0} Facebook accounts`);
    } catch (error: any) {
      results.tests.accountRetrieval = {
        success: false,
        error: error.message
      };
    }

    // Test 2: Test Facebook Graph API connection for each account
    if (results.tests.accountRetrieval.success && results.tests.accountRetrieval.accounts.length > 0) {
      results.tests.connectionTests = [];

      for (const account of results.tests.accountRetrieval.accounts) {
        try {
          console.log(`Test 2: Testing Facebook API connection for ${account.account_name}...`);
          
          // Get full account data
          const supabase = createServiceRoleClient();
          const { data: fullAccount } = await supabase
            .from('social_accounts')
            .select('*')
            .eq('id', account.id)
            .single();

          if (!fullAccount?.access_token) {
            results.tests.connectionTests.push({
              accountName: account.account_name,
              success: false,
              error: 'No access token available'
            });
            continue;
          }

          // Test Facebook Graph API connection using direct API call
          const response = await fetch(`https://graph.facebook.com/v19.0/me?fields=id,name,email&access_token=${fullAccount.access_token}&appsecret_proof=${require('crypto').createHmac('sha256', process.env.FACEBOOK_APP_SECRET!).update(fullAccount.access_token).digest('hex')}`);

          if (response.ok) {
            const userData = await response.json();
            results.tests.connectionTests.push({
              accountName: account.account_name,
              success: true,
              userInfo: {
                id: userData.id,
                name: userData.name,
                email: userData.email
              },
              readyForPosting: true
            });
            console.log(`✅ Facebook API connection test passed for ${account.account_name}`);
          } else {
            const errorText = await response.text();
            results.tests.connectionTests.push({
              accountName: account.account_name,
              success: false,
              error: `API error: ${response.status} - ${errorText}`,
              readyForPosting: false
            });
            console.log(`❌ Facebook API connection test failed for ${account.account_name}: ${response.status}`);
          }

        } catch (error: any) {
          results.tests.connectionTests.push({
            accountName: account.account_name,
            success: false,
            error: error.message,
            readyForPosting: false
          });
        }
      }
    }

    // Test 3: Test Facebook Pages access (if applicable)
    if (results.tests.accountRetrieval.success && results.tests.accountRetrieval.accounts.length > 0) {
      results.tests.pagesTests = [];

      for (const account of results.tests.accountRetrieval.accounts) {
        try {
          console.log(`Test 3: Testing Facebook Pages access for ${account.account_name}...`);
          
          // Get full account data
          const supabase = createServiceRoleClient();
          const { data: fullAccount } = await supabase
            .from('social_accounts')
            .select('*')
            .eq('id', account.id)
            .single();

          if (!fullAccount?.access_token) {
            results.tests.pagesTests.push({
              accountName: account.account_name,
              success: false,
              error: 'No access token available'
            });
            continue;
          }

          // Test Facebook Pages API using direct API call
          try {
            const pagesResponse = await fetch(`https://graph.facebook.com/v19.0/me/accounts?fields=id,name,category,fan_count&access_token=${fullAccount.access_token}&appsecret_proof=${require('crypto').createHmac('sha256', process.env.FACEBOOK_APP_SECRET!).update(fullAccount.access_token).digest('hex')}`);

            if (pagesResponse.ok) {
              const pagesData = await pagesResponse.json();
              results.tests.pagesTests.push({
                accountName: account.account_name,
                success: true,
                pagesFound: pagesData.data?.length || 0,
                pages: pagesData.data?.map((page: any) => ({
                  id: page.id,
                  name: page.name,
                  category: page.category,
                  fanCount: page.fan_count
                })) || [],
                canManagePages: true
              });
              console.log(`✅ Facebook Pages access test passed for ${account.account_name} - ${pagesData.data?.length || 0} pages found`);
            } else {
              const errorText = await pagesResponse.text();
              results.tests.pagesTests.push({
                accountName: account.account_name,
                success: false,
                error: `Pages API error: ${pagesResponse.status} - ${errorText}`,
                canManagePages: false
              });
            }
          } catch (pagesError: any) {
            results.tests.pagesTests.push({
              accountName: account.account_name,
              success: false,
              error: `Pages API error: ${pagesError.message}`,
              canManagePages: false
            });
            console.log(`❌ Facebook Pages access test failed for ${account.account_name}: ${pagesError.message}`);
          }

        } catch (error: any) {
          results.tests.pagesTests.push({
            accountName: account.account_name,
            success: false,
            error: error.message,
            canManagePages: false
          });
        }
      }
    }

    // Test 4: Test Facebook posting capability (dry run)
    if (results.tests.accountRetrieval.success && results.tests.accountRetrieval.accounts.length > 0) {
      results.tests.postingCapability = [];

      for (const account of results.tests.accountRetrieval.accounts) {
        try {
          console.log(`Test 4: Testing Facebook posting capability for ${account.account_name}...`);
          
          // Prepare test post data (dry run - not actually posting)
          const testPostData = {
            message: '🧪 Test post from eWasl Social Scheduler - Facebook Integration Test! 🚀 #eWasl #Facebook #SocialMediaManagement',
            access_token: 'TEST_TOKEN' // This would be the real token in actual posting
          };

          // Validate post data structure (without actually posting)
          const isValidPostData = testPostData.message && 
                                 testPostData.message.length > 0 &&
                                 testPostData.message.length <= 63206; // Facebook character limit

          results.tests.postingCapability.push({
            accountName: account.account_name,
            success: isValidPostData,
            postDataValid: isValidPostData,
            contentLength: testPostData.message.length,
            withinCharacterLimit: testPostData.message.length <= 63206,
            note: 'Dry run - no actual post created'
          });

          console.log(`✅ Facebook posting capability test passed for ${account.account_name} (dry run)`);

        } catch (error: any) {
          results.tests.postingCapability.push({
            accountName: account.account_name,
            success: false,
            error: error.message
          });
        }
      }
    }

    // Generate summary
    const totalAccounts = results.tests.accountRetrieval?.accountsFound || 0;
    const successfulConnections = results.tests.connectionTests?.filter((test: any) => test.success).length || 0;
    const successfulPostingTests = results.tests.postingCapability?.filter((test: any) => test.success).length || 0;
    const pagesAccessible = results.tests.pagesTests?.filter((test: any) => test.success).length || 0;

    results.summary = {
      totalFacebookAccounts: totalAccounts,
      successfulConnections,
      successfulPostingTests,
      pagesAccessible,
      overallStatus: totalAccounts > 0 && successfulConnections > 0 && successfulPostingTests > 0 ? 'READY' : 'NEEDS_ATTENTION',
      readyForPosting: successfulConnections > 0 && successfulPostingTests > 0
    };

    console.log('🎯 Facebook connectivity test summary:', results.summary);

    return NextResponse.json({
      success: true,
      message: '🧪 Facebook API connectivity test completed',
      results,
      recommendation: results.summary.readyForPosting ? 
        '✅ Facebook integration is ready for posting!' :
        '⚠️ Facebook integration needs attention before posting'
    });

  } catch (error) {
    console.error('❌ Facebook connectivity test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Facebook connectivity test failed'
    }, { status: 500 });
  }
}
