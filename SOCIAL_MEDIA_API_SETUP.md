# 🔗 Social Media API Setup Guide

This guide provides step-by-step instructions for obtaining API credentials from each social media platform and configuring them in eWasl.

## 📋 **Quick Status Check**

Visit `/api/social/oauth-status` to check the current configuration status of all platforms.

## 🐦 **Twitter API Setup**

### **Step 1: Create Twitter Developer Account**
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Sign in with your Twitter account
3. Apply for a developer account (may require approval)
4. Create a new project/app

### **Step 2: Get API Credentials**
1. In your Twitter app dashboard, go to "Keys and tokens"
2. Generate the following credentials:
   - **API Key** (Consumer Key)
   - **API Secret** (Consumer Secret)
   - **Bearer <PERSON>ken**
   - **Client ID** (OAuth 2.0)
   - **Client Secret** (OAuth 2.0)

### **Step 3: Configure OAuth Settings**
1. Go to "App settings" → "Authentication settings"
2. Enable "OAuth 2.0"
3. Set callback URL: `https://app.ewasl.com/api/social/callback/twitter`
4. Set website URL: `https://app.ewasl.com`

### **Step 4: Environment Variables**
```bash
TWITTER_CLIENT_ID=your_client_id_here
TWITTER_CLIENT_SECRET=your_client_secret_here
TWITTER_API_KEY=your_api_key_here
TWITTER_API_SECRET=your_api_secret_here
TWITTER_BEARER_TOKEN=your_bearer_token_here
```

---

## 📘 **Facebook API Setup**

### **Step 1: Create Facebook App**
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Select "Business" as app type
4. Add "Facebook Login" product

### **Step 2: Get App Credentials**
1. Go to "App Settings" → "Basic"
2. Copy your **App ID** and **App Secret**

### **Step 3: Configure OAuth Settings**
1. Go to "Facebook Login" → "Settings"
2. Add redirect URI: `https://app.ewasl.com/api/social/callback/facebook`
3. Enable required permissions:
   - `pages_manage_posts`
   - `pages_read_engagement`
   - `pages_show_list`
   - `publish_to_groups`

### **Step 4: Environment Variables**
```bash
FACEBOOK_APP_ID=your_app_id_here
FACEBOOK_APP_SECRET=your_app_secret_here
```

---

## 📷 **Instagram API Setup**

### **Step 1: Use Facebook App**
Instagram API uses the same app as Facebook (Instagram Basic Display or Instagram Graph API).

### **Step 2: Add Instagram Product**
1. In your Facebook app, add "Instagram Basic Display" or "Instagram Graph API"
2. Configure the same redirect URI as Facebook

### **Step 3: Environment Variables**
```bash
# Instagram can use the same credentials as Facebook
INSTAGRAM_APP_ID=your_facebook_app_id_here
INSTAGRAM_APP_SECRET=your_facebook_app_secret_here

# Or separate credentials if needed
INSTAGRAM_APP_ID=your_instagram_app_id_here
INSTAGRAM_APP_SECRET=your_instagram_app_secret_here
```

---

## 💼 **LinkedIn API Setup**

### **Step 1: Create LinkedIn App**
1. Go to [LinkedIn Developers](https://www.linkedin.com/developers/)
2. Create a new app
3. Fill in required information

### **Step 2: Get API Credentials**
1. Go to "Auth" tab
2. Copy your **Client ID** and **Client Secret**

### **Step 3: Configure OAuth Settings**
1. Add redirect URL: `https://app.ewasl.com/api/social/callback/linkedin`
2. Request access to required scopes:
   - `r_liteprofile`
   - `r_emailaddress`
   - `w_member_social`

### **Step 4: Environment Variables**
```bash
LINKEDIN_CLIENT_ID=your_client_id_here
LINKEDIN_CLIENT_SECRET=your_client_secret_here
```

---

## 🎵 **TikTok API Setup**

### **Step 1: Apply for TikTok Developer Account**
1. Go to [TikTok Developers](https://developers.tiktok.com/)
2. Apply for developer access (requires approval)
3. Create a new app

### **Step 2: Get API Credentials**
1. In your app dashboard, get:
   - **Client Key** (Client ID)
   - **Client Secret**

### **Step 3: Configure OAuth Settings**
1. Set redirect URI: `https://app.ewasl.com/api/social/callback/tiktok`
2. Request required scopes:
   - `user.info.basic`
   - `video.publish`

### **Step 4: Environment Variables**
```bash
TIKTOK_CLIENT_ID=your_client_key_here
TIKTOK_CLIENT_SECRET=your_client_secret_here
```

---

## 👻 **Snapchat API Setup**

### **Step 1: Create Snapchat App**
1. Go to [Snap Kit](https://kit.snapchat.com/)
2. Create a new app
3. Enable required products

### **Step 2: Get API Credentials**
1. In your app settings, get:
   - **Client ID**
   - **Client Secret**

### **Step 3: Configure OAuth Settings**
1. Set redirect URI: `https://app.ewasl.com/api/social/callback/snapchat`
2. Configure required scopes

### **Step 4: Environment Variables**
```bash
SNAPCHAT_CLIENT_ID=your_client_id_here
SNAPCHAT_CLIENT_SECRET=your_client_secret_here
```

---

## 🔧 **Configuration in eWasl**

### **Method 1: Environment Variables (Recommended)**
Add the credentials to your `.env.local` file for development or environment variables in production.

### **Method 2: DigitalOcean App Platform**
1. Go to your app in DigitalOcean
2. Navigate to "Settings" → "App-Level Environment Variables"
3. Add each credential as a new environment variable
4. Deploy the updated configuration

### **Method 3: Update app-spec.yaml**
Add the credentials to your `app-spec.yaml` file:

```yaml
envs:
  # Twitter
  - key: TWITTER_CLIENT_ID
    scope: RUN_AND_BUILD_TIME
    value: "your_twitter_client_id"
  - key: TWITTER_CLIENT_SECRET
    scope: RUN_AND_BUILD_TIME
    value: "your_twitter_client_secret"
  
  # Facebook
  - key: FACEBOOK_APP_ID
    scope: RUN_AND_BUILD_TIME
    value: "your_facebook_app_id"
  - key: FACEBOOK_APP_SECRET
    scope: RUN_AND_BUILD_TIME
    value: "your_facebook_app_secret"
  
  # Add other platforms...
```

---

## ✅ **Verification**

After adding credentials:

1. **Check Status**: Visit `/api/social/oauth-status` to verify configuration
2. **Test OAuth**: Use the test endpoints to verify OAuth flows
3. **Test Publishing**: Try publishing a test post to verify functionality

---

## 🚨 **Important Notes**

### **Security**
- Never commit API credentials to version control
- Use environment variables for all sensitive data
- Regularly rotate API keys and secrets

### **Rate Limits**
- Each platform has different rate limits
- Implement proper rate limiting in your application
- Monitor API usage to avoid hitting limits

### **Compliance**
- Review each platform's terms of service
- Ensure your app complies with platform policies
- Some platforms require app review before going live

### **Testing**
- Use sandbox/development environments when available
- Test OAuth flows thoroughly before production
- Implement proper error handling for API failures

---

## 📞 **Support**

If you encounter issues:
1. Check the OAuth status endpoint for detailed error messages
2. Review platform-specific documentation
3. Verify callback URLs are correctly configured
4. Ensure all required scopes are requested

For platform-specific issues, consult the official documentation:
- [Twitter API Docs](https://developer.twitter.com/en/docs)
- [Facebook API Docs](https://developers.facebook.com/docs)
- [Instagram API Docs](https://developers.facebook.com/docs/instagram)
- [LinkedIn API Docs](https://docs.microsoft.com/en-us/linkedin/)
- [TikTok API Docs](https://developers.tiktok.com/doc)
- [Snapchat API Docs](https://developers.snap.com/)
