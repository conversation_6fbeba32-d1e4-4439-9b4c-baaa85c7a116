import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { BulkScheduler } from '@/lib/scheduling/bulk-scheduler';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/posts/bulk-operations - List user's bulk operations
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching bulk operations...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status'); // 'pending', 'processing', 'completed', 'failed', 'cancelled'
    const type = searchParams.get('type'); // 'import', 'schedule', 'update', 'delete'

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

    // Get bulk operations using BulkScheduler
    const bulkScheduler = new BulkScheduler();
    let operations = await bulkScheduler.getUserBulkOperations(user.id, limit);

    // Filter by status if specified
    if (status) {
      operations = operations.filter(op => op.status === status);
    }

    // Filter by type if specified
    if (type) {
      operations = operations.filter(op => op.operationType === type);
    }

    // Calculate summary statistics
    const summary = {
      total: operations.length,
      pending: operations.filter(op => op.status === 'pending').length,
      processing: operations.filter(op => op.status === 'processing').length,
      completed: operations.filter(op => op.status === 'completed').length,
      failed: operations.filter(op => op.status === 'failed').length,
      cancelled: operations.filter(op => op.status === 'cancelled').length,
    };

    console.log(`Found ${operations.length} bulk operations for user`);

    return NextResponse.json({
      success: true,
      data: {
        operations,
        summary,
        filters: {
          status,
          type,
          limit,
        },
      },
    });

  } catch (error: any) {
    console.error('Error fetching bulk operations:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch bulk operations',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/posts/bulk-operations - Process bulk operation
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Processing bulk operation...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { operationId, action } = body;

    if (!operationId) {
      return NextResponse.json(
        { error: 'Operation ID is required' },
        { status: 400 }
      );
    }

    const bulkScheduler = new BulkScheduler();

    switch (action) {
      case 'process':
        // Start processing the bulk operation
        await bulkScheduler.processBulkSchedule(operationId);
        
        return NextResponse.json({
          success: true,
          message: 'تم بدء معالجة العملية المجمعة',
        });

      case 'cancel':
        // Cancel the bulk operation
        await bulkScheduler.cancelBulkOperation(operationId, user.id);
        
        return NextResponse.json({
          success: true,
          message: 'تم إلغاء العملية المجمعة',
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use "process" or "cancel"' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Error processing bulk operation:', error);
    return NextResponse.json(
      {
        error: 'Failed to process bulk operation',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
