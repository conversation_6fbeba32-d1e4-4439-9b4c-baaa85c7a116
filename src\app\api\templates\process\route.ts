import { NextRequest, NextResponse } from 'next/server';
import { supabaseServiceRole as supabase } from '@/lib/supabase/service-role';

// POST /api/templates/process - Process template with variables
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, variables = {}, trackUsage = true } = body;

    if (!templateId) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Get the template
    const { data: template, error: templateError } = await supabase
      .from('content_templates')
      .select('*')
      .eq('id', templateId)
      .eq('is_public', true)
      .single();

    if (templateError || !template) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Process the template content by replacing variables
    let processedContent = template.content_body;

    // Replace variables in the format {{variableName}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processedContent = processedContent.replace(regex, String(value || ''));
    });

    // Extract hashtags from the processed content
    const hashtagMatches = processedContent.match(/#[\w\u0600-\u06FF]+/g) || [];
    const extractedHashtags = hashtagMatches.map((tag: string) => tag.substring(1)); // Remove # symbol

    // Combine template hashtags with extracted hashtags
    const allHashtags = [...new Set([...template.hashtags, ...extractedHashtags])];

    // Track usage if requested
    if (trackUsage) {
      try {
        const dummyUserId = '00000000-0000-0000-0000-000000000000';

        await supabase
          .from('template_usage')
          .insert({
            template_id: templateId,
            user_id: dummyUserId,
            platform: variables.platform || 'unknown',
            success: true,
            metadata: { variables }
          });
      } catch (usageError) {
        console.error('Error tracking template usage:', usageError);
        // Don't fail the request if usage tracking fails
      }
    }

    return NextResponse.json({
      success: true,
      content: processedContent,
      hashtags: allHashtags,
      template: {
        id: template.id,
        name: template.name,
        category: template.category,
        tone: template.tone,
        platform_compatibility: template.platform_compatibility
      }
    });

  } catch (error) {
    console.error('Template processing error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
