// =====================================================
// eWasl Recurring Manager - Core Logic Implementation
// =====================================================

import { createClient } from '@/lib/supabase/server';
import {
  RecurringPost,
  CreateRecurringPostData,
  RecurringPattern,
  CalendarPost,
  GeneratedPostSchedule,
  PatternGenerationOptions,
  SchedulingConflict
} from '@/lib/types/scheduling';

export class RecurringManager {
  private supabase = createClient();

  /**
   * Create a new recurring post pattern
   */
  async createRecurringPost(userId: string, data: CreateRecurringPostData) {
    try {
      console.log('Creating recurring post for user:', userId);

      // 1. Validate pattern configuration
      this.validatePattern(data.pattern);

      // 2. Calculate next generation time
      const nextGenerationAt = this.calculateNextGeneration(data.startDate, data.pattern);

      // 3. Create recurring post record
      const { data: recurringPost, error } = await this.supabase
        .from('recurring_posts')
        .insert({
          user_id: userId,
          title: data.title,
          content: data.content,
          media_url: data.mediaUrl,
          platforms: data.platforms,
          pattern_type: data.pattern.type,
          pattern_config: data.pattern.config,
          start_date: data.startDate.toISOString(),
          end_date: data.endDate?.toISOString(),
          timezone: data.timezone,
          next_generation_at: nextGenerationAt.toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating recurring post:', error);
        throw error;
      }

      // 4. Generate initial batch of posts (next 30 days)
      const generatedPosts = await this.generateNextBatch(recurringPost.id, 30);

      // 5. Log activity
      await this.supabase
        .from('activities')
        .insert({
          user_id: userId,
          action: 'RECURRING_POST_CREATED',
          metadata: {
            recurringPostId: recurringPost.id,
            patternType: data.pattern.type,
            postsGenerated: generatedPosts.length,
            platforms: data.platforms,
          },
        });

      return {
        recurringPost,
        generatedPosts,
        previewCount: generatedPosts.length,
      };
    } catch (error) {
      console.error('Error creating recurring post:', error);
      throw error;
    }
  }

  /**
   * Generate next batch of posts for a recurring pattern
   */
  async generateNextBatch(recurringPostId: string, days: number = 30): Promise<CalendarPost[]> {
    try {
      console.log('Generating batch for recurring post:', recurringPostId);

      // 1. Get recurring post configuration
      const { data: recurringPost, error } = await this.supabase
        .from('recurring_posts')
        .select('*')
        .eq('id', recurringPostId)
        .single();

      if (error || !recurringPost) {
        throw new Error('Recurring post not found');
      }

      if (!recurringPost.is_active) {
        throw new Error('Recurring post is not active');
      }

      // 2. Generate dates based on pattern
      const schedule = this.generateSchedule(
        recurringPost.pattern_type,
        recurringPost.pattern_config,
        new Date(recurringPost.start_date),
        recurringPost.end_date ? new Date(recurringPost.end_date) : undefined,
        { maxDays: days, respectOptimalTimes: true }
      );

      // 3. Create individual posts
      const postsToCreate = schedule.dates.map(date => ({
        user_id: recurringPost.user_id,
        content: recurringPost.content,
        media_url: recurringPost.media_url,
        scheduled_at: date.toISOString(),
        status: 'SCHEDULED',
        is_recurring: true,
        parent_post_id: recurringPostId,
        platforms: recurringPost.platforms,
        optimal_time_score: this.calculateOptimalScore(date, recurringPost.platforms),
      }));

      // 4. Insert posts in batches to avoid timeout
      const batchSize = 50;
      const createdPosts = [];

      for (let i = 0; i < postsToCreate.length; i += batchSize) {
        const batch = postsToCreate.slice(i, i + batchSize);
        const { data: batchPosts, error: createError } = await this.supabase
          .from('posts')
          .insert(batch)
          .select();

        if (createError) {
          console.error('Error creating batch:', createError);
          throw createError;
        }

        createdPosts.push(...(batchPosts || []));
      }

      // 5. Update recurring post statistics
      await this.supabase
        .from('recurring_posts')
        .update({
          posts_generated: recurringPost.posts_generated + createdPosts.length,
          last_generated_at: new Date().toISOString(),
          next_generation_at: this.calculateNextGeneration(
            new Date(),
            { type: recurringPost.pattern_type, config: recurringPost.pattern_config }
          ).toISOString(),
        })
        .eq('id', recurringPostId);

      console.log(`Generated ${createdPosts.length} posts for recurring pattern`);

      return createdPosts.map(post => ({
        id: post.id,
        content: post.content,
        platforms: post.platforms,
        scheduledAt: new Date(post.scheduled_at),
        status: post.status,
        isRecurring: true,
        parentRecurringId: recurringPostId,
        optimalTimeScore: post.optimal_time_score,
      }));

    } catch (error) {
      console.error('Error generating batch:', error);
      throw error;
    }
  }

  /**
   * Generate schedule based on pattern type
   */
  private generateSchedule(
    type: string,
    config: any,
    startDate: Date,
    endDate?: Date,
    options: PatternGenerationOptions = {}
  ): GeneratedPostSchedule {
    const {
      maxPosts = 100,
      maxDays = 30,
      skipWeekends = false,
      respectOptimalTimes = false
    } = options;

    const dates: Date[] = [];
    const conflicts: SchedulingConflict[] = [];
    const optimizations: any[] = [];

    const maxDate = endDate || new Date(Date.now() + maxDays * 24 * 60 * 60 * 1000);
    let currentDate = new Date(startDate);

    switch (type) {
      case 'daily':
        dates.push(...this.generateDailyPattern(currentDate, maxDate, config, maxPosts, skipWeekends));
        break;

      case 'weekly':
        dates.push(...this.generateWeeklyPattern(currentDate, maxDate, config, maxPosts));
        break;

      case 'monthly':
        dates.push(...this.generateMonthlyPattern(currentDate, maxDate, config, maxPosts));
        break;

      case 'custom':
        dates.push(...this.generateCustomPattern(config, startDate, maxDate));
        break;

      default:
        throw new Error(`Unsupported pattern type: ${type}`);
    }

    // Apply optimal time adjustments if requested
    if (respectOptimalTimes) {
      // This would integrate with optimal times analysis
      // For now, we'll implement basic time optimization
    }

    return {
      dates: dates.sort((a, b) => a.getTime() - b.getTime()),
      conflicts,
      optimizations,
      statistics: {
        totalPosts: dates.length,
        optimizedPosts: optimizations.length,
        conflictsPrevented: conflicts.length,
        averageOptimalScore: 0.8, // Placeholder
      }
    };
  }

  /**
   * Generate daily pattern dates
   */
  private generateDailyPattern(
    startDate: Date,
    maxDate: Date,
    config: any,
    maxPosts: number,
    skipWeekends: boolean
  ): Date[] {
    const dates: Date[] = [];
    const interval = config.interval || 1;
    const time = config.time || '09:00';
    const [hours, minutes] = time.split(':').map(Number);

    let currentDate = new Date(startDate);
    currentDate.setHours(hours, minutes, 0, 0);

    while (currentDate <= maxDate && dates.length < maxPosts) {
      // Skip weekends if requested
      if (skipWeekends && (currentDate.getDay() === 0 || currentDate.getDay() === 6)) {
        currentDate.setDate(currentDate.getDate() + 1);
        continue;
      }

      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + interval);
    }

    return dates;
  }

  /**
   * Generate weekly pattern dates
   */
  private generateWeeklyPattern(
    startDate: Date,
    maxDate: Date,
    config: any,
    maxPosts: number
  ): Date[] {
    const dates: Date[] = [];
    const targetDays = config.days || ['monday'];
    const time = config.time || '09:00';
    const [hours, minutes] = time.split(':').map(Number);

    const dayMap: Record<string, number> = {
      sunday: 0, monday: 1, tuesday: 2, wednesday: 3,
      thursday: 4, friday: 5, saturday: 6
    };

    let currentDate = new Date(startDate);
    currentDate.setHours(0, 0, 0, 0);

    while (currentDate <= maxDate && dates.length < maxPosts) {
      const dayName = currentDate.toLocaleDateString('en', { weekday: 'long' }).toLowerCase();

      if (targetDays.includes(dayName)) {
        const postDate = new Date(currentDate);
        postDate.setHours(hours, minutes, 0, 0);

        if (postDate >= startDate) {
          dates.push(postDate);
        }
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  }

  /**
   * Generate monthly pattern dates
   */
  private generateMonthlyPattern(
    startDate: Date,
    maxDate: Date,
    config: any,
    maxPosts: number
  ): Date[] {
    const dates: Date[] = [];
    const dayOfMonth = config.dayOfMonth || 1;
    const time = config.time || '09:00';
    const [hours, minutes] = time.split(':').map(Number);

    let currentDate = new Date(startDate);
    currentDate.setDate(1); // Start from first day of month

    while (currentDate <= maxDate && dates.length < maxPosts) {
      // Calculate the target date for this month
      const targetDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), dayOfMonth);
      targetDate.setHours(hours, minutes, 0, 0);

      // Check if the day exists in this month (e.g., Feb 30th doesn't exist)
      if (targetDate.getMonth() === currentDate.getMonth() && targetDate >= startDate && targetDate <= maxDate) {
        dates.push(new Date(targetDate));
      }

      // Move to next month
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return dates;
  }

  /**
   * Generate custom pattern dates
   */
  private generateCustomPattern(
    config: any,
    startDate: Date,
    maxDate: Date
  ): Date[] {
    const dates: Date[] = [];
    const customDates = config.dates || [];

    customDates.forEach((dateStr: string) => {
      const date = new Date(dateStr);
      if (date >= startDate && date <= maxDate) {
        dates.push(date);
      }
    });

    return dates;
  }

  /**
   * Calculate optimal score for a given time and platforms
   */
  private calculateOptimalScore(date: Date, platforms: string[]): number {
    // Placeholder implementation
    // This would integrate with the optimal times analysis
    const hour = date.getHours();
    const dayOfWeek = date.getDay();

    // Basic scoring based on general best practices
    let score = 0.5; // Base score

    // Peak hours bonus (9-11 AM, 1-3 PM, 7-9 PM)
    if ((hour >= 9 && hour <= 11) || (hour >= 13 && hour <= 15) || (hour >= 19 && hour <= 21)) {
      score += 0.2;
    }

    // Weekday bonus
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      score += 0.1;
    }

    // Platform-specific adjustments
    if (platforms.includes('TWITTER') && hour >= 9 && hour <= 10) {
      score += 0.1;
    }
    if (platforms.includes('LINKEDIN') && dayOfWeek >= 1 && dayOfWeek <= 5 && hour >= 8 && hour <= 17) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate next generation time for recurring posts
   */
  private calculateNextGeneration(lastDate: Date, pattern: RecurringPattern): Date {
    const nextDate = new Date(lastDate);

    switch (pattern.type) {
      case 'daily':
        nextDate.setDate(nextDate.getDate() + (pattern.config.interval || 1));
        break;
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      default:
        nextDate.setDate(nextDate.getDate() + 1);
    }

    return nextDate;
  }

  /**
   * Validate pattern configuration
   */
  private validatePattern(pattern: RecurringPattern): void {
    if (!pattern.type || !['daily', 'weekly', 'monthly', 'custom'].includes(pattern.type)) {
      throw new Error('Invalid pattern type');
    }

    switch (pattern.type) {
      case 'daily':
        if (pattern.config.interval && (pattern.config.interval < 1 || pattern.config.interval > 365)) {
          throw new Error('Daily interval must be between 1 and 365 days');
        }
        break;

      case 'weekly':
        if (!pattern.config.days || !Array.isArray(pattern.config.days) || pattern.config.days.length === 0) {
          throw new Error('Weekly pattern must specify at least one day');
        }
        break;

      case 'monthly':
        if (pattern.config.dayOfMonth && (pattern.config.dayOfMonth < 1 || pattern.config.dayOfMonth > 31)) {
          throw new Error('Day of month must be between 1 and 31');
        }
        break;

      case 'custom':
        if (!pattern.config.dates || !Array.isArray(pattern.config.dates) || pattern.config.dates.length === 0) {
          throw new Error('Custom pattern must specify at least one date');
        }
        break;
    }
  }

  /**
   * Get user's recurring posts
   */
  async getUserRecurringPosts(userId: string): Promise<RecurringPost[]> {
    try {
      const { data: posts, error } = await this.supabase
        .from('recurring_posts')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (posts || []).map(post => ({
        id: post.id,
        userId: post.user_id,
        title: post.title,
        content: post.content,
        mediaUrl: post.media_url,
        platforms: post.platforms,
        patternType: post.pattern_type,
        patternConfig: post.pattern_config,
        startDate: new Date(post.start_date),
        endDate: post.end_date ? new Date(post.end_date) : undefined,
        timezone: post.timezone,
        isActive: post.is_active,
        postsGenerated: post.posts_generated,
        lastGeneratedAt: post.last_generated_at ? new Date(post.last_generated_at) : undefined,
        nextGenerationAt: post.next_generation_at ? new Date(post.next_generation_at) : undefined,
        createdAt: new Date(post.created_at),
        updatedAt: new Date(post.updated_at),
      }));
    } catch (error) {
      console.error('Error fetching recurring posts:', error);
      throw error;
    }
  }

  /**
   * Update recurring post
   */
  async updateRecurringPost(id: string, userId: string, updates: Partial<CreateRecurringPostData>): Promise<RecurringPost> {
    try {
      const updateData: any = {};

      if (updates.title) updateData.title = updates.title;
      if (updates.content) updateData.content = updates.content;
      if (updates.mediaUrl !== undefined) updateData.media_url = updates.mediaUrl;
      if (updates.platforms) updateData.platforms = updates.platforms;
      if (updates.pattern) {
        this.validatePattern(updates.pattern);
        updateData.pattern_type = updates.pattern.type;
        updateData.pattern_config = updates.pattern.config;
      }
      if (updates.startDate) updateData.start_date = updates.startDate.toISOString();
      if (updates.endDate !== undefined) updateData.end_date = updates.endDate?.toISOString();
      if (updates.timezone) updateData.timezone = updates.timezone;

      const { data: updatedPost, error } = await this.supabase
        .from('recurring_posts')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return {
        id: updatedPost.id,
        userId: updatedPost.user_id,
        title: updatedPost.title,
        content: updatedPost.content,
        mediaUrl: updatedPost.media_url,
        platforms: updatedPost.platforms,
        patternType: updatedPost.pattern_type,
        patternConfig: updatedPost.pattern_config,
        startDate: new Date(updatedPost.start_date),
        endDate: updatedPost.end_date ? new Date(updatedPost.end_date) : undefined,
        timezone: updatedPost.timezone,
        isActive: updatedPost.is_active,
        postsGenerated: updatedPost.posts_generated,
        lastGeneratedAt: updatedPost.last_generated_at ? new Date(updatedPost.last_generated_at) : undefined,
        nextGenerationAt: updatedPost.next_generation_at ? new Date(updatedPost.next_generation_at) : undefined,
        createdAt: new Date(updatedPost.created_at),
        updatedAt: new Date(updatedPost.updated_at),
      };
    } catch (error) {
      console.error('Error updating recurring post:', error);
      throw error;
    }
  }

  /**
   * Pause/Resume recurring post
   */
  async toggleRecurringPost(id: string, userId: string, isActive: boolean): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('recurring_posts')
        .update({ is_active: isActive })
        .eq('id', id)
        .eq('user_id', userId);

      if (error) throw error;

      // Log activity
      await this.supabase
        .from('activities')
        .insert({
          user_id: userId,
          action: isActive ? 'RECURRING_POST_RESUMED' : 'RECURRING_POST_PAUSED',
          metadata: { recurringPostId: id },
        });

    } catch (error) {
      console.error('Error toggling recurring post:', error);
      throw error;
    }
  }

  /**
   * Delete recurring post and all associated scheduled posts
   */
  async deleteRecurringPost(id: string, userId: string): Promise<void> {
    try {
      // Delete all scheduled posts first
      await this.supabase
        .from('posts')
        .delete()
        .eq('parent_post_id', id)
        .eq('user_id', userId)
        .eq('status', 'SCHEDULED');

      // Delete the recurring post
      const { error } = await this.supabase
        .from('recurring_posts')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) throw error;

      // Log activity
      await this.supabase
        .from('activities')
        .insert({
          user_id: userId,
          action: 'RECURRING_POST_DELETED',
          metadata: { recurringPostId: id },
        });

    } catch (error) {
      console.error('Error deleting recurring post:', error);
      throw error;
    }
  }
}
