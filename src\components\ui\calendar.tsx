"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay } from "date-fns"
import { arSA, type Locale } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface CalendarProps {
  mode?: "single" | "range" | "multiple"
  selected?: Date | Date[] | { from: Date; to: Date }
  onSelect?: (date: Date | undefined) => void
  className?: string
  locale?: Locale
  initialFocus?: boolean
}

export function Calendar({
  mode = "single",
  selected,
  onSelect,
  className,
  locale = arSA,
  initialFocus,
}: CalendarProps) {
  const [currentMonth, setCurrentMonth] = React.useState<Date>(
    selected instanceof Date ? selected : new Date()
  )

  const days = React.useMemo(() => {
    const firstDay = startOfMonth(currentMonth)
    const lastDay = endOfMonth(currentMonth)
    return eachDayOfInterval({ start: firstDay, end: lastDay })
  }, [currentMonth])

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1))
  }

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1))
  }

  const isSelected = (day: Date) => {
    if (!selected) return false
    if (selected instanceof Date) {
      return isSameDay(day, selected)
    }
    return false
  }

  return (
    <div className={cn("p-3", className)}>
      <div className="flex justify-between items-center mb-4">
        <Button
          variant="outline"
          size="icon"
          onClick={handlePreviousMonth}
          className="h-7 w-7"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <div className="text-sm font-medium">
          {format(currentMonth, "MMMM yyyy", { locale })}
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={handleNextMonth}
          className="h-7 w-7"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      </div>
      <div className="grid grid-cols-7 gap-1 text-center">
        {["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"].map((day) => (
          <div key={day} className="text-xs text-muted-foreground py-1">
            {day}
          </div>
        ))}
        {Array.from({ length: new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1).getDay() }).map((_, i) => (
          <div key={`empty-${i}`} className="h-9 w-9" />
        ))}
        {days.map((day) => {
          const isToday = isSameDay(day, new Date())
          return (
            <Button
              key={day.toString()}
              variant="ghost"
              className={cn(
                "h-9 w-9 p-0 font-normal",
                isSelected(day) && "bg-primary text-primary-foreground",
                isToday && !isSelected(day) && "bg-accent text-accent-foreground",
                !isSameMonth(day, currentMonth) && "text-muted-foreground opacity-50"
              )}
              onClick={() => onSelect?.(day)}
            >
              {format(day, "d")}
            </Button>
          )
        })}
      </div>
    </div>
  )
}

Calendar.displayName = "Calendar"
