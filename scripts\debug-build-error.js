const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 eWasl Build Error Debugging Tool');
console.log('===================================');

// Function to check if a command exists
function commandExists(command) {
  try {
    execSync(`which ${command}`, { stdio: 'ignore' });
    return true;
  } catch (error) {
    try {
      execSync(`where ${command}`, { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Check system requirements
console.log('\n📋 System Requirements Check:');
console.log('-----------------------------');

const requirements = [
  { name: 'Node.js', command: 'node', version: '--version' },
  { name: 'npm', command: 'npm', version: '--version' },
  { name: 'Git', command: 'git', version: '--version' },
];

for (const req of requirements) {
  if (commandExists(req.command)) {
    try {
      const version = execSync(`${req.command} ${req.version}`, { encoding: 'utf8' }).trim();
      console.log(`✅ ${req.name}: ${version}`);
    } catch (error) {
      console.log(`⚠️  ${req.name}: Installed but version check failed`);
    }
  } else {
    console.log(`❌ ${req.name}: Not found`);
  }
}

// Check package.json
console.log('\n📦 Package Configuration:');
console.log('-------------------------');

const packagePath = path.join(__dirname, '..', 'package.json');
if (fs.existsSync(packagePath)) {
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  console.log(`✅ Package name: ${packageJson.name}`);
  console.log(`✅ Version: ${packageJson.version}`);
  
  if (packageJson.engines) {
    console.log(`✅ Node.js requirement: ${packageJson.engines.node || 'Not specified'}`);
    console.log(`✅ npm requirement: ${packageJson.engines.npm || 'Not specified'}`);
  } else {
    console.log('⚠️  No engine requirements specified');
  }
  
  // Check critical dependencies
  const criticalDeps = ['next', 'react', 'typescript'];
  console.log('\n🔗 Critical Dependencies:');
  for (const dep of criticalDeps) {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]} (dev)`);
    } else {
      console.log(`❌ ${dep}: Missing`);
    }
  }
} else {
  console.log('❌ package.json not found');
}

// Check Next.js configuration
console.log('\n⚙️  Next.js Configuration:');
console.log('--------------------------');

const nextConfigPath = path.join(__dirname, '..', 'next.config.js');
if (fs.existsSync(nextConfigPath)) {
  console.log('✅ next.config.js found');
  
  try {
    const configContent = fs.readFileSync(nextConfigPath, 'utf8');
    if (configContent.includes('output: \'standalone\'')) {
      console.log('✅ Standalone output configured');
    } else {
      console.log('⚠️  Standalone output not configured');
    }
    
    if (configContent.includes('swcMinify')) {
      console.log('✅ SWC minification enabled');
    } else {
      console.log('⚠️  SWC minification not configured');
    }
  } catch (error) {
    console.log('⚠️  Could not read next.config.js');
  }
} else {
  console.log('❌ next.config.js not found');
}

// Check environment files
console.log('\n🌐 Environment Configuration:');
console.log('-----------------------------');

const envFiles = ['.env', '.env.local', '.env.production'];
for (const envFile of envFiles) {
  const envPath = path.join(__dirname, '..', envFile);
  if (fs.existsSync(envPath)) {
    console.log(`✅ ${envFile} found`);
  } else {
    console.log(`⚠️  ${envFile} not found`);
  }
}

// Check for common problematic files
console.log('\n🚨 Potential Issues:');
console.log('--------------------');

const problematicPatterns = [
  { pattern: 'node_modules', message: 'Large node_modules directory (may cause memory issues)' },
  { pattern: '.next', message: 'Previous build cache exists' },
  { pattern: 'package-lock.json', message: 'npm lock file exists' },
  { pattern: 'yarn.lock', message: 'Yarn lock file exists' },
];

for (const item of problematicPatterns) {
  const itemPath = path.join(__dirname, '..', item.pattern);
  if (fs.existsSync(itemPath)) {
    if (item.pattern === 'node_modules') {
      try {
        const stats = fs.statSync(itemPath);
        console.log(`⚠️  ${item.message}`);
      } catch (error) {
        console.log(`✅ ${item.pattern} exists but accessible`);
      }
    } else {
      console.log(`✅ ${item.pattern} exists`);
    }
  } else {
    console.log(`⚠️  ${item.pattern} not found`);
  }
}

// Try to identify specific build errors
console.log('\n🔧 Build Error Analysis:');
console.log('------------------------');

try {
  console.log('Attempting to run build...');
  const buildOutput = execSync('npm run build', { 
    cwd: path.join(__dirname, '..'),
    encoding: 'utf8',
    timeout: 60000 // 1 minute timeout
  });
  
  console.log('✅ Build completed successfully!');
  
  // Check for warnings in build output
  if (buildOutput.includes('warning') || buildOutput.includes('Warning')) {
    console.log('⚠️  Build completed with warnings');
  }
  
} catch (error) {
  console.log('❌ Build failed with error:');
  console.log('Error output:');
  console.log(error.stdout || error.message);
  
  // Analyze common error patterns
  const errorMessage = error.stdout || error.message || '';
  
  if (errorMessage.includes('out of memory') || errorMessage.includes('heap')) {
    console.log('\n💡 SOLUTION: Memory issue detected');
    console.log('   - Increase memory allocation');
    console.log('   - Use smaller instance size');
    console.log('   - Optimize bundle size');
  }
  
  if (errorMessage.includes('Module not found') || errorMessage.includes('Cannot resolve')) {
    console.log('\n💡 SOLUTION: Dependency issue detected');
    console.log('   - Run npm install');
    console.log('   - Check import paths');
    console.log('   - Verify all dependencies are installed');
  }
  
  if (errorMessage.includes('TypeScript') || errorMessage.includes('type')) {
    console.log('\n💡 SOLUTION: TypeScript issue detected');
    console.log('   - Run npx tsc --noEmit to check types');
    console.log('   - Fix type errors');
    console.log('   - Update @types packages');
  }
  
  if (errorMessage.includes('timeout') || errorMessage.includes('ETIMEDOUT')) {
    console.log('\n💡 SOLUTION: Timeout issue detected');
    console.log('   - Increase build timeout');
    console.log('   - Check network connectivity');
    console.log('   - Use local dependencies');
  }
}

console.log('\n📞 Support Resources:');
console.log('=====================');
console.log('- DigitalOcean Docs: https://docs.digitalocean.com/products/app-platform/');
console.log('- Next.js Deployment: https://nextjs.org/docs/deployment');
console.log('- GitHub Issues: https://github.com/TahaOsa/eWasl.com/issues');

console.log('\n🎯 Quick Fixes to Try:');
console.log('======================');
console.log('1. Delete .next folder and rebuild');
console.log('2. Run npm ci to clean install dependencies');
console.log('3. Check environment variables in DigitalOcean');
console.log('4. Increase instance size in DigitalOcean');
console.log('5. Check build logs for specific error messages');
