# 🎨 **eWasl Dashboard Design Specification for Bolt.new**

## 🎯 **PROJECT OVERVIEW**
Create a modern, Arabic-first social media management dashboard with proper RTL layout, matching the refined Lovable design while fixing critical RTL positioning issues.

---

## 🚨 **CRITICAL PRIORITY SPECIFICATIONS**

### **1. RTL LAYOUT STRUCTURE**

#### **Sidebar Menu (RIGHT SIDE)**
```css
/* Critical: Sidebar positioning */
.sidebar {
  position: fixed;
  right: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1e40af 0%, #7c3aed 100%);
  z-index: 1000;
}

/* Main content adjustment */
.main-content {
  margin-right: 280px;
  margin-left: 0;
  padding: 24px;
}
```

#### **Header Layout (RTL)**
```
[تسجيل الخروج] [👤] [🔔3] [العربية🌐] [البحث...🔍] [إدارة ذكية | إي وصل | eW]
```

#### **Responsive Breakpoints**
```css
/* Desktop: 1200px+ */
.sidebar { width: 280px; }
.main-content { margin-right: 280px; }

/* Tablet: 768px-1199px */
.sidebar { width: 240px; }
.main-content { margin-right: 240px; }

/* Mobile: <768px */
.sidebar { transform: translateX(100%); } /* Hidden by default */
.main-content { margin-right: 0; }
```

---

### **2. HEADER COMPONENT**

#### **Structure (RTL Order)**
```html
<header class="header-rtl">
  <!-- Right side: Logo -->
  <div class="logo-section">
    <div class="logo-box">eW</div>
    <div class="brand-text">
      <h1>إي وصل</h1>
      <p>إدارة ذكية</p>
    </div>
  </div>
  
  <!-- Center: Search -->
  <div class="search-section">
    <input placeholder="البحث..." dir="rtl" />
    <icon>🔍</icon>
  </div>
  
  <!-- Left side: User controls -->
  <div class="user-controls">
    <button class="lang-toggle">🌐 العربية</button>
    <button class="notifications">🔔<span class="badge">3</span></button>
    <button class="user-menu">👤</button>
    <button class="logout">تسجيل الخروج</button>
  </div>
</header>
```

#### **Styling**
```css
.header-rtl {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  direction: rtl;
}

.logo-box {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: bold;
}
```

---

### **3. SIDEBAR NAVIGATION (RIGHT SIDE)**

#### **Menu Items (Arabic)**
```html
<nav class="sidebar-nav">
  <div class="nav-header">
    <div class="logo">eW</div>
    <h2>إي وصل</h2>
    <p>إدارة ذكية</p>
  </div>
  
  <ul class="nav-menu">
    <li><a href="/">🏠 لوحة التحكم</a></li>
    <li><a href="/posts">📝 إدارة المنشورات</a></li>
    <li><a href="/schedule">📅 جدولة المنشورات</a></li>
    <li><a href="/accounts">🔗 الحسابات الاجتماعية</a></li>
    <li><a href="/analytics">📊 التحليلات</a></li>
    <li><a href="/templates">📚 مكتبة القوالب</a></li>
    <li><a href="/settings">⚙️ الإعدادات</a></li>
  </ul>
  
  <div class="nav-footer">
    <button class="lang-switch">English</button>
    <button class="logout-btn">تسجيل الخروج</button>
  </div>
</nav>
```

---

## 🔥 **HIGH PRIORITY COMPONENTS**

### **4. WELCOME SECTION**

#### **Content Structure**
```html
<section class="welcome-section">
  <div class="welcome-content">
    <div class="greeting-area">
      <div class="welcome-icon">👋</div>
      <div class="welcome-text">
        <h1>مرحباً يا طه!</h1>
        <p>إليك نظرة سريعة على أداء حساباتك اليوم</p>
      </div>
    </div>
    <button class="cta-button">
      ➕ إنشاء منشور جديد
    </button>
  </div>
</section>
```

#### **Styling**
```css
.welcome-section {
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  direction: rtl;
}

.greeting-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.welcome-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-text h1 {
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8px;
}
```

---

### **5. STATISTICS CARDS**

#### **Exact Metrics Content**
```html
<section class="stats-grid">
  <div class="stat-card">
    <div class="stat-icon">📊</div>
    <div class="stat-content">
      <h3>إجمالي المنشورات</h3>
      <div class="stat-value">156</div>
      <div class="stat-change positive">****% من الشهر الماضي</div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="stat-icon">📅</div>
    <div class="stat-content">
      <h3>منشورات مجدولة</h3>
      <div class="stat-value">24</div>
      <div class="stat-change neutral">هذا الأسبوع</div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="stat-icon">👥</div>
    <div class="stat-content">
      <h3>تفاعلات متوقعة</h3>
      <div class="stat-value">8</div>
      <div class="stat-change new">24 جديد</div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="stat-icon">❤️</div>
    <div class="stat-content">
      <h3>إجمالي التفاعلات</h3>
      <div class="stat-value">12,543</div>
      <div class="stat-change positive">+15.3% من الشهر الماضي</div>
    </div>
  </div>
</section>
```

#### **Grid Layout**
```css
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  direction: rtl;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-change.positive {
  color: #10b981;
  background: #d1fae5;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px;
}
```

---

### **6. QUICK ACTIONS SECTION**

#### **Action Cards Content**
```html
<section class="quick-actions">
  <h2>إجراءات سريعة</h2>
  <div class="actions-grid">
    <div class="action-card create">
      <div class="action-icon">✍️</div>
      <div class="action-content">
        <h3>إنشاء منشور</h3>
        <p>منشور جديد لجميع المنصات</p>
      </div>
    </div>

    <div class="action-card schedule">
      <div class="action-icon">📅</div>
      <div class="action-content">
        <h3>جدولة منشور</h3>
        <p>جدولة منشور جديد</p>
      </div>
    </div>

    <div class="action-card analytics">
      <div class="action-icon">📊</div>
      <div class="action-content">
        <h3>عرض التحليلات</h3>
        <p>إحصائيات مفصلة</p>
      </div>
    </div>
  </div>
</section>
```

#### **Styling**
```css
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  direction: rtl;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px -10px rgba(0, 0, 0, 0.15);
  border-color: #2563eb;
}

.action-icon {
  font-size: 32px;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  border-radius: 12px;
  padding: 16px;
  display: inline-block;
}
```

---

## ⚡ **MEDIUM PRIORITY COMPONENTS**

### **7. ACTIVITY FEED**

#### **Activity Items Content**
```html
<section class="activity-feed">
  <h2>النشاط الأخير</h2>
  <div class="activity-list">
    <div class="activity-item success">
      <div class="activity-icon">✅</div>
      <div class="activity-content">
        <p>تم نشر منشور جديد بنجاح على Facebook</p>
        <span class="activity-time">منذ ساعتين</span>
      </div>
    </div>

    <div class="activity-item scheduled">
      <div class="activity-icon">📅</div>
      <div class="activity-content">
        <p>تم جدولة منشور جديد بنجاح لـ Instagram</p>
        <span class="activity-time">منذ 5 ساعات</span>
      </div>
    </div>

    <div class="activity-item failed">
      <div class="activity-icon">❌</div>
      <div class="activity-content">
        <p>فشل في نشر المنشور على Twitter</p>
        <span class="activity-time">منذ 8 ساعات</span>
      </div>
    </div>

    <div class="activity-item info">
      <div class="activity-icon">ℹ️</div>
      <div class="activity-content">
        <p>تم تحديث إعدادات LinkedIn</p>
        <span class="activity-time">منذ 12 ساعة</span>
      </div>
    </div>
  </div>
</section>
```

### **8. RECENT POSTS SECTION**

#### **Posts Content with Platform Icons**
```html
<section class="recent-posts">
  <h2>المنشورات الأخيرة</h2>
  <div class="posts-list">
    <div class="post-card">
      <div class="post-platforms">📘 🐦</div>
      <div class="post-status published">منشور</div>
      <div class="post-content">
        <p>أهلاً بكم في منصة eWasl لإدارة وسائل التواصل الاجتماعي!</p>
      </div>
      <div class="post-metrics">
        <span>❤️ 145</span>
        <span>💬 45</span>
        <span>🔄 12</span>
        <span class="post-time">منذ ساعتين</span>
      </div>
    </div>

    <div class="post-card">
      <div class="post-platforms">📷 💼</div>
      <div class="post-status scheduled">مجدول</div>
      <div class="post-content">
        <p>نحن نعمل على تحسين المنصة للحصول على تجربة أفضل للمستخدمين</p>
      </div>
      <div class="post-metrics">
        <span>❤️ 89</span>
        <span>💬 23</span>
        <span>🔄 7</span>
        <span class="post-time">منذ 4 ساعات</span>
      </div>
    </div>

    <div class="post-card">
      <div class="post-platforms">📘</div>
      <div class="post-status draft">مسودة</div>
      <div class="post-content">
        <p>هذا منشور تجريبي لاختبار واجهة المستخدم</p>
      </div>
      <div class="post-metrics">
        <span>❤️ 34</span>
        <span>💬 8</span>
        <span>🔄 3</span>
        <span class="post-time">منذ 6 ساعات</span>
      </div>
    </div>
  </div>
</section>
```

---

## 🎨 **VISUAL DESIGN SYSTEM**

### **Color Palette**
```css
:root {
  /* Primary Colors */
  --primary-blue: #2563eb;
  --primary-purple: #7c3aed;
  --gradient-primary: linear-gradient(135deg, #2563eb, #7c3aed);

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-600: #4b5563;
  --gray-900: #111827;

  /* Background */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
}
```

### **Typography (Arabic RTL)**
```css
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
}

h1 { font-size: 32px; font-weight: 700; }
h2 { font-size: 24px; font-weight: 600; }
h3 { font-size: 18px; font-weight: 600; }
p { font-size: 16px; font-weight: 400; line-height: 1.6; }
```

### **Spacing System**
```css
.spacing {
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
}
```

---

## 📱 **MOBILE RESPONSIVENESS**

### **Mobile Layout Adjustments**
```css
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-right: 0;
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .welcome-section {
    padding: 24px 16px;
  }

  .welcome-text h1 {
    font-size: 24px;
  }
}
```

---

## 🔧 **TECHNICAL IMPLEMENTATION NOTES**

### **RTL CSS Framework**
```css
/* Global RTL Setup */
html {
  direction: rtl;
}

* {
  box-sizing: border-box;
}

/* RTL-specific utilities */
.text-right { text-align: right; }
.float-right { float: right; }
.mr-auto { margin-right: auto; }
.ml-0 { margin-left: 0; }
```

### **Component Data Structure**
```javascript
// Dashboard data structure
const dashboardData = {
  user: {
    name: "طه",
    avatar: "/avatar.jpg"
  },
  stats: {
    totalPosts: { value: 156, change: "****%" },
    scheduledPosts: { value: 24, label: "هذا الأسبوع" },
    expectedEngagement: { value: 8, new: "24 جديد" },
    totalEngagement: { value: 12543, change: "+15.3%" }
  },
  activities: [
    { type: "success", icon: "✅", message: "تم نشر منشور جديد بنجاح على Facebook", time: "منذ ساعتين" },
    { type: "scheduled", icon: "📅", message: "تم جدولة منشور جديد بنجاح لـ Instagram", time: "منذ 5 ساعات" },
    { type: "failed", icon: "❌", message: "فشل في نشر المنشور على Twitter", time: "منذ 8 ساعات" },
    { type: "info", icon: "ℹ️", message: "تم تحديث إعدادات LinkedIn", time: "منذ 12 ساعة" }
  ],
  recentPosts: [
    {
      platforms: ["📘", "🐦"],
      status: "منشور",
      content: "أهلاً بكم في منصة eWasl لإدارة وسائل التواصل الاجتماعي!",
      metrics: { likes: 145, comments: 45, shares: 12 },
      time: "منذ ساعتين"
    }
  ]
};
```

---

## 🎯 **IMPLEMENTATION PRIORITY**

### **Phase 1: Critical (Week 1)**
- ✅ RTL sidebar positioning (RIGHT side)
- ✅ Header layout with proper RTL order
- ✅ Welcome section with personalized greeting
- ✅ Statistics cards with exact metrics

### **Phase 2: High Priority (Week 2)**
- ✅ Quick actions section
- ✅ Activity feed with status icons
- ✅ Recent posts with platform icons
- ✅ Mobile responsiveness

### **Phase 3: Polish (Week 3)**
- ✅ Hover effects and animations
- ✅ Loading states
- ✅ Error handling
- ✅ Performance optimization

---

## 📋 **BOLT.NEW PROMPT TEMPLATE**

**Use this exact prompt with bolt.new:**

"Create an Arabic RTL social media management dashboard for eWasl with:

1. **RIGHT-SIDE SIDEBAR** with navigation menu in Arabic
2. **RTL HEADER** with logo on right, user controls on left
3. **WELCOME SECTION** with '👋 مرحباً يا طه!' greeting
4. **STATISTICS CARDS** showing exact metrics: 156 posts (****%), 24 scheduled, 8 expected engagement (24 new), 12,543 total engagement (+15.3%)
5. **QUICK ACTIONS** with three cards: إنشاء منشور, جدولة منشور, عرض التحليلات
6. **ACTIVITY FEED** with status icons ✅📅❌ℹ️ and Arabic messages
7. **RECENT POSTS** with platform icons 📘🐦📷💼 and engagement metrics ❤️💬🔄

Use blue-purple gradient theme, Cairo font, proper RTL layout, and mobile responsive design. All text in Arabic with proper RTL alignment."

---

## ✅ **SUCCESS CRITERIA**

- ✅ Sidebar positioned on RIGHT side (not left)
- ✅ All Arabic text properly aligned RTL
- ✅ Exact metrics and content matching specification
- ✅ Platform icons and status indicators working
- ✅ Mobile responsive with proper RTL behavior
- ✅ Blue-purple gradient theme consistent
- ✅ Hover effects and smooth transitions
- ✅ Professional Arabic typography
