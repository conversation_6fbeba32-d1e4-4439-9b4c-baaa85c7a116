#!/usr/bin/env node

/**
 * Enhanced Providers Testing
 * Tests the Postiz-enhanced provider implementations
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-Enhanced-Tester/1.0',
        ...options.headers
      },
      timeout: 15000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function testCallbackEndpoints() {
  console.log('🔗 TESTING OAUTH CALLBACK ENDPOINTS...\n');
  
  const platforms = ['twitter', 'facebook', 'instagram', 'linkedin'];
  const results = [];
  
  for (const platform of platforms) {
    console.log(`Testing ${platform.toUpperCase()} callback endpoint...`);
    
    try {
      // Test callback endpoint with minimal params
      const callbackUrl = `${BASE_URL}/api/social/callback/${platform}`;
      const response = await makeRequest(callbackUrl, {
        method: 'GET'
      });
      
      console.log(`  Status: ${response.status}`);
      
      // We expect 400 (missing params) or 401 (auth required), not 404
      if (response.status === 404) {
        console.log(`  ❌ Endpoint not found`);
        results.push({ platform, status: 'FAIL', issue: 'endpoint_not_found' });
      } else if (response.status === 400) {
        console.log(`  ✅ Endpoint exists (missing parameters expected)`);
        results.push({ platform, status: 'PASS', issue: 'missing_params_expected' });
      } else if (response.status === 401) {
        console.log(`  ✅ Endpoint exists (authentication required)`);
        results.push({ platform, status: 'PASS', issue: 'auth_required' });
      } else if (response.status === 307 || response.status === 302) {
        console.log(`  ✅ Endpoint exists (redirect response)`);
        results.push({ platform, status: 'PASS', issue: 'redirect_response' });
      } else {
        console.log(`  ⚠️  Unexpected status: ${response.status}`);
        results.push({ platform, status: 'WARN', issue: `unexpected_status_${response.status}` });
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      results.push({ platform, status: 'FAIL', error: error.message });
    }
    
    console.log('');
  }
  
  return results;
}

async function testEnhancedProviderEndpoints() {
  console.log('🚀 TESTING ENHANCED PROVIDER ENDPOINTS...\n');
  
  const endpoints = [
    { name: 'Enhanced Connect', url: '/api/social/enhanced/connect', method: 'POST' },
    { name: 'Enhanced Publish', url: '/api/social/enhanced/publish', method: 'POST' },
    { name: 'Enhanced Test', url: '/api/social/enhanced/test', method: 'GET' }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    console.log(`Testing ${endpoint.name}...`);
    
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint.url}`, {
        method: endpoint.method,
        body: endpoint.method === 'POST' ? { platform: 'twitter' } : undefined
      });
      
      console.log(`  Status: ${response.status}`);
      
      if (response.status === 200) {
        console.log(`  ✅ Endpoint working correctly`);
        results.push({ endpoint: endpoint.name, status: 'PASS' });
      } else if (response.status === 400 || response.status === 401) {
        console.log(`  ✅ Endpoint exists (${response.status === 400 ? 'validation' : 'auth'} required)`);
        results.push({ endpoint: endpoint.name, status: 'PASS' });
      } else if (response.status === 404) {
        console.log(`  ❌ Endpoint not found`);
        results.push({ endpoint: endpoint.name, status: 'FAIL' });
      } else {
        console.log(`  ⚠️  Unexpected status: ${response.status}`);
        results.push({ endpoint: endpoint.name, status: 'WARN' });
      }
      
      if (response.data && response.data.error) {
        console.log(`  Info: ${response.data.error}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      results.push({ endpoint: endpoint.name, status: 'FAIL', error: error.message });
    }
    
    console.log('');
  }
  
  return results;
}

async function testSocialAccountsIntegration() {
  console.log('📱 TESTING SOCIAL ACCOUNTS INTEGRATION...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/accounts`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Social accounts endpoint working');
      
      if (response.data.accounts && Array.isArray(response.data.accounts)) {
        console.log(`📊 Connected accounts: ${response.data.accounts.length}`);
        
        const platformCounts = {};
        response.data.accounts.forEach(account => {
          platformCounts[account.platform] = (platformCounts[account.platform] || 0) + 1;
          console.log(`  - ${account.platform}: ${account.account_name} (${account.status || 'unknown'})`);
        });
        
        console.log('\n📈 Platform distribution:');
        Object.entries(platformCounts).forEach(([platform, count]) => {
          console.log(`  ${platform.toUpperCase()}: ${count} account(s)`);
        });
        
        return { 
          status: 'PASS', 
          accountCount: response.data.accounts.length,
          platforms: Object.keys(platformCounts)
        };
      } else {
        console.log('📝 No accounts found or invalid format');
        return { status: 'PASS', accountCount: 0, platforms: [] };
      }
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function testConfigValidation() {
  console.log('\n⚙️ TESTING CONFIGURATION VALIDATION...\n');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/social/config/validate`);
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Configuration validation working');
      
      if (response.data.platforms) {
        console.log('\n📋 Platform configurations:');
        
        const configuredPlatforms = [];
        const unconfiguredPlatforms = [];
        
        Object.entries(response.data.platforms).forEach(([platform, config]) => {
          const status = config.configured ? '✅' : '❌';
          console.log(`  ${status} ${platform.toUpperCase()}: ${config.configured ? 'Configured' : 'Not configured'}`);
          
          if (config.configured) {
            configuredPlatforms.push(platform);
          } else {
            unconfiguredPlatforms.push(platform);
            if (config.missingEnvVars && config.missingEnvVars.length > 0) {
              console.log(`    Missing: ${config.missingEnvVars.join(', ')}`);
            }
          }
        });
        
        console.log(`\n📊 Summary: ${configuredPlatforms.length} configured, ${unconfiguredPlatforms.length} need setup`);
        
        return { 
          status: 'PASS', 
          configured: configuredPlatforms,
          unconfigured: unconfiguredPlatforms
        };
      } else {
        console.log('❌ No platform configuration data');
        return { status: 'FAIL', issue: 'no_config_data' };
      }
    } else {
      console.log(`❌ Unexpected response: ${response.status}`);
      return { status: 'FAIL', httpStatus: response.status };
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return { status: 'FAIL', error: error.message };
  }
}

async function runEnhancedProviderTests() {
  console.log('🧪 ENHANCED PROVIDERS COMPREHENSIVE TESTING');
  console.log('=' .repeat(70));
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  // Run all test suites
  const callbackResults = await testCallbackEndpoints();
  const providerResults = await testEnhancedProviderEndpoints();
  const accountsResult = await testSocialAccountsIntegration();
  const configResult = await testConfigValidation();
  
  // Calculate overall results
  const allResults = [
    ...callbackResults,
    ...providerResults,
    accountsResult,
    configResult
  ];
  
  const passed = allResults.filter(r => r.status === 'PASS').length;
  const warned = allResults.filter(r => r.status === 'WARN').length;
  const failed = allResults.filter(r => r.status === 'FAIL').length;
  const total = allResults.length;
  
  console.log('\n📊 ENHANCED PROVIDERS TESTING SUMMARY:');
  console.log('=' .repeat(60));
  console.log(`✅ Passed: ${passed}`);
  console.log(`⚠️  Warnings: ${warned}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  // Detailed breakdown
  console.log('\n📋 DETAILED BREAKDOWN:');
  console.log(`🔗 OAuth Callbacks: ${callbackResults.filter(r => r.status === 'PASS').length}/${callbackResults.length} passed`);
  console.log(`🚀 Enhanced Endpoints: ${providerResults.filter(r => r.status === 'PASS').length}/${providerResults.length} passed`);
  console.log(`📱 Accounts Integration: ${accountsResult.status}`);
  console.log(`⚙️  Configuration: ${configResult.status}`);
  
  if (accountsResult.status === 'PASS') {
    console.log(`   Connected accounts: ${accountsResult.accountCount}`);
    console.log(`   Active platforms: ${accountsResult.platforms.join(', ') || 'none'}`);
  }
  
  if (configResult.status === 'PASS') {
    console.log(`   Configured platforms: ${configResult.configured?.join(', ') || 'none'}`);
    console.log(`   Need setup: ${configResult.unconfigured?.join(', ') || 'none'}`);
  }
  
  console.log('\n🏁 TESTING COMPLETE');
  console.log(`Finished at: ${new Date().toISOString()}`);
  
  return {
    callbacks: callbackResults,
    providers: providerResults,
    accounts: accountsResult,
    config: configResult,
    summary: {
      total,
      passed,
      warned,
      failed,
      successRate: (passed / total) * 100
    }
  };
}

if (require.main === module) {
  runEnhancedProviderTests().then(results => {
    process.exit(results.summary.successRate >= 70 ? 0 : 1);
  }).catch(error => {
    console.error('❌ CRITICAL ERROR:', error);
    process.exit(1);
  });
}

module.exports = { runEnhancedProviderTests };
