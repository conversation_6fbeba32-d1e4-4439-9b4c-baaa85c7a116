/**
 * Tests for Recurring Posts API
 * Tests the /api/posts/recurring endpoint functionality
 */

import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';

// Mock Next.js request/response
const mockRequest = (body: any, method: string = 'POST') => ({
  json: jest.fn().mockResolvedValue(body),
  url: 'http://localhost:3000/api/posts/recurring',
  method,
});

const mockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  single: jest.fn(),
  limit: jest.fn().mockReturnThis(),
};

jest.mock('@/lib/supabase/server', () => ({
  createClient: () => mockSupabase,
}));

describe('Recurring Posts API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default successful auth
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'test-user-id' } },
      error: null,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('POST /api/posts/recurring', () => {
    it('should create daily recurring posts successfully', async () => {
      const requestBody = {
        content: 'Test daily recurring post',
        social_account_ids: ['account-1', 'account-2'],
        recurring_pattern: {
          frequency: 'daily',
          interval: 1,
          time: '09:00',
        },
        start_date: '2024-01-01T09:00:00Z',
        end_date: '2024-01-05T09:00:00Z',
        time_zone: 'UTC',
      };

      // Mock social accounts validation
      mockSupabase.select.mockResolvedValueOnce({
        data: [{ id: 'account-1' }, { id: 'account-2' }],
        error: null,
      });

      // Mock parent post creation
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'parent-post-id',
          content: requestBody.content,
          recurring_pattern: requestBody.recurring_pattern,
        },
        error: null,
      });

      // Mock instances creation
      mockSupabase.select.mockResolvedValueOnce({
        data: [
          { id: 'instance-1', scheduled_at: '2024-01-01T09:00:00Z' },
          { id: 'instance-2', scheduled_at: '2024-01-02T09:00:00Z' },
          { id: 'instance-3', scheduled_at: '2024-01-03T09:00:00Z' },
          { id: 'instance-4', scheduled_at: '2024-01-04T09:00:00Z' },
          { id: 'instance-5', scheduled_at: '2024-01-05T09:00:00Z' },
        ],
        error: null,
      });

      // Mock activity logging
      mockSupabase.insert.mockResolvedValue({ error: null });

      const { POST } = await import('@/app/api/posts/recurring/route');
      const req = mockRequest(requestBody);
      const res = mockResponse();

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.instancesCreated).toBe(5);
      expect(responseData.parentPost).toBeDefined();
    });

    it('should create weekly recurring posts with specific days', async () => {
      const requestBody = {
        content: 'Test weekly recurring post',
        social_account_ids: ['account-1'],
        recurring_pattern: {
          frequency: 'weekly',
          interval: 1,
          days_of_week: [1, 3, 5], // Monday, Wednesday, Friday
          time: '14:00',
        },
        start_date: '2024-01-01T14:00:00Z', // Monday
        end_date: '2024-01-15T14:00:00Z',
        time_zone: 'UTC',
      };

      // Mock successful responses
      mockSupabase.select.mockResolvedValueOnce({
        data: [{ id: 'account-1' }],
        error: null,
      });

      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'parent-post-id', content: requestBody.content },
        error: null,
      });

      mockSupabase.select.mockResolvedValueOnce({
        data: [
          { id: 'instance-1', scheduled_at: '2024-01-01T14:00:00Z' }, // Monday
          { id: 'instance-2', scheduled_at: '2024-01-03T14:00:00Z' }, // Wednesday
          { id: 'instance-3', scheduled_at: '2024-01-05T14:00:00Z' }, // Friday
          { id: 'instance-4', scheduled_at: '2024-01-08T14:00:00Z' }, // Monday
          { id: 'instance-5', scheduled_at: '2024-01-10T14:00:00Z' }, // Wednesday
          { id: 'instance-6', scheduled_at: '2024-01-12T14:00:00Z' }, // Friday
        ],
        error: null,
      });

      mockSupabase.insert.mockResolvedValue({ error: null });

      const { POST } = await import('@/app/api/posts/recurring/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.instancesCreated).toBe(6);
    });

    it('should create monthly recurring posts', async () => {
      const requestBody = {
        content: 'Test monthly recurring post',
        social_account_ids: ['account-1'],
        recurring_pattern: {
          frequency: 'monthly',
          interval: 1,
          day_of_month: 15,
          time: '10:00',
        },
        start_date: '2024-01-15T10:00:00Z',
        end_date: '2024-06-15T10:00:00Z',
        time_zone: 'UTC',
      };

      // Mock successful responses
      mockSupabase.select.mockResolvedValueOnce({
        data: [{ id: 'account-1' }],
        error: null,
      });

      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'parent-post-id', content: requestBody.content },
        error: null,
      });

      mockSupabase.select.mockResolvedValueOnce({
        data: [
          { id: 'instance-1', scheduled_at: '2024-01-15T10:00:00Z' },
          { id: 'instance-2', scheduled_at: '2024-02-15T10:00:00Z' },
          { id: 'instance-3', scheduled_at: '2024-03-15T10:00:00Z' },
          { id: 'instance-4', scheduled_at: '2024-04-15T10:00:00Z' },
          { id: 'instance-5', scheduled_at: '2024-05-15T10:00:00Z' },
          { id: 'instance-6', scheduled_at: '2024-06-15T10:00:00Z' },
        ],
        error: null,
      });

      mockSupabase.insert.mockResolvedValue({ error: null });

      const { POST } = await import('@/app/api/posts/recurring/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.instancesCreated).toBe(6);
    });

    it('should reject invalid input data', async () => {
      const invalidRequestBody = {
        content: '', // Empty content
        social_account_ids: [], // No social accounts
        recurring_pattern: {
          frequency: 'invalid', // Invalid frequency
          interval: 0, // Invalid interval
          time: '25:00', // Invalid time
        },
        start_date: 'invalid-date', // Invalid date
      };

      const { POST } = await import('@/app/api/posts/recurring/route');
      const req = mockRequest(invalidRequestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Invalid input');
      expect(responseData.details).toBeDefined();
    });

    it('should reject unauthorized requests', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: new Error('Unauthorized'),
      });

      const requestBody = {
        content: 'Test post',
        social_account_ids: ['account-1'],
        recurring_pattern: {
          frequency: 'daily',
          interval: 1,
          time: '09:00',
        },
        start_date: '2024-01-01T09:00:00Z',
      };

      const { POST } = await import('@/app/api/posts/recurring/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(401);
      expect(responseData.error).toBe('Unauthorized');
    });

    it('should reject invalid social account IDs', async () => {
      const requestBody = {
        content: 'Test post',
        social_account_ids: ['invalid-account-1', 'invalid-account-2'],
        recurring_pattern: {
          frequency: 'daily',
          interval: 1,
          time: '09:00',
        },
        start_date: '2024-01-01T09:00:00Z',
      };

      // Mock social accounts validation - return fewer accounts than requested
      mockSupabase.select.mockResolvedValueOnce({
        data: [{ id: 'invalid-account-1' }], // Only one account found
        error: null,
      });

      const { POST } = await import('@/app/api/posts/recurring/route');
      const req = mockRequest(requestBody);

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.error).toBe('Invalid social accounts');
    });
  });

  describe('GET /api/posts/recurring', () => {
    it('should fetch recurring posts with pagination', async () => {
      const mockRecurringPosts = [
        {
          id: 'post-1',
          content: 'Recurring post 1',
          is_recurring_parent: true,
          recurring_pattern: { frequency: 'daily', interval: 1 },
          post_social_accounts: [
            { social_account: { id: 'account-1', platform: 'TWITTER', account_name: 'test' } }
          ],
        },
        {
          id: 'post-2',
          content: 'Recurring post 2',
          is_recurring_parent: true,
          recurring_pattern: { frequency: 'weekly', interval: 1 },
          post_social_accounts: [
            { social_account: { id: 'account-2', platform: 'FACEBOOK', account_name: 'test' } }
          ],
        },
      ];

      // Mock recurring posts fetch
      mockSupabase.select.mockResolvedValueOnce({
        data: mockRecurringPosts,
        error: null,
      });

      // Mock instance counts
      mockSupabase.select.mockResolvedValue({
        count: 10,
        error: null,
      });

      // Mock total count
      mockSupabase.select.mockResolvedValueOnce({
        count: 2,
        error: null,
      });

      const { GET } = await import('@/app/api/posts/recurring/route');
      const req = {
        url: 'http://localhost:3000/api/posts/recurring?page=1&limit=10',
      };

      const response = await GET(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.posts).toHaveLength(2);
      expect(responseData.pagination).toBeDefined();
      expect(responseData.pagination.page).toBe(1);
      expect(responseData.pagination.limit).toBe(10);
    });
  });
});
