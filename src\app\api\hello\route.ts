import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Hello from eWasl Enhanced Media Processing Pipeline!',
    status: 'success',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    deployment: 'DigitalOcean App Platform'
  });
}

export async function POST() {
  return NextResponse.json({
    message: 'POST request received successfully!',
    status: 'success',
    timestamp: new Date().toISOString()
  });
}
