'use client';

import React, { forwardRef, useState } from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'gradient';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  loading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  confirmAction?: boolean;
  confirmMessage?: string;
  onConfirm?: () => void;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'default',
    size = 'default',
    loading = false,
    loadingText,
    icon,
    iconPosition = 'left',
    fullWidth = false,
    confirmAction = false,
    confirmMessage = 'هل أنت متأكد؟',
    onConfirm,
    onClick,
    disabled,
    children,
    ...props
  }, ref) => {
    const [showConfirm, setShowConfirm] = useState(false);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (confirmAction && !showConfirm) {
        e.preventDefault();
        setShowConfirm(true);
        return;
      }

      if (confirmAction && showConfirm) {
        setShowConfirm(false);
        onConfirm?.();
      } else {
        onClick?.(e);
      }
    };

    const baseClasses = cn(
      'inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200',
      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
      'disabled:pointer-events-none disabled:opacity-50',
      'active:scale-95',
      fullWidth && 'w-full'
    );

    const variants = {
      default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg',
      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg',
      outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-sm',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'text-primary underline-offset-4 hover:underline',
      gradient: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl',
    };

    const sizes = {
      default: 'h-10 px-4 py-2',
      sm: 'h-9 rounded-md px-3',
      lg: 'h-11 rounded-md px-8',
      icon: 'h-10 w-10',
    };

    const isDisabled = disabled || loading;

    return (
      <button
        className={cn(
          baseClasses,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        disabled={isDisabled}
        onClick={handleClick}
        {...props}
      >
        {loading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        
        {!loading && icon && iconPosition === 'left' && (
          <span className="mr-2">{icon}</span>
        )}
        
        {loading ? (loadingText || 'جاري التحميل...') : (
          showConfirm ? confirmMessage : children
        )}
        
        {!loading && icon && iconPosition === 'right' && (
          <span className="ml-2">{icon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Specialized button components
export const LogoutButton = forwardRef<HTMLButtonElement, Omit<ButtonProps, 'confirmAction' | 'confirmMessage' | 'onConfirm'> & {
  onLogout?: () => void;
}>(({ onLogout, ...props }, ref) => {
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      // Call NextAuth signOut
      const { signOut } = await import('next-auth/react');
      await signOut({ callbackUrl: '/auth/signin' });
      onLogout?.();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <Button
      ref={ref}
      variant="ghost"
      confirmAction
      confirmMessage="تسجيل الخروج؟"
      onConfirm={handleLogout}
      loading={isLoggingOut}
      loadingText="جاري تسجيل الخروج..."
      className="text-red-600 hover:text-red-700 hover:bg-red-50"
      {...props}
    >
      تسجيل الخروج
    </Button>
  );
});

LogoutButton.displayName = 'LogoutButton';

export const ActionButton = forwardRef<HTMLButtonElement, ButtonProps & {
  action: 'create' | 'edit' | 'delete' | 'save' | 'cancel' | 'refresh' | 'connect';
}>(({ action, ...props }, ref) => {
  const actionConfig = {
    create: { variant: 'gradient' as const, loadingText: 'جاري الإنشاء...' },
    edit: { variant: 'outline' as const, loadingText: 'جاري التعديل...' },
    delete: { variant: 'destructive' as const, loadingText: 'جاري الحذف...', confirmAction: true, confirmMessage: 'هل تريد الحذف؟' },
    save: { variant: 'default' as const, loadingText: 'جاري الحفظ...' },
    cancel: { variant: 'ghost' as const },
    refresh: { variant: 'outline' as const, loadingText: 'جاري التحديث...' },
    connect: { variant: 'gradient' as const, loadingText: 'جاري الاتصال...' },
  };

  const config = actionConfig[action];

  return (
    <Button
      ref={ref}
      {...config}
      {...props}
    />
  );
});

ActionButton.displayName = 'ActionButton';

// Loading button with automatic state management
export const AsyncButton = forwardRef<HTMLButtonElement, ButtonProps & {
  asyncAction: () => Promise<void>;
  successMessage?: string;
  errorMessage?: string;
}>(({ asyncAction, successMessage, errorMessage, children, ...props }, ref) => {
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleClick = async () => {
    setIsLoading(true);
    setStatus('idle');
    
    try {
      await asyncAction();
      setStatus('success');
      
      // Reset status after 2 seconds
      setTimeout(() => setStatus('idle'), 2000);
    } catch (error) {
      setStatus('error');
      console.error('Async action error:', error);
      
      // Reset status after 3 seconds
      setTimeout(() => setStatus('idle'), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonContent = () => {
    if (status === 'success') return successMessage || 'تم بنجاح ✓';
    if (status === 'error') return errorMessage || 'حدث خطأ ✗';
    return children;
  };

  const getVariant = () => {
    if (status === 'success') return 'default';
    if (status === 'error') return 'destructive';
    return props.variant || 'default';
  };

  return (
    <Button
      ref={ref}
      {...props}
      variant={getVariant()}
      loading={isLoading}
      onClick={handleClick}
      disabled={isLoading || props.disabled}
    >
      {getButtonContent()}
    </Button>
  );
});

AsyncButton.displayName = 'AsyncButton';

export { Button };
export default Button;
