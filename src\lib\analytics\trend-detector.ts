import { createClient } from '@/lib/supabase/server';
import { SchedulerLogger } from '../scheduler/scheduler-logger';

export interface TrendData {
  hashtag: string;
  platform: string;
  usageCount: number;
  uniqueUsers: number;
  totalEngagement: number;
  trendScore: number;
  growthRate: number;
  peakUsageTime: Date | null;
  topCountries: string[];
  ageGroups: Record<string, number>;
  category: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  relatedHashtags: string[];
}

export interface TrendInsight {
  type: 'emerging' | 'declining' | 'stable' | 'viral';
  hashtag: string;
  platforms: string[];
  confidence: number;
  timeframe: string;
  description: string;
  recommendation: string;
  expectedDuration: string;
}

export interface ContentTrend {
  contentType: 'image' | 'video' | 'text' | 'carousel';
  platform: string;
  growthRate: number;
  avgEngagementRate: number;
  popularityScore: number;
  trend: 'rising' | 'falling' | 'stable';
  recommendation: string;
}

/**
 * Service for detecting and analyzing social media trends
 */
export class TrendDetector {
  private logger: SchedulerLogger;

  constructor() {
    this.logger = new SchedulerLogger('trend-detector');
  }

  /**
   * Detect trending hashtags across platforms
   */
  async detectHashtagTrends(
    platforms: string[] = [],
    timeframe: 'daily' | 'weekly' | 'monthly' = 'weekly'
  ): Promise<TrendData[]> {
    try {
      this.logger.info('Detecting hashtag trends', { platforms, timeframe });

      const supabase = createClient();

      // Calculate date range based on timeframe
      const endDate = new Date();
      const startDate = new Date();

      switch (timeframe) {
        case 'daily':
          startDate.setDate(endDate.getDate() - 7); // Last 7 days for daily trends
          break;
        case 'weekly':
          startDate.setDate(endDate.getDate() - 30); // Last 30 days for weekly trends
          break;
        case 'monthly':
          startDate.setDate(endDate.getDate() - 90); // Last 90 days for monthly trends
          break;
      }

      // Get hashtag analytics data
      let query = supabase
        .from('hashtag_analytics')
        .select('*')
        .gte('period_date', startDate.toISOString().split('T')[0])
        .lte('period_date', endDate.toISOString().split('T')[0])
        .eq('period_type', timeframe)
        .order('trend_score', { ascending: false });

      if (platforms.length > 0) {
        query = query.in('platform', platforms);
      }

      const { data: hashtagData, error } = await query.limit(100);

      if (error) {
        throw error;
      }

      if (!hashtagData || hashtagData.length === 0) {
        // Generate mock trending data if no real data exists
        return this.generateMockTrendData(platforms);
      }

      // Process and enrich hashtag data
      const trends: TrendData[] = [];

      for (const hashtag of hashtagData) {
        const trendData = await this.enrichHashtagData(hashtag);
        trends.push(trendData);
      }

      // Sort by trend score and return top trends
      const sortedTrends = trends
        .sort((a, b) => b.trendScore - a.trendScore)
        .slice(0, 50);

      this.logger.info('Hashtag trends detected successfully', {
        trendsCount: sortedTrends.length,
        timeframe,
      });

      return sortedTrends;

    } catch (error) {
      this.logger.error('Failed to detect hashtag trends', error);
      throw error;
    }
  }

  /**
   * Enrich hashtag data with additional insights
   */
  private async enrichHashtagData(hashtagAnalytics: any): Promise<TrendData> {
    // Calculate related hashtags (simplified)
    const relatedHashtags = await this.findRelatedHashtags(hashtagAnalytics.hashtag);

    // Determine category based on hashtag content
    const category = this.categorizeHashtag(hashtagAnalytics.hashtag);

    // Analyze sentiment (simplified)
    const sentiment = this.analyzeHashtagSentiment(hashtagAnalytics.hashtag);

    return {
      hashtag: hashtagAnalytics.hashtag,
      platform: hashtagAnalytics.platform,
      usageCount: hashtagAnalytics.usage_count || 0,
      uniqueUsers: hashtagAnalytics.unique_users || 0,
      totalEngagement: hashtagAnalytics.total_engagement || 0,
      trendScore: hashtagAnalytics.trend_score || 0,
      growthRate: hashtagAnalytics.growth_rate || 0,
      peakUsageTime: hashtagAnalytics.peak_usage_time ? new Date(hashtagAnalytics.peak_usage_time) : null,
      topCountries: hashtagAnalytics.top_countries || [],
      ageGroups: hashtagAnalytics.age_groups || {},
      category,
      sentiment,
      relatedHashtags,
    };
  }

  /**
   * Find related hashtags
   */
  private async findRelatedHashtags(hashtag: string): Promise<string[]> {
    try {
      const supabase = createClient();

      // This is a simplified implementation
      // In a real system, you would use more sophisticated algorithms
      const { data: relatedData } = await supabase
        .from('hashtag_analytics')
        .select('hashtag')
        .neq('hashtag', hashtag)
        .order('trend_score', { ascending: false })
        .limit(5);

      return relatedData?.map(item => item.hashtag) || [];

    } catch (error) {
      this.logger.warn('Failed to find related hashtags', error as Record<string, any>);
      return [];
    }
  }

  /**
   * Categorize hashtag based on content
   */
  private categorizeHashtag(hashtag: string): string {
    const categories = {
      technology: ['تقنية', 'تكنولوجيا', 'ذكي', 'tech', 'ai', 'digital', 'innovation'],
      business: ['عمل', 'شركة', 'تجارة', 'business', 'startup', 'entrepreneur'],
      lifestyle: ['حياة', 'يوم', 'صحة', 'lifestyle', 'health', 'fitness'],
      entertainment: ['ترفيه', 'فيلم', 'موسيقى', 'entertainment', 'movie', 'music'],
      sports: ['رياضة', 'كرة', 'لعب', 'sports', 'football', 'game'],
      food: ['طعام', 'أكل', 'طبخ', 'food', 'cooking', 'recipe'],
      travel: ['سفر', 'رحلة', 'سياحة', 'travel', 'trip', 'vacation'],
      fashion: ['موضة', 'أزياء', 'جمال', 'fashion', 'style', 'beauty'],
    };

    const hashtagLower = hashtag.toLowerCase();

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => hashtagLower.includes(keyword))) {
        return category;
      }
    }

    return 'general';
  }

  /**
   * Analyze hashtag sentiment
   */
  private analyzeHashtagSentiment(hashtag: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['رائع', 'جميل', 'ممتاز', 'نجح', 'فرح', 'great', 'amazing', 'success', 'happy'];
    const negativeWords = ['سيء', 'فشل', 'حزين', 'مشكلة', 'bad', 'fail', 'sad', 'problem'];

    const hashtagLower = hashtag.toLowerCase();

    if (positiveWords.some(word => hashtagLower.includes(word))) {
      return 'positive';
    }

    if (negativeWords.some(word => hashtagLower.includes(word))) {
      return 'negative';
    }

    return 'neutral';
  }

  /**
   * Generate trend insights based on hashtag data
   */
  async generateTrendInsights(trends: TrendData[]): Promise<TrendInsight[]> {
    const insights: TrendInsight[] = [];

    for (const trend of trends) {
      const insight = this.analyzeTrendPattern(trend);
      insights.push(insight);
    }

    return insights.slice(0, 10); // Return top 10 insights
  }

  /**
   * Analyze individual trend pattern
   */
  private analyzeTrendPattern(trend: TrendData): TrendInsight {
    let type: TrendInsight['type'] = 'stable';
    let confidence = 0;
    let description = '';
    let recommendation = '';
    let expectedDuration = '';

    // Determine trend type based on growth rate and trend score
    if (trend.growthRate > 50 && trend.trendScore > 80) {
      type = 'viral';
      confidence = 90;
      description = `الهاشتاغ ${trend.hashtag} ينتشر بسرعة كبيرة`;
      recommendation = 'استخدم هذا الهاشتاغ فوراً للاستفادة من الانتشار';
      expectedDuration = '1-3 أيام';
    } else if (trend.growthRate > 20 && trend.trendScore > 60) {
      type = 'emerging';
      confidence = 75;
      description = `الهاشتاغ ${trend.hashtag} في بداية الانتشار`;
      recommendation = 'فرصة جيدة للدخول مبكراً في هذا الترند';
      expectedDuration = '1-2 أسابيع';
    } else if (trend.growthRate < -20) {
      type = 'declining';
      confidence = 80;
      description = `الهاشتاغ ${trend.hashtag} يفقد شعبيته`;
      recommendation = 'تجنب استخدام هذا الهاشتاغ أو ابحث عن بدائل';
      expectedDuration = 'مستمر';
    } else {
      type = 'stable';
      confidence = 60;
      description = `الهاشتاغ ${trend.hashtag} مستقر في الاستخدام`;
      recommendation = 'يمكن استخدامه كجزء من استراتيجية طويلة المدى';
      expectedDuration = 'عدة أشهر';
    }

    return {
      type,
      hashtag: trend.hashtag,
      platforms: [trend.platform],
      confidence,
      timeframe: 'أسبوعي',
      description,
      recommendation,
      expectedDuration,
    };
  }

  /**
   * Detect content type trends
   */
  async detectContentTrends(
    userId: string,
    platforms: string[] = []
  ): Promise<ContentTrend[]> {
    try {
      this.logger.info('Detecting content trends', { userId, platforms });

      const supabase = createClient();

      // Get user's posts from the last 60 days
      const startDate = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000);

      let query = supabase
        .from('posts')
        .select(`
          id,
          content,
          media_url,
          platforms,
          published_at,
          post_analytics (
            platform,
            engagement_rate,
            likes_count,
            comments_count,
            shares_count,
            reach
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'PUBLISHED')
        .gte('published_at', startDate.toISOString());

      const { data: posts, error } = await query;

      if (error) {
        throw error;
      }

      if (!posts || posts.length === 0) {
        return [];
      }

      // Categorize posts by content type and platform
      const contentTypes = this.categorizePostsByType(posts);
      const trends: ContentTrend[] = [];

      for (const [contentType, typePosts] of Object.entries(contentTypes)) {
        const platformTrends = this.analyzeContentTypeByPlatform(contentType as any, typePosts as any[]);
        trends.push(...platformTrends);
      }

      this.logger.info('Content trends detected successfully', {
        trendsCount: trends.length,
      });

      return trends;

    } catch (error) {
      this.logger.error('Failed to detect content trends', error);
      throw error;
    }
  }

  /**
   * Categorize posts by content type
   */
  private categorizePostsByType(posts: any[]): Record<string, any[]> {
    const categories: Record<string, any[]> = {
      text: [],
      image: [],
      video: [],
      carousel: [],
    };

    posts.forEach(post => {
      let category = 'text';

      if (post.media_url) {
        if (this.isVideoUrl(post.media_url)) {
          category = 'video';
        } else if (Array.isArray(post.media_url)) {
          category = 'carousel';
        } else {
          category = 'image';
        }
      }

      categories[category].push(post);
    });

    return categories;
  }

  /**
   * Check if URL is a video
   */
  private isVideoUrl(url: string): boolean {
    if (typeof url !== 'string') return false;
    const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm'];
    return videoExtensions.some(ext => url.toLowerCase().includes(ext));
  }

  /**
   * Analyze content type performance by platform
   */
  private analyzeContentTypeByPlatform(contentType: string, posts: any[]): ContentTrend[] {
    const platformData: Record<string, any> = {};

    posts.forEach(post => {
      if (post.post_analytics && post.post_analytics.length > 0) {
        post.post_analytics.forEach((analytics: any) => {
          if (!platformData[analytics.platform]) {
            platformData[analytics.platform] = {
              posts: [],
              totalEngagement: 0,
              totalEngagementRate: 0,
            };
          }

          platformData[analytics.platform].posts.push(post);
          platformData[analytics.platform].totalEngagement +=
            (analytics.likes_count + analytics.comments_count + analytics.shares_count);
          platformData[analytics.platform].totalEngagementRate += analytics.engagement_rate || 0;
        });
      }
    });

    const trends: ContentTrend[] = [];

    Object.entries(platformData).forEach(([platform, data]) => {
      const avgEngagementRate = data.totalEngagementRate / data.posts.length;
      const growthRate = this.calculateGrowthRate(data.posts);
      const popularityScore = this.calculatePopularityScore(avgEngagementRate, data.posts.length);

      let trend: 'rising' | 'falling' | 'stable' = 'stable';
      let recommendation = '';

      if (growthRate > 20) {
        trend = 'rising';
        recommendation = `${contentType} يحقق نمواً ممتازاً على ${platform} - استمر في إنتاج هذا النوع`;
      } else if (growthRate < -20) {
        trend = 'falling';
        recommendation = `${contentType} يفقد فعاليته على ${platform} - جرب أنواع محتوى أخرى`;
      } else {
        recommendation = `${contentType} مستقر على ${platform} - حافظ على الجودة`;
      }

      trends.push({
        contentType: contentType as any,
        platform,
        growthRate,
        avgEngagementRate,
        popularityScore,
        trend,
        recommendation,
      });
    });

    return trends;
  }

  /**
   * Calculate growth rate for posts
   */
  private calculateGrowthRate(posts: any[]): number {
    if (posts.length < 2) return 0;

    // Sort posts by date
    const sortedPosts = posts.sort((a, b) =>
      new Date(a.published_at).getTime() - new Date(b.published_at).getTime()
    );

    const midPoint = Math.floor(sortedPosts.length / 2);
    const firstHalf = sortedPosts.slice(0, midPoint);
    const secondHalf = sortedPosts.slice(midPoint);

    const firstHalfAvg = this.calculateAverageEngagement(firstHalf);
    const secondHalfAvg = this.calculateAverageEngagement(secondHalf);

    if (firstHalfAvg === 0) return 0;

    return Math.round(((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100);
  }

  /**
   * Calculate average engagement for posts
   */
  private calculateAverageEngagement(posts: any[]): number {
    if (posts.length === 0) return 0;

    const totalEngagement = posts.reduce((sum, post) => {
      const postEngagement = post.post_analytics?.reduce((analytics_sum: number, analytics: any) =>
        analytics_sum + (analytics.likes_count + analytics.comments_count + analytics.shares_count), 0) || 0;
      return sum + postEngagement;
    }, 0);

    return totalEngagement / posts.length;
  }

  /**
   * Calculate popularity score
   */
  private calculatePopularityScore(avgEngagementRate: number, postCount: number): number {
    // Simple scoring algorithm
    const engagementScore = Math.min(avgEngagementRate * 20, 70); // Max 70 points
    const volumeScore = Math.min(postCount * 2, 30); // Max 30 points

    return Math.round(engagementScore + volumeScore);
  }

  /**
   * Generate mock trend data for testing
   */
  private generateMockTrendData(platforms: string[]): TrendData[] {
    const mockHashtags = [
      '#تقنية', '#ذكي_اصطناعي', '#ريادة_أعمال', '#تطوير', '#برمجة',
      '#تسويق_رقمي', '#محتوى', '#إبداع', '#نجاح', '#تحفيز'
    ];

    const mockPlatforms = platforms.length > 0 ? platforms : ['TWITTER', 'INSTAGRAM', 'LINKEDIN'];

    return mockHashtags.map((hashtag, index) => ({
      hashtag,
      platform: mockPlatforms[index % mockPlatforms.length],
      usageCount: Math.floor(Math.random() * 1000) + 100,
      uniqueUsers: Math.floor(Math.random() * 500) + 50,
      totalEngagement: Math.floor(Math.random() * 5000) + 500,
      trendScore: Math.floor(Math.random() * 100) + 1,
      growthRate: Math.floor(Math.random() * 200) - 50, // -50 to +150
      peakUsageTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      topCountries: ['السعودية', 'الإمارات', 'مصر'],
      ageGroups: {
        '18-24': 30,
        '25-34': 40,
        '35-44': 20,
        '45+': 10,
      },
      category: this.categorizeHashtag(hashtag),
      sentiment: ['positive', 'neutral', 'negative'][Math.floor(Math.random() * 3)] as any,
      relatedHashtags: mockHashtags.filter(h => h !== hashtag).slice(0, 3),
    }));
  }
}
