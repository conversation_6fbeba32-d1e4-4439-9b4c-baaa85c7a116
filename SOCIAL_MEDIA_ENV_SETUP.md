# 🔧 Social Media Integrations - Environment Variables Setup

## 📋 **REQUIRED ENVIRONMENT VARIABLES**

### **Core Application Settings**
```bash
# Application URLs
NEXT_PUBLIC_APP_URL=https://app.ewasl.com
NEXTAUTH_URL=https://app.ewasl.com
NEXTAUTH_SECRET=your-nextauth-secret-key

# Database
DATABASE_URL=your-supabase-database-url
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
```

### **🐦 Twitter/X API Configuration (OAuth 2.0)**
```bash
# Twitter OAuth 2.0 Credentials
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret

# Alternative X branding (same values)
X_CLIENT_ID=your-twitter-client-id
X_CLIENT_SECRET=your-twitter-client-secret
```

**Setup Instructions:**
1. Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
2. Create a new app or use existing one
3. Enable OAuth 2.0 in app settings
4. Add callback URL: `https://app.ewasl.com/api/social/callback/twitter`
5. Required scopes: `tweet.read tweet.write users.read media.upload offline.access`

### **📘 Facebook & Instagram Configuration**
```bash
# Facebook App Credentials (used for both Facebook and Instagram)
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
```

**Setup Instructions:**
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or use existing one
3. Add Facebook Login and Instagram Graph API products
4. Add callback URLs:
   - `https://app.ewasl.com/api/social/callback/facebook`
   - `https://app.ewasl.com/api/social/callback/instagram`
5. Required permissions:
   - Facebook: `pages_manage_posts`, `pages_read_engagement`, `business_management`
   - Instagram: `instagram_graph_user_profile`, `instagram_graph_user_media`, `instagram_content_publish`

### **💼 LinkedIn Configuration**
```bash
# LinkedIn OAuth Credentials
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
```

**Setup Instructions:**
1. Go to [LinkedIn Developers](https://www.linkedin.com/developers/)
2. Create a new app
3. Add callback URL: `https://app.ewasl.com/api/linkedin/callback`
4. Required scopes: `openid`, `profile`, `w_member_social`, `email`

### **🎵 TikTok Configuration (Optional)**
```bash
# TikTok API Credentials
TIKTOK_CLIENT_ID=your-tiktok-client-id
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret
```

### **👻 Snapchat Configuration (Optional)**
```bash
# Snapchat API Credentials
SNAPCHAT_CLIENT_ID=your-snapchat-client-id
SNAPCHAT_CLIENT_SECRET=your-snapchat-client-secret
```

## 🔄 **API VERSION SETTINGS**

### **Instagram Graph API**
- **Version**: v19.0 (latest)
- **Migration**: Migrated from deprecated Instagram Basic Display API
- **Endpoint**: `https://graph.facebook.com/v19.0/`

### **Twitter API**
- **Version**: v2 (latest)
- **Migration**: Migrated from OAuth 1.0a to OAuth 2.0 with PKCE
- **Endpoint**: `https://api.twitter.com/2/`

### **LinkedIn API**
- **Version**: v2 (current)
- **Endpoint**: `https://api.linkedin.com/v2/`

### **Facebook Graph API**
- **Version**: v19.0 (latest)
- **Endpoint**: `https://graph.facebook.com/v19.0/`

## 🚀 **DEVELOPMENT VS PRODUCTION**

### **Development Environment**
```bash
# Use localhost for development (required by Facebook)
NEXT_PUBLIC_APP_URL=http://127.0.0.1:3001
NEXTAUTH_URL=http://127.0.0.1:3001

# Development callback URLs
# Twitter: http://127.0.0.1:3001/api/social/callback/twitter
# Facebook: http://127.0.0.1:3001/api/social/callback/facebook
# Instagram: http://127.0.0.1:3001/api/social/callback/instagram
# LinkedIn: http://127.0.0.1:3001/api/linkedin/callback
```

### **Production Environment**
```bash
# Production URLs
NEXT_PUBLIC_APP_URL=https://app.ewasl.com
NEXTAUTH_URL=https://app.ewasl.com

# Production callback URLs
# Twitter: https://app.ewasl.com/api/social/callback/twitter
# Facebook: https://app.ewasl.com/api/social/callback/facebook
# Instagram: https://app.ewasl.com/api/social/callback/instagram
# LinkedIn: https://app.ewasl.com/api/linkedin/callback
```

## ✅ **VALIDATION CHECKLIST**

### **Twitter/X Setup**
- [ ] Twitter Developer account approved
- [ ] App created with OAuth 2.0 enabled
- [ ] Callback URL configured correctly
- [ ] Client ID and Secret added to environment
- [ ] Required scopes enabled: `tweet.read tweet.write users.read media.upload offline.access`

### **Facebook & Instagram Setup**
- [ ] Facebook Developer account created
- [ ] App created with Facebook Login product
- [ ] Instagram Graph API product added
- [ ] Business verification completed (for Instagram)
- [ ] Callback URLs configured for both platforms
- [ ] App ID and Secret added to environment
- [ ] Required permissions requested and approved

### **LinkedIn Setup**
- [ ] LinkedIn Developer account created
- [ ] App created and verified
- [ ] Callback URL configured
- [ ] Client ID and Secret added to environment
- [ ] Required scopes enabled

## 🔧 **CONFIGURATION VALIDATION**

### **Test OAuth Configurations**
```bash
# Test if all platforms are properly configured
curl -X GET "https://app.ewasl.com/api/social/config/validate"
```

### **Check Platform Status**
```bash
# Check which platforms are enabled
curl -X GET "https://app.ewasl.com/api/social/platforms/status"
```

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **Twitter OAuth 2.0 Issues**
- **Error**: "Invalid client_id"
  - **Solution**: Ensure TWITTER_CLIENT_ID is set correctly
- **Error**: "PKCE challenge failed"
  - **Solution**: Check that OAuth 2.0 is enabled in Twitter app settings

### **Instagram Graph API Issues**
- **Error**: "No Instagram business accounts found"
  - **Solution**: Connect Instagram business account to Facebook page
- **Error**: "Insufficient permissions"
  - **Solution**: Request Instagram Graph API permissions in Facebook app

### **Facebook App Review**
- **Required for production**: Submit app for review to access advanced permissions
- **Development**: Use test users and pages for development

### **LinkedIn Permissions**
- **Marketing Developer Platform**: Required for advanced features
- **Verification**: May require company verification for certain scopes

## 📚 **ADDITIONAL RESOURCES**

- [Twitter API v2 Documentation](https://developer.twitter.com/en/docs/twitter-api)
- [Instagram Graph API Documentation](https://developers.facebook.com/docs/instagram-api)
- [Facebook Graph API Documentation](https://developers.facebook.com/docs/graph-api)
- [LinkedIn API Documentation](https://docs.microsoft.com/en-us/linkedin/)

## 🔐 **SECURITY BEST PRACTICES**

1. **Never commit secrets to version control**
2. **Use different credentials for development and production**
3. **Regularly rotate API keys and secrets**
4. **Monitor API usage and rate limits**
5. **Implement proper error handling for expired tokens**
6. **Use HTTPS for all callback URLs**
7. **Validate all OAuth state parameters**

---

**✅ Once all environment variables are configured, restart your application and test the OAuth flows for each platform.**
