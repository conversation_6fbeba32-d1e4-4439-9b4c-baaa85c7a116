"use client";

import React from 'react';
import { PlusCircle, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface EnhancedPostsHeaderProps {
  language: 'ar' | 'en';
  searchQuery: string;
  onSearchChange: (query: string) => void;
  statusFilter: string;
  onStatusFilterChange: (status: string) => void;
  totalPosts: number;
}

const EnhancedPostsHeader = ({
  language,
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  totalPosts
}: EnhancedPostsHeaderProps) => {
  const text = {
    ar: {
      title: 'إدارة المنشورات',
      subtitle: 'إدارة وتنظيم منشوراتك على جميع منصات التواصل الاجتماعي',
      createPost: 'إنشاء منشور جديد',
      searchPlaceholder: 'البحث في المنشورات...',
      totalPosts: `إجمالي المنشورات: ${totalPosts}`,
      filters: {
        all: 'الكل',
        draft: 'مسودة',
        scheduled: 'مجدول',
        published: 'منشور',
        failed: 'فشل'
      }
    },
    en: {
      title: 'Posts Management',
      subtitle: 'Manage and organize your posts across all social media platforms',
      createPost: 'Create New Post',
      searchPlaceholder: 'Search posts...',
      totalPosts: `Total Posts: ${totalPosts}`,
      filters: {
        all: 'All',
        draft: 'Draft',
        scheduled: 'Scheduled',
        published: 'Published',
        failed: 'Failed'
      }
    }
  };

  const currentText = text[language];

  return (
    <div className="section-pro">
      {/* Welcome Section */}
      <Card className="mb-8 bg-gradient-to-r from-purple-50 to-indigo-50 border-0 shadow-lg card-pro animate-pro-fade-in">
        <CardContent className="p-8">
          <div className={cn(
            "flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6",
            language === 'ar' ? "lg:flex-row-reverse" : ""
          )}>
            <div className={cn(
              "flex items-center gap-4",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                <PlusCircle className="h-8 w-8 text-white" />
              </div>
              <div className={cn(
                language === 'ar' ? "text-right" : "text-left"
              )}>
                <h1 className="text-h1 bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
                  {currentText.title} 📝
                </h1>
                <p className="text-gray-600 text-body-lg mt-1">
                  {currentText.subtitle}
                </p>
              </div>
            </div>
            
            <Link href="/posts/new">
              <Button 
                size="lg" 
                className={cn(
                  "btn-pro-primary btn-pro-lg shadow-lg hover:shadow-xl transition-all duration-200",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}
              >
                <PlusCircle className={cn(
                  "h-5 w-5",
                  language === 'ar' ? "mr-2" : "ml-2"
                )} />
                {currentText.createPost}
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filter Section */}
      <Card className="card-pro card-pro-padding-md mb-6">
        <div className={cn(
          "flex flex-col md:flex-row gap-4 items-start md:items-center justify-between",
          language === 'ar' ? "md:flex-row-reverse" : ""
        )}>
          <div className={cn(
            "flex-1 max-w-md",
            language === 'ar' ? "md:order-2" : ""
          )}>
            <div className="relative">
              <Search className={cn(
                "absolute top-3 h-4 w-4 text-gray-400",
                language === 'ar' ? "right-3" : "left-3"
              )} />
              <Input
                placeholder={currentText.searchPlaceholder}
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className={cn(
                  "input-pro",
                  language === 'ar' ? "pr-10 text-right" : "pl-10 text-left"
                )}
              />
            </div>
          </div>
          
          <div className={cn(
            "flex items-center gap-4",
            language === 'ar' ? "flex-row-reverse md:order-1" : ""
          )}>
            <div className={cn(
              "flex items-center gap-2",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={statusFilter}
                onChange={(e) => onStatusFilterChange(e.target.value)}
                className={cn(
                  "input-pro w-auto min-w-[120px]",
                  language === 'ar' ? "text-right" : "text-left"
                )}
              >
                <option value="ALL">{currentText.filters.all}</option>
                <option value="DRAFT">{currentText.filters.draft}</option>
                <option value="SCHEDULED">{currentText.filters.scheduled}</option>
                <option value="PUBLISHED">{currentText.filters.published}</option>
                <option value="FAILED">{currentText.filters.failed}</option>
              </select>
            </div>
            
            <div className={cn(
              "text-body-sm text-gray-600 font-medium",
              language === 'ar' ? "text-right" : "text-left"
            )}>
              {currentText.totalPosts}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EnhancedPostsHeader;
