import { NextRequest, NextResponse } from 'next/server';
import { create<PERSON>piH<PERSON><PERSON>, createSuccessResponse, createErrorResponse } from '@/lib/api-middleware';
import { z } from 'zod';
import type { AuthenticatedRequest } from '@/lib/api-middleware';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

const errorReportSchema = z.object({
  message: z.string().min(1, 'Error message is required'),
  stack: z.string().optional(),
  componentStack: z.string().optional(),
  errorId: z.string().optional(),
  timestamp: z.string().datetime(),
  userAgent: z.string().optional(),
  url: z.string().url().optional(),
  userId: z.string().optional(),
  additionalInfo: z.record(z.any()).optional(),
});

async function errorReportHandler(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const { prisma } = await import('@/lib/db');
    
    // Get validated data from middleware
    const errorData = request.validatedData!.body;
    
    // Get client IP and user agent
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Create error log entry
    const errorLog = await prisma.errorLog.create({
      data: {
        message: errorData.message,
        stack: errorData.stack,
        componentStack: errorData.componentStack,
        errorId: errorData.errorId || `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        url: errorData.url,
        userAgent: errorData.userAgent || userAgent,
        clientIp,
        userId: request.user?.id || errorData.userId,
        additionalInfo: errorData.additionalInfo || {},
        severity: determineSeverity(errorData.message, errorData.stack),
        environment: process.env.NODE_ENV || 'unknown',
        timestamp: new Date(errorData.timestamp),
      },
    });

    // Send to external error tracking service if configured
    if (process.env.SENTRY_DSN || process.env.ERROR_TRACKING_WEBHOOK) {
      await sendToExternalService(errorData, {
        clientIp,
        userAgent,
        userId: request.user?.id,
      });
    }

    // Send alert for critical errors
    if (errorLog.severity === 'CRITICAL') {
      await sendCriticalErrorAlert(errorLog);
    }

    return createSuccessResponse({
      message: 'Error report received',
      errorId: errorLog.errorId,
    });

  } catch (error) {
    console.error('Failed to log error report:', error);
    
    // Fallback logging to console/file
    console.error('Original error report:', request.validatedData?.body);
    
    return createErrorResponse('Failed to process error report', 500);
  }
}

function determineSeverity(message: string, stack?: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const criticalKeywords = [
    'ChunkLoadError',
    'SecurityError',
    'Database connection',
    'Authentication failed',
    'Payment processing',
  ];
  
  const highKeywords = [
    'TypeError',
    'ReferenceError',
    'Network Error',
    'API Error',
  ];
  
  const mediumKeywords = [
    'Warning',
    'Validation',
    'Form error',
  ];

  const errorText = `${message} ${stack || ''}`.toLowerCase();
  
  if (criticalKeywords.some(keyword => errorText.includes(keyword.toLowerCase()))) {
    return 'CRITICAL';
  }
  
  if (highKeywords.some(keyword => errorText.includes(keyword.toLowerCase()))) {
    return 'HIGH';
  }
  
  if (mediumKeywords.some(keyword => errorText.includes(keyword.toLowerCase()))) {
    return 'MEDIUM';
  }
  
  return 'LOW';
}

async function sendToExternalService(
  errorData: any, 
  context: { clientIp: string; userAgent: string; userId?: string }
) {
  try {
    // Example: Send to Sentry
    if (process.env.SENTRY_DSN) {
      // Sentry integration would go here
      console.log('Would send to Sentry:', errorData);
    }
    
    // Example: Send to webhook
    if (process.env.ERROR_TRACKING_WEBHOOK) {
      await fetch(process.env.ERROR_TRACKING_WEBHOOK, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ERROR_TRACKING_TOKEN}`,
        },
        body: JSON.stringify({
          ...errorData,
          context,
          service: 'ewasl-app',
          environment: process.env.NODE_ENV,
        }),
      });
    }
  } catch (error) {
    console.error('Failed to send error to external service:', error);
  }
}

async function sendCriticalErrorAlert(errorLog: any) {
  try {
    // Send email alert to administrators
    if (process.env.SENDGRID_API_KEY && process.env.ADMIN_EMAIL) {
      const emailData = {
        to: process.env.ADMIN_EMAIL,
        subject: `🚨 Critical Error in eWasl App - ${errorLog.errorId}`,
        html: `
          <h2>Critical Error Alert</h2>
          <p><strong>Error ID:</strong> ${errorLog.errorId}</p>
          <p><strong>Message:</strong> ${errorLog.message}</p>
          <p><strong>URL:</strong> ${errorLog.url}</p>
          <p><strong>User ID:</strong> ${errorLog.userId || 'Anonymous'}</p>
          <p><strong>Timestamp:</strong> ${errorLog.timestamp}</p>
          <p><strong>Environment:</strong> ${errorLog.environment}</p>
          
          <h3>Stack Trace:</h3>
          <pre>${errorLog.stack}</pre>
          
          <h3>Component Stack:</h3>
          <pre>${errorLog.componentStack}</pre>
        `,
      };
      
      // Send email (implementation depends on email service)
      console.log('Would send critical error email:', emailData);
    }
    
    // Send to Slack/Discord webhook if configured
    if (process.env.SLACK_ERROR_WEBHOOK) {
      await fetch(process.env.SLACK_ERROR_WEBHOOK, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: `🚨 Critical Error in eWasl App`,
          attachments: [
            {
              color: 'danger',
              fields: [
                {
                  title: 'Error ID',
                  value: errorLog.errorId,
                  short: true,
                },
                {
                  title: 'Message',
                  value: errorLog.message,
                  short: false,
                },
                {
                  title: 'URL',
                  value: errorLog.url,
                  short: true,
                },
                {
                  title: 'Environment',
                  value: errorLog.environment,
                  short: true,
                },
              ],
            },
          ],
        }),
      });
    }
  } catch (error) {
    console.error('Failed to send critical error alert:', error);
  }
}

// Export the API handler with middleware
export const POST = createApiHandler(
  {
    rateLimit: 'general',
    validation: {
      body: errorReportSchema,
    },
  },
  errorReportHandler
);
