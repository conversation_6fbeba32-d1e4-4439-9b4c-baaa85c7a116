/**
 * Enhanced Social Media Testing API
 * Comprehensive testing suite for all integrations
 */

import { NextRequest, NextResponse } from 'next/server';
import { integrationManager } from '@/lib/social/postiz-integration/integration-manager';
import { supabaseServiceRole } from '@/lib/supabase/service-role';

export async function POST(request: NextRequest) {
  try {
    const { testType, platform, integrationId, content } = await request.json();

    const supabase = supabaseServiceRole;
    const testResults: any = {
      timestamp: new Date().toISOString(),
      testType,
      platform,
      integrationId,
      results: {},
    };

    switch (testType) {
      case 'connection':
        testResults.results = await testConnection(integrationId);
        break;
        
      case 'auth_flow':
        testResults.results = await testAuthFlow(platform);
        break;
        
      case 'posting':
        testResults.results = await testPosting(integrationId, content);
        break;
        
      case 'comprehensive':
        testResults.results = await runComprehensiveTest(platform, integrationId, content);
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid test type' },
          { status: 400 }
        );
    }

    // Save test results
    await supabase
      .from('test_results')
      .insert({
        test_type: testType,
        platform,
        integration_id: integrationId,
        results: testResults.results,
        success: testResults.results.overall?.success || false,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      testResults,
    });
  } catch (error) {
    console.error('[Enhanced Test API] Error:', error);
    return NextResponse.json(
      { 
        error: 'Test execution failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function testConnection(integrationId: string) {
  const startTime = Date.now();
  
  try {
    const result = await integrationManager.testConnection(integrationId);
    
    return {
      success: result.success,
      duration: Date.now() - startTime,
      accountInfo: result.accountInfo,
      error: result.error,
      details: {
        message: result.success ? 'Connection test passed' : 'Connection test failed',
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      duration: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: {
        message: 'Connection test threw an exception',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

async function testAuthFlow(platform: string) {
  const startTime = Date.now();
  
  try {
    const authUrl = await integrationManager.generateAuthUrl(platform);
    
    return {
      success: !!authUrl,
      duration: Date.now() - startTime,
      authUrl: authUrl?.url,
      details: {
        message: authUrl ? 'Auth URL generated successfully' : 'Failed to generate auth URL',
        hasCodeVerifier: !!authUrl?.codeVerifier,
        hasState: !!authUrl?.state,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      duration: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: {
        message: 'Auth flow test threw an exception',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

async function testPosting(integrationId: string, content: string = 'Test post from eWasl Enhanced Integration 🚀') {
  const startTime = Date.now();
  
  try {
    const postDetails = [{
      id: `test_${Date.now()}`,
      message: content,
      settings: {},
    }];

    const results = await integrationManager.post(integrationId, postDetails);
    
    return {
      success: results.length > 0 && results[0].status === 'posted',
      duration: Date.now() - startTime,
      results,
      details: {
        message: results.length > 0 && results[0].status === 'posted' 
          ? 'Post published successfully' 
          : 'Post failed to publish',
        postCount: results.length,
        successCount: results.filter(r => r.status === 'posted').length,
        failedCount: results.filter(r => r.status === 'failed').length,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      duration: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: {
        message: 'Posting test threw an exception',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

async function runComprehensiveTest(platform: string, integrationId?: string, content?: string) {
  const startTime = Date.now();
  const tests: any = {};

  // Test 1: Provider availability
  tests.providerAvailability = {
    success: !!integrationManager.getProvider(platform),
    message: integrationManager.getProvider(platform) 
      ? 'Provider is available' 
      : 'Provider not found',
  };

  // Test 2: Auth flow (if no integration provided)
  if (!integrationId) {
    tests.authFlow = await testAuthFlow(platform);
  }

  // Test 3: Connection test (if integration provided)
  if (integrationId) {
    tests.connection = await testConnection(integrationId);
  }

  // Test 4: Posting test (if integration and content provided)
  if (integrationId && content) {
    tests.posting = await testPosting(integrationId, content);
  }

  // Test 5: Provider configuration
  const provider = integrationManager.getProvider(platform);
  if (provider) {
    tests.providerConfig = {
      success: true,
      details: {
        identifier: provider.identifier,
        name: provider.name,
        scopes: provider.scopes,
        isBetweenSteps: provider.isBetweenSteps,
        oneTimeToken: provider.oneTimeToken,
        refreshWait: provider.refreshWait,
      },
    };
  }

  // Overall success calculation
  const testKeys = Object.keys(tests);
  const successfulTests = testKeys.filter(key => tests[key].success).length;
  const overallSuccess = successfulTests === testKeys.length;

  return {
    overall: {
      success: overallSuccess,
      duration: Date.now() - startTime,
      testsRun: testKeys.length,
      testsSuccessful: successfulTests,
      testsFailed: testKeys.length - successfulTests,
      timestamp: new Date().toISOString(),
    },
    tests,
    summary: {
      platform,
      integrationId,
      contentProvided: !!content,
      recommendations: generateRecommendations(tests),
    },
  };
}

function generateRecommendations(tests: any): string[] {
  const recommendations: string[] = [];

  if (!tests.providerAvailability?.success) {
    recommendations.push('Provider needs to be implemented or registered');
  }

  if (tests.authFlow && !tests.authFlow.success) {
    recommendations.push('Check OAuth configuration and environment variables');
  }

  if (tests.connection && !tests.connection.success) {
    recommendations.push('Verify access token validity and refresh if needed');
  }

  if (tests.posting && !tests.posting.success) {
    recommendations.push('Check posting permissions and API rate limits');
  }

  if (recommendations.length === 0) {
    recommendations.push('All tests passed! Integration is working correctly.');
  }

  return recommendations;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');

    // Get available providers
    const providers = integrationManager.getAllProviders().map(p => ({
      identifier: p.identifier,
      name: p.name,
      scopes: p.scopes,
      isBetweenSteps: p.isBetweenSteps,
    }));

    // Get recent test results
    const supabase = supabaseServiceRole;
    const { data: recentTests } = await supabase
      .from('test_results')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    return NextResponse.json({
      success: true,
      providers,
      recentTests: recentTests || [],
      availableTestTypes: [
        'connection',
        'auth_flow', 
        'posting',
        'comprehensive'
      ],
    });
  } catch (error) {
    console.error('[Enhanced Test API] GET Error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get test information',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
