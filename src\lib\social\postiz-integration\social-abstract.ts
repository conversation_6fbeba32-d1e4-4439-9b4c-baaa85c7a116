/**
 * Social Abstract Base Class
 * Adapted from Postiz for eWasl integration
 */

import { supabaseServiceRole } from '@/lib/supabase/service-role';

export abstract class SocialAbstract {
  protected supabase = supabaseServiceRole;

  protected async fetch(url: string, options: RequestInit = {}, context?: string) {
    try {
      console.log(`[${this.constructor.name}] Fetching: ${url}`, { context });
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'User-Agent': 'eWasl/1.0 (+https://app.ewasl.com)',
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[${this.constructor.name}] HTTP Error:`, {
          status: response.status,
          statusText: response.statusText,
          url,
          context,
          error: errorText,
        });
        
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      return response;
    } catch (error) {
      console.error(`[${this.constructor.name}] Fetch Error:`, {
        url,
        context,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  protected checkScopes(requiredScopes: string[], grantedScopes: string[] | string) {
    const granted = Array.isArray(grantedScopes) 
      ? grantedScopes 
      : grantedScopes.split(' ');

    const missing = requiredScopes.filter(scope => !granted.includes(scope));
    
    if (missing.length > 0) {
      console.warn(`[${this.constructor.name}] Missing scopes:`, missing);
      throw new Error(`Missing required scopes: ${missing.join(', ')}`);
    }
  }

  protected makeId(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }

  protected async logActivity(
    userId: string,
    platform: string,
    action: string,
    details: Record<string, any>
  ) {
    try {
      await this.supabase
        .from('activity_logs')
        .insert({
          user_id: userId,
          platform,
          action,
          details,
          created_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error(`[${this.constructor.name}] Failed to log activity:`, error);
    }
  }

  protected async updateTokens(
    integrationId: string,
    accessToken: string,
    refreshToken?: string,
    expiresIn?: number
  ) {
    try {
      const updateData: any = {
        access_token: accessToken,
        updated_at: new Date().toISOString(),
      };

      if (refreshToken) {
        updateData.refresh_token = refreshToken;
      }

      if (expiresIn) {
        updateData.expires_at = new Date(Date.now() + expiresIn * 1000).toISOString();
      }

      const { error } = await this.supabase
        .from('social_accounts')
        .update(updateData)
        .eq('id', integrationId);

      if (error) {
        throw error;
      }

      console.log(`[${this.constructor.name}] Tokens updated for integration:`, integrationId);
    } catch (error) {
      console.error(`[${this.constructor.name}] Failed to update tokens:`, error);
      throw error;
    }
  }

  protected async getIntegration(integrationId: string) {
    try {
      const { data, error } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('id', integrationId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error(`[${this.constructor.name}] Failed to get integration:`, error);
      throw error;
    }
  }

  protected async saveIntegration(integrationData: {
    user_id: string;
    platform: string;
    account_id: string;
    account_name: string;
    account_username: string;
    account_picture?: string;
    access_token: string;
    refresh_token?: string;
    expires_at?: Date;
    account_type?: string;
    business_account_id?: string;
    permissions?: string[];
  }) {
    try {
      const { data, error } = await this.supabase
        .from('social_accounts')
        .upsert({
          ...integrationData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true,
        }, {
          onConflict: 'user_id,platform,account_id'
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      console.log(`[${this.constructor.name}] Integration saved:`, data.id);
      return data;
    } catch (error) {
      console.error(`[${this.constructor.name}] Failed to save integration:`, error);
      throw error;
    }
  }

  protected async handleRateLimit(error: any, retryCount = 0, maxRetries = 3): Promise<boolean> {
    if (retryCount >= maxRetries) {
      return false;
    }

    // Check if it's a rate limit error
    if (error.status === 429 || error.message?.includes('rate limit')) {
      const retryAfter = error.headers?.get('retry-after') || Math.pow(2, retryCount);
      const delay = parseInt(retryAfter) * 1000;
      
      console.log(`[${this.constructor.name}] Rate limited, retrying in ${delay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return true;
    }

    return false;
  }

  protected fixText(text: string): string {
    // Basic text sanitization - can be overridden by specific providers
    return text
      .replace(/\\/g, '\\\\')
      .replace(/"/g, '\\"')
      .trim();
  }
}
