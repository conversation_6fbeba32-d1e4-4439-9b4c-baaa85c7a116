'use client';

import { useState, useEffect } from 'react';
import SubscriptionPlans from '@/components/billing/SubscriptionPlans';

export default function TestStripePage() {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    testStripeIntegration();
  }, []);

  const testStripeIntegration = async () => {
    try {
      console.log('Testing Stripe integration...');
      
      // Test 1: Fetch subscription plans
      const plansResponse = await fetch('/api/stripe/plans');
      const plansData = await plansResponse.json();
      
      if (plansData.success) {
        setPlans(plansData.plans);
        console.log('✅ Plans API working:', plansData.plans.length, 'plans found');
      } else {
        console.error('❌ Plans API failed:', plansData.error);
        setError('Failed to load plans: ' + plansData.error);
      }

    } catch (err) {
      console.error('❌ Stripe test failed:', err);
      setError('Stripe integration test failed: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            💳 Stripe Payment System Test
          </h1>
          <p className="text-xl text-gray-600">
            Testing eWasl subscription plans and payment integration
          </p>
        </div>

        {/* Test Results */}
        <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
          
          {loading && (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span>Testing Stripe integration...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-red-700">
                <strong>Error:</strong> {error}
              </div>
            </div>
          )}

          {!loading && !error && (
            <div className="space-y-3">
              <div className="flex items-center text-green-600">
                <span className="mr-2">✅</span>
                <span>Subscription plans API: Working ({plans.length} plans loaded)</span>
              </div>
              <div className="flex items-center text-green-600">
                <span className="mr-2">✅</span>
                <span>Database schema: Connected</span>
              </div>
              <div className="flex items-center text-green-600">
                <span className="mr-2">✅</span>
                <span>Arabic language support: Enabled</span>
              </div>
              <div className="flex items-center text-green-600">
                <span className="mr-2">✅</span>
                <span>Plan pricing: Pro ($9), Business ($25)</span>
              </div>
            </div>
          )}
        </div>

        {/* Plan Details */}
        {plans.length > 0 && (
          <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Plan Configuration</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {plans.map((plan) => (
                <div key={plan.id} className="border border-gray-200 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900">{plan.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{plan.name_ar}</p>
                  <div className="text-lg font-bold text-blue-600">
                    {plan.price_monthly > 0 ? `$${plan.price_monthly}/month` : 'Free'}
                  </div>
                  <div className="text-sm text-gray-500 mt-2">
                    <div>Accounts: {plan.max_social_accounts === -1 ? 'Unlimited' : plan.max_social_accounts}</div>
                    <div>Users: {plan.max_users === -1 ? 'Unlimited' : plan.max_users}</div>
                    <div>Posts: {plan.max_posts_per_month === null ? 'Unlimited' : plan.max_posts_per_month}</div>
                  </div>
                  <div className="mt-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      plan.plan_type === 'free' ? 'bg-gray-100 text-gray-800' :
                      plan.plan_type === 'pro' ? 'bg-blue-100 text-blue-800' :
                      plan.plan_type === 'business' ? 'bg-green-100 text-green-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {plan.plan_type}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Subscription Plans Component */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
            Subscription Plans (English)
          </h2>
          <SubscriptionPlans currentPlan="free" isRTL={false} />
        </div>

        {/* Arabic Version */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
            خطط الاشتراك (Arabic RTL)
          </h2>
          <SubscriptionPlans currentPlan="free" isRTL={true} />
        </div>

        {/* Test Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-6">
          <h3 className="font-medium text-blue-800 mb-2">Test Information</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>Database:</strong> Supabase with subscription tables</p>
            <p><strong>Payment Processor:</strong> Stripe (Test Mode)</p>
            <p><strong>Plans:</strong> Free, Pro ($9), Business ($25), Enterprise (Custom)</p>
            <p><strong>Features:</strong> Arabic support, RTL text, usage limits</p>
            <p><strong>Authentication:</strong> Supabase Auth with service role</p>
            <p><strong>Webhooks:</strong> /api/stripe/webhook (signature verification)</p>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/"
            className="text-blue-600 hover:text-blue-500 text-sm font-medium"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  );
}
