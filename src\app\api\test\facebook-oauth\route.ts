import { NextRequest, NextResponse } from 'next/server';
import { createOAuthManager } from '@/lib/auth/oauth-manager';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * Test Facebook OAuth initiation
 * GET /api/test/facebook-oauth - Initiate Facebook OAuth flow
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook OAuth initiation...');

    const results = {
      timestamp: new Date().toISOString(),
      oauthTest: {} as any
    };

    // Demo User ID for testing
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';

    // Test OAuth manager
    try {
      console.log('Creating OAuth manager...');
      const oauthManager = createOAuthManager();
      
      console.log('Initiating Facebook OAuth...');
      const oauthResult = await oauthManager.initiateOAuth(demoUserId, 'FACEBOOK');
      
      results.oauthTest = {
        success: true,
        authUrl: oauthResult.authUrl,
        state: oauthResult.state,
        message: 'Facebook OAuth URL generated successfully'
      };

      console.log('✅ Facebook OAuth URL generated successfully');

      // Return redirect response to Facebook OAuth
      return NextResponse.redirect(oauthResult.authUrl);

    } catch (oauthError: any) {
      results.oauthTest = {
        success: false,
        error: oauthError.message,
        message: 'Facebook OAuth initiation failed'
      };

      console.error('❌ Facebook OAuth initiation failed:', oauthError);

      return NextResponse.json({
        success: false,
        message: '❌ Facebook OAuth initiation failed',
        results,
        error: oauthError.message
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('❌ Facebook OAuth test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'Facebook OAuth test failed'
    }, { status: 500 });
  }
}

/**
 * Test Facebook OAuth configuration
 * POST /api/test/facebook-oauth - Test OAuth configuration without redirect
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook OAuth configuration...');

    const results = {
      timestamp: new Date().toISOString(),
      configuration: {} as any,
      oauthTest: {} as any
    };

    // Test configuration
    try {
      console.log('Testing Facebook configuration...');
      
      const hasAppId = !!(process.env.FACEBOOK_APP_ID);
      const hasAppSecret = !!(process.env.FACEBOOK_APP_SECRET);
      
      results.configuration = {
        hasAppId,
        hasAppSecret,
        appId: process.env.FACEBOOK_APP_ID || 'missing',
        appSecretLength: process.env.FACEBOOK_APP_SECRET?.length || 0,
        configured: hasAppId && hasAppSecret
      };

      console.log('Facebook configuration:', results.configuration);

    } catch (configError: any) {
      results.configuration = {
        success: false,
        error: configError.message
      };
    }

    // Test OAuth URL generation
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';

    try {
      console.log('Testing OAuth URL generation...');
      const oauthManager = createOAuthManager();
      
      const oauthResult = await oauthManager.initiateOAuth(demoUserId, 'FACEBOOK');
      
      results.oauthTest = {
        success: true,
        authUrl: oauthResult.authUrl,
        state: oauthResult.state,
        message: 'Facebook OAuth URL generated successfully',
        urlValid: oauthResult.authUrl.includes('facebook.com') && oauthResult.authUrl.includes('oauth')
      };

      console.log('✅ Facebook OAuth URL generated successfully');

    } catch (oauthError: any) {
      results.oauthTest = {
        success: false,
        error: oauthError.message,
        message: 'Facebook OAuth URL generation failed'
      };

      console.error('❌ Facebook OAuth URL generation failed:', oauthError);
    }

    const allTestsPassed = results.configuration.configured && results.oauthTest.success;

    return NextResponse.json({
      success: allTestsPassed,
      message: allTestsPassed ? 
        '✅ Facebook OAuth configuration and URL generation working!' :
        '❌ Facebook OAuth configuration or URL generation failed',
      results,
      nextSteps: allTestsPassed ? {
        step1: 'Navigate to the generated authUrl to test OAuth flow',
        step2: 'Complete Facebook authentication',
        step3: 'Verify account appears in social accounts list',
        authUrl: results.oauthTest.authUrl
      } : {
        step1: 'Fix Facebook configuration issues',
        step2: 'Ensure FACEBOOK_APP_ID and FACEBOOK_APP_SECRET are set',
        step3: 'Retry OAuth URL generation'
      }
    });

  } catch (error: any) {
    console.error('❌ Facebook OAuth configuration test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'Facebook OAuth configuration test failed'
    }, { status: 500 });
  }
}
