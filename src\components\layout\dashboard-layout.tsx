"use client";

import { ReactNode, useState } from "react";
import { Sidebar } from "./sidebar";
import { Head<PERSON> } from "./header";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

interface DashboardLayoutProps {
  children: ReactNode;
  title?: string;
}

export function DashboardLayout({ children, title }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30" dir="rtl">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 right-0 z-50 w-72 transform bg-white/95 backdrop-blur-xl border-l border-gray-200/50 shadow-2xl transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 md:z-auto md:shadow-none",
        sidebarOpen ? "translate-x-0" : "translate-x-full"
      )}>
        <div className="flex h-full flex-col">
          {/* Mobile close button */}
          <div className="flex items-center justify-between p-4 md:hidden">
            <h2 className="text-lg font-semibold text-gray-900">القائمة</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <Sidebar />
        </div>
      </div>

      {/* Main content */}
      <div className="md:mr-72">
        <Header
          title={title}
          onMenuClick={() => setSidebarOpen(true)}
          showMenuButton={true}
        />
        <main className="min-h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-50/30 via-white/50 to-blue-50/20">
          <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
