"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle, ExternalLink, RefreshCw } from "lucide-react";

interface TestResult {
  id: string;
  platform: string;
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
  timestamp: string;
}

interface PlatformStatus {
  platform: string;
  configured: boolean;
  oauthReady: boolean;
  publishingReady: boolean;
  issues: string[];
}

export default function TestSocialMediaIntegrationPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [platformStatuses, setPlatformStatuses] = useState<PlatformStatus[]>([]);

  const addResult = (platform: string, test: string, status: 'success' | 'error' | 'warning', message: string, details?: any) => {
    const result: TestResult = {
      id: Date.now().toString(),
      platform,
      test,
      status,
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev]);
    
    if (status === 'success') {
      toast.success(`✅ ${platform}: ${message}`);
    } else if (status === 'error') {
      toast.error(`❌ ${platform}: ${message}`);
    } else {
      toast.warning(`⚠️ ${platform}: ${message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setPlatformStatuses([]);
    toast.info("تم مسح نتائج الاختبار");
  };

  const testOAuthConfiguration = async (platform: string) => {
    try {
      const response = await fetch(`/api/social/oauth-status?platform=${platform}&action=validate`);
      const data = await response.json();
      
      if (data.valid) {
        addResult(platform, 'OAuth Config', 'success', 'OAuth configuration is valid', data);
      } else {
        addResult(platform, 'OAuth Config', 'error', `Configuration errors: ${data.errors.join(', ')}`, data);
      }
      
      return data.valid;
    } catch (error: any) {
      addResult(platform, 'OAuth Config', 'error', error.message);
      return false;
    }
  };

  const testOAuthFlow = async (platform: string) => {
    try {
      const response = await fetch('/api/social/connect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ platform: platform.toUpperCase() })
      });
      
      const data = await response.json();
      
      if (data.authUrl) {
        addResult(platform, 'OAuth Flow', 'success', 'OAuth URL generated successfully', { authUrl: data.authUrl });
        return true;
      } else {
        addResult(platform, 'OAuth Flow', 'error', data.error || 'Failed to generate OAuth URL', data);
        return false;
      }
    } catch (error: any) {
      addResult(platform, 'OAuth Flow', 'error', error.message);
      return false;
    }
  };

  const testPublishingCapability = async (platform: string) => {
    try {
      // Test if publishing endpoints are available
      const response = await fetch('/api/posts/publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Test post from eWasl social media integration test',
          platforms: [platform.toUpperCase()],
          dryRun: true // Don't actually publish
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        addResult(platform, 'Publishing', 'success', 'Publishing capability available', data);
        return true;
      } else {
        addResult(platform, 'Publishing', 'warning', data.error || 'Publishing not ready', data);
        return false;
      }
    } catch (error: any) {
      addResult(platform, 'Publishing', 'error', error.message);
      return false;
    }
  };

  const testPlatform = async (platform: string) => {
    addResult(platform, 'Platform Test', 'warning', `Starting comprehensive test for ${platform}...`);
    
    const configValid = await testOAuthConfiguration(platform);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const oauthReady = await testOAuthFlow(platform);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const publishingReady = await testPublishingCapability(platform);
    
    const issues: string[] = [];
    if (!configValid) issues.push('OAuth configuration invalid');
    if (!oauthReady) issues.push('OAuth flow not working');
    if (!publishingReady) issues.push('Publishing not ready');
    
    const status: PlatformStatus = {
      platform,
      configured: configValid,
      oauthReady,
      publishingReady,
      issues
    };
    
    setPlatformStatuses(prev => [...prev.filter(p => p.platform !== platform), status]);
    
    if (issues.length === 0) {
      addResult(platform, 'Platform Test', 'success', `${platform} is fully functional!`);
    } else {
      addResult(platform, 'Platform Test', 'warning', `${platform} has ${issues.length} issue(s): ${issues.join(', ')}`);
    }
  };

  const testAllPlatforms = async () => {
    setIsLoading(true);
    clearResults();
    
    addResult('System', 'Integration Test', 'warning', 'Starting comprehensive social media integration test...');
    
    const platforms = ['linkedin', 'facebook', 'instagram', 'twitter'];
    
    for (const platform of platforms) {
      await testPlatform(platform);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    addResult('System', 'Integration Test', 'success', 'All platform tests completed!');
    setIsLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getPlatformStatusBadge = (status: PlatformStatus) => {
    if (status.configured && status.oauthReady && status.publishingReady) {
      return <Badge className="bg-green-100 text-green-800">✅ Ready</Badge>;
    } else if (status.configured && status.oauthReady) {
      return <Badge className="bg-yellow-100 text-yellow-800">⚠️ Partial</Badge>;
    } else if (status.configured) {
      return <Badge className="bg-orange-100 text-orange-800">🔧 Config Only</Badge>;
    } else {
      return <Badge className="bg-red-100 text-red-800">❌ Not Ready</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <RefreshCw className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
            Social Media Integration Test
          </h1>
          <p className="text-gray-600 text-lg">اختبار شامل لتكامل وسائل التواصل الاجتماعي</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Test Controls */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>🧪 Integration Tests</CardTitle>
                <CardDescription>Test social media platform integrations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={testAllPlatforms} 
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : null}
                  🚀 Test All Platforms
                </Button>
                
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    onClick={() => testPlatform('linkedin')} 
                    variant="outline"
                    disabled={isLoading}
                    size="sm"
                  >
                    💼 LinkedIn
                  </Button>
                  <Button 
                    onClick={() => testPlatform('facebook')} 
                    variant="outline"
                    disabled={isLoading}
                    size="sm"
                  >
                    📘 Facebook
                  </Button>
                  <Button 
                    onClick={() => testPlatform('instagram')} 
                    variant="outline"
                    disabled={isLoading}
                    size="sm"
                  >
                    📷 Instagram
                  </Button>
                  <Button 
                    onClick={() => testPlatform('twitter')} 
                    variant="outline"
                    disabled={isLoading}
                    size="sm"
                  >
                    🐦 Twitter/X
                  </Button>
                </div>
                
                <Button 
                  onClick={clearResults} 
                  variant="ghost" 
                  className="w-full"
                >
                  Clear Results
                </Button>
              </CardContent>
            </Card>

            {/* Platform Status Summary */}
            {platformStatuses.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>📊 Platform Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {platformStatuses.map((status) => (
                    <div key={status.platform} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium capitalize">{status.platform}</h4>
                        {status.issues.length > 0 && (
                          <p className="text-xs text-gray-500">{status.issues.join(', ')}</p>
                        )}
                      </div>
                      {getPlatformStatusBadge(status)}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Test Results */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
                <CardDescription>
                  {testResults.length} test results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {testResults.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">
                      No test results yet. Run integration tests to see results here.
                    </p>
                  ) : (
                    testResults.map((result) => (
                      <div
                        key={result.id}
                        className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                      >
                        <div className="flex items-start gap-3">
                          {getStatusIcon(result.status)}
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">
                                {result.platform} - {result.test}
                              </h4>
                              <span className="text-xs text-gray-500">
                                {result.timestamp}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {result.message}
                            </p>
                            {result.details && (
                              <details className="mt-2">
                                <summary className="text-xs text-gray-500 cursor-pointer">
                                  View Details
                                </summary>
                                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                                  {JSON.stringify(result.details, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Integration Guide */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">📋 Integration Test Guide</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">✅ What This Tests:</h4>
              <ul className="space-y-1">
                <li>• OAuth configuration validation</li>
                <li>• OAuth flow URL generation</li>
                <li>• Publishing endpoint availability</li>
                <li>• Platform-specific implementations</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔧 Common Issues:</h4>
              <ul className="space-y-1">
                <li>• Missing environment variables</li>
                <li>• Incorrect callback URLs</li>
                <li>• Invalid OAuth credentials</li>
                <li>• Platform API limitations</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
