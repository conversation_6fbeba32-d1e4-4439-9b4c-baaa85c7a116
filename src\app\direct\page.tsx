"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function DirectAccessPage() {
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Use window.location for a hard navigation
          window.location.href = "/dashboard";
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 className="text-2xl font-bold mb-4 text-center">الوصول المباشر للوحة التحكم</h1>
        
        <p className="mb-6 text-center">
          سيتم توجيهك تلقائياً إلى لوحة التحكم خلال {countdown} ثوان
        </p>
        
        <div className="space-y-4">
          <Button 
            onClick={() => window.location.href = "/dashboard"} 
            className="w-full"
          >
            الانتقال الآن إلى لوحة التحكم
          </Button>
          
          <div className="flex space-x-4 rtl:space-x-reverse">
            <Button asChild variant="outline" className="w-1/2">
              <Link href="/auth/signin">العودة لتسجيل الدخول</Link>
            </Button>
            
            <Button asChild variant="outline" className="w-1/2">
              <Link href="/test">صفحة الاختبار</Link>
            </Button>
          </div>
        </div>
        
        <div className="mt-6 text-sm text-gray-500">
          <p>ملاحظة: هذه الصفحة مخصصة للتطوير فقط وتتجاوز التحقق من المصادقة.</p>
        </div>
      </div>
    </div>
  );
}
