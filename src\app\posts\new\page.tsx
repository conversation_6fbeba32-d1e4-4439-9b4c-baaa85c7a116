"use client";

import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { PostForm } from "@/components/posts/post-form";

export default function NewPostPage() {
  return (
    <DashboardLayout title="منشور جديد">
      <div className="space-y-8" dir="rtl">
        {/* Header Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/50">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              إنشاء منشور جديد ✨
            </h1>
            <p className="text-gray-600 text-lg">
              قم بإنشاء محتوى جذاب ونشره على جميع منصات التواصل الاجتماعي
            </p>
            <p className="text-sm text-gray-500">
              استخدم الأدوات المتقدمة لإنشاء محتوى احترافي وجدولة النشر
            </p>
          </div>
        </div>

        {/* Post Form */}
        <PostForm />
      </div>
    </DashboardLayout>
  );
}
