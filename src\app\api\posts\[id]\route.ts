import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for updating posts
const updatePostSchema = z.object({
  content: z.string().min(1, 'Content is required').max(2800, 'Content too long').optional(),
  media_url: z.string().url().optional().or(z.literal('')),
  status: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED']).optional(),
  scheduled_at: z.string().datetime().optional(),
  social_account_ids: z.array(z.string()).optional(),
});

// GET - Fetch single post
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id: postId } = await params;
    console.log('Fetching post:', postId);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch post
    const { data: post, error } = await supabase
      .from('posts')
      .select(`
        id,
        content,
        media_url,
        status,
        scheduled_at,
        published_at,
        created_at,
        updated_at
      `)
      .eq('id', postId)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error fetching post:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Post not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to fetch post' },
        { status: 500 }
      );
    }

    console.log('Post fetched successfully:', post.id);

    return NextResponse.json({ post });

  } catch (error) {
    console.error('Post fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id: postId } = await params;
    console.log('Updating post:', postId);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log('Update request body received');

    // Validate request body
    const validation = updatePostSchema.safeParse(body);
    if (!validation.success) {
      console.log('Validation failed:', validation.error.errors);
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    const updateData = validation.data;

    // Check if post exists and belongs to user
    const { data: existingPost, error: fetchError } = await supabase
      .from('posts')
      .select('id, status')
      .eq('id', postId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching post for update:', fetchError);
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Post not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to fetch post' },
        { status: 500 }
      );
    }

    // Prepare update data
    const updatePayload: any = {
      ...updateData,
      updated_at: new Date().toISOString(),
    };

    // Handle scheduled_at
    if (updateData.scheduled_at) {
      updatePayload.scheduled_at = new Date(updateData.scheduled_at).toISOString();
    }

    // Handle media_url
    if (updateData.media_url === '') {
      updatePayload.media_url = null;
    }

    console.log('Updating post with data:', { ...updatePayload, content: updatePayload.content?.substring(0, 50) + '...' });

    // Update post
    const { data: post, error: updateError } = await supabase
      .from('posts')
      .update(updatePayload)
      .eq('id', postId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating post:', updateError);
      return NextResponse.json(
        { error: 'Failed to update post' },
        { status: 500 }
      );
    }

    console.log('Post updated successfully:', post.id);

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        post_id: post.id,
        action: 'POST_UPDATED',
        details: `Post updated: ${post.content.substring(0, 50)}...`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      post,
      message: 'Post updated successfully',
    });

  } catch (error) {
    console.error('Post update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const supabase = getSupabaseClient();
  try {
    const { id: postId } = await params;
    console.log('Deleting post:', postId);

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if post exists and belongs to user
    const { data: existingPost, error: fetchError } = await supabase
      .from('posts')
      .select('id, content')
      .eq('id', postId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching post for deletion:', fetchError);
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Post not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to fetch post' },
        { status: 500 }
      );
    }

    // Delete post
    const { error: deleteError } = await supabase
      .from('posts')
      .delete()
      .eq('id', postId)
      .eq('user_id', user.id);

    if (deleteError) {
      console.error('Error deleting post:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete post' },
        { status: 500 }
      );
    }

    console.log('Post deleted successfully:', postId);

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        post_id: postId,
        action: 'POST_DELETED',
        details: `Post deleted: ${existingPost.content.substring(0, 50)}...`,
        created_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      message: 'Post deleted successfully',
    });

  } catch (error) {
    console.error('Post deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
