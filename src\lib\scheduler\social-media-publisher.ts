import { SchedulerLogger } from './scheduler-logger';
import { TwitterPublisher } from './publishers/twitter-publisher';
import { FacebookPublisher } from './publishers/facebook-publisher';
import { LinkedInPublisher } from './publishers/linkedin-publisher';
import { InstagramPublisher } from './publishers/instagram-publisher';
import ContentFormatter from '@/lib/social/content-formatter';

export interface SocialAccount {
  id: string;
  platform: string;
  account_name: string;
  access_token: string;
  refresh_token?: string;
  token_expires_at?: string;
  account_id: string;
  is_active: boolean;
  user_id: string;
  metadata?: Record<string, any>;
}

export interface PostContent {
  content: string;
  mediaUrl?: string;
  mediaType?: 'image' | 'video';
}

export interface PublishResult {
  platform: string;
  success: boolean;
  url?: string;
  postId?: string;
  error?: string;
  retry_after?: number;
}

/**
 * Main social media publisher that handles posting to all platforms
 */
export class SocialMediaPublisher {
  private logger: SchedulerLogger;
  private publishers: Map<string, any>;
  private contentFormatter: ContentFormatter;

  constructor() {
    this.logger = new SchedulerLogger('social-publisher');
    this.publishers = new Map();
    this.contentFormatter = new ContentFormatter();

    // Initialize platform publishers
    this.initializePublishers();
  }

  /**
   * Initialize platform-specific publishers
   */
  private initializePublishers(): void {
    this.publishers.set('TWITTER', new TwitterPublisher());
    this.publishers.set('FACEBOOK', new FacebookPublisher());
    this.publishers.set('LINKEDIN', new LinkedInPublisher());
    this.publishers.set('INSTAGRAM', new InstagramPublisher());
  }

  /**
   * Publish content to a specific social account
   */
  async publishToAccount(account: SocialAccount, content: PostContent): Promise<PublishResult> {
    const logger = this.logger.child(account.platform.toLowerCase(), {
      accountId: account.id,
      accountName: account.account_name,
    });

    try {
      logger.info('Starting post publication', {
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
      });

      // Check if account is active
      if (!account.is_active) {
        throw new Error('Social account is not active');
      }

      // Check token expiration
      if (account.token_expires_at) {
        const expiresAt = new Date(account.token_expires_at);
        if (expiresAt <= new Date()) {
          throw new Error('Access token has expired');
        }
      }

      // Get platform publisher
      const publisher = this.publishers.get(account.platform);
      if (!publisher) {
        throw new Error(`No publisher available for platform: ${account.platform}`);
      }

      // Validate content for platform
      await this.validateContent(account.platform, content);

      // Publish to platform
      const result = await publisher.publish(account, content);

      logger.info('Post published successfully', {
        postId: result.postId,
        url: result.url,
      });

      return {
        platform: account.platform,
        success: true,
        url: result.url,
        postId: result.postId,
      };

    } catch (error) {
      logger.error('Failed to publish post', error, {
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl,
      });

      // Check if it's a rate limit error
      const isRateLimit = this.isRateLimitError(error);
      const retryAfter = isRateLimit ? this.extractRetryAfter(error) : undefined;

      return {
        platform: account.platform,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        retry_after: retryAfter,
      };
    }
  }

  /**
   * Validate content for specific platform using enhanced ContentFormatter
   */
  private async validateContent(platform: string, content: PostContent): Promise<void> {
    // Use ContentFormatter for platform-specific validation
    const platformKey = platform.toLowerCase() as any;
    const formattedContent = this.contentFormatter.formatForPlatform(content.content, platformKey);

    if (!formattedContent.isValid) {
      throw new Error(`Content validation failed for ${platform}: ${formattedContent.warnings.join(', ')}`);
    }

    // Log warnings if any
    if (formattedContent.warnings.length > 0) {
      this.logger.warn(`Content formatting warnings for ${platform}`, {
        warnings: formattedContent.warnings
      });
    }

    // Check media requirements
    const limits = this.getPlatformLimits(platform);
    if (content.mediaUrl) {
      if (!limits.supportsMedia) {
        throw new Error(`${platform} does not support media attachments`);
      }

      // Validate media URL
      if (!this.isValidMediaUrl(content.mediaUrl)) {
        throw new Error('Invalid media URL format');
      }
    }

    // Platform-specific validations
    switch (platform) {
      case 'TWITTER':
        await this.validateTwitterContent(content);
        break;
      case 'FACEBOOK':
        await this.validateFacebookContent(content);
        break;
      case 'LINKEDIN':
        await this.validateLinkedInContent(content);
        break;
      case 'INSTAGRAM':
        await this.validateInstagramContent(content);
        break;
    }
  }

  /**
   * Get platform-specific limits
   */
  private getPlatformLimits(platform: string): {
    maxContentLength: number;
    supportsMedia: boolean;
    requiresMedia?: boolean;
  } {
    switch (platform) {
      case 'TWITTER':
        return {
          maxContentLength: 280,
          supportsMedia: true,
        };
      case 'FACEBOOK':
        return {
          maxContentLength: 63206,
          supportsMedia: true,
        };
      case 'LINKEDIN':
        return {
          maxContentLength: 3000,
          supportsMedia: true,
        };
      case 'INSTAGRAM':
        return {
          maxContentLength: 2200,
          supportsMedia: true,
          requiresMedia: true,
        };
      default:
        return {
          maxContentLength: 1000,
          supportsMedia: false,
        };
    }
  }

  /**
   * Validate Twitter-specific content
   */
  private async validateTwitterContent(content: PostContent): Promise<void> {
    // Twitter-specific validations
    if (content.content.trim().length === 0) {
      throw new Error('Twitter posts cannot be empty');
    }

    // Check for prohibited content patterns
    if (content.content.includes('@everyone')) {
      throw new Error('Twitter does not allow @everyone mentions');
    }
  }

  /**
   * Validate Facebook-specific content
   */
  private async validateFacebookContent(content: PostContent): Promise<void> {
    // Facebook-specific validations
    if (content.content.trim().length === 0 && !content.mediaUrl) {
      throw new Error('Facebook posts must have either text or media');
    }
  }

  /**
   * Validate LinkedIn-specific content
   */
  private async validateLinkedInContent(content: PostContent): Promise<void> {
    // LinkedIn-specific validations
    if (content.content.trim().length === 0) {
      throw new Error('LinkedIn posts cannot be empty');
    }
  }

  /**
   * Validate Instagram-specific content
   */
  private async validateInstagramContent(content: PostContent): Promise<void> {
    // Instagram requires media
    if (!content.mediaUrl) {
      throw new Error('Instagram posts require media (image or video)');
    }

    // Instagram has specific media requirements
    if (!this.isValidInstagramMedia(content.mediaUrl)) {
      throw new Error('Invalid media format for Instagram');
    }
  }

  /**
   * Check if URL is a valid media URL
   */
  private isValidMediaUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      const pathname = parsedUrl.pathname.toLowerCase();
      
      // Check for common media file extensions
      const mediaExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.mov', '.avi'];
      return mediaExtensions.some(ext => pathname.endsWith(ext));
    } catch {
      return false;
    }
  }

  /**
   * Check if media is valid for Instagram
   */
  private isValidInstagramMedia(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      const pathname = parsedUrl.pathname.toLowerCase();
      
      // Instagram supports specific formats
      const instagramFormats = ['.jpg', '.jpeg', '.png', '.mp4'];
      return instagramFormats.some(ext => pathname.endsWith(ext));
    } catch {
      return false;
    }
  }

  /**
   * Check if error is a rate limit error
   */
  private isRateLimitError(error: any): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      return message.includes('rate limit') || 
             message.includes('too many requests') ||
             message.includes('429');
    }
    return false;
  }

  /**
   * Extract retry-after value from rate limit error
   */
  private extractRetryAfter(error: any): number | undefined {
    if (error instanceof Error) {
      const message = error.message;
      const match = message.match(/retry after (\d+)/i);
      if (match) {
        return parseInt(match[1]) * 1000; // Convert to milliseconds
      }
    }
    return 300000; // Default 5 minutes
  }

  /**
   * Get supported platforms
   */
  getSupportedPlatforms(): string[] {
    return Array.from(this.publishers.keys());
  }

  /**
   * Check if platform is supported
   */
  isPlatformSupported(platform: string): boolean {
    return this.publishers.has(platform);
  }

  /**
   * Get publisher for platform
   */
  getPublisher(platform: string): any {
    return this.publishers.get(platform);
  }

  /**
   * Test connection to a social account
   */
  async testConnection(account: SocialAccount): Promise<{
    success: boolean;
    error?: string;
    accountInfo?: any;
  }> {
    try {
      const publisher = this.publishers.get(account.platform);
      if (!publisher) {
        throw new Error(`No publisher available for platform: ${account.platform}`);
      }

      if (typeof publisher.testConnection === 'function') {
        const result = await publisher.testConnection(account);
        return { success: true, accountInfo: result };
      } else {
        return { success: true, accountInfo: { message: 'Test connection not implemented for this platform' } };
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
