# 🔴 FEATURE 1: <PERSON><PERSON><PERSON><PERSON> CONTENT SCHEDULING SYSTEM
## Technical Implementation Specification

**Priority**: CRITICAL | **Timeline**: 8-10 days | **Complexity**: HIGH

---

## 📋 **IMPLEMENTATION ROADMAP**

### **Phase 1: Database Schema & Core Logic (Days 1-2)**
### **Phase 2: Recurring Posts System (Days 3-4)**
### **Phase 3: Bulk Scheduling System (Days 5-6)**
### **Phase 4: Optimal Time Suggestions (Days 7-8)**
### **Phase 5: Advanced Calendar & Testing (Days 9-10)**

---

## 🗄️ **DATABASE SCHEMA IMPLEMENTATION**

### **Step 1: Update Existing Posts Table**
```sql
-- Add recurring post support to existing posts table
ALTER TABLE posts ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE;
ALTER TABLE posts ADD COLUMN recurring_pattern JSONB;
ALTER TABLE posts ADD COLUMN parent_post_id TEXT;
<PERSON><PERSON><PERSON> TABLE posts ADD COLUMN recurring_end_date TIMESTAMP;
<PERSON><PERSON><PERSON> TABLE posts ADD COLUMN optimal_time_score DECIMAL(3,2);

-- Add indexes for performance
CREATE INDEX idx_posts_recurring ON posts(is_recurring, parent_post_id);
CREATE INDEX idx_posts_scheduled_at ON posts(scheduled_at) WHERE status = 'SCHEDULED';
```

### **Step 2: Create Recurring Posts Management Table**
```sql
CREATE TABLE recurring_posts (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  media_url TEXT,
  platforms TEXT[] NOT NULL,
  
  -- Pattern Configuration
  pattern_type TEXT NOT NULL CHECK (pattern_type IN ('daily', 'weekly', 'monthly', 'custom')),
  pattern_config JSONB NOT NULL,
  
  -- Scheduling
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP,
  timezone TEXT DEFAULT 'UTC',
  
  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  posts_generated INTEGER DEFAULT 0,
  last_generated_at TIMESTAMP,
  
  -- Metadata
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Pattern config examples:
-- Daily: {"interval": 1, "time": "09:00"}
-- Weekly: {"days": ["monday", "wednesday", "friday"], "time": "14:00"}
-- Monthly: {"day_of_month": 15, "time": "10:00"}
-- Custom: {"dates": ["2025-02-01T09:00:00Z", "2025-02-15T14:00:00Z"]}
```

### **Step 3: Create Bulk Operations Tracking**
```sql
CREATE TABLE bulk_operations (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  operation_type TEXT NOT NULL CHECK (operation_type IN ('import', 'schedule', 'update')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  
  -- Progress Tracking
  total_items INTEGER NOT NULL,
  processed_items INTEGER DEFAULT 0,
  successful_items INTEGER DEFAULT 0,
  failed_items INTEGER DEFAULT 0,
  
  -- Configuration
  source_file_url TEXT,
  operation_config JSONB,
  
  -- Results
  error_log JSONB,
  result_summary JSONB,
  
  -- Metadata
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

---

## 🔄 **RECURRING POSTS SYSTEM**

### **Core Logic Implementation**

#### **File: `/src/lib/scheduling/recurring-manager.ts`**
```typescript
export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  config: {
    interval?: number;
    time?: string;
    days?: string[];
    dayOfMonth?: number;
    dates?: string[];
  };
}

export interface RecurringPost {
  id: string;
  userId: string;
  title: string;
  content: string;
  mediaUrl?: string;
  platforms: string[];
  pattern: RecurringPattern;
  startDate: Date;
  endDate?: Date;
  timezone: string;
  isActive: boolean;
}

export class RecurringManager {
  async createRecurringPost(data: CreateRecurringPostData): Promise<RecurringPost>
  async generateNextBatch(recurringPostId: string, batchSize: number = 30): Promise<Post[]>
  async updateRecurringPost(id: string, updates: Partial<RecurringPost>): Promise<RecurringPost>
  async pauseRecurringPost(id: string): Promise<void>
  async resumeRecurringPost(id: string): Promise<void>
  async deleteRecurringPost(id: string): Promise<void>
  async getUpcomingPosts(recurringPostId: string, limit: number = 10): Promise<Post[]>
}
```

#### **Pattern Generation Logic**
```typescript
export class PatternGenerator {
  generateDailyPattern(startDate: Date, endDate: Date, interval: number, time: string): Date[]
  generateWeeklyPattern(startDate: Date, endDate: Date, days: string[], time: string): Date[]
  generateMonthlyPattern(startDate: Date, endDate: Date, dayOfMonth: number, time: string): Date[]
  generateCustomPattern(dates: string[]): Date[]
  
  private calculateNextOccurrence(lastDate: Date, pattern: RecurringPattern): Date
  private validatePattern(pattern: RecurringPattern): boolean
  private adjustForTimezone(date: Date, timezone: string): Date
}
```

### **API Endpoints Implementation**

#### **File: `/src/app/api/posts/recurring/route.ts`**
```typescript
// POST /api/posts/recurring - Create recurring post
export async function POST(request: NextRequest) {
  // 1. Validate user authentication
  // 2. Parse and validate recurring post data
  // 3. Create recurring post record
  // 4. Generate initial batch of posts (next 30 days)
  // 5. Return recurring post with preview of generated posts
}

// GET /api/posts/recurring - List user's recurring posts
export async function GET(request: NextRequest) {
  // 1. Get user's recurring posts with pagination
  // 2. Include statistics (total generated, next occurrence)
  // 3. Return formatted list
}
```

#### **File: `/src/app/api/posts/recurring/[id]/route.ts`**
```typescript
// GET /api/posts/recurring/[id] - Get specific recurring post
// PUT /api/posts/recurring/[id] - Update recurring post
// DELETE /api/posts/recurring/[id] - Delete recurring post

// POST /api/posts/recurring/[id]/generate - Generate next batch
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  // 1. Get recurring post configuration
  // 2. Generate next batch of posts
  // 3. Save to database
  // 4. Return generated posts
}
```

---

## 📦 **BULK SCHEDULING SYSTEM**

### **CSV Import Implementation**

#### **File: `/src/lib/scheduling/bulk-scheduler.ts`**
```typescript
export interface BulkScheduleItem {
  content: string;
  mediaUrl?: string;
  platforms: string[];
  scheduledAt: Date;
  tags?: string[];
}

export class BulkScheduler {
  async importFromCSV(file: File, userId: string): Promise<BulkOperation>
  async validateBulkData(items: BulkScheduleItem[]): Promise<ValidationResult>
  async processBulkSchedule(operationId: string): Promise<void>
  async getBulkOperationStatus(operationId: string): Promise<BulkOperationStatus>
  
  private parseCSVFile(file: File): Promise<BulkScheduleItem[]>
  private validateScheduleItem(item: BulkScheduleItem): ValidationError[]
  private optimizeScheduleTimes(items: BulkScheduleItem[]): BulkScheduleItem[]
}
```

### **CSV Template & Validation**
```typescript
export const CSV_TEMPLATE = {
  headers: ['content', 'platforms', 'scheduled_date', 'scheduled_time', 'media_url', 'tags'],
  example: [
    'Hello from eWasl! 🚀', 'twitter,facebook', '2025-02-01', '09:00', '', 'marketing,social'
  ],
  validation: {
    content: { required: true, maxLength: 2000 },
    platforms: { required: true, validValues: ['twitter', 'facebook', 'instagram', 'linkedin'] },
    scheduled_date: { required: true, format: 'YYYY-MM-DD' },
    scheduled_time: { required: true, format: 'HH:MM' },
    media_url: { required: false, format: 'url' },
    tags: { required: false, format: 'comma-separated' }
  }
};
```

---

## 🎯 **OPTIMAL TIME SUGGESTIONS**

### **AI-Powered Time Optimization**

#### **File: `/src/lib/analytics/optimal-times.ts`**
```typescript
export interface OptimalTimeAnalysis {
  platform: string;
  recommendedTimes: {
    time: string;
    score: number;
    reasoning: string;
  }[];
  audienceInsights: {
    timezone: string;
    activeHours: string[];
    peakDays: string[];
  };
  historicalPerformance: {
    bestPerformingTime: string;
    averageEngagement: number;
    sampleSize: number;
  };
}

export class OptimalTimeAnalyzer {
  async analyzeOptimalTimes(userId: string, platform: string): Promise<OptimalTimeAnalysis>
  async getPersonalizedRecommendations(userId: string): Promise<OptimalTimeAnalysis[]>
  async getBenchmarkData(industry: string, platform: string): Promise<BenchmarkData>
  
  private analyzeHistoricalPerformance(userId: string, platform: string): Promise<PerformanceData>
  private getAudienceInsights(userId: string, platform: string): Promise<AudienceData>
  private calculateOptimalScore(time: string, data: AnalysisData): number
}
```

### **Machine Learning Integration**
```typescript
export class TimeOptimizationML {
  async trainUserModel(userId: string): Promise<MLModel>
  async predictEngagement(content: string, time: Date, platform: string): Promise<number>
  async getTimeRecommendations(content: string, platforms: string[]): Promise<TimeRecommendation[]>
  
  private extractContentFeatures(content: string): ContentFeatures
  private getTimeFeatures(date: Date): TimeFeatures
  private combineFeatures(content: ContentFeatures, time: TimeFeatures): MLFeatures
}
```

---

## 📅 **ADVANCED CALENDAR IMPLEMENTATION**

### **Enhanced Calendar Component**

#### **File: `/src/components/scheduling/advanced-calendar.tsx`**
```typescript
export interface CalendarPost {
  id: string;
  content: string;
  platforms: string[];
  scheduledAt: Date;
  status: 'scheduled' | 'published' | 'failed';
  isRecurring: boolean;
  parentRecurringId?: string;
}

export function AdvancedCalendar() {
  // Features:
  // - Drag and drop post rescheduling
  // - Multi-view (month, week, day)
  // - Recurring post indicators
  // - Platform-specific color coding
  // - Bulk selection and operations
  // - Time slot optimization suggestions
  // - Conflict detection and resolution
}
```

### **Drag & Drop Functionality**
```typescript
export class CalendarDragDrop {
  handlePostDrag(postId: string, newDateTime: Date): Promise<void>
  validateTimeSlot(dateTime: Date, platforms: string[]): ValidationResult
  suggestAlternativeSlots(dateTime: Date, platforms: string[]): Date[]
  handleBulkReschedule(postIds: string[], newDateTime: Date): Promise<BulkRescheduleResult>
}
```

---

## 🧪 **TESTING STRATEGY**

### **Unit Tests**
- Pattern generation algorithms
- CSV parsing and validation
- Optimal time calculations
- Recurring post logic

### **Integration Tests**
- Database operations
- API endpoint functionality
- Bulk operation processing
- Calendar drag & drop

### **Performance Tests**
- Bulk import of 1000+ posts
- Recurring post generation
- Calendar rendering with 500+ posts
- Optimal time analysis speed

### **User Acceptance Tests**
- Complete recurring post workflow
- CSV import and bulk scheduling
- Calendar interaction and rescheduling
- Time optimization suggestions

---

## 📊 **SUCCESS METRICS**

### **Functional Requirements**
- [ ] Create daily/weekly/monthly recurring posts
- [ ] Import and schedule 1000+ posts via CSV
- [ ] Drag-drop rescheduling in calendar
- [ ] AI-powered optimal time suggestions
- [ ] Bulk operations with progress tracking

### **Performance Requirements**
- [ ] Generate 100 recurring posts in <5 seconds
- [ ] Process CSV import of 1000 posts in <30 seconds
- [ ] Calendar loads 500+ posts in <2 seconds
- [ ] Optimal time analysis completes in <10 seconds

### **User Experience Requirements**
- [ ] Intuitive recurring post creation flow
- [ ] Real-time progress for bulk operations
- [ ] Responsive calendar on mobile devices
- [ ] Clear error messages and validation
- [ ] Undo/redo functionality for scheduling changes

This specification provides the complete technical foundation for implementing the Advanced Content Scheduling System as the first critical feature for eWasl's commercial launch.
