/**
 * Twitter OAuth 2.0 Service
 * Handles Twitter API v2 OAuth 2.0 with PKCE
 * Replaces OAuth 1.0a implementation
 */

import crypto from 'crypto';

export interface TwitterTokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in?: number;
  token_type: string;
  scope?: string;
}

export interface TwitterUser {
  id: string;
  name: string;
  username: string;
  profile_image_url?: string;
  public_metrics?: {
    followers_count: number;
    following_count: number;
    tweet_count: number;
    listed_count: number;
  };
}

export interface PKCEChallenge {
  codeVerifier: string;
  codeChallenge: string;
}

export class TwitterOAuthService {
  private readonly baseUrl = 'https://api.twitter.com/2';
  private readonly authUrl = 'https://twitter.com/i/oauth2/authorize';
  private readonly tokenUrl = 'https://api.twitter.com/2/oauth2/token';

  /**
   * Generate PKCE challenge for OAuth 2.0
   */
  generatePKCE(): PKCEChallenge {
    // Generate a random code verifier (43-128 characters)
    const codeVerifier = this.generateRandomString(128);
    
    // Create SHA256 hash of the code verifier
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    
    // Base64 URL encode the hash
    const codeChallenge = this.base64URLEncode(hash);
    
    return { codeVerifier, codeChallenge };
  }

  /**
   * Get OAuth 2.0 authorization URL
   */
  getAuthorizationUrl(
    clientId: string, 
    redirectUri: string, 
    state: string,
    codeChallenge: string
  ): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: 'tweet.read tweet.write users.read media.upload offline.access',
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256'
    });

    return `${this.authUrl}?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(
    code: string,
    codeVerifier: string,
    clientId: string,
    redirectUri: string
  ): Promise<TwitterTokenResponse> {
    try {
      console.log('Exchanging Twitter authorization code for token...');

      const response = await fetch(this.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${clientId}:${process.env.X_CLIENT_SECRET || process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code,
          redirect_uri: redirectUri,
          code_verifier: codeVerifier
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token exchange failed: ${response.status} ${errorData}`);
      }

      const tokenData = await response.json();

      if (tokenData.error) {
        throw new Error(`Twitter OAuth error: ${tokenData.error_description || tokenData.error}`);
      }

      console.log('Twitter token exchange successful');
      return tokenData;

    } catch (error) {
      console.error('Twitter token exchange error:', error);
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string, clientId: string): Promise<TwitterTokenResponse> {
    try {
      console.log('Refreshing Twitter access token...');

      const response = await fetch(this.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${clientId}:${process.env.X_CLIENT_SECRET || process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: refreshToken
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token refresh failed: ${response.status} ${errorData}`);
      }

      const tokenData = await response.json();

      if (tokenData.error) {
        throw new Error(`Twitter refresh error: ${tokenData.error_description || tokenData.error}`);
      }

      console.log('Twitter token refresh successful');
      return tokenData;

    } catch (error) {
      console.error('Twitter token refresh error:', error);
      throw error;
    }
  }

  /**
   * Get user information
   */
  async getUserInfo(accessToken: string): Promise<TwitterUser> {
    try {
      console.log('Fetching Twitter user info...');

      const response = await fetch(
        `${this.baseUrl}/users/me?user.fields=id,name,username,profile_image_url,public_metrics`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Failed to fetch user info: ${response.status} ${errorData}`);
      }

      const userData = await response.json();

      if (userData.errors) {
        throw new Error(`Twitter API error: ${userData.errors[0].detail}`);
      }

      console.log('Twitter user info fetched successfully:', userData.data.username);
      return userData.data;

    } catch (error) {
      console.error('Twitter user info fetch error:', error);
      throw error;
    }
  }

  /**
   * Validate access token
   */
  async validateToken(accessToken: string): Promise<boolean> {
    try {
      await this.getUserInfo(accessToken);
      return true;
    } catch (error) {
      console.warn('Twitter token validation failed:', error);
      return false;
    }
  }

  /**
   * Revoke access token
   */
  async revokeToken(accessToken: string, clientId: string): Promise<void> {
    try {
      console.log('Revoking Twitter access token...');

      const response = await fetch('https://api.twitter.com/2/oauth2/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${clientId}:${process.env.X_CLIENT_SECRET || process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`
        },
        body: new URLSearchParams({
          token: accessToken,
          token_type_hint: 'access_token'
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.warn(`Token revocation failed: ${response.status} ${errorData}`);
      } else {
        console.log('Twitter token revoked successfully');
      }

    } catch (error) {
      console.error('Twitter token revocation error:', error);
      throw error;
    }
  }

  /**
   * Generate random string for code verifier
   */
  private generateRandomString(length: number): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return result;
  }

  /**
   * Base64 URL encode
   */
  private base64URLEncode(buffer: Buffer): string {
    return buffer
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Get rate limit information
   */
  async getRateLimitStatus(accessToken: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/tweets/search/recent?query=test&max_results=10`,
        {
          method: 'HEAD',
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      return {
        limit: response.headers.get('x-rate-limit-limit'),
        remaining: response.headers.get('x-rate-limit-remaining'),
        reset: response.headers.get('x-rate-limit-reset')
      };

    } catch (error) {
      console.error('Error fetching rate limit status:', error);
      return null;
    }
  }

  /**
   * Check if scopes are sufficient for required operations
   */
  validateScopes(grantedScopes: string[], requiredScopes: string[]): boolean {
    return requiredScopes.every(scope => grantedScopes.includes(scope));
  }

  /**
   * Get scope upgrade URL if additional permissions are needed
   */
  getScopeUpgradeUrl(
    clientId: string,
    redirectUri: string,
    state: string,
    codeChallenge: string,
    additionalScopes: string[]
  ): string {
    const allScopes = [
      'tweet.read',
      'tweet.write', 
      'users.read',
      'media.upload',
      'offline.access',
      ...additionalScopes
    ];

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: [...new Set(allScopes)].join(' '), // Remove duplicates
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256'
    });

    return `${this.authUrl}?${params.toString()}`;
  }
}
