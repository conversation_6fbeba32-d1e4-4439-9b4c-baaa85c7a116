-- Create team collaboration and user management tables
-- This migration adds multi-tenant workspace support with role-based access control

-- Create workspaces table for team/organization management
CREATE TABLE IF NOT EXISTS workspaces (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL CHECK (length(name) >= 2 AND length(name) <= 100),
  slug TEXT UNIQUE NOT NULL CHECK (slug ~ '^[a-z0-9-]+$' AND length(slug) >= 2 AND length(slug) <= 50),
  description TEXT,
  
  -- Workspace settings
  settings JSONB DEFAULT '{}',
  timezone TEXT DEFAULT 'UTC',
  language TEXT DEFAULT 'ar' CHECK (language IN ('ar', 'en')),
  
  -- Subscription and limits
  plan_type TEXT DEFAULT 'FREE' CHECK (plan_type IN ('FREE', 'PRO', 'ENTERPRISE')),
  subscription_id TEXT, -- Stripe subscription ID
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'canceled', 'past_due', 'unpaid')),
  
  -- Usage limits based on plan
  limits JSONB DEFAULT '{
    "posts_per_month": 5,
    "social_accounts": 2,
    "team_members": 1,
    "scheduled_posts": 10
  }',
  
  -- Workspace metadata
  avatar_url TEXT,
  website_url TEXT,
  industry TEXT,
  company_size TEXT CHECK (company_size IN ('1-10', '11-50', '51-200', '201-1000', '1000+')),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Soft delete
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create user roles enum
CREATE TYPE user_role AS ENUM ('OWNER', 'ADMIN', 'MANAGER', 'EDITOR', 'VIEWER');

-- Create workspace members table for team management
CREATE TABLE IF NOT EXISTS workspace_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Role and permissions
  role user_role NOT NULL DEFAULT 'VIEWER',
  permissions JSONB DEFAULT '{}', -- Custom permissions override
  
  -- Member status
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'suspended')),
  
  -- Invitation details
  invited_by UUID REFERENCES auth.users(id),
  invited_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE,
  
  -- Member metadata
  title TEXT, -- Job title
  department TEXT,
  notes TEXT, -- Admin notes about the member
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(workspace_id, user_id)
);

-- Create workspace invitations table
CREATE TABLE IF NOT EXISTS workspace_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role user_role NOT NULL DEFAULT 'VIEWER',
  
  -- Invitation details
  invited_by UUID NOT NULL REFERENCES auth.users(id),
  token TEXT UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
  
  -- Status and expiry
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'canceled')),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  
  -- Invitation message
  message TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(workspace_id, email)
);

-- Create approval workflows table
CREATE TABLE IF NOT EXISTS approval_workflows (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  
  -- Workflow configuration
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  
  -- Workflow rules
  rules JSONB DEFAULT '{}', -- Conditions for when this workflow applies
  steps JSONB DEFAULT '[]', -- Array of approval steps
  
  -- Auto-approval settings
  auto_approve_roles user_role[] DEFAULT '{}',
  auto_approve_conditions JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create post approvals table
CREATE TABLE IF NOT EXISTS post_approvals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  workflow_id UUID REFERENCES approval_workflows(id),
  
  -- Approval status
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'canceled')),
  current_step INTEGER DEFAULT 1,
  
  -- Approval details
  requested_by UUID NOT NULL REFERENCES auth.users(id),
  approved_by UUID REFERENCES auth.users(id),
  
  -- Comments and feedback
  comments TEXT,
  feedback JSONB DEFAULT '[]', -- Array of feedback from reviewers
  
  -- Timestamps
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  
  -- Constraints
  UNIQUE(post_id)
);

-- Create team comments table for collaboration
CREATE TABLE IF NOT EXISTS team_comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Comment target (can be post, approval, etc.)
  target_type TEXT NOT NULL CHECK (target_type IN ('post', 'approval', 'workspace')),
  target_id UUID NOT NULL,
  
  -- Comment details
  content TEXT NOT NULL CHECK (length(content) >= 1 AND length(content) <= 2000),
  author_id UUID NOT NULL REFERENCES auth.users(id),
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  
  -- Comment metadata
  is_internal BOOLEAN DEFAULT false, -- Internal team comment vs client-visible
  mentions UUID[] DEFAULT '{}', -- Array of mentioned user IDs
  
  -- Reply threading
  parent_id UUID REFERENCES team_comments(id),
  thread_id UUID, -- Root comment ID for threading
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workspaces_slug ON workspaces(slug);
CREATE INDEX IF NOT EXISTS idx_workspaces_plan_type ON workspaces(plan_type);
CREATE INDEX IF NOT EXISTS idx_workspaces_created_at ON workspaces(created_at);

CREATE INDEX IF NOT EXISTS idx_workspace_members_workspace_id ON workspace_members(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workspace_members_user_id ON workspace_members(user_id);
CREATE INDEX IF NOT EXISTS idx_workspace_members_role ON workspace_members(role);
CREATE INDEX IF NOT EXISTS idx_workspace_members_status ON workspace_members(status);

CREATE INDEX IF NOT EXISTS idx_workspace_invitations_workspace_id ON workspace_invitations(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_email ON workspace_invitations(email);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_token ON workspace_invitations(token);
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_status ON workspace_invitations(status);

CREATE INDEX IF NOT EXISTS idx_approval_workflows_workspace_id ON approval_workflows(workspace_id);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_is_active ON approval_workflows(is_active);

CREATE INDEX IF NOT EXISTS idx_post_approvals_post_id ON post_approvals(post_id);
CREATE INDEX IF NOT EXISTS idx_post_approvals_status ON post_approvals(status);
CREATE INDEX IF NOT EXISTS idx_post_approvals_requested_by ON post_approvals(requested_by);

CREATE INDEX IF NOT EXISTS idx_team_comments_target ON team_comments(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_team_comments_workspace_id ON team_comments(workspace_id);
CREATE INDEX IF NOT EXISTS idx_team_comments_author_id ON team_comments(author_id);
CREATE INDEX IF NOT EXISTS idx_team_comments_parent_id ON team_comments(parent_id);

-- Add RLS (Row Level Security) policies
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_comments ENABLE ROW LEVEL SECURITY;

-- Workspace policies
CREATE POLICY "Users can view workspaces they are members of" ON workspaces
  FOR SELECT USING (
    id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Workspace owners and admins can update workspaces" ON workspaces
  FOR UPDATE USING (
    id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND role IN ('OWNER', 'ADMIN') AND status = 'active'
    )
  );

-- Workspace members policies
CREATE POLICY "Users can view workspace members of their workspaces" ON workspace_members
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Workspace admins can manage members" ON workspace_members
  FOR ALL USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND role IN ('OWNER', 'ADMIN') AND status = 'active'
    )
  );

-- Workspace invitations policies
CREATE POLICY "Workspace admins can manage invitations" ON workspace_invitations
  FOR ALL USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND role IN ('OWNER', 'ADMIN') AND status = 'active'
    )
  );

-- Approval workflows policies
CREATE POLICY "Workspace members can view approval workflows" ON approval_workflows
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Workspace managers can manage approval workflows" ON approval_workflows
  FOR ALL USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND role IN ('OWNER', 'ADMIN', 'MANAGER') AND status = 'active'
    )
  );

-- Post approvals policies
CREATE POLICY "Users can view post approvals for their workspace posts" ON post_approvals
  FOR SELECT USING (
    post_id IN (
      SELECT p.id FROM posts p
      JOIN workspace_members wm ON p.user_id = wm.user_id
      WHERE wm.user_id = auth.uid() AND wm.status = 'active'
    )
  );

-- Team comments policies
CREATE POLICY "Workspace members can view team comments" ON team_comments
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Workspace members can create team comments" ON team_comments
  FOR INSERT WITH CHECK (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    ) AND author_id = auth.uid()
  );

-- Create functions for workspace management
CREATE OR REPLACE FUNCTION get_user_workspace_role(workspace_uuid UUID, user_uuid UUID)
RETURNS user_role AS $$
BEGIN
  RETURN (
    SELECT role FROM workspace_members 
    WHERE workspace_id = workspace_uuid AND user_id = user_uuid AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check workspace permissions
CREATE OR REPLACE FUNCTION has_workspace_permission(workspace_uuid UUID, required_role user_role)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM workspace_members 
    WHERE workspace_id = workspace_uuid 
    AND user_id = auth.uid() 
    AND status = 'active'
    AND (
      role = 'OWNER' OR
      (required_role = 'ADMIN' AND role IN ('OWNER', 'ADMIN')) OR
      (required_role = 'MANAGER' AND role IN ('OWNER', 'ADMIN', 'MANAGER')) OR
      (required_role = 'EDITOR' AND role IN ('OWNER', 'ADMIN', 'MANAGER', 'EDITOR')) OR
      (required_role = 'VIEWER' AND role IN ('OWNER', 'ADMIN', 'MANAGER', 'EDITOR', 'VIEWER'))
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_workspaces_updated_at
  BEFORE UPDATE ON workspaces
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_workspace_members_updated_at
  BEFORE UPDATE ON workspace_members
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_workspace_invitations_updated_at
  BEFORE UPDATE ON workspace_invitations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_team_comments_updated_at
  BEFORE UPDATE ON team_comments
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert default workspace for existing users
INSERT INTO workspaces (name, slug, description, plan_type)
VALUES ('Personal Workspace', 'personal', 'Default personal workspace', 'FREE')
ON CONFLICT DO NOTHING;

-- Add workspace_id to existing tables
ALTER TABLE posts ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id);
ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id);
ALTER TABLE analytics ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id);
ALTER TABLE activities ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id);

-- Create indexes for new workspace_id columns
CREATE INDEX IF NOT EXISTS idx_posts_workspace_id ON posts(workspace_id);
CREATE INDEX IF NOT EXISTS idx_social_accounts_workspace_id ON social_accounts(workspace_id);
CREATE INDEX IF NOT EXISTS idx_analytics_workspace_id ON analytics(workspace_id);
CREATE INDEX IF NOT EXISTS idx_activities_workspace_id ON activities(workspace_id);

-- Update RLS policies for existing tables to include workspace context
DROP POLICY IF EXISTS "Users can view their own posts" ON posts;
CREATE POLICY "Users can view posts in their workspaces" ON posts
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

DROP POLICY IF EXISTS "Users can insert their own posts" ON posts;
CREATE POLICY "Users can create posts in their workspaces" ON posts
  FOR INSERT WITH CHECK (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members 
      WHERE user_id = auth.uid() AND status = 'active'
      AND role IN ('OWNER', 'ADMIN', 'MANAGER', 'EDITOR')
    ) AND user_id = auth.uid()
  );

-- Grant necessary permissions
GRANT SELECT ON workspaces TO authenticated;
GRANT SELECT ON workspace_members TO authenticated;
GRANT SELECT ON workspace_invitations TO authenticated;
GRANT SELECT ON approval_workflows TO authenticated;
GRANT SELECT ON post_approvals TO authenticated;
GRANT SELECT ON team_comments TO authenticated;

-- Add comments to tables
COMMENT ON TABLE workspaces IS 'Multi-tenant workspaces for team collaboration';
COMMENT ON TABLE workspace_members IS 'Team members and their roles within workspaces';
COMMENT ON TABLE workspace_invitations IS 'Pending invitations to join workspaces';
COMMENT ON TABLE approval_workflows IS 'Configurable approval workflows for content';
COMMENT ON TABLE post_approvals IS 'Post approval requests and their status';
COMMENT ON TABLE team_comments IS 'Team collaboration comments and feedback';
