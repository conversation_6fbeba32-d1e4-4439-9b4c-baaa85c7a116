/**
 * Tests for Recurring Post Form Component
 * Tests the RecurringPostForm component functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { RecurringPostForm } from '@/components/scheduling/recurring-post-form';

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('RecurringPostForm Component', () => {
  const mockSocialAccounts = [
    {
      id: 'account-1',
      platform: 'TWITTER',
      account_name: 'Test Twitter',
      account_id: 'twitter123',
    },
    {
      id: 'account-2',
      platform: 'FACEBOOK',
      account_name: 'Test Facebook',
      account_id: 'facebook123',
    },
    {
      id: 'account-3',
      platform: 'LINKEDIN',
      account_name: 'Test LinkedIn',
      account_id: 'linkedin123',
    },
  ];

  const defaultProps = {
    socialAccounts: mockSocialAccounts,
    onSubmit: jest.fn().mockResolvedValue(undefined),
    onCancel: jest.fn(),
    loading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render form with all required fields', () => {
    render(<RecurringPostForm {...defaultProps} />);

    // Check form title
    expect(screen.getByText('إنشاء منشورات متكررة')).toBeInTheDocument();

    // Check required fields
    expect(screen.getByLabelText('محتوى المنشور')).toBeInTheDocument();
    expect(screen.getByLabelText('رابط الوسائط (اختياري)')).toBeInTheDocument();
    expect(screen.getByText('الحسابات الاجتماعية')).toBeInTheDocument();
    expect(screen.getByText('نمط التكرار')).toBeInTheDocument();
    expect(screen.getByLabelText('تاريخ البداية')).toBeInTheDocument();
    expect(screen.getByLabelText('تاريخ النهاية (اختياري)')).toBeInTheDocument();

    // Check action buttons
    expect(screen.getByText('إنشاء المنشورات المتكررة')).toBeInTheDocument();
    expect(screen.getByText('إلغاء')).toBeInTheDocument();
  });

  it('should render social accounts with checkboxes', () => {
    render(<RecurringPostForm {...defaultProps} />);

    // Check that all social accounts are rendered
    expect(screen.getByText('Test Twitter')).toBeInTheDocument();
    expect(screen.getByText('Test Facebook')).toBeInTheDocument();
    expect(screen.getByText('Test LinkedIn')).toBeInTheDocument();

    // Check platform badges
    expect(screen.getByText('TWITTER')).toBeInTheDocument();
    expect(screen.getByText('FACEBOOK')).toBeInTheDocument();
    expect(screen.getByText('LINKEDIN')).toBeInTheDocument();
  });

  it('should handle content input and character count', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    const contentTextarea = screen.getByLabelText('محتوى المنشور');
    const testContent = 'This is a test recurring post content';

    await user.type(contentTextarea, testContent);

    expect(contentTextarea).toHaveValue(testContent);
    expect(screen.getByText(`${testContent.length}/2800`)).toBeInTheDocument();
  });

  it('should handle social account selection', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    // Select Twitter account
    const twitterCheckbox = screen.getByRole('checkbox', { name: /Test Twitter/ });
    await user.click(twitterCheckbox);

    expect(twitterCheckbox).toBeChecked();

    // Select Facebook account
    const facebookCheckbox = screen.getByRole('checkbox', { name: /Test Facebook/ });
    await user.click(facebookCheckbox);

    expect(facebookCheckbox).toBeChecked();

    // Unselect Twitter account
    await user.click(twitterCheckbox);
    expect(twitterCheckbox).not.toBeChecked();
  });

  it('should handle frequency selection changes', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    // Check default frequency (daily)
    const frequencySelect = screen.getByDisplayValue('يومي');
    expect(frequencySelect).toBeInTheDocument();

    // Change to weekly
    await user.click(frequencySelect);
    const weeklyOption = screen.getByText('أسبوعي');
    await user.click(weeklyOption);

    // Check that days of week selection appears
    expect(screen.getByText('أيام الأسبوع')).toBeInTheDocument();
    expect(screen.getByText('الأحد')).toBeInTheDocument();
    expect(screen.getByText('الاثنين')).toBeInTheDocument();
    expect(screen.getByText('الثلاثاء')).toBeInTheDocument();
  });

  it('should show days of week selection for weekly frequency', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    // Change to weekly frequency
    const frequencySelect = screen.getByDisplayValue('يومي');
    await user.click(frequencySelect);
    await user.click(screen.getByText('أسبوعي'));

    // Check that all days are available
    const dayButtons = [
      'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 
      'الخميس', 'الجمعة', 'السبت'
    ];

    dayButtons.forEach(day => {
      expect(screen.getByText(day)).toBeInTheDocument();
    });

    // Select some days
    await user.click(screen.getByText('الاثنين'));
    await user.click(screen.getByText('الأربعاء'));
    await user.click(screen.getByText('الجمعة'));

    // Check that selected days have different styling (would need to check classes)
    expect(screen.getByText('الاثنين')).toBeInTheDocument();
  });

  it('should show day of month input for monthly frequency', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    // Change to monthly frequency
    const frequencySelect = screen.getByDisplayValue('يومي');
    await user.click(frequencySelect);
    await user.click(screen.getByText('شهري'));

    // Check that day of month input appears
    expect(screen.getByText('يوم الشهر')).toBeInTheDocument();
    const dayInput = screen.getByDisplayValue('1');
    expect(dayInput).toBeInTheDocument();

    // Change day of month
    await user.clear(dayInput);
    await user.type(dayInput, '15');
    expect(dayInput).toHaveValue(15);
  });

  it('should handle interval and time inputs', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    // Change interval
    const intervalInput = screen.getByDisplayValue('1');
    await user.clear(intervalInput);
    await user.type(intervalInput, '2');
    expect(intervalInput).toHaveValue(2);

    // Change time
    const timeInput = screen.getByDisplayValue('09:00');
    await user.clear(timeInput);
    await user.type(timeInput, '14:30');
    expect(timeInput).toHaveValue('14:30');
  });

  it('should handle date inputs', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    const startDateInput = screen.getByLabelText('تاريخ البداية');
    const endDateInput = screen.getByLabelText('تاريخ النهاية (اختياري)');

    await user.type(startDateInput, '2024-01-01T09:00');
    await user.type(endDateInput, '2024-01-31T09:00');

    expect(startDateInput).toHaveValue('2024-01-01T09:00');
    expect(endDateInput).toHaveValue('2024-01-31T09:00');
  });

  it('should show preview when dates are set', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    // Set start date
    const startDateInput = screen.getByLabelText('تاريخ البداية');
    await user.type(startDateInput, '2024-01-01T09:00');

    // Preview should appear
    await waitFor(() => {
      expect(screen.getByText('معاينة المواعيد (أول 10 منشورات)')).toBeInTheDocument();
    });
  });

  it('should validate required fields on submit', async () => {
    const user = userEvent.setup();
    const { toast } = require('sonner');
    
    render(<RecurringPostForm {...defaultProps} />);

    // Try to submit without content
    const submitButton = screen.getByText('إنشاء المنشورات المتكررة');
    await user.click(submitButton);

    expect(toast.error).toHaveBeenCalledWith('يرجى إدخال محتوى المنشور');
    expect(defaultProps.onSubmit).not.toHaveBeenCalled();
  });

  it('should validate social account selection', async () => {
    const user = userEvent.setup();
    const { toast } = require('sonner');
    
    render(<RecurringPostForm {...defaultProps} />);

    // Add content but no social accounts
    const contentTextarea = screen.getByLabelText('محتوى المنشور');
    await user.type(contentTextarea, 'Test content');

    const submitButton = screen.getByText('إنشاء المنشورات المتكررة');
    await user.click(submitButton);

    expect(toast.error).toHaveBeenCalledWith('يرجى اختيار حساب واحد على الأقل');
    expect(defaultProps.onSubmit).not.toHaveBeenCalled();
  });

  it('should validate start date', async () => {
    const user = userEvent.setup();
    const { toast } = require('sonner');
    
    render(<RecurringPostForm {...defaultProps} />);

    // Add content and select account but no start date
    const contentTextarea = screen.getByLabelText('محتوى المنشور');
    await user.type(contentTextarea, 'Test content');

    const twitterCheckbox = screen.getByRole('checkbox', { name: /Test Twitter/ });
    await user.click(twitterCheckbox);

    const submitButton = screen.getByText('إنشاء المنشورات المتكررة');
    await user.click(submitButton);

    expect(toast.error).toHaveBeenCalledWith('يرجى تحديد تاريخ البداية');
    expect(defaultProps.onSubmit).not.toHaveBeenCalled();
  });

  it('should validate weekly frequency days selection', async () => {
    const user = userEvent.setup();
    const { toast } = require('sonner');
    
    render(<RecurringPostForm {...defaultProps} />);

    // Set up form with weekly frequency but no days selected
    const contentTextarea = screen.getByLabelText('محتوى المنشور');
    await user.type(contentTextarea, 'Test content');

    const twitterCheckbox = screen.getByRole('checkbox', { name: /Test Twitter/ });
    await user.click(twitterCheckbox);

    const startDateInput = screen.getByLabelText('تاريخ البداية');
    await user.type(startDateInput, '2024-01-01T09:00');

    // Change to weekly
    const frequencySelect = screen.getByDisplayValue('يومي');
    await user.click(frequencySelect);
    await user.click(screen.getByText('أسبوعي'));

    const submitButton = screen.getByText('إنشاء المنشورات المتكررة');
    await user.click(submitButton);

    expect(toast.error).toHaveBeenCalledWith('يرجى اختيار أيام الأسبوع للمنشورات الأسبوعية');
    expect(defaultProps.onSubmit).not.toHaveBeenCalled();
  });

  it('should submit valid form data', async () => {
    const user = userEvent.setup();
    const { toast } = require('sonner');
    
    render(<RecurringPostForm {...defaultProps} />);

    // Fill out complete form
    const contentTextarea = screen.getByLabelText('محتوى المنشور');
    await user.type(contentTextarea, 'Test recurring post content');

    const mediaInput = screen.getByLabelText('رابط الوسائط (اختياري)');
    await user.type(mediaInput, 'https://example.com/image.jpg');

    const twitterCheckbox = screen.getByRole('checkbox', { name: /Test Twitter/ });
    await user.click(twitterCheckbox);

    const startDateInput = screen.getByLabelText('تاريخ البداية');
    await user.type(startDateInput, '2024-01-01T09:00');

    const endDateInput = screen.getByLabelText('تاريخ النهاية (اختياري)');
    await user.type(endDateInput, '2024-01-31T09:00');

    const submitButton = screen.getByText('إنشاء المنشورات المتكررة');
    await user.click(submitButton);

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith({
        content: 'Test recurring post content',
        media_url: 'https://example.com/image.jpg',
        social_account_ids: ['account-1'],
        recurring_pattern: {
          frequency: 'daily',
          interval: 1,
          days_of_week: [],
          day_of_month: 1,
          time: '09:00',
        },
        start_date: '2024-01-01T09:00',
        end_date: '2024-01-31T09:00',
        time_zone: 'UTC',
      });
    });

    expect(toast.success).toHaveBeenCalledWith('تم إنشاء المنشورات المتكررة بنجاح');
  });

  it('should handle submit errors', async () => {
    const user = userEvent.setup();
    const { toast } = require('sonner');
    const onSubmitError = jest.fn().mockRejectedValue(new Error('Submit failed'));
    
    render(<RecurringPostForm {...defaultProps} onSubmit={onSubmitError} />);

    // Fill out minimal valid form
    const contentTextarea = screen.getByLabelText('محتوى المنشور');
    await user.type(contentTextarea, 'Test content');

    const twitterCheckbox = screen.getByRole('checkbox', { name: /Test Twitter/ });
    await user.click(twitterCheckbox);

    const startDateInput = screen.getByLabelText('تاريخ البداية');
    await user.type(startDateInput, '2024-01-01T09:00');

    const submitButton = screen.getByText('إنشاء المنشورات المتكررة');
    await user.click(submitButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('فشل في إنشاء المنشورات المتكررة');
    });
  });

  it('should handle cancel action', async () => {
    const user = userEvent.setup();
    render(<RecurringPostForm {...defaultProps} />);

    const cancelButton = screen.getByText('إلغاء');
    await user.click(cancelButton);

    expect(defaultProps.onCancel).toHaveBeenCalled();
  });

  it('should show loading state', () => {
    render(<RecurringPostForm {...defaultProps} loading={true} />);

    expect(screen.getByText('جاري الإنشاء...')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /جاري الإنشاء/ })).toBeDisabled();
  });
});
