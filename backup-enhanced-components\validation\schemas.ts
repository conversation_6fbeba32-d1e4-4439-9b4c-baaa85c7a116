import { z } from 'zod';

// Social Account Schemas
export const socialAccountSchema = z.object({
  platform: z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'SNAPCHAT']),
  accountId: z.string().min(1, 'Account ID is required'),
  accountName: z.string().min(1, 'Account name is required'),
  accessToken: z.string().min(1, 'Access token is required'),
  refreshToken: z.string().optional(),
});

export const disconnectSocialAccountSchema = z.object({
  accountId: z.string().uuid('Invalid account ID format'),
});

// Post Schemas
export const createPostSchema = z.object({
  content: z.string().min(1, 'Content is required').max(2000, 'Content too long'),
  scheduledFor: z.string().datetime().optional(),
  socialAccountIds: z.array(z.string().uuid()).min(1, 'At least one social account required'),
  mediaUrls: z.array(z.string().url()).optional(),
  tags: z.array(z.string()).optional(),
});

export const updatePostSchema = z.object({
  content: z.string().min(1, 'Content is required').max(2000, 'Content too long').optional(),
  scheduledFor: z.string().datetime().optional(),
  socialAccountIds: z.array(z.string().uuid()).optional(),
  mediaUrls: z.array(z.string().url()).optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED', 'FAILED']).optional(),
});

export const publishPostSchema = z.object({
  postId: z.string().uuid('Invalid post ID format'),
});

// Twitter OAuth Schemas
export const twitterOAuthCompleteSchema = z.object({
  oauth_token: z.string().min(1, 'OAuth token is required'),
  oauth_verifier: z.string().min(1, 'OAuth verifier is required'),
  oauth_token_secret: z.string().min(1, 'OAuth token secret is required'),
});

// Authentication Schemas
export const signUpSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name too long'),
  email: z.string().email('Invalid email format').toLowerCase(),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const signInSchema = z.object({
  email: z.string().email('Invalid email format').toLowerCase(),
  password: z.string().min(1, 'Password is required'),
});

export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format').toLowerCase(),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// User Profile Schemas
export const updateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  email: z.string().email('Invalid email format').optional(),
  timezone: z.string().optional(),
  language: z.enum(['en', 'ar']).optional(),
});

// Subscription Schemas
export const createSubscriptionSchema = z.object({
  priceId: z.string().min(1, 'Price ID is required'),
  successUrl: z.string().url('Invalid success URL').optional(),
  cancelUrl: z.string().url('Invalid cancel URL').optional(),
});

// Analytics Schemas
export const analyticsQuerySchema = z.object({
  startDate: z.string().datetime('Invalid start date'),
  endDate: z.string().datetime('Invalid end date'),
  platform: z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'SNAPCHAT']).optional(),
  metrics: z.array(z.enum(['impressions', 'engagements', 'clicks', 'shares'])).optional(),
});

// Media Upload Schemas
export const uploadMediaSchema = z.object({
  fileName: z.string().min(1, 'File name is required'),
  fileType: z.string().regex(/^(image|video)\//, 'Invalid file type'),
  fileSize: z.number().max(50 * 1024 * 1024, 'File too large (max 50MB)'),
});

// Caption Generation Schemas
export const generateCaptionSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required').max(500, 'Prompt too long'),
  language: z.enum(['en', 'ar']).default('ar'),
  tone: z.enum(['professional', 'casual', 'humorous', 'inspirational']).default('professional'),
  hashtags: z.boolean().default(true),
  maxLength: z.number().min(50).max(2000).default(280),
});

// Webhook Schemas
export const stripeWebhookSchema = z.object({
  id: z.string(),
  object: z.literal('event'),
  type: z.string(),
  data: z.object({
    object: z.any(),
  }),
});

// Admin Schemas
export const adminUserSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email format'),
  role: z.enum(['USER', 'ADMIN']).default('USER'),
});

// Search and Filter Schemas
export const searchPostsSchema = z.object({
  query: z.string().optional(),
  status: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED', 'FAILED']).optional(),
  platform: z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'SNAPCHAT']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

// Activity Log Schemas
export const activityLogSchema = z.object({
  action: z.enum([
    'CONNECT_SOCIAL',
    'DISCONNECT_SOCIAL',
    'POST_CREATED',
    'POST_PUBLISHED',
    'POST_FAILED',
    'SUBSCRIPTION_CREATED',
    'SUBSCRIPTION_CANCELLED',
    'LOGIN',
    'LOGOUT'
  ]),
  details: z.string().max(500, 'Details too long'),
  metadata: z.record(z.any()).optional(),
});

// Export all schemas for easy access
export const schemas = {
  // Authentication
  signUp: signUpSchema,
  signIn: signInSchema,
  forgotPassword: forgotPasswordSchema,
  resetPassword: resetPasswordSchema,
  changePassword: changePasswordSchema,
  // Social accounts
  socialAccount: socialAccountSchema,
  disconnectSocialAccount: disconnectSocialAccountSchema,
  // Posts
  createPost: createPostSchema,
  updatePost: updatePostSchema,
  publishPost: publishPostSchema,
  // OAuth
  twitterOAuthComplete: twitterOAuthCompleteSchema,
  // User management
  updateProfile: updateProfileSchema,
  adminUser: adminUserSchema,
  // Subscriptions
  createSubscription: createSubscriptionSchema,
  // Analytics
  analyticsQuery: analyticsQuerySchema,
  // Media
  uploadMedia: uploadMediaSchema,
  // AI
  generateCaption: generateCaptionSchema,
  // Webhooks
  stripeWebhook: stripeWebhookSchema,
  // Search
  searchPosts: searchPostsSchema,
  // Activity
  activityLog: activityLogSchema,
};

// Type exports for TypeScript
export type SignUpInput = z.infer<typeof signUpSchema>;
export type SignInInput = z.infer<typeof signInSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type SocialAccountInput = z.infer<typeof socialAccountSchema>;
export type CreatePostInput = z.infer<typeof createPostSchema>;
export type UpdatePostInput = z.infer<typeof updatePostSchema>;
export type PublishPostInput = z.infer<typeof publishPostSchema>;
export type TwitterOAuthCompleteInput = z.infer<typeof twitterOAuthCompleteSchema>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type CreateSubscriptionInput = z.infer<typeof createSubscriptionSchema>;
export type AnalyticsQueryInput = z.infer<typeof analyticsQuerySchema>;
export type UploadMediaInput = z.infer<typeof uploadMediaSchema>;
export type GenerateCaptionInput = z.infer<typeof generateCaptionSchema>;
export type SearchPostsInput = z.infer<typeof searchPostsSchema>;
export type ActivityLogInput = z.infer<typeof activityLogSchema>;
