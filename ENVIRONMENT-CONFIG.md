# 🔧 eWasl Environment Variables Configuration

## 📋 PRODUCTION DEPLOYMENT CHECKLIST

### **🌐 DigitalOcean App Platform Configuration**

Add these environment variables to your DigitalOcean app settings:

**Navigate to:** https://cloud.digitalocean.com/apps/92d1f7b6-85f2-47e2-8a69-d823b1586159/settings

---

## **🗄️ DATABASE CONFIGURATION**

```bash
# Supabase Database
NEXT_PUBLIC_SUPABASE_URL=https://ajpcbugydftdyhlbddpl.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dHPGCdWDJV-BEeQTnNhSDcck8PnvRMYKCIo2xn49Yqg
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc
```

---

## **💳 STRIPE PAYMENT CONFIGURATION**

```bash
# Stripe Payment Processing
STRIPE_SECRET_KEY=sk_live_51RATy6ApPIt9xqOO...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51RATy6ApPIt9xqOO...
STRIPE_WEBHOOK_SECRET=whsec_...
```

**⚠️ Important:** Use your actual Stripe live keys for production

---

## **🐦 SOCIAL MEDIA API CONFIGURATION**

### **Twitter/X Integration**
```bash
# Production Twitter API Keys (from https://developer.twitter.com)
TWITTER_API_KEY=K1PnzsvQ5hHMPWdYdKHRMTQVf
TWITTER_API_SECRET=your_actual_twitter_api_secret_from_developer_portal
TWITTER_BEARER_TOKEN=your_actual_twitter_bearer_token_from_developer_portal
```

**Setup Instructions:**
1. Go to https://developer.twitter.com/en/portal/dashboard
2. Create a new app or use existing app
3. Generate API Key, API Secret, and Bearer Token
4. Enable OAuth 1.0a for user authentication
5. Set callback URL to: `https://app.ewasl.com/api/social/callback/twitter`

### **Facebook/Instagram Integration**
```bash
# Production Facebook API Keys (from https://developers.facebook.com)
FACEBOOK_APP_ID=1366325774493759
FACEBOOK_APP_SECRET=your_actual_facebook_app_secret_from_developer_console
```

**Setup Instructions:**
1. Go to https://developers.facebook.com/apps/
2. Use existing app ID: 1366325774493759 or create new app
3. Add Facebook Login and Instagram Basic Display products
4. Set redirect URIs to: `https://app.ewasl.com/api/social/callback/facebook`
5. Request permissions: `pages_manage_posts`, `pages_read_engagement`, `instagram_basic`, `instagram_content_publish`

### **LinkedIn Integration**
```bash
# Production LinkedIn API Keys (from https://developer.linkedin.com)
LINKEDIN_CLIENT_ID=787coegnsdocvq
LINKEDIN_CLIENT_SECRET=your_actual_linkedin_client_secret_from_developer_portal
```

**Setup Instructions:**
1. Go to https://developer.linkedin.com/apps
2. Use existing app ID: 787coegnsdocvq or create new app
3. Add Sign In with LinkedIn and Share on LinkedIn products
4. Set redirect URL to: `https://app.ewasl.com/api/social/callback/linkedin`
5. Request permissions: `r_liteprofile`, `r_emailaddress`, `w_member_social`

---

## **📧 EMAIL CONFIGURATION**

### **SendGrid SMTP (Recommended)**
```bash
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=eWasl Social Scheduler
```

---

## **🔐 AUTHENTICATION CONFIGURATION**

```bash
# NextAuth.js Configuration
NEXTAUTH_URL=https://app.ewasl.com
NEXTAUTH_SECRET=your_nextauth_secret_key_here
```

**Generate NEXTAUTH_SECRET:**
```bash
openssl rand -base64 32
```

---

## **🌐 APPLICATION CONFIGURATION**

```bash
# Application URLs
NEXT_PUBLIC_APP_URL=https://app.ewasl.com
NEXT_PUBLIC_API_URL=https://app.ewasl.com/api

# Environment
NODE_ENV=production
```

---

## **📊 ANALYTICS & MONITORING**

```bash
# Optional: Analytics Configuration
GOOGLE_ANALYTICS_ID=your_ga_id
SENTRY_DSN=your_sentry_dsn
```

---

## **🔧 DEPLOYMENT STEPS**

### **1. DigitalOcean App Platform**
1. Navigate to: https://cloud.digitalocean.com/apps/92d1f7b6-85f2-47e2-8a69-d823b1586159/settings
2. Go to "Environment Variables" section
3. Add all the variables listed above
4. Click "Save" and redeploy

### **2. Supabase SMTP Configuration**
1. Open: https://supabase.com/dashboard/project/ajpcbugydftdyhlbddpl/auth/providers
2. Scroll to "SMTP Settings"
3. Configure SendGrid SMTP:
   - **Host:** smtp.sendgrid.net
   - **Port:** 587
   - **Username:** apikey
   - **Password:** [Your SendGrid API Key]
   - **Sender Email:** <EMAIL>
   - **Sender Name:** eWasl Social Scheduler

### **3. Stripe Webhook Configuration**
1. Open Stripe Dashboard: https://dashboard.stripe.com/webhooks
2. Create new webhook endpoint: `https://app.ewasl.com/api/stripe/webhook`
3. Select events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
4. Copy webhook secret to `STRIPE_WEBHOOK_SECRET`

### **4. Social Media App Configuration**

#### **Twitter Developer Portal**
1. Update callback URLs to: `https://app.ewasl.com/api/auth/twitter/callback`
2. Update website URL to: `https://app.ewasl.com`

#### **Facebook Developer Console**
1. Update Valid OAuth Redirect URIs: `https://app.ewasl.com/api/auth/facebook/callback`
2. Update App Domains: `app.ewasl.com`

#### **LinkedIn Developer Console**
1. Update Authorized Redirect URLs: `https://app.ewasl.com/api/auth/linkedin/callback`

---

## **✅ VERIFICATION CHECKLIST**

After configuration, verify:

- [ ] Application loads at https://app.ewasl.com
- [ ] User registration works with valid emails
- [ ] Stripe payment processing functional
- [ ] Twitter account connection works
- [ ] Email sending works (test with valid email)
- [ ] Database operations successful
- [ ] All API endpoints responding

---

## **🚨 SECURITY NOTES**

1. **Never commit secrets to Git**
2. **Use environment variables for all sensitive data**
3. **Regularly rotate API keys**
4. **Monitor for unauthorized access**
5. **Use HTTPS for all production traffic**

---

## **📞 SUPPORT CONTACTS**

- **Supabase Support:** https://supabase.com/support
- **Stripe Support:** https://support.stripe.com
- **DigitalOcean Support:** https://www.digitalocean.com/support

---

**🎉 After completing this configuration, eWasl will be fully operational in production!**
