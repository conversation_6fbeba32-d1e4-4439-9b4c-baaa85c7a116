/**
 * Business Account Types
 * Type definitions for business account management across platforms
 */

export interface BusinessAccount {
  id: string;
  name: string;
  platform: 'FACEBOOK' | 'LINKEDIN' | 'INSTAGRAM' | 'TWITTER';
  type: 'PERSONAL' | 'BUSINESS' | 'PAGE' | 'COMPANY';
  accessToken?: string;
  permissions: string[];
  metadata: Record<string, any>;
  isSelected: boolean;
  isVerified?: boolean;
  followerCount?: number;
  profilePictureUrl?: string;
  website?: string;
  category?: string;
  description?: string;
}

export interface FacebookBusinessPage extends BusinessAccount {
  platform: 'FACEBOOK';
  type: 'PAGE';
  pageId: string;
  pageName: string;
  pageAccessToken: string;
  pageCategory: string;
  fanCount: number;
  tasks: string[];
  metadata: {
    pictureUrl?: string;
    website?: string;
    isVerified: boolean;
    about?: string;
    phone?: string;
    email?: string;
  };
}

export interface LinkedInCompanyPage extends BusinessAccount {
  platform: 'LINKEDIN';
  type: 'COMPANY';
  organizationId: string;
  organizationName: string;
  organizationUrn: string;
  followerCount: number;
  permissions: string[];
  metadata: {
    logoUrl?: string;
    website?: string;
    industry?: string;
    description?: string;
    employeeCount?: string;
    headquarters?: {
      country: string;
      city: string;
    };
  };
}

export interface InstagramBusinessAccount extends BusinessAccount {
  platform: 'INSTAGRAM';
  type: 'BUSINESS';
  instagramAccountId: string;
  instagramUsername: string;
  connectedFacebookPageId: string;
  mediaCount: number;
  metadata: {
    profilePictureUrl?: string;
    biography?: string;
    website?: string;
    isVerified: boolean;
    accountType: 'BUSINESS' | 'CREATOR';
  };
}

export interface TwitterBusinessAccount extends BusinessAccount {
  platform: 'TWITTER';
  type: 'BUSINESS';
  twitterUserId: string;
  twitterUsername: string;
  followerCount: number;
  metadata: {
    profileImageUrl?: string;
    description?: string;
    website?: string;
    isVerified: boolean;
    location?: string;
    tweetCount: number;
  };
}

export interface BusinessAccountSelection {
  socialAccountId: string;
  platform: string;
  accountType: 'PERSONAL' | 'BUSINESS';
  selectedBusinessId?: string;
  configuration: {
    postingPreferences: {
      defaultAccount: string;
      allowPersonalFallback: boolean;
    };
    permissions: {
      canPost: boolean;
      canSchedule: boolean;
      canAnalyze: boolean;
    };
  };
}

export interface BusinessAccountConfig {
  platform: string;
  isConfigured: boolean;
  hasBusinessAccounts: boolean;
  businessAccounts: BusinessAccount[];
  selectedAccount?: BusinessAccount;
  requiresReconnection: boolean;
  missingPermissions: string[];
  lastUpdated: string;
}

export interface PlatformBusinessRequirements {
  platform: string;
  requiredScopes: string[];
  businessAccountTypes: string[];
  features: {
    pageSelection: boolean;
    companySelection: boolean;
    multipleAccounts: boolean;
    permissionValidation: boolean;
  };
  apiEndpoints: {
    fetchAccounts: string;
    validatePermissions: string;
    refreshTokens: string;
  };
}

// Platform-specific requirements
export const PLATFORM_BUSINESS_REQUIREMENTS: Record<string, PlatformBusinessRequirements> = {
  FACEBOOK: {
    platform: 'FACEBOOK',
    requiredScopes: ['pages_manage_posts', 'pages_read_engagement', 'business_management'],
    businessAccountTypes: ['PAGE'],
    features: {
      pageSelection: true,
      companySelection: false,
      multipleAccounts: true,
      permissionValidation: true,
    },
    apiEndpoints: {
      fetchAccounts: 'https://graph.facebook.com/v19.0/me/accounts',
      validatePermissions: 'https://graph.facebook.com/v19.0/{page-id}?fields=tasks',
      refreshTokens: 'https://graph.facebook.com/v19.0/oauth/access_token',
    },
  },
  LINKEDIN: {
    platform: 'LINKEDIN',
    requiredScopes: ['openid', 'profile', 'w_member_social', 'w_organization_social', 'email'],
    businessAccountTypes: ['COMPANY'],
    features: {
      pageSelection: false,
      companySelection: true,
      multipleAccounts: true,
      permissionValidation: true,
    },
    apiEndpoints: {
      fetchAccounts: 'https://api.linkedin.com/v2/organizationAcls',
      validatePermissions: 'https://api.linkedin.com/v2/organizationAcls',
      refreshTokens: 'https://www.linkedin.com/oauth/v2/accessToken',
    },
  },
  INSTAGRAM: {
    platform: 'INSTAGRAM',
    requiredScopes: ['instagram_graph_user_profile', 'instagram_content_publish', 'pages_read_engagement'],
    businessAccountTypes: ['BUSINESS'],
    features: {
      pageSelection: false,
      companySelection: false,
      multipleAccounts: true,
      permissionValidation: true,
    },
    apiEndpoints: {
      fetchAccounts: 'https://graph.facebook.com/v19.0/me/accounts',
      validatePermissions: 'https://graph.facebook.com/v19.0/{instagram-account-id}',
      refreshTokens: 'https://graph.facebook.com/v19.0/oauth/access_token',
    },
  },
  TWITTER: {
    platform: 'TWITTER',
    requiredScopes: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
    businessAccountTypes: ['BUSINESS'],
    features: {
      pageSelection: false,
      companySelection: false,
      multipleAccounts: false,
      permissionValidation: true,
    },
    apiEndpoints: {
      fetchAccounts: 'https://api.twitter.com/2/users/me',
      validatePermissions: 'https://api.twitter.com/2/users/me',
      refreshTokens: 'https://api.twitter.com/2/oauth2/token',
    },
  },
};

// Business account validation rules
export const BUSINESS_ACCOUNT_VALIDATION = {
  FACEBOOK: {
    requiredTasks: ['MANAGE', 'CREATE_CONTENT'],
    minimumFollowers: 0,
    requiredVerification: false,
  },
  LINKEDIN: {
    requiredRoles: ['ADMINISTRATOR', 'CONTENT_ADMIN'],
    minimumFollowers: 0,
    requiredVerification: false,
  },
  INSTAGRAM: {
    requiredAccountType: ['BUSINESS', 'CREATOR'],
    minimumFollowers: 0,
    requiredVerification: false,
  },
  TWITTER: {
    requiredPermissions: ['tweet.write'],
    minimumFollowers: 0,
    requiredVerification: false,
  },
};

// Error types for business account operations
export interface BusinessAccountError {
  code: string;
  message: string;
  platform: string;
  accountId?: string;
  details?: Record<string, any>;
}

export const BUSINESS_ACCOUNT_ERRORS = {
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_NOT_FOUND: 'ACCOUNT_NOT_FOUND',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  API_RATE_LIMITED: 'API_RATE_LIMITED',
  PLATFORM_ERROR: 'PLATFORM_ERROR',
  CONFIGURATION_INVALID: 'CONFIGURATION_INVALID',
  BUSINESS_ACCOUNT_REQUIRED: 'BUSINESS_ACCOUNT_REQUIRED',
} as const;

export type BusinessAccountErrorCode = typeof BUSINESS_ACCOUNT_ERRORS[keyof typeof BUSINESS_ACCOUNT_ERRORS];
