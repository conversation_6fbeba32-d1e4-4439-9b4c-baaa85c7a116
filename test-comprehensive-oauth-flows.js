#!/usr/bin/env node

/**
 * Comprehensive OAuth Flow and Social Media Integration Testing
 * Tests all enhanced providers and OAuth callback consistency
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
const LOCAL_URL = 'http://localhost:3000';

// Test Results Storage
const testResults = {
  healthChecks: [],
  oauthFlows: [],
  socialIntegrations: [],
  enhancedProviders: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  }
};

// Utility Functions
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-OAuth-Tester/1.0',
        ...options.headers
      },
      timeout: 10000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: e.message
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

function logTest(category, name, status, details = '') {
  const timestamp = new Date().toISOString();
  const result = {
    timestamp,
    category,
    name,
    status,
    details
  };
  
  console.log(`[${timestamp}] ${category}: ${name} - ${status}`);
  if (details) console.log(`  Details: ${details}`);
  
  testResults[category].push(result);
  testResults.summary.total++;
  
  if (status === 'PASS') testResults.summary.passed++;
  else if (status === 'FAIL') testResults.summary.failed++;
  else if (status === 'WARN') testResults.summary.warnings++;
}

// Test Functions
async function testHealthEndpoints() {
  console.log('\n🏥 TESTING HEALTH ENDPOINTS...\n');
  
  const endpoints = [
    '/api/health',
    '/api/system/health',
    '/api/scheduler/health',
    '/api/billing/health'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint}`);
      
      if (response.status === 200) {
        logTest('healthChecks', endpoint, 'PASS', `Status: ${response.status}`);
      } else {
        logTest('healthChecks', endpoint, 'FAIL', `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('healthChecks', endpoint, 'FAIL', `Error: ${error.message}`);
    }
  }
}

async function testOAuthCallbackConsistency() {
  console.log('\n🔐 TESTING OAUTH CALLBACK URL CONSISTENCY...\n');
  
  const platforms = ['twitter', 'facebook', 'instagram', 'linkedin'];
  
  for (const platform of platforms) {
    try {
      const callbackUrl = `${BASE_URL}/api/social/callback/${platform}`;
      const response = await makeRequest(callbackUrl);
      
      // We expect these to return 400 or 401 (missing params) rather than 404
      if (response.status === 404) {
        logTest('oauthFlows', `${platform}-callback`, 'FAIL', 
          `Callback endpoint not found: ${response.status}`);
      } else if (response.status === 400 || response.status === 401) {
        logTest('oauthFlows', `${platform}-callback`, 'PASS', 
          `Callback endpoint exists: ${response.status}`);
      } else {
        logTest('oauthFlows', `${platform}-callback`, 'WARN', 
          `Unexpected status: ${response.status}`);
      }
    } catch (error) {
      logTest('oauthFlows', `${platform}-callback`, 'FAIL', 
        `Error: ${error.message}`);
    }
  }
}

async function testSocialMediaIntegrations() {
  console.log('\n📱 TESTING SOCIAL MEDIA INTEGRATION ENDPOINTS...\n');
  
  const endpoints = [
    '/api/social/accounts',
    '/api/social/config/validate',
    '/api/social/enhanced/test',
    '/api/test/social-media-integrations'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint}`);
      
      if (response.status === 200 || response.status === 401) {
        logTest('socialIntegrations', endpoint, 'PASS', 
          `Endpoint accessible: ${response.status}`);
      } else {
        logTest('socialIntegrations', endpoint, 'FAIL', 
          `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('socialIntegrations', endpoint, 'FAIL', 
        `Error: ${error.message}`);
    }
  }
}

async function testEnhancedProviders() {
  console.log('\n🚀 TESTING ENHANCED PROVIDER ENDPOINTS...\n');
  
  const providerTests = [
    '/api/test/facebook-connectivity',
    '/api/test/linkedin-posting',
    '/api/social/enhanced/connect',
    '/api/social/enhanced/publish'
  ];
  
  for (const endpoint of providerTests) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint}`);
      
      if (response.status === 200 || response.status === 401 || response.status === 400) {
        logTest('enhancedProviders', endpoint, 'PASS', 
          `Provider endpoint accessible: ${response.status}`);
      } else {
        logTest('enhancedProviders', endpoint, 'FAIL', 
          `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('enhancedProviders', endpoint, 'FAIL', 
        `Error: ${error.message}`);
    }
  }
}

async function testJobQueueSystem() {
  console.log('\n⏰ TESTING JOB QUEUE SYSTEM...\n');
  
  const queueEndpoints = [
    '/api/scheduler/status',
    '/api/scheduler/health',
    '/api/scheduler/jobs'
  ];
  
  for (const endpoint of queueEndpoints) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint}`);
      
      if (response.status === 200 || response.status === 401) {
        logTest('enhancedProviders', `queue-${endpoint}`, 'PASS', 
          `Queue endpoint accessible: ${response.status}`);
      } else {
        logTest('enhancedProviders', `queue-${endpoint}`, 'FAIL', 
          `Status: ${response.status}`);
      }
    } catch (error) {
      logTest('enhancedProviders', `queue-${endpoint}`, 'FAIL', 
        `Error: ${error.message}`);
    }
  }
}

// Main Test Runner
async function runComprehensiveTests() {
  console.log('🧪 STARTING COMPREHENSIVE OAUTH & SOCIAL MEDIA TESTING');
  console.log('=' .repeat(60));
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`Started at: ${new Date().toISOString()}`);
  
  try {
    await testHealthEndpoints();
    await testOAuthCallbackConsistency();
    await testSocialMediaIntegrations();
    await testEnhancedProviders();
    await testJobQueueSystem();
    
    // Generate Summary Report
    console.log('\n📊 TEST SUMMARY REPORT');
    console.log('=' .repeat(60));
    console.log(`Total Tests: ${testResults.summary.total}`);
    console.log(`✅ Passed: ${testResults.summary.passed}`);
    console.log(`❌ Failed: ${testResults.summary.failed}`);
    console.log(`⚠️  Warnings: ${testResults.summary.warnings}`);
    
    const successRate = ((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1);
    console.log(`📈 Success Rate: ${successRate}%`);
    
    // Detailed Results
    console.log('\n📋 DETAILED RESULTS BY CATEGORY:');
    
    Object.keys(testResults).forEach(category => {
      if (category === 'summary') return;
      
      const categoryResults = testResults[category];
      if (categoryResults.length > 0) {
        console.log(`\n${category.toUpperCase()}:`);
        categoryResults.forEach(result => {
          const icon = result.status === 'PASS' ? '✅' : 
                      result.status === 'FAIL' ? '❌' : '⚠️';
          console.log(`  ${icon} ${result.name}: ${result.status}`);
          if (result.details) {
            console.log(`     ${result.details}`);
          }
        });
      }
    });
    
    console.log('\n🏁 TESTING COMPLETE');
    console.log(`Finished at: ${new Date().toISOString()}`);
    
    // Exit with appropriate code
    process.exit(testResults.summary.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ CRITICAL ERROR DURING TESTING:', error);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runComprehensiveTests();
}

module.exports = {
  runComprehensiveTests,
  testResults
};
