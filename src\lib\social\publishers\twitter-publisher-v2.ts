/**
 * Enhanced Twitter Publisher V2
 * Real API implementation for Twitter API v2 posting with media support
 * Replaces mock implementation and OAuth 1.0a
 */

export interface SocialAccount {
  id: string;
  platform: string;
  access_token: string;
  account_id: string;
  account_name: string;
}

export interface PostContent {
  content: string;
  mediaUrl?: string;
  mediaType?: 'IMAGE' | 'VIDEO' | 'GIF';
}

export interface PublishResult {
  success: boolean;
  postId?: string;
  url?: string;
  error?: string;
  platformResponse?: any;
}

export interface TwitterMediaUpload {
  media_id: string;
  media_id_string: string;
  size: number;
  expires_after_secs: number;
}

export class TwitterPublisherV2 {
  private readonly apiUrl = 'https://api.twitter.com/2';
  private readonly uploadUrl = 'https://upload.twitter.com/1.1';

  /**
   * Publish a tweet using Twitter API v2
   */
  async publishPost(account: SocialAccount, content: PostContent): Promise<PublishResult> {
    try {
      console.log('Publishing to Twitter:', {
        accountId: account.account_id,
        contentLength: content.content.length,
        hasMedia: !!content.mediaUrl
      });

      // Validate token first
      await this.validateToken(account.access_token);

      let tweetData: any = {
        text: content.content
      };

      // Handle media upload if present
      if (content.mediaUrl) {
        try {
          const mediaId = await this.uploadMedia(account, content.mediaUrl, content.mediaType);
          tweetData.media = {
            media_ids: [mediaId]
          };
        } catch (mediaError) {
          console.warn('Media upload failed, posting without media:', mediaError);
        }
      }

      // Post tweet
      const response = await fetch(`${this.apiUrl}/tweets`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tweetData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Twitter API error: ${errorData.detail || errorData.title || response.statusText}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(`Twitter posting error: ${result.errors[0].detail}`);
      }

      const postId = result.data.id;
      const postUrl = `https://twitter.com/${account.account_name}/status/${postId}`;

      console.log('Twitter post published successfully:', {
        postId,
        postUrl
      });

      return {
        success: true,
        postId,
        url: postUrl,
        platformResponse: result
      };

    } catch (error) {
      console.error('Twitter publishing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Upload media to Twitter
   */
  private async uploadMedia(account: SocialAccount, mediaUrl: string, mediaType?: string): Promise<string> {
    try {
      console.log('Uploading media to Twitter:', { mediaUrl, mediaType });

      // Step 1: Download media from URL
      const mediaResponse = await fetch(mediaUrl);
      if (!mediaResponse.ok) {
        throw new Error(`Failed to download media: ${mediaResponse.statusText}`);
      }

      const mediaBuffer = await mediaResponse.arrayBuffer();
      const mediaSize = mediaBuffer.byteLength;

      // Step 2: Initialize upload
      const initResponse = await fetch(`${this.uploadUrl}/media/upload.json`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          command: 'INIT',
          total_bytes: mediaSize.toString(),
          media_type: this.getMediaMimeType(mediaUrl, mediaType)
        })
      });

      if (!initResponse.ok) {
        throw new Error(`Failed to initialize upload: ${initResponse.statusText}`);
      }

      const initData = await initResponse.json();
      const mediaId = initData.media_id_string;

      // Step 3: Upload media in chunks (for large files)
      const chunkSize = 5 * 1024 * 1024; // 5MB chunks
      const chunks = Math.ceil(mediaSize / chunkSize);

      for (let i = 0; i < chunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, mediaSize);
        const chunk = mediaBuffer.slice(start, end);

        const formData = new FormData();
        formData.append('command', 'APPEND');
        formData.append('media_id', mediaId);
        formData.append('segment_index', i.toString());
        formData.append('media', new Blob([chunk]));

        const appendResponse = await fetch(`${this.uploadUrl}/media/upload.json`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${account.access_token}`
          },
          body: formData
        });

        if (!appendResponse.ok) {
          throw new Error(`Failed to upload chunk ${i}: ${appendResponse.statusText}`);
        }
      }

      // Step 4: Finalize upload
      const finalizeResponse = await fetch(`${this.uploadUrl}/media/upload.json`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.access_token}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          command: 'FINALIZE',
          media_id: mediaId
        })
      });

      if (!finalizeResponse.ok) {
        throw new Error(`Failed to finalize upload: ${finalizeResponse.statusText}`);
      }

      const finalizeData = await finalizeResponse.json();

      // Step 5: Wait for processing if needed (for videos)
      if (finalizeData.processing_info) {
        await this.waitForProcessing(mediaId, account.access_token);
      }

      console.log('Twitter media uploaded successfully:', mediaId);
      return mediaId;

    } catch (error) {
      console.error('Twitter media upload error:', error);
      throw error;
    }
  }

  /**
   * Wait for media processing to complete
   */
  private async waitForProcessing(mediaId: string, accessToken: string, maxAttempts: number = 10): Promise<void> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response = await fetch(
          `${this.uploadUrl}/media/upload.json?command=STATUS&media_id=${mediaId}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          
          if (data.processing_info) {
            if (data.processing_info.state === 'succeeded') {
              return;
            } else if (data.processing_info.state === 'failed') {
              throw new Error('Media processing failed');
            }
            
            // Wait for the recommended time
            const waitTime = data.processing_info.check_after_secs || 5;
            await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
          } else {
            // No processing info means it's ready
            return;
          }
        }
      } catch (error) {
        console.warn(`Processing status check attempt ${attempt + 1} failed:`, error);
      }
    }

    throw new Error('Media processing did not complete within timeout');
  }

  /**
   * Get media MIME type
   */
  private getMediaMimeType(url: string, mediaType?: string): string {
    if (mediaType === 'VIDEO') return 'video/mp4';
    if (mediaType === 'GIF') return 'image/gif';
    
    const urlLower = url.toLowerCase();
    if (urlLower.includes('.mp4') || urlLower.includes('.mov')) return 'video/mp4';
    if (urlLower.includes('.gif')) return 'image/gif';
    if (urlLower.includes('.png')) return 'image/png';
    if (urlLower.includes('.jpg') || urlLower.includes('.jpeg')) return 'image/jpeg';
    
    return 'image/jpeg'; // Default
  }

  /**
   * Validate Twitter access token
   */
  private async validateToken(accessToken: string): Promise<void> {
    try {
      const response = await fetch(`${this.apiUrl}/users/me`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Twitter token is invalid or expired');
        }
        throw new Error(`Twitter token validation failed: ${response.statusText}`);
      }

      const userData = await response.json();
      
      if (userData.errors) {
        throw new Error(`Twitter API error: ${userData.errors[0].detail}`);
      }

    } catch (error) {
      console.error('Twitter token validation error:', error);
      throw error;
    }
  }

  /**
   * Get Twitter post analytics
   */
  async getPostAnalytics(tweetId: string, accessToken: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.apiUrl}/tweets/${tweetId}?tweet.fields=public_metrics,non_public_metrics,organic_metrics`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.errors) {
        throw new Error(`Twitter analytics error: ${data.errors[0].detail}`);
      }

      return data;

    } catch (error) {
      console.error('Twitter analytics fetch error:', error);
      throw error;
    }
  }

  /**
   * Test Twitter connection
   */
  async testConnection(account: SocialAccount): Promise<any> {
    try {
      console.log('Testing Twitter connection:', {
        accountId: account.account_id
      });

      const response = await fetch(
        `${this.apiUrl}/users/me?user.fields=id,name,username,public_metrics,profile_image_url`,
        {
          headers: {
            'Authorization': `Bearer ${account.access_token}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Connection test failed: ${response.statusText}`);
      }

      const userData = await response.json();
      
      if (userData.errors) {
        throw new Error(`Twitter API error: ${userData.errors[0].detail}`);
      }

      console.log('Twitter connection test successful:', {
        username: userData.data.username,
        name: userData.data.name
      });

      return {
        success: true,
        accountInfo: {
          id: userData.data.id,
          name: userData.data.name,
          username: userData.data.username,
          profileImageUrl: userData.data.profile_image_url,
          publicMetrics: userData.data.public_metrics
        }
      };

    } catch (error) {
      console.error('Twitter connection test failed:', error);
      throw error;
    }
  }

  /**
   * Format content for Twitter (character limits, etc.)
   */
  formatContent(content: string): { text: string; isValid: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let formattedText = content;

    // Twitter has a 280 character limit
    if (formattedText.length > 280) {
      formattedText = formattedText.substring(0, 277) + '...';
      warnings.push('Content was truncated to fit Twitter character limit');
    }

    return {
      text: formattedText,
      isValid: formattedText.length > 0 && formattedText.length <= 280,
      warnings
    };
  }
}

export default TwitterPublisherV2;
