#!/usr/bin/env node

/**
 * API Integration Verification Script
 * Comprehensive verification that both APIs are properly integrated
 */

const { testDigitalOcean, testStripe } = require('./simple-api-test');
const fs = require('fs');
const path = require('path');

/**
 * Verify environment configuration
 */
function verifyEnvironmentConfig() {
  console.log('🔧 Verifying Environment Configuration...');
  
  const appSpecPath = path.join(process.cwd(), 'app-spec.yaml');
  
  if (!fs.existsSync(appSpecPath)) {
    console.log('❌ app-spec.yaml not found');
    return false;
  }
  
  const appSpecContent = fs.readFileSync(appSpecPath, 'utf8');
  
  // Check for required environment variables
  const requiredVars = [
    'DIGITALOCEAN_API_TOKEN',
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
  ];
  
  let allFound = true;
  requiredVars.forEach(varName => {
    if (appSpecContent.includes(varName)) {
      console.log(`✅ ${varName}: Found in app-spec.yaml`);
    } else {
      console.log(`❌ ${varName}: Missing from app-spec.yaml`);
      allFound = false;
    }
  });
  
  return allFound;
}

/**
 * Verify API client files exist
 */
function verifyAPIClients() {
  console.log('\n📁 Verifying API Client Files...');
  
  const files = [
    'src/lib/digitalocean/client.ts',
    'src/lib/stripe/enhanced-client.ts',
    'src/app/api/system/health/route.ts',
    'src/app/api-health/page.tsx'
  ];
  
  let allExist = true;
  files.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${filePath}: Exists`);
    } else {
      console.log(`❌ ${filePath}: Missing`);
      allExist = false;
    }
  });
  
  return allExist;
}

/**
 * Test API connectivity
 */
async function testAPIConnectivity() {
  console.log('\n🌐 Testing API Connectivity...');
  
  const doResult = await testDigitalOcean();
  const stripeResult = await testStripe();
  
  return doResult.success && stripeResult.success;
}

/**
 * Generate integration report
 */
function generateIntegrationReport(results) {
  const reportPath = path.join(process.cwd(), 'API_INTEGRATION_REPORT.md');
  
  const report = `# API Integration Report

**Generated**: ${new Date().toISOString()}
**Status**: ${results.overall ? '✅ SUCCESS' : '❌ FAILED'}

## Summary

- **Environment Configuration**: ${results.envConfig ? '✅ PASSED' : '❌ FAILED'}
- **API Client Files**: ${results.clientFiles ? '✅ PASSED' : '❌ FAILED'}
- **API Connectivity**: ${results.connectivity ? '✅ PASSED' : '❌ FAILED'}

## API Status

### DigitalOcean API
- **Status**: ${results.digitalOcean?.success ? '✅ CONNECTED' : '❌ FAILED'}
- **Account**: ${results.digitalOcean?.data?.account?.email || 'N/A'}
- **Droplet Limit**: ${results.digitalOcean?.data?.account?.droplet_limit || 'N/A'}

### Stripe API
- **Status**: ${results.stripe?.success ? '✅ CONNECTED' : '❌ FAILED'}
- **Available Balance**: ${results.stripe?.data?.available?.[0] ? 
  `${results.stripe.data.available[0].currency.toUpperCase()}: ${(results.stripe.data.available[0].amount / 100).toFixed(2)}` : 'N/A'}

## Next Steps

${results.overall ? `
✅ **APIs Successfully Integrated!**

You can now:
1. Deploy the updated configuration to DigitalOcean
2. Test the APIs from the web interface at /api-health
3. Implement payment processing features
4. Set up automated deployment workflows

` : `
❌ **Integration Issues Found**

Please:
1. Fix the failed checks above
2. Verify API keys are correct and active
3. Re-run this verification script
4. Contact support if issues persist
`}

## Files Created

- \`src/lib/digitalocean/client.ts\` - DigitalOcean API client
- \`src/lib/stripe/enhanced-client.ts\` - Enhanced Stripe client
- \`src/app/api/system/health/route.ts\` - Health check API endpoint
- \`src/app/api-health/page.tsx\` - API health dashboard
- \`scripts/verify-api-integration.js\` - This verification script

## Environment Variables

The following environment variables have been configured in app-spec.yaml:
- \`DIGITALOCEAN_API_TOKEN\`
- \`STRIPE_SECRET_KEY\`
- \`NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY\`

---

**Report generated by eWasl API Integration Verification**
`;

  fs.writeFileSync(reportPath, report);
  console.log(`\n📄 Integration report saved to: ${reportPath}`);
}

/**
 * Main verification function
 */
async function runVerification() {
  console.log('🚀 API Integration Verification');
  console.log('================================');
  
  const results = {
    envConfig: false,
    clientFiles: false,
    connectivity: false,
    digitalOcean: null,
    stripe: null,
    overall: false
  };
  
  try {
    // Step 1: Verify environment configuration
    results.envConfig = verifyEnvironmentConfig();
    
    // Step 2: Verify API client files
    results.clientFiles = verifyAPIClients();
    
    // Step 3: Test API connectivity
    if (results.envConfig) {
      console.log('\n🌐 Testing API Connectivity...');
      
      // Test DigitalOcean
      console.log('🌊 Testing DigitalOcean API...');
      results.digitalOcean = await testDigitalOcean();
      
      // Test Stripe
      console.log('\n💳 Testing Stripe API...');
      results.stripe = await testStripe();
      
      results.connectivity = results.digitalOcean.success && results.stripe.success;
    }
    
    // Calculate overall result
    results.overall = results.envConfig && results.clientFiles && results.connectivity;
    
    // Generate report
    generateIntegrationReport(results);
    
    // Final summary
    console.log('\n' + '='.repeat(50));
    console.log('🎯 VERIFICATION SUMMARY');
    console.log('='.repeat(50));
    
    console.log(`📋 Environment Config: ${results.envConfig ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`📁 Client Files: ${results.clientFiles ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`🌐 API Connectivity: ${results.connectivity ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`🌊 DigitalOcean: ${results.digitalOcean?.success ? '✅ WORKING' : '❌ FAILED'}`);
    console.log(`💳 Stripe: ${results.stripe?.success ? '✅ WORKING' : '❌ FAILED'}`);
    
    if (results.overall) {
      console.log('\n🎉 ALL CHECKS PASSED!');
      console.log('✅ APIs are successfully integrated and ready for use');
      console.log('🚀 You can now deploy and test the application');
      console.log('📊 Visit /api-health to monitor API status');
    } else {
      console.log('\n⚠️  SOME CHECKS FAILED');
      console.log('❌ Please fix the issues above before proceeding');
      console.log('📄 Check the integration report for details');
    }
    
    return results.overall;
    
  } catch (error) {
    console.error('\n💥 VERIFICATION FAILED:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run verification
if (require.main === module) {
  runVerification()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 Fatal error:', error);
      process.exit(2);
    });
}

module.exports = { runVerification };
