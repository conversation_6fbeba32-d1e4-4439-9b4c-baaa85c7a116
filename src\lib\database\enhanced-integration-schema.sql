-- Enhanced Integration Database Schema
-- Updates to support Postiz integration features

-- Update social_accounts table with new fields
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS business_account_id TEXT,
ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'personal',
ADD COLUMN IF NOT EXISTS permissions TEXT[],
ADD COLUMN IF NOT EXISTS last_used_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS page_access_token TEXT,
ADD COLUMN IF NOT EXISTS token_expires_at TIMESTAMP;

-- Create test_results table for tracking integration tests
CREATE TABLE IF NOT EXISTS test_results (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  test_type TEXT NOT NULL,
  platform TEXT NOT NULL,
  integration_id UUID REFERENCES social_accounts(id),
  results JSONB NOT NULL,
  success BOOLEAN NOT NULL DEFAULT false,
  duration_ms INTEGER,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- <PERSON>reate scheduled_posts table for enhanced scheduling
CREATE TABLE IF NOT EXISTS scheduled_posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  integration_id UUID NOT NULL REFERENCES social_accounts(id),
  content TEXT NOT NULL,
  media_urls TEXT[],
  scheduled_at TIMESTAMP NOT NULL,
  platform_settings JSONB DEFAULT '{}',
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'publishing', 'published', 'failed', 'cancelled')),
  published_at TIMESTAMP,
  platform_post_id TEXT,
  platform_url TEXT,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create posts table for tracking published posts
CREATE TABLE IF NOT EXISTS posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  integration_id UUID NOT NULL REFERENCES social_accounts(id),
  content TEXT NOT NULL,
  media_urls TEXT[],
  platform_post_id TEXT NOT NULL,
  platform_url TEXT,
  status TEXT DEFAULT 'published' CHECK (status IN ('published', 'failed', 'deleted')),
  platform_settings JSONB DEFAULT '{}',
  engagement_metrics JSONB DEFAULT '{}',
  published_at TIMESTAMP DEFAULT NOW(),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create integration_analytics table for storing analytics data
CREATE TABLE IF NOT EXISTS integration_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  integration_id UUID NOT NULL REFERENCES social_accounts(id),
  metric_type TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  metric_date DATE NOT NULL,
  additional_data JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(integration_id, metric_type, metric_date)
);

-- Create media_uploads table for tracking media processing
CREATE TABLE IF NOT EXISTS media_uploads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  original_filename TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  processed_urls JSONB DEFAULT '{}',
  processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create webhook_events table for handling platform webhooks
CREATE TABLE IF NOT EXISTS webhook_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  platform TEXT NOT NULL,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL,
  processed BOOLEAN DEFAULT false,
  processing_error TEXT,
  received_at TIMESTAMP DEFAULT NOW(),
  processed_at TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_social_accounts_user_platform ON social_accounts(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_social_accounts_account_type ON social_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_social_accounts_last_used ON social_accounts(last_used_at);

CREATE INDEX IF NOT EXISTS idx_test_results_platform ON test_results(platform);
CREATE INDEX IF NOT EXISTS idx_test_results_created_at ON test_results(created_at);
CREATE INDEX IF NOT EXISTS idx_test_results_success ON test_results(success);

CREATE INDEX IF NOT EXISTS idx_scheduled_posts_user_id ON scheduled_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_scheduled_at ON scheduled_posts(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_status ON scheduled_posts(status);

CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id);
CREATE INDEX IF NOT EXISTS idx_posts_integration_id ON posts(integration_id);
CREATE INDEX IF NOT EXISTS idx_posts_published_at ON posts(published_at);

CREATE INDEX IF NOT EXISTS idx_integration_analytics_integration_id ON integration_analytics(integration_id);
CREATE INDEX IF NOT EXISTS idx_integration_analytics_metric_date ON integration_analytics(metric_date);

CREATE INDEX IF NOT EXISTS idx_media_uploads_user_id ON media_uploads(user_id);
CREATE INDEX IF NOT EXISTS idx_media_uploads_status ON media_uploads(processing_status);

CREATE INDEX IF NOT EXISTS idx_webhook_events_platform ON webhook_events(platform);
CREATE INDEX IF NOT EXISTS idx_webhook_events_processed ON webhook_events(processed);

-- Create RLS policies for enhanced security
ALTER TABLE test_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- RLS policies for test_results
CREATE POLICY "Users can view their own test results" ON test_results
  FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can create test results" ON test_results
  FOR INSERT WITH CHECK (created_by = auth.uid());

-- RLS policies for scheduled_posts
CREATE POLICY "Users can manage their own scheduled posts" ON scheduled_posts
  FOR ALL USING (user_id = auth.uid());

-- RLS policies for posts
CREATE POLICY "Users can manage their own posts" ON posts
  FOR ALL USING (user_id = auth.uid());

-- RLS policies for integration_analytics
CREATE POLICY "Users can view analytics for their integrations" ON integration_analytics
  FOR SELECT USING (
    integration_id IN (
      SELECT id FROM social_accounts WHERE user_id = auth.uid()
    )
  );

-- RLS policies for media_uploads
CREATE POLICY "Users can manage their own media uploads" ON media_uploads
  FOR ALL USING (user_id = auth.uid());

-- RLS policies for webhook_events (admin only)
CREATE POLICY "Only service role can access webhook events" ON webhook_events
  FOR ALL USING (auth.role() = 'service_role');

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_scheduled_posts_updated_at 
  BEFORE UPDATE ON scheduled_posts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_posts_updated_at 
  BEFORE UPDATE ON posts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_media_uploads_updated_at 
  BEFORE UPDATE ON media_uploads 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean up old test results
CREATE OR REPLACE FUNCTION cleanup_old_test_results()
RETURNS void AS $$
BEGIN
  DELETE FROM test_results 
  WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create function to update last_used_at for social accounts
CREATE OR REPLACE FUNCTION update_social_account_last_used(account_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE social_accounts 
  SET last_used_at = NOW() 
  WHERE id = account_id;
END;
$$ LANGUAGE plpgsql;

-- Create view for integration statistics
CREATE OR REPLACE VIEW integration_stats AS
SELECT 
  sa.user_id,
  sa.platform,
  COUNT(*) as total_posts,
  COUNT(*) FILTER (WHERE p.status = 'published') as successful_posts,
  COUNT(*) FILTER (WHERE p.status = 'failed') as failed_posts,
  MAX(p.published_at) as last_post_at,
  AVG(EXTRACT(EPOCH FROM (p.updated_at - p.created_at))) as avg_publish_time
FROM social_accounts sa
LEFT JOIN posts p ON sa.id = p.integration_id
WHERE sa.is_active = true
GROUP BY sa.user_id, sa.platform, sa.id;

-- Grant necessary permissions
GRANT SELECT ON integration_stats TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_test_results() TO service_role;
GRANT EXECUTE ON FUNCTION update_social_account_last_used(UUID) TO authenticated;
