'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  UserPlus, 
  Settings, 
  Crown, 
  Shield, 
  Edit, 
  Eye,
  Mail,
  Calendar,
  MoreVertical,
  Trash2
} from 'lucide-react';

interface WorkspaceMember {
  id: string;
  user_id: string;
  role: 'OWNER' | 'ADMIN' | 'MANAGER' | 'EDITOR' | 'VIEWER';
  status: string;
  title?: string;
  department?: string;
  joined_at: string;
  user: {
    email: string;
    user_metadata?: {
      full_name?: string;
      avatar_url?: string;
    };
  };
}

interface WorkspaceInvitation {
  id: string;
  email: string;
  role: string;
  status: string;
  expires_at: string;
  created_at: string;
}

interface Workspace {
  id: string;
  name: string;
  slug: string;
  plan_type: string;
  limits: {
    team_members: number;
  };
}

export default function TeamManagementPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [members, setMembers] = useState<WorkspaceMember[]>([]);
  const [invitations, setInvitations] = useState<WorkspaceInvitation[]>([]);
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [userRole, setUserRole] = useState<string>('');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('VIEWER');
  const [inviteMessage, setInviteMessage] = useState('');
  const [isInviting, setIsInviting] = useState(false);

  useEffect(() => {
    loadTeamData();
  }, []);

  const loadTeamData = async () => {
    setIsLoading(true);
    try {
      // Get current workspace (for now, use the first workspace)
      const workspacesResponse = await fetch('/api/workspaces');
      const workspacesData = await workspacesResponse.json();
      
      if (workspacesData.success && workspacesData.workspaces.length > 0) {
        const workspace = workspacesData.workspaces[0];
        setCurrentWorkspace(workspace);
        
        // Load team members
        const membersResponse = await fetch(`/api/workspaces/${workspace.id}/members`);
        const membersData = await membersResponse.json();
        
        if (membersData.success) {
          setMembers(membersData.members);
          setInvitations(membersData.invitations);
          setUserRole(membersData.user_role);
        }
      }
    } catch (error) {
      console.error('Error loading team data:', error);
      toast.error('فشل في تحميل بيانات الفريق');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteMember = async () => {
    if (!currentWorkspace || !inviteEmail) return;
    
    setIsInviting(true);
    try {
      const response = await fetch(`/api/workspaces/${currentWorkspace.id}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: inviteEmail,
          role: inviteRole,
          message: inviteMessage,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('تم إرسال الدعوة بنجاح');
        setShowInviteModal(false);
        setInviteEmail('');
        setInviteMessage('');
        loadTeamData(); // Reload to show new invitation
      } else {
        toast.error(data.error || 'فشل في إرسال الدعوة');
      }
    } catch (error) {
      console.error('Error inviting member:', error);
      toast.error('حدث خطأ أثناء إرسال الدعوة');
    } finally {
      setIsInviting(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!currentWorkspace) return;
    
    if (!confirm('هل أنت متأكد من إزالة هذا العضو؟')) return;
    
    try {
      const response = await fetch(`/api/workspaces/${currentWorkspace.id}/members?member_id=${memberId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('تم إزالة العضو بنجاح');
        loadTeamData(); // Reload team data
      } else {
        toast.error(data.error || 'فشل في إزالة العضو');
      }
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('حدث خطأ أثناء إزالة العضو');
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'OWNER':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'ADMIN':
        return <Shield className="h-4 w-4 text-red-500" />;
      case 'MANAGER':
        return <Settings className="h-4 w-4 text-blue-500" />;
      case 'EDITOR':
        return <Edit className="h-4 w-4 text-green-500" />;
      case 'VIEWER':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getRoleName = (role: string) => {
    switch (role) {
      case 'OWNER':
        return 'مالك';
      case 'ADMIN':
        return 'مدير';
      case 'MANAGER':
        return 'مشرف';
      case 'EDITOR':
        return 'محرر';
      case 'VIEWER':
        return 'مشاهد';
      default:
        return role;
    }
  };

  const canManageMembers = ['OWNER', 'ADMIN'].includes(userRole);
  const canInviteMembers = ['OWNER', 'ADMIN', 'MANAGER'].includes(userRole);

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
        direction: 'rtl',
        fontFamily: 'Inter, sans-serif',
        padding: '2rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'white',
          padding: '2rem',
          borderRadius: '1rem',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          textAlign: 'center'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #e2e8f0',
            borderTop: '4px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#64748b' }}>جاري تحميل بيانات الفريق...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif',
      padding: '2rem'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '0.5rem'
            }}>
              👥 إدارة الفريق
            </h1>
            <p style={{ color: '#64748b' }}>
              إدارة أعضاء الفريق والأدوار والصلاحيات
            </p>
          </div>
          
          {canInviteMembers && (
            <Button
              onClick={() => setShowInviteModal(true)}
              style={{
                background: 'linear-gradient(to right, #10b981, #059669)',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.5rem',
                fontSize: '1rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <UserPlus className="h-4 w-4" />
              دعوة عضو جديد
            </Button>
          )}
        </div>

        {/* Workspace Info */}
        {currentWorkspace && (
          <Card style={{ marginBottom: '2rem' }}>
            <CardHeader>
              <CardTitle style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Users className="h-5 w-5" />
                {currentWorkspace.name}
              </CardTitle>
              <CardDescription>
                الخطة: {currentWorkspace.plan_type} • 
                الحد الأقصى للأعضاء: {currentWorkspace.limits.team_members} • 
                الأعضاء الحاليون: {members.length}
              </CardDescription>
            </CardHeader>
          </Card>
        )}

        {/* Team Members */}
        <Card style={{ marginBottom: '2rem' }}>
          <CardHeader>
            <CardTitle>أعضاء الفريق ({members.length})</CardTitle>
            <CardDescription>
              إدارة أعضاء الفريق وأدوارهم
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div style={{
              display: 'grid',
              gap: '1rem'
            }}>
              {members.map((member) => (
                <div
                  key={member.id}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '1rem',
                    background: '#f8fafc',
                    borderRadius: '0.5rem',
                    border: '1px solid #e2e8f0'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <Avatar>
                      <AvatarImage src={member.user?.user_metadata?.avatar_url} />
                      <AvatarFallback>
                        {member.user?.user_metadata?.full_name?.charAt(0) || member.user?.email?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                        {member.user?.user_metadata?.full_name || member.user?.email}
                      </div>
                      <div style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '0.25rem' }}>
                        {member.user?.email}
                      </div>
                      {member.title && (
                        <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                          {member.title}
                        </div>
                      )}
                    </div>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <Badge
                      variant="secondary"
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem'
                      }}
                    >
                      {getRoleIcon(member.role)}
                      {getRoleName(member.role)}
                    </Badge>
                    
                    <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                      انضم في {new Date(member.joined_at).toLocaleDateString('ar')}
                    </div>

                    {canManageMembers && member.role !== 'OWNER' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveMember(member.id)}
                        style={{
                          color: '#ef4444',
                          padding: '0.25rem'
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Pending Invitations */}
        {invitations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>الدعوات المعلقة ({invitations.length})</CardTitle>
              <CardDescription>
                الدعوات التي لم يتم قبولها بعد
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div style={{
                display: 'grid',
                gap: '1rem'
              }}>
                {invitations.map((invitation) => (
                  <div
                    key={invitation.id}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '1rem',
                      background: '#fef3c7',
                      borderRadius: '0.5rem',
                      border: '1px solid #fbbf24'
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                      <Mail className="h-5 w-5 text-amber-600" />
                      <div>
                        <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                          {invitation.email}
                        </div>
                        <div style={{ fontSize: '0.875rem', color: '#92400e' }}>
                          مدعو كـ {getRoleName(invitation.role)}
                        </div>
                      </div>
                    </div>

                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                      <Badge variant="outline" style={{ color: '#92400e', borderColor: '#fbbf24' }}>
                        معلق
                      </Badge>
                      <div style={{ fontSize: '0.75rem', color: '#92400e' }}>
                        ينتهي في {new Date(invitation.expires_at).toLocaleDateString('ar')}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Invite Modal */}
        {showInviteModal && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div style={{
              background: 'white',
              padding: '2rem',
              borderRadius: '1rem',
              maxWidth: '500px',
              width: '90%',
              maxHeight: '90vh',
              overflow: 'auto'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                marginBottom: '1rem'
              }}>
                دعوة عضو جديد
              </h2>

              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: 'bold',
                  marginBottom: '0.5rem'
                }}>
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #e2e8f0',
                    borderRadius: '0.5rem',
                    fontSize: '1rem'
                  }}
                />
              </div>

              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: 'bold',
                  marginBottom: '0.5rem'
                }}>
                  الدور
                </label>
                <select
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #e2e8f0',
                    borderRadius: '0.5rem',
                    fontSize: '1rem'
                  }}
                >
                  <option value="VIEWER">مشاهد</option>
                  <option value="EDITOR">محرر</option>
                  <option value="MANAGER">مشرف</option>
                  {userRole === 'OWNER' && <option value="ADMIN">مدير</option>}
                </select>
              </div>

              <div style={{ marginBottom: '2rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: 'bold',
                  marginBottom: '0.5rem'
                }}>
                  رسالة (اختيارية)
                </label>
                <textarea
                  value={inviteMessage}
                  onChange={(e) => setInviteMessage(e.target.value)}
                  placeholder="رسالة ترحيبية للعضو الجديد..."
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #e2e8f0',
                    borderRadius: '0.5rem',
                    fontSize: '1rem',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{
                display: 'flex',
                gap: '1rem',
                justifyContent: 'flex-end'
              }}>
                <Button
                  variant="outline"
                  onClick={() => setShowInviteModal(false)}
                  disabled={isInviting}
                >
                  إلغاء
                </Button>
                <Button
                  onClick={handleInviteMember}
                  disabled={isInviting || !inviteEmail}
                  style={{
                    background: 'linear-gradient(to right, #10b981, #059669)',
                    color: 'white',
                    border: 'none'
                  }}
                >
                  {isInviting ? 'جاري الإرسال...' : 'إرسال الدعوة'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
