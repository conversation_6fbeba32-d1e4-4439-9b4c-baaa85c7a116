#!/usr/bin/env node

/**
 * 🔌 ENHANCED SOCIAL MEDIA DISCONNECT API TESTING SCRIPT
 * 
 * This script tests the enhanced disconnect functionality with comprehensive error handling
 */

const BASE_URL = 'https://app.ewasl.com';

async function testDisconnectAPI() {
  console.log('🚀 Starting Enhanced Social Media Disconnect API Testing...\n');

  try {
    // Step 1: Get current connected accounts
    console.log('📋 Step 1: Fetching current connected accounts...');
    const accountsResponse = await fetch(`${BASE_URL}/api/social/connect`, {
      method: 'GET'
    });

    if (!accountsResponse.ok) {
      console.error('❌ Failed to fetch accounts:', accountsResponse.status);
      return;
    }

    const accountsData = await accountsResponse.json();
    console.log('✅ Connected accounts:', {
      total: accountsData.connectedAccounts?.length || 0,
      accounts: accountsData.connectedAccounts?.map(acc => ({
        id: acc.id,
        platform: acc.platform,
        name: acc.account_name
      })) || []
    });

    if (!accountsData.connectedAccounts || accountsData.connectedAccounts.length === 0) {
      console.log('📭 No connected accounts found for testing');
      return;
    }

    // Step 2: Test disconnect functionality with the first account
    const testAccount = accountsData.connectedAccounts[0];
    console.log(`\n🔌 Step 2: Testing disconnect for ${testAccount.platform} account: ${testAccount.account_name}`);

    const disconnectResponse = await fetch(`${BASE_URL}/api/social/disconnect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        accountId: testAccount.id
      })
    });

    const disconnectData = await disconnectResponse.json();
    
    console.log('📡 Disconnect API Response:', {
      status: disconnectResponse.status,
      success: disconnectData.success,
      message: disconnectData.message,
      error: disconnectData.error,
      details: disconnectData.details
    });

    if (disconnectResponse.ok && disconnectData.success) {
      console.log('✅ DISCONNECT TEST PASSED: Account disconnected successfully');
      
      // Step 3: Verify account was removed
      console.log('\n🔍 Step 3: Verifying account removal...');
      const verifyResponse = await fetch(`${BASE_URL}/api/social/connect`, {
        method: 'GET'
      });

      if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json();
        const remainingAccounts = verifyData.connectedAccounts?.length || 0;
        console.log('📊 Verification result:', {
          remainingAccounts,
          removedSuccessfully: remainingAccounts < accountsData.connectedAccounts.length
        });

        if (remainingAccounts < accountsData.connectedAccounts.length) {
          console.log('✅ VERIFICATION PASSED: Account successfully removed from database');
        } else {
          console.log('❌ VERIFICATION FAILED: Account still exists in database');
        }
      }
    } else {
      console.log('❌ DISCONNECT TEST FAILED:', disconnectData.error || disconnectData.details);
    }

    // Step 4: Test publish functionality with remaining accounts
    console.log('\n📤 Step 4: Testing publish functionality...');
    const publishResponse = await fetch(`${BASE_URL}/api/posts/test-publish`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform: 'FACEBOOK',
        content: 'Test post from enhanced API testing script',
        isTest: true
      })
    });

    const publishData = await publishResponse.json();
    console.log('📊 Publish test result:', {
      status: publishResponse.status,
      success: publishData.success,
      platform: publishData.platform,
      message: publishData.message,
      error: publishData.error
    });

    if (publishData.success) {
      console.log('✅ PUBLISH TEST PASSED: Test post published successfully');
    } else {
      console.log('❌ PUBLISH TEST FAILED:', publishData.error);
    }

  } catch (error) {
    console.error('💥 Test script error:', error.message);
  }

  console.log('\n🎯 Enhanced Social Media Disconnect API Testing Complete!');
}

// Run the test
testDisconnectAPI();
