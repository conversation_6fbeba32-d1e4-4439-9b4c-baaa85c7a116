@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic Font Imports */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

/* EMERGENCY CSS - COMPLETE STYLING OVERRIDE */
* {
  box-sizing: border-box;
}

:root {
  /* Font families */
  --font-arabic: 'Noto Sans Arabic', 'Cairo', 'Amiri', sans-serif;
  --font-inter: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* RTL support variables */
  --text-align-start: right;
  --text-align-end: left;
  --margin-start: margin-right;
  --margin-end: margin-left;
  --padding-start: padding-right;
  --padding-end: padding-left;
  --border-start: border-right;
  --border-end: border-left;

  /* Professional Design System */
  --pro-primary: 238 75% 65%;
  --pro-primary-hover: 238 69% 59%;
  --pro-secondary: 271 91% 65%;
  --pro-success: 160 84% 39%;
  --pro-warning: 38 92% 50%;
  --pro-error: 0 84% 60%;
  --pro-gray-50: 250 20% 99%;
  --pro-gray-100: 240 20% 96%;
  --pro-gray-200: 240 16% 88%;
  --pro-gray-300: 240 12% 80%;
  --pro-gray-400: 240 8% 64%;
  --pro-gray-500: 240 6% 50%;
  --pro-gray-600: 240 8% 40%;
  --pro-gray-700: 240 10% 30%;
  --pro-gray-800: 240 12% 20%;
  --pro-gray-900: 240 20% 12%;

  /* Sidebar variables */
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 238 75% 65%;
}

[dir="ltr"] {
  --text-align-start: left;
  --text-align-end: right;
  --margin-start: margin-left;
  --margin-end: margin-right;
  --padding-start: padding-left;
  --padding-end: padding-right;
  --border-start: border-left;
  --border-end: border-right;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: var(--font-arabic);
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%);
  min-height: 100vh;
  direction: rtl;
}

[dir="ltr"] html,
[dir="ltr"] body {
  font-family: var(--font-inter);
  direction: ltr;
}

/* FORCE ALL CRITICAL STYLES */
.min-h-screen { min-height: 100vh !important; }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important; }
.from-slate-50 { --tw-gradient-from: #f8fafc !important; --tw-gradient-to: rgb(248 250 252 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
.via-blue-50 { --tw-gradient-to: rgb(239 246 255 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), #eff6ff, var(--tw-gradient-to) !important; }
.to-purple-50\/30 { --tw-gradient-to: rgb(250 245 255 / 0.3) !important; }

/* Layout Classes */
.fixed { position: fixed !important; }
.inset-0 { top: 0px !important; right: 0px !important; bottom: 0px !important; left: 0px !important; }
.inset-y-0 { top: 0px !important; bottom: 0px !important; }
.right-0 { right: 0px !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }
.z-30 { z-index: 30 !important; }
.w-72 { width: 18rem !important; }
.h-16 { height: 4rem !important; }
.h-full { height: 100% !important; }
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important; }
.translate-x-0 { --tw-translate-x: 0px !important; }
.translate-x-full { --tw-translate-x: 100% !important; }
.transition-transform { transition-property: transform !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.duration-300 { transition-duration: 300ms !important; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; }

/* Force basic Tailwind classes */
.bg-white { background-color: white !important; }
.bg-gray-50 { background-color: #f9fafb !important; }
.bg-blue-50 { background-color: #eff6ff !important; }
.bg-purple-50 { background-color: #faf5ff !important; }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }
.from-blue-600 { --tw-gradient-from: #2563eb !important; --tw-gradient-to: rgb(37 99 235 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
.to-purple-600 { --tw-gradient-to: #9333ea !important; }
.text-white { color: white !important; }
.text-gray-600 { color: #4b5563 !important; }
.text-gray-700 { color: #374151 !important; }
.text-gray-900 { color: #111827 !important; }
.p-4 { padding: 1rem !important; }
.p-6 { padding: 1.5rem !important; }
.p-8 { padding: 2rem !important; }
.px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
.py-6 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }
.rounded-2xl { border-radius: 1rem !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }
.shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important; }
.border { border-width: 1px !important; border-color: #e5e7eb !important; }
.mb-4 { margin-bottom: 1rem !important; }
.space-y-8 > :not([hidden]) ~ :not([hidden]) { margin-top: 2rem !important; }
.flex { display: flex !important; }
.items-center { align-items: center !important; }
.justify-between { justify-content: space-between !important; }
.gap-6 { gap: 1.5rem !important; }
.text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
.text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
.font-bold { font-weight: 700 !important; }
.font-semibold { font-weight: 600 !important; }

/* Additional critical styles */
.backdrop-blur-sm { backdrop-filter: blur(4px) !important; }
.backdrop-blur-xl { backdrop-filter: blur(24px) !important; }
.bg-white\/80 { background-color: rgba(255, 255, 255, 0.8) !important; }
.bg-white\/50 { background-color: rgba(255, 255, 255, 0.5) !important; }
.border-white\/50 { border-color: rgba(255, 255, 255, 0.5) !important; }
.min-h-screen { min-height: 100vh !important; }
.h-16 { height: 4rem !important; }
.w-72 { width: 18rem !important; }
.fixed { position: fixed !important; }
.inset-y-0 { top: 0 !important; bottom: 0 !important; }
.right-0 { right: 0 !important; }
.z-50 { z-index: 50 !important; }
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important; }
.translate-x-0 { --tw-translate-x: 0px !important; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important; }
.translate-x-full { --tw-translate-x: 100% !important; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important; }
.transition-transform { transition-property: transform !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.duration-300 { transition-duration: 300ms !important; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; }
.md\:translate-x-0 { --tw-translate-x: 0px !important; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important; }
.md\:static { position: static !important; }
.md\:inset-0 { top: 0px !important; right: 0px !important; bottom: 0px !important; left: 0px !important; }
.md\:z-auto { z-index: auto !important; }
.md\:shadow-none { box-shadow: 0 0 #0000 !important; }
.md\:mr-72 { margin-right: 18rem !important; }
.sticky { position: sticky !important; }
.top-0 { top: 0px !important; }
.z-30 { z-index: 30 !important; }
.bg-white\/95 { background-color: rgba(255, 255, 255, 0.95) !important; }
.h-full { height: 100% !important; }
.flex-col { flex-direction: column !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-y-auto { overflow-y: auto !important; }
.max-w-7xl { max-width: 80rem !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

/* Background and Color Classes */
.bg-white\/80 { background-color: rgba(255, 255, 255, 0.8) !important; }
.bg-white\/95 { background-color: rgba(255, 255, 255, 0.95) !important; }
.bg-white\/50 { background-color: rgba(255, 255, 255, 0.5) !important; }
.backdrop-blur-sm { backdrop-filter: blur(4px) !important; }
.backdrop-blur-xl { backdrop-filter: blur(24px) !important; }
.border { border-width: 1px !important; border-color: #e5e7eb !important; }
.border-white\/50 { border-color: rgba(255, 255, 255, 0.5) !important; }
.border-gray-200\/50 { border-color: rgba(229, 231, 235, 0.5) !important; }
.rounded-2xl { border-radius: 1rem !important; }
.rounded-3xl { border-radius: 1.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important; }

/* Flexbox and Grid */
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.items-center { align-items: center !important; }
.items-start { align-items: flex-start !important; }
.justify-between { justify-content: space-between !important; }
.justify-center { justify-content: center !important; }
.gap-3 { gap: 0.75rem !important; }
.gap-6 { gap: 1.5rem !important; }
.gap-8 { gap: 2rem !important; }
.space-y-2 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.5rem !important; }
.space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1.5rem !important; }
.space-y-8 > :not([hidden]) ~ :not([hidden]) { margin-top: 2rem !important; }
.grid { display: grid !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }

/* Typography */
.text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
.text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
.text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
.text-sm { font-size: 0.875rem !important; line-height: 1.25rem !important; }
.text-xs { font-size: 0.75rem !important; line-height: 1rem !important; }
.font-bold { font-weight: 700 !important; }
.font-semibold { font-weight: 600 !important; }
.font-medium { font-weight: 500 !important; }
.text-white { color: rgb(255 255 255) !important; }
.text-gray-900 { color: rgb(17 24 39) !important; }
.text-gray-700 { color: rgb(55 65 81) !important; }
.text-gray-600 { color: rgb(75 85 99) !important; }
.text-gray-500 { color: rgb(107 114 128) !important; }
.text-blue-600 { color: rgb(37 99 235) !important; }
.text-purple-600 { color: rgb(147 51 234) !important; }

/* Gradient Text */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }
.from-blue-600 { --tw-gradient-from: #2563eb !important; --tw-gradient-to: rgb(37 99 235 / 0) !important; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
.to-purple-600 { --tw-gradient-to: #9333ea !important; }
.bg-clip-text { -webkit-background-clip: text !important; background-clip: text !important; }
.text-transparent { color: transparent !important; }

/* Padding and Margin */
.p-4 { padding: 1rem !important; }
.p-6 { padding: 1.5rem !important; }
.p-8 { padding: 2rem !important; }
.px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
.py-6 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.mb-3 { margin-bottom: 0.75rem !important; }
.mt-2 { margin-top: 0.5rem !important; }

/* Button Styles */
.bg-gradient-to-r.from-blue-600.to-purple-600 {
  background: linear-gradient(to right, #2563eb, #9333ea) !important;
}
.hover\:from-blue-700:hover { --tw-gradient-from: #1d4ed8 !important; }
.hover\:to-purple-700:hover { --tw-gradient-to: #7c3aed !important; }
.transition-all { transition-property: all !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.duration-200 { transition-duration: 200ms !important; }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important; }

/* Responsive Classes */
@media (min-width: 768px) {
  .md\:mr-72 { margin-right: 18rem !important; }
  .md\:translate-x-0 { --tw-translate-x: 0px !important; }
  .md\:static { position: static !important; }
  .md\:inset-0 { top: 0px !important; right: 0px !important; bottom: 0px !important; left: 0px !important; }
  .md\:z-auto { z-index: auto !important; }
  .md\:shadow-none { box-shadow: 0 0 #0000 !important; }
  .md\:hidden { display: none !important; }
}

@media (min-width: 640px) {
  .sm\:flex-row { flex-direction: row !important; }
  .sm\:items-center { align-items: center !important; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .sm\:block { display: block !important; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
  .lg\:col-span-2 { grid-column: span 2 / span 2 !important; }
  .lg\:col-span-1 { grid-column: span 1 / span 1 !important; }
}



@layer base {
  :root {

    --background: 0 0% 100%;

    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;

    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;

    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;

    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;

    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;

    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;

    --input: 0 0% 89.8%;

    --ring: 0 0% 3.9%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem
  }
  .dark {

    --background: 0 0% 3.9%;

    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;

    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;

    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;

    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;

    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;

    --input: 0 0% 14.9%;

    --ring: 0 0% 83.1%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%
  }
}



@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Modern glass morphism effect */
  .glass {
    @apply bg-white/80 backdrop-blur-xl border border-white/20;
  }

  /* Modern gradient backgrounds */
  .gradient-bg {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100;
  }

  /* Modern card styling */
  .modern-card {
    @apply bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20;
  }

  /* Modern button gradients */
  .btn-gradient-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-gradient-success {
    @apply bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }

  /* Modern input styling */
  .modern-input {
    @apply h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors rounded-lg;
  }

  /* RTL support improvements */
  [dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Loading spinner */
  .spinner {
    @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
  }

  /* Supabase Auth UI Custom Styles */
  .auth-container {
    direction: rtl;
  }

  .auth-container input {
    text-align: right;
    direction: rtl;
  }

  .auth-container button {
    font-family: inherit;
  }

  .auth-container .supabase-auth-ui_ui-button {
    background: linear-gradient(to right, #2563eb, #7c3aed) !important;
    border: none !important;
    border-radius: 0.5rem !important;
    height: 3rem !important;
    font-weight: 500 !important;
  }

  .auth-container .supabase-auth-ui_ui-button:hover {
    background: linear-gradient(to right, #1d4ed8, #6d28d9) !important;
  }

  .auth-container .supabase-auth-ui_ui-input {
    border-radius: 0.5rem !important;
    height: 3rem !important;
    text-align: right !important;
    direction: rtl !important;
  }

  .auth-container .supabase-auth-ui_ui-label {
    text-align: right !important;
    direction: rtl !important;
    font-weight: 500 !important;
  }

  /* Professional Design System Components */

  /* Professional Typography Scale */
  .text-display {
    @apply text-5xl lg:text-6xl font-bold leading-none tracking-tight;
  }

  .text-h1 {
    @apply text-3xl lg:text-4xl font-bold leading-tight tracking-tight;
  }

  .text-h2 {
    @apply text-2xl lg:text-3xl font-semibold leading-tight tracking-tight;
  }

  .text-h3 {
    @apply text-xl lg:text-2xl font-semibold leading-snug;
  }

  .text-h4 {
    @apply text-lg lg:text-xl font-medium leading-snug;
  }

  .text-body-lg {
    @apply text-lg leading-relaxed;
  }

  .text-body {
    @apply text-base leading-relaxed;
  }

  .text-body-sm {
    @apply text-sm leading-normal;
  }

  .text-caption {
    @apply text-xs leading-normal font-medium;
  }

  /* Professional Button System */
  .btn-pro {
    @apply inline-flex items-center justify-center gap-2 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-pro-primary {
    @apply btn-pro bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl shadow-md hover:from-indigo-700 hover:to-purple-700 hover:shadow-lg hover:-translate-y-0.5 focus:ring-indigo-500;
  }

  .btn-pro-secondary {
    @apply btn-pro bg-white text-gray-700 border border-gray-200 rounded-xl shadow-sm hover:bg-gray-50 hover:border-gray-300 hover:shadow-md focus:ring-indigo-500;
  }

  .btn-pro-ghost {
    @apply btn-pro text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 focus:ring-indigo-500;
  }

  .btn-pro-danger {
    @apply btn-pro bg-red-600 text-white rounded-xl shadow-md hover:bg-red-700 hover:shadow-lg hover:-translate-y-0.5 focus:ring-red-500;
  }

  /* Button Sizes */
  .btn-pro-xs {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-pro-sm {
    @apply px-4 py-2 text-sm;
  }

  .btn-pro-md {
    @apply px-6 py-2.5 text-sm;
  }

  .btn-pro-lg {
    @apply px-8 py-3 text-base;
  }

  .btn-pro-xl {
    @apply px-10 py-4 text-lg;
  }

  /* Professional Card System */
  .card-pro {
    @apply bg-white rounded-xl border border-gray-100 shadow-sm transition-all duration-200;
  }

  .card-pro-hover {
    @apply card-pro hover:shadow-md hover:-translate-y-1 cursor-pointer;
  }

  .card-pro-padding-sm {
    @apply p-4;
  }

  .card-pro-padding-md {
    @apply p-6;
  }

  .card-pro-padding-lg {
    @apply p-8;
  }

  /* Professional Status System */
  .status-pro {
    @apply inline-flex items-center gap-1.5 px-3 py-1 text-xs font-medium rounded-full;
  }

  .status-success {
    @apply status-pro bg-emerald-50 text-emerald-700 border border-emerald-200;
  }

  .status-warning {
    @apply status-pro bg-amber-50 text-amber-700 border border-amber-200;
  }

  .status-error {
    @apply status-pro bg-red-50 text-red-700 border border-red-200;
  }

  .status-info {
    @apply status-pro bg-blue-50 text-blue-700 border border-blue-200;
  }

  .status-neutral {
    @apply status-pro bg-gray-50 text-gray-700 border border-gray-200;
  }

  /* Professional Layout */
  .section-pro {
    @apply space-y-6;
  }

  .grid-pro-1 {
    @apply grid grid-cols-1 gap-6;
  }

  .grid-pro-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  .grid-pro-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  .grid-pro-4 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
  }

  /* Professional Animations */
  .animate-pro-fade-in {
    animation: fadeInPro 0.4s ease-out;
  }

  .animate-pro-slide-up {
    animation: slideUpPro 0.4s ease-out;
  }

  .animate-pro-scale {
    animation: scalePro 0.3s ease-out;
  }

  /* Professional Input System */
  .input-pro {
    @apply w-full px-4 py-3 text-base border border-gray-200 rounded-xl bg-white placeholder:text-gray-400 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 transition-all duration-200;
  }

  .input-pro:hover {
    @apply border-gray-300;
  }

  /* Professional Number Display */
  .number-pro-sm {
    @apply text-lg font-bold tracking-tight;
  }

  .number-pro-md {
    @apply text-xl font-bold tracking-tight;
  }

  .number-pro-lg {
    @apply text-2xl lg:text-3xl font-bold tracking-tight;
  }

  .number-pro-xl {
    @apply text-3xl lg:text-4xl font-bold tracking-tight;
  }

  /* Professional Loading States */
  .loading-pro {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .loading-pro-text {
    @apply loading-pro h-4 w-24;
  }

  .loading-pro-title {
    @apply loading-pro h-6 w-32;
  }

  .loading-pro-card {
    @apply loading-pro h-32 w-full;
  }
}

/* Professional Keyframes */
@keyframes fadeInPro {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUpPro {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scalePro {
  from {
    opacity: 0;
    transform: scale(0.96);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
