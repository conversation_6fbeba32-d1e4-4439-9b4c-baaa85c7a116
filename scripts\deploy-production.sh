#!/bin/bash

# Production Deployment Script for eWasl.com
# Enhanced Media Processing Pipeline - DigitalOcean App Platform

set -e  # Exit on any error

echo "🚀 Starting eWasl.com Production Deployment to DigitalOcean..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ewasl-production"
REGION="nyc3"
GITHUB_REPO="TahaOsa/eWasl.com"
GITHUB_BRANCH="main"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
        log_error "DigitalOcean CLI (doctl) is not installed"
        log_info "Install it from: https://docs.digitalocean.com/reference/doctl/how-to/install/"
        exit 1
    fi
    
    # Check if doctl is authenticated
    if ! doctl account get &> /dev/null; then
        log_error "DigitalOcean CLI is not authenticated"
        log_info "Run: doctl auth init"
        exit 1
    fi
    
    # Check if git is available
    if ! command -v git &> /dev/null; then
        log_error "Git is not installed"
        exit 1
    fi
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    log_success "All prerequisites are met"
}

# Validate environment
validate_environment() {
    log_info "Validating environment configuration..."
    
    # Check if required files exist
    if [ ! -f ".do/app.yaml" ]; then
        log_error "DigitalOcean app specification (.do/app.yaml) not found"
        exit 1
    fi
    
    if [ ! -f ".env.production" ]; then
        log_warning ".env.production file not found - using environment variables"
    fi
    
    if [ ! -f "package.json" ]; then
        log_error "package.json not found"
        exit 1
    fi
    
    log_success "Environment validation completed"
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check git status
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "There are uncommitted changes in the repository"
        read -p "Do you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Deployment cancelled"
            exit 1
        fi
    fi
    
    # Check current branch
    CURRENT_BRANCH=$(git branch --show-current)
    if [ "$CURRENT_BRANCH" != "$GITHUB_BRANCH" ]; then
        log_warning "Current branch ($CURRENT_BRANCH) is not the deployment branch ($GITHUB_BRANCH)"
        read -p "Do you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Deployment cancelled"
            exit 1
        fi
    fi
    
    # Test build locally
    log_info "Testing local build..."
    if npm run build; then
        log_success "Local build successful"
    else
        log_error "Local build failed"
        exit 1
    fi
    
    log_success "Pre-deployment checks completed"
}

# Create DigitalOcean Spaces bucket for media
create_spaces_bucket() {
    log_info "Creating DigitalOcean Spaces bucket for media storage..."
    
    BUCKET_NAME="ewasl-media-production"
    
    # Check if bucket already exists
    if doctl spaces bucket list | grep -q "$BUCKET_NAME"; then
        log_warning "Spaces bucket '$BUCKET_NAME' already exists"
    else
        # Create the bucket
        if doctl spaces bucket create "$BUCKET_NAME" --region "$REGION"; then
            log_success "Spaces bucket '$BUCKET_NAME' created successfully"
        else
            log_error "Failed to create Spaces bucket"
            exit 1
        fi
    fi
    
    # Enable CDN
    log_info "Enabling CDN for Spaces bucket..."
    if doctl spaces bucket put-cdn "$BUCKET_NAME" --ttl 31536000; then
        log_success "CDN enabled for bucket '$BUCKET_NAME'"
    else
        log_warning "Failed to enable CDN (bucket might already have CDN enabled)"
    fi
}

# Deploy application
deploy_application() {
    log_info "Deploying application to DigitalOcean App Platform..."
    
    # Check if app already exists
    if doctl apps list | grep -q "$PROJECT_NAME"; then
        log_info "App '$PROJECT_NAME' already exists, updating..."
        
        # Get app ID
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$PROJECT_NAME" | awk '{print $1}')
        
        # Update the app
        if doctl apps update "$APP_ID" --spec .do/app.yaml; then
            log_success "App updated successfully"
        else
            log_error "Failed to update app"
            exit 1
        fi
    else
        log_info "Creating new app '$PROJECT_NAME'..."
        
        # Create new app
        if doctl apps create --spec .do/app.yaml; then
            log_success "App created successfully"
        else
            log_error "Failed to create app"
            exit 1
        fi
    fi
    
    # Get app ID for monitoring
    APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$PROJECT_NAME" | awk '{print $1}')
    echo "App ID: $APP_ID"
}

# Monitor deployment
monitor_deployment() {
    log_info "Monitoring deployment progress..."
    
    APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$PROJECT_NAME" | awk '{print $1}')
    
    if [ -z "$APP_ID" ]; then
        log_error "Could not find app ID"
        exit 1
    fi
    
    log_info "App ID: $APP_ID"
    log_info "Monitoring deployment... (this may take several minutes)"
    
    # Monitor deployment status
    TIMEOUT=1800  # 30 minutes timeout
    ELAPSED=0
    INTERVAL=30
    
    while [ $ELAPSED -lt $TIMEOUT ]; do
        STATUS=$(doctl apps get "$APP_ID" --format Phase --no-header)
        
        case $STATUS in
            "ACTIVE")
                log_success "Deployment completed successfully!"
                break
                ;;
            "BUILDING"|"DEPLOYING")
                log_info "Deployment in progress... ($STATUS)"
                ;;
            "ERROR"|"FAILED")
                log_error "Deployment failed with status: $STATUS"
                exit 1
                ;;
            *)
                log_info "Current status: $STATUS"
                ;;
        esac
        
        sleep $INTERVAL
        ELAPSED=$((ELAPSED + INTERVAL))
    done
    
    if [ $ELAPSED -ge $TIMEOUT ]; then
        log_error "Deployment timeout reached"
        exit 1
    fi
}

# Get deployment information
get_deployment_info() {
    log_info "Getting deployment information..."
    
    APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$PROJECT_NAME" | awk '{print $1}')
    
    if [ -z "$APP_ID" ]; then
        log_error "Could not find app ID"
        return 1
    fi
    
    # Get app details
    echo ""
    echo "📊 Deployment Information:"
    echo "========================="
    doctl apps get "$APP_ID" --format Name,Phase,CreatedAt,UpdatedAt
    
    # Get app URL
    APP_URL=$(doctl apps get "$APP_ID" --format LiveURL --no-header)
    if [ -n "$APP_URL" ]; then
        echo ""
        echo "🌐 Application URL: $APP_URL"
        echo "🔗 Health Check: $APP_URL/api/system/health"
        echo "📊 Media Pipeline Test: $APP_URL/api/test/enhanced-media-pipeline-final"
    fi
    
    # Get deployment logs
    echo ""
    echo "📋 Recent Deployment Logs:"
    echo "=========================="
    doctl apps logs "$APP_ID" --type build --tail 20
}

# Test deployment
test_deployment() {
    log_info "Testing deployment..."
    
    APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$PROJECT_NAME" | awk '{print $1}')
    APP_URL=$(doctl apps get "$APP_ID" --format LiveURL --no-header)
    
    if [ -z "$APP_URL" ]; then
        log_error "Could not get app URL"
        return 1
    fi
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    if curl -f -s "$APP_URL/api/system/health" > /dev/null; then
        log_success "Health endpoint is responding"
    else
        log_error "Health endpoint is not responding"
        return 1
    fi
    
    # Test enhanced media pipeline
    log_info "Testing Enhanced Media Processing Pipeline..."
    if curl -f -s -X POST "$APP_URL/api/test/enhanced-media-pipeline-final" > /dev/null; then
        log_success "Enhanced Media Processing Pipeline is working"
    else
        log_warning "Enhanced Media Processing Pipeline test failed (this might be expected in production)"
    fi
    
    log_success "Deployment testing completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    # Add any cleanup tasks here
    log_success "Cleanup completed"
}

# Main deployment process
main() {
    echo "🚀 eWasl.com Production Deployment"
    echo "Enhanced Media Processing Pipeline"
    echo "DigitalOcean App Platform"
    echo ""
    
    # Run deployment steps
    check_prerequisites
    validate_environment
    pre_deployment_checks
    create_spaces_bucket
    deploy_application
    monitor_deployment
    get_deployment_info
    test_deployment
    
    echo ""
    log_success "🎉 Production deployment completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Configure custom domain (if not already done)"
    echo "2. Set up monitoring and alerts"
    echo "3. Configure SSL certificate"
    echo "4. Test all Enhanced Media Processing Pipeline features"
    echo "5. Monitor application performance"
    echo ""
}

# Trap cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
