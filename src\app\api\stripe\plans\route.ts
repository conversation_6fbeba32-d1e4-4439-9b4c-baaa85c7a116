import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';
import { SUBSCRIPTION_PLANS } from '@/lib/stripe/config';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServiceRoleClient();

    // Get all active subscription plans from database
    const { data: plans, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Failed to get subscription plans:', error);
      return NextResponse.json(
        { error: 'Failed to get subscription plans' },
        { status: 500 }
      );
    }

    // Enhance plans with configuration data
    const enhancedPlans = plans.map(plan => {
      const configPlan = Object.values(SUBSCRIPTION_PLANS).find(
        p => p.id === plan.plan_type
      );

      return {
        ...plan,
        config: configPlan,
        features: plan.features || {},
        stripePriceIds: configPlan?.stripePriceIds || { monthly: null, yearly: null },
      };
    });

    return NextResponse.json({
      success: true,
      plans: enhancedPlans,
    });

  } catch (error) {
    console.error('Get plans error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get plans',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
