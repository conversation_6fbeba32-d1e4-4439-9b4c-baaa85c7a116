'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Send, 
  Calendar, 
  Image, 
  Hash, 
  AtSign, 
  MapPin,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  Facebook,
  Linkedin,
  Instagram,
  Twitter,
  Plus,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';

interface SocialAccount {
  id: string;
  platform: string;
  username: string;
  isActive: boolean;
  businessAccountId?: string;
  businessAccountName?: string;
}

interface PublishingFormData {
  content: string;
  platforms: {
    platform: string;
    accountId: string;
    businessAccountId?: string;
    enabled: boolean;
    customContent?: string;
  }[];
  mediaUrls: string[];
  scheduledAt?: string;
  hashtags: string[];
  mentions: string[];
  location?: string;
}

export function PublishingDashboard() {
  const [formData, setFormData] = useState<PublishingFormData>({
    content: '',
    platforms: [],
    mediaUrls: [],
    hashtags: [],
    mentions: [],
    location: ''
  });

  const [socialAccounts, setSocialAccounts] = useState<SocialAccount[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [publishingResults, setPublishingResults] = useState<any>(null);

  useEffect(() => {
    loadSocialAccounts();
  }, []);

  const loadSocialAccounts = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/social/accounts');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load social accounts');
      }

      const accounts: SocialAccount[] = data.accounts?.map((account: any) => ({
        id: account.id,
        platform: account.platform,
        username: account.username || account.platform_username,
        isActive: account.is_active,
        businessAccountId: account.business_account_id,
        businessAccountName: account.business_account_name
      })) || [];

      setSocialAccounts(accounts);

      // Initialize platform selections
      const platformSelections = accounts.map(account => ({
        platform: account.platform,
        accountId: account.id,
        businessAccountId: account.businessAccountId,
        enabled: false,
        customContent: ''
      }));

      setFormData(prev => ({
        ...prev,
        platforms: platformSelections
      }));

    } catch (error: any) {
      console.error('Error loading social accounts:', error);
      toast.error('فشل في تحميل الحسابات الاجتماعية');
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
  };

  const handlePlatformToggle = (platformIndex: number, enabled: boolean) => {
    setFormData(prev => ({
      ...prev,
      platforms: prev.platforms.map((platform, index) =>
        index === platformIndex ? { ...platform, enabled } : platform
      )
    }));
  };

  const handleCustomContentChange = (platformIndex: number, customContent: string) => {
    setFormData(prev => ({
      ...prev,
      platforms: prev.platforms.map((platform, index) =>
        index === platformIndex ? { ...platform, customContent } : platform
      )
    }));
  };

  const handleMediaUrlAdd = () => {
    const url = prompt('أدخل رابط الصورة أو الفيديو:');
    if (url) {
      setFormData(prev => ({
        ...prev,
        mediaUrls: [...prev.mediaUrls, url]
      }));
    }
  };

  const handleMediaLibraryOpen = () => {
    // Open media library modal or navigate to media management
    window.open('/media-management', '_blank');
  };

  const handleMediaUrlRemove = (index: number) => {
    setFormData(prev => ({
      ...prev,
      mediaUrls: prev.mediaUrls.filter((_, i) => i !== index)
    }));
  };

  const handleHashtagsChange = (hashtags: string) => {
    const hashtagArray = hashtags.split(' ').filter(tag => tag.startsWith('#'));
    setFormData(prev => ({
      ...prev,
      hashtags: hashtagArray
    }));
  };

  const handleMentionsChange = (mentions: string) => {
    const mentionArray = mentions.split(' ').filter(mention => mention.startsWith('@'));
    setFormData(prev => ({
      ...prev,
      mentions: mentionArray
    }));
  };

  const publishNow = async () => {
    await publishContent(false);
  };

  const schedulePost = async () => {
    if (!formData.scheduledAt) {
      toast.error('يرجى تحديد وقت الجدولة');
      return;
    }
    await publishContent(true);
  };

  const publishContent = async (isScheduled: boolean) => {
    try {
      setIsPublishing(true);
      setPublishingResults(null);

      // Validate form
      if (!formData.content.trim()) {
        toast.error('يرجى إدخال محتوى المنشور');
        return;
      }

      const enabledPlatforms = formData.platforms.filter(p => p.enabled);
      if (enabledPlatforms.length === 0) {
        toast.error('يرجى اختيار منصة واحدة على الأقل');
        return;
      }

      // Prepare publishing request
      const publishingRequest = {
        content: formData.content,
        platforms: enabledPlatforms.map(platform => ({
          platform: platform.platform,
          accountId: platform.accountId,
          businessAccountId: platform.businessAccountId,
          customContent: platform.customContent || undefined
        })),
        mediaUrls: formData.mediaUrls.length > 0 ? formData.mediaUrls : undefined,
        scheduledAt: isScheduled ? formData.scheduledAt : undefined,
        hashtags: formData.hashtags,
        mentions: formData.mentions,
        location: formData.location || undefined
      };

      console.log('📤 Publishing request:', publishingRequest);

      const response = await fetch('/api/publishing/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(publishingRequest)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'فشل في النشر');
      }

      setPublishingResults(data.data);
      
      if (isScheduled) {
        toast.success(`تم جدولة المنشور بنجاح لـ ${new Date(formData.scheduledAt!).toLocaleString('ar-SA')}`);
      } else {
        toast.success(`تم نشر المحتوى على ${data.data.successfulPlatforms} من ${data.data.totalPlatforms} منصات`);
      }

      // Reset form if publishing was successful
      if (data.data.successfulPlatforms > 0 || isScheduled) {
        setFormData(prev => ({
          ...prev,
          content: '',
          mediaUrls: [],
          hashtags: [],
          mentions: [],
          location: '',
          scheduledAt: undefined,
          platforms: prev.platforms.map(p => ({ ...p, enabled: false, customContent: '' }))
        }));
      }

    } catch (error: any) {
      console.error('Publishing error:', error);
      toast.error(error.message || 'فشل في النشر');
    } finally {
      setIsPublishing(false);
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return <Facebook className="w-4 h-4 text-blue-600" />;
      case 'linkedin':
        return <Linkedin className="w-4 h-4 text-blue-700" />;
      case 'instagram':
        return <Instagram className="w-4 h-4 text-pink-600" />;
      case 'twitter':
        return <Twitter className="w-4 h-4 text-blue-400" />;
      default:
        return <div className="w-4 h-4 bg-gray-400 rounded" />;
    }
  };

  const getPlatformName = (platform: string) => {
    const names: Record<string, string> = {
      facebook: 'فيسبوك',
      linkedin: 'لينكد إن',
      instagram: 'إنستغرام',
      twitter: 'تويتر'
    };
    return names[platform.toLowerCase()] || platform;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
            <span className="mr-2">جاري تحميل الحسابات...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Publishing Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="w-5 h-5" />
            نشر محتوى جديد
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Content Input */}
          <div className="space-y-2">
            <Label htmlFor="content">محتوى المنشور</Label>
            <Textarea
              id="content"
              placeholder="اكتب محتوى منشورك هنا..."
              value={formData.content}
              onChange={(e) => handleContentChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <div className="text-sm text-muted-foreground text-left">
              {formData.content.length} / 10,000 حرف
            </div>
          </div>

          {/* Platform Selection */}
          <div className="space-y-3">
            <Label>اختيار المنصات</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {formData.platforms.map((platform, index) => {
                const account = socialAccounts.find(acc => acc.id === platform.accountId);
                if (!account) return null;

                return (
                  <div key={platform.accountId} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={platform.enabled}
                          onCheckedChange={(checked) => handlePlatformToggle(index, !!checked)}
                        />
                        {getPlatformIcon(platform.platform)}
                        <div>
                          <div className="font-medium">{getPlatformName(platform.platform)}</div>
                          <div className="text-sm text-muted-foreground">
                            {account.businessAccountName || account.username}
                          </div>
                        </div>
                      </div>
                      <Badge variant={account.isActive ? 'default' : 'secondary'}>
                        {account.isActive ? 'نشط' : 'غير نشط'}
                      </Badge>
                    </div>

                    {platform.enabled && (
                      <div className="space-y-2">
                        <Label className="text-sm">محتوى مخصص (اختياري)</Label>
                        <Textarea
                          placeholder={`محتوى مخصص لـ ${getPlatformName(platform.platform)}...`}
                          value={platform.customContent}
                          onChange={(e) => handleCustomContentChange(index, e.target.value)}
                          rows={2}
                          className="text-sm"
                        />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Media URLs */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>الصور والفيديوهات</Label>
              <div className="flex gap-2">
                <Button onClick={handleMediaLibraryOpen} variant="outline" size="sm">
                  <Image className="w-4 h-4 ml-1" />
                  مكتبة الوسائط
                </Button>
                <Button onClick={handleMediaUrlAdd} variant="outline" size="sm">
                  <Plus className="w-4 h-4 ml-1" />
                  إضافة رابط
                </Button>
              </div>
            </div>
            {formData.mediaUrls.length > 0 && (
              <div className="space-y-2">
                {formData.mediaUrls.map((url, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 border rounded">
                    <Image className="w-4 h-4 text-muted-foreground" />
                    <span className="flex-1 text-sm truncate">{url}</span>
                    <Button
                      onClick={() => handleMediaUrlRemove(index)}
                      variant="ghost"
                      size="sm"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Additional Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="hashtags">الهاشتاغات</Label>
              <Input
                id="hashtags"
                placeholder="#هاشتاغ #آخر"
                onChange={(e) => handleHashtagsChange(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mentions">الإشارات</Label>
              <Input
                id="mentions"
                placeholder="@اسم_المستخدم @آخر"
                onChange={(e) => handleMentionsChange(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">الموقع</Label>
              <Input
                id="location"
                placeholder="الرياض، السعودية"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
              />
            </div>
          </div>

          {/* Scheduling */}
          <div className="space-y-2">
            <Label htmlFor="scheduledAt">جدولة المنشور (اختياري)</Label>
            <Input
              id="scheduledAt"
              type="datetime-local"
              value={formData.scheduledAt}
              onChange={(e) => setFormData(prev => ({ ...prev, scheduledAt: e.target.value }))}
              min={new Date().toISOString().slice(0, 16)}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={publishNow}
              disabled={isPublishing}
              className="flex items-center gap-2"
            >
              {isPublishing ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              نشر الآن
            </Button>

            <Button
              onClick={schedulePost}
              disabled={isPublishing || !formData.scheduledAt}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Calendar className="w-4 h-4" />
              جدولة المنشور
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Publishing Results */}
      {publishingResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {publishingResults.success ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-500" />
              )}
              نتائج النشر
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {publishingResults.successfulPlatforms}
                  </div>
                  <div className="text-sm text-muted-foreground">نجح</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {publishingResults.failedPlatforms}
                  </div>
                  <div className="text-sm text-muted-foreground">فشل</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {publishingResults.totalPlatforms}
                  </div>
                  <div className="text-sm text-muted-foreground">المجموع</div>
                </div>
              </div>

              {publishingResults.results && publishingResults.results.length > 0 && (
                <div className="space-y-2">
                  <Label>تفاصيل النشر</Label>
                  {publishingResults.results.map((result: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        {getPlatformIcon(result.platform)}
                        <span>{getPlatformName(result.platform)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {result.success ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <Badge className="bg-green-500">نجح</Badge>
                          </>
                        ) : (
                          <>
                            <AlertCircle className="w-4 h-4 text-red-500" />
                            <Badge variant="destructive">فشل</Badge>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
