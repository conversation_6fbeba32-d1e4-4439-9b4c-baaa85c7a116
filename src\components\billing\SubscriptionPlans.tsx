'use client';

import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface Plan {
  id: string;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  price_monthly: number;
  price_yearly: number;
  max_social_accounts: number;
  max_users: number;
  max_posts_per_month: number | null;
  features: Record<string, boolean>;
  plan_type: string;
  config?: {
    stripePriceIds: {
      monthly: string | null;
      yearly: string | null;
    };
  };
}

interface SubscriptionPlansProps {
  currentPlan?: string;
  isRTL?: boolean;
}

export default function SubscriptionPlans({ currentPlan = 'free', isRTL = false }: SubscriptionPlansProps) {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [subscribingTo, setSubscribingTo] = useState<string | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/stripe/plans');
      const data = await response.json();
      
      if (data.success) {
        setPlans(data.plans);
      } else {
        setError('Failed to load subscription plans');
      }
    } catch (err) {
      setError('Failed to load subscription plans');
      console.error('Error fetching plans:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (plan: Plan) => {
    if (plan.plan_type === 'free') return;
    if (plan.plan_type === 'enterprise') {
      // Handle enterprise contact form
      window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank');
      return;
    }

    setSubscribingTo(plan.id);
    setError('');

    try {
      const priceId = billingCycle === 'monthly' 
        ? plan.config?.stripePriceIds.monthly 
        : plan.config?.stripePriceIds.yearly;

      if (!priceId) {
        throw new Error('Price ID not found for this plan');
      }

      const response = await fetch('/api/stripe/create-subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          priceId,
          billingCycle 
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to create subscription');
      }

      if (data.clientSecret) {
        const stripe = await stripePromise;
        if (!stripe) throw new Error('Stripe not loaded');

        const { error: stripeError } = await stripe.confirmPayment({
          clientSecret: data.clientSecret,
          confirmParams: {
            return_url: `${window.location.origin}/billing/success?plan=${plan.plan_type}`,
          },
        });

        if (stripeError) {
          throw new Error(stripeError.message);
        }
      } else {
        // Subscription created successfully (trial or free)
        window.location.href = `/billing/success?plan=${plan.plan_type}`;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Subscription failed');
      console.error('Subscription error:', err);
    } finally {
      setSubscribingTo(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getFeatureList = (features: Record<string, boolean>) => {
    const featureMap: Record<string, { en: string; ar: string }> = {
      basicScheduling: { en: 'Basic Scheduling', ar: 'جدولة أساسية' },
      basicAnalytics: { en: 'Basic Analytics', ar: 'تحليلات أساسية' },
      arabicSupport: { en: 'Arabic Support', ar: 'دعم اللغة العربية' },
      rtlSupport: { en: 'RTL Text Support', ar: 'دعم النص من اليمين لليسار' },
      imageUpload: { en: 'Image Upload', ar: 'رفع الصور' },
      basicNotifications: { en: 'Basic Notifications', ar: 'إشعارات أساسية' },
      unlimitedPosts: { en: 'Unlimited Posts', ar: 'منشورات غير محدودة' },
      postScheduling: { en: 'Advanced Scheduling', ar: 'جدولة متقدمة' },
      basicAiContent: { en: 'Basic AI Content', ar: 'محتوى ذكي أساسي' },
      calendarView: { en: 'Calendar View', ar: 'عرض التقويم' },
      smartScheduling: { en: 'Smart Scheduling', ar: 'جدولة ذكية' },
      imageVideoUpload: { en: 'Image & Video Upload', ar: 'رفع الصور والفيديو' },
      commentReplies: { en: 'Comment Replies', ar: 'الرد على التعليقات' },
      activityMonitoring: { en: 'Activity Monitoring', ar: 'مراقبة النشاط' },
      advancedAnalytics: { en: 'Advanced Analytics', ar: 'تحليلات متقدمة' },
      dragDropScheduling: { en: 'Drag & Drop Scheduling', ar: 'جدولة بالسحب والإفلات' },
      performanceAnalytics: { en: 'Performance Analytics', ar: 'تحليلات الأداء' },
      contentLibrary: { en: 'Content Library', ar: 'مكتبة المحتوى' },
      contentTemplates: { en: 'Content Templates', ar: 'قوالب المحتوى' },
      advancedAiContent: { en: 'Advanced AI Content', ar: 'محتوى ذكي متقدم' },
      realtimeNotifications: { en: 'Real-time Notifications', ar: 'إشعارات فورية' },
      activityAnalysis: { en: 'Activity Analysis', ar: 'تحليل النشاط' },
      dataExport: { en: 'Data Export', ar: 'تصدير البيانات' },
      prioritySupport: { en: 'Priority Support', ar: 'دعم أولوية' },
      automatedReports: { en: 'Automated Reports', ar: 'تقارير آلية' },
      teamCollaboration: { en: 'Team Collaboration', ar: 'تعاون الفريق' },
    };

    return Object.entries(features)
      .filter(([_, enabled]) => enabled)
      .map(([key, _]) => featureMap[key] || { en: key, ar: key });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Billing Cycle Toggle */}
      <div className="flex justify-center mb-8">
        <div className="bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setBillingCycle('monthly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'monthly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-900'
            }`}
          >
            {isRTL ? 'شهري' : 'Monthly'}
          </button>
          <button
            onClick={() => setBillingCycle('yearly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'yearly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-900'
            }`}
          >
            {isRTL ? 'سنوي' : 'Yearly'}
            <span className="ml-1 text-green-600 text-xs">
              {isRTL ? 'وفر 17%' : 'Save 17%'}
            </span>
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700 text-sm">{error}</div>
        </div>
      )}

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {plans.map((plan) => {
          const isCurrentPlan = plan.plan_type === currentPlan;
          const isPopular = plan.plan_type === 'business';
          const price = billingCycle === 'monthly' ? plan.price_monthly : plan.price_yearly;
          const features = getFeatureList(plan.features);

          return (
            <div
              key={plan.id}
              className={`relative bg-white rounded-lg shadow-lg border-2 transition-all duration-200 hover:shadow-xl ${
                isPopular ? 'border-blue-500' : 'border-gray-200'
              } ${isCurrentPlan ? 'ring-2 ring-green-500' : ''}`}
            >
              {/* Popular Badge */}
              {isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    {isRTL ? 'الأكثر شعبية' : 'Most Popular'}
                  </span>
                </div>
              )}

              {/* Current Plan Badge */}
              {isCurrentPlan && (
                <div className="absolute -top-3 right-4">
                  <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    {isRTL ? 'الخطة الحالية' : 'Current Plan'}
                  </span>
                </div>
              )}

              <div className="p-6">
                {/* Plan Name */}
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {isRTL ? plan.name_ar : plan.name}
                </h3>

                {/* Plan Description */}
                <p className="text-gray-600 text-sm mb-4">
                  {isRTL ? plan.description_ar : plan.description}
                </p>

                {/* Price */}
                <div className="mb-6">
                  {plan.plan_type === 'free' ? (
                    <div className="text-3xl font-bold text-gray-900">
                      {isRTL ? 'مجاني' : 'Free'}
                    </div>
                  ) : plan.plan_type === 'enterprise' ? (
                    <div className="text-3xl font-bold text-gray-900">
                      {isRTL ? 'مخصص' : 'Custom'}
                    </div>
                  ) : (
                    <div>
                      <span className="text-3xl font-bold text-gray-900">
                        {formatPrice(price)}
                      </span>
                      <span className="text-gray-600 ml-1">
                        /{isRTL ? (billingCycle === 'monthly' ? 'شهر' : 'سنة') : (billingCycle === 'monthly' ? 'month' : 'year')}
                      </span>
                    </div>
                  )}
                </div>

                {/* Plan Limits */}
                <div className="mb-6 space-y-2 text-sm text-gray-600">
                  <div>
                    {plan.max_social_accounts === -1 ? (
                      isRTL ? 'حسابات اجتماعية غير محدودة' : 'Unlimited social accounts'
                    ) : (
                      `${plan.max_social_accounts} ${isRTL ? 'حسابات اجتماعية' : 'social accounts'}`
                    )}
                  </div>
                  <div>
                    {plan.max_users === -1 ? (
                      isRTL ? 'مستخدمين غير محدودين' : 'Unlimited users'
                    ) : (
                      `${plan.max_users} ${isRTL ? 'مستخدمين كحد أقصى' : 'users maximum'}`
                    )}
                  </div>
                  <div>
                    {plan.max_posts_per_month === null ? (
                      isRTL ? 'منشورات غير محدودة' : 'Unlimited posts'
                    ) : (
                      `${plan.max_posts_per_month} ${isRTL ? 'منشورات شهرياً' : 'posts/month'}`
                    )}
                  </div>
                </div>

                {/* Features List */}
                <div className="mb-6">
                  <ul className="space-y-2">
                    {features.slice(0, 6).map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <CheckIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        <span>{isRTL ? feature.ar : feature.en}</span>
                      </li>
                    ))}
                    {features.length > 6 && (
                      <li className="text-sm text-gray-500">
                        {isRTL ? `و ${features.length - 6} ميزات أخرى...` : `And ${features.length - 6} more features...`}
                      </li>
                    )}
                  </ul>
                </div>

                {/* Subscribe Button */}
                <button
                  onClick={() => handleSubscribe(plan)}
                  disabled={isCurrentPlan || subscribingTo === plan.id}
                  className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                    isCurrentPlan
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      : plan.plan_type === 'free'
                      ? 'bg-gray-600 text-white hover:bg-gray-700'
                      : isPopular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-900 text-white hover:bg-gray-800'
                  } ${subscribingTo === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {subscribingTo === plan.id ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {isRTL ? 'جاري المعالجة...' : 'Processing...'}
                    </div>
                  ) : isCurrentPlan ? (
                    isRTL ? 'الخطة الحالية' : 'Current Plan'
                  ) : plan.plan_type === 'free' ? (
                    isRTL ? 'البدء مجاناً' : 'Get Started Free'
                  ) : plan.plan_type === 'enterprise' ? (
                    isRTL ? 'تواصل معنا' : 'Contact Sales'
                  ) : (
                    isRTL ? 'اشترك الآن' : 'Subscribe Now'
                  )}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Trial Notice */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-600">
          {isRTL 
            ? 'جميع الخطط المدفوعة تأتي مع فترة تجريبية مجانية لمدة 14 يوماً. يمكنك الإلغاء في أي وقت.'
            : 'All paid plans come with a 14-day free trial. Cancel anytime.'
          }
        </p>
      </div>
    </div>
  );
}
