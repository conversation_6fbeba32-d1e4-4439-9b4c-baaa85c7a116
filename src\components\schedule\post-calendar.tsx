"use client";

import { useState } from "react";
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import { format, parse, startOfWeek, getDay } from "date-fns";
import { arSA } from "date-fns/locale";
import "react-big-calendar/lib/css/react-big-calendar.css";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatDate, formatTime, getPlatformLabel } from "@/lib/utils";
import Link from "next/link";

interface Post {
  id: string;
  title: string;
  content: string;
  start: Date;
  end: Date;
  status: "DRAFT" | "SCHEDULED" | "PUBLISHED" | "FAILED";
  platforms: string[];
}

const locales = {
  "ar-SA": arSA,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

const messages = {
  today: "اليوم",
  previous: "السابق",
  next: "التالي",
  month: "شهر",
  week: "أسبوع",
  day: "يوم",
  agenda: "جدول",
  date: "تاريخ",
  time: "وقت",
  event: "حدث",
  allDay: "طوال اليوم",
  noEventsInRange: "لا توجد منشورات في هذا النطاق",
};

export function PostCalendar({ posts }: { posts: Post[] }) {
  const [selectedEvent, setSelectedEvent] = useState<Post | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleEventClick = (event: Post) => {
    setSelectedEvent(event);
    setIsDialogOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "bg-gray-500";
      case "SCHEDULED":
        return "bg-blue-500";
      case "PUBLISHED":
        return "bg-green-500";
      case "FAILED":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "مسودة";
      case "SCHEDULED":
        return "مجدول";
      case "PUBLISHED":
        return "منشور";
      case "FAILED":
        return "فشل";
      default:
        return status;
    }
  };

  const eventStyleGetter = (event: Post) => {
    let backgroundColor = "";
    switch (event.status) {
      case "DRAFT":
        backgroundColor = "#6B7280";
        break;
      case "SCHEDULED":
        backgroundColor = "#3B82F6";
        break;
      case "PUBLISHED":
        backgroundColor = "#10B981";
        break;
      case "FAILED":
        backgroundColor = "#EF4444";
        break;
      default:
        backgroundColor = "#6B7280";
    }

    return {
      style: {
        backgroundColor,
        borderRadius: "4px",
        opacity: 0.8,
        color: "white",
        border: "0px",
        display: "block",
      },
    };
  };

  return (
    <>
      <div className="h-[600px] bg-white rounded-md border p-2">
        <Calendar
          localizer={localizer}
          events={posts}
          startAccessor="start"
          endAccessor="end"
          style={{ height: "100%" }}
          onSelectEvent={handleEventClick}
          eventPropGetter={eventStyleGetter}
          messages={messages}
          culture="ar-SA"
          formats={{
            dayHeaderFormat: (date) => format(date, "EEEE d MMMM", { locale: arSA }),
            dayRangeHeaderFormat: ({ start, end }) =>
              `${format(start, "d MMMM", { locale: arSA })} - ${format(end, "d MMMM", { locale: arSA })}`,
            monthHeaderFormat: (date) => format(date, "MMMM yyyy", { locale: arSA }),
          }}
        />
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        {selectedEvent && (
          <DialogContent className="sm:max-w-md" dir="rtl">
            <DialogHeader>
              <DialogTitle>تفاصيل المنشور</DialogTitle>
              <DialogDescription>
                معلومات عن المنشور المجدول
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">المحتوى</p>
                <p className="text-sm text-muted-foreground">
                  {selectedEvent.content}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">الحالة</p>
                <Badge variant="default" className={getStatusColor(selectedEvent.status)}>
                  {getStatusLabel(selectedEvent.status)}
                </Badge>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">موعد النشر</p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(selectedEvent.start)} - {formatTime(selectedEvent.start)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">المنصات</p>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedEvent.platforms.map((platform) => (
                    <Badge key={platform} variant="outline">
                      {getPlatformLabel(platform)}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter className="sm:justify-between">
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                إغلاق
              </Button>
              <div className="flex gap-2">
                <Link href={`/posts/${selectedEvent.id}/edit`}>
                  <Button variant="outline">تعديل</Button>
                </Link>
                <Link href={`/posts/${selectedEvent.id}`}>
                  <Button>عرض التفاصيل</Button>
                </Link>
              </div>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </>
  );
}
