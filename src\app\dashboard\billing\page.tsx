'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSupabase } from '@/components/auth/supabase-provider';
import { SUBSCRIPTION_PLANS, formatPrice } from '@/lib/stripe/config';
import { checkoutAction, customerPortalAction } from '@/lib/stripe/actions';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Crown, Star, Zap, ExternalLink, CreditCard, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';

interface BillingInfo {
  plan: string;
  status: string;
  current_period_end: string | null;
  cancel_at_period_end: boolean;
}

function BillingContent() {
  const { user } = useSupabase();
  const [billingInfo, setBillingInfo] = useState<BillingInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (user) {
      fetchBillingInfo();
    }

    // Handle success/error states from URL params
    const success = searchParams.get('success');
    const canceled = searchParams.get('canceled');
    const error = searchParams.get('error');

    if (success === 'true') {
      toast.success('تم الاشتراك بنجاح! مرحباً بك في الخطة الجديدة.');
      // Clean up URL
      router.replace('/dashboard/billing');
    } else if (canceled === 'true') {
      toast.info('تم إلغاء عملية الدفع.');
      router.replace('/dashboard/billing');
    } else if (error) {
      toast.error('حدث خطأ في عملية الدفع. يرجى المحاولة مرة أخرى.');
      router.replace('/dashboard/billing');
    }
  }, [user, searchParams, router]);

  const fetchBillingInfo = async () => {
    try {
      const response = await fetch(`/api/stripe/manage-billing?userId=${user?.id}`);
      if (response.ok) {
        const data = await response.json();
        setBillingInfo(data);
      }
    } catch (error) {
      console.error('Error fetching billing info:', error);
      toast.error('فشل في تحميل معلومات الفوترة');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    if (!user) {
      toast.error('يجب تسجيل الدخول أولاً');
      return;
    }

    setUpgrading(planId);

    try {
      const plan = SUBSCRIPTION_PLANS[planId as keyof typeof SUBSCRIPTION_PLANS];
      if (!plan || !plan.stripePriceIds.monthly) {
        toast.error('خطة غير صالحة');
        return;
      }

      // Create form data for server action
      const formData = new FormData();
      formData.append('priceId', plan.stripePriceIds.monthly);

      // Call server action
      await checkoutAction(formData);
    } catch (error) {
      console.error('Upgrade error:', error);
      toast.error('حدث خطأ أثناء الترقية');
    } finally {
      setUpgrading(null);
    }
  };

  const handleManageBilling = async () => {
    if (!user) {
      toast.error('يجب تسجيل الدخول أولاً');
      return;
    }

    try {
      // Call server action
      await customerPortalAction();
    } catch (error) {
      console.error('Error opening customer portal:', error);
      toast.error('حدث خطأ أثناء فتح بوابة إدارة الفواتير');
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'PRO':
        return <Star className="h-6 w-6 text-blue-500" />;
      case 'BUSINESS':
        return <Crown className="h-6 w-6 text-purple-500" />;
      case 'ENTERPRISE':
        return <Zap className="h-6 w-6 text-orange-500" />;
      default:
        return <CheckCircle className="h-6 w-6 text-green-500" />;
    }
  };

  const getPlanColor = (planId: string) => {
    switch (planId) {
      case 'PRO':
        return 'border-blue-200 bg-blue-50';
      case 'BUSINESS':
        return 'border-purple-200 bg-purple-50';
      case 'ENTERPRISE':
        return 'border-orange-200 bg-orange-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-96 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">إدارة الاشتراك</h1>
        <p className="text-gray-600">اختر الخطة المناسبة لاحتياجاتك</p>

        {billingInfo && billingInfo.plan !== 'FREE' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-center gap-2">
              {getPlanIcon(billingInfo.plan)}
              <span className="font-semibold">
                الخطة الحالية: {SUBSCRIPTION_PLANS[billingInfo.plan as keyof typeof SUBSCRIPTION_PLANS]?.name}
              </span>
              <Badge variant={billingInfo.status === 'active' ? 'default' : 'secondary'}>
                {billingInfo.status === 'active' ? 'نشط' : billingInfo.status}
              </Badge>
            </div>
            {billingInfo.current_period_end && (
              <p className="text-sm text-gray-600 mt-2">
                {billingInfo.cancel_at_period_end
                  ? `سينتهي في: ${new Date(billingInfo.current_period_end).toLocaleDateString('ar-SA')}`
                  : `التجديد التالي: ${new Date(billingInfo.current_period_end).toLocaleDateString('ar-SA')}`
                }
              </p>
            )}
            <Button
              onClick={handleManageBilling}
              variant="outline"
              size="sm"
              className="mt-3"
            >
              <ExternalLink className="h-4 w-4 ml-2" />
              إدارة الاشتراك
            </Button>
          </div>
        )}
      </div>

      {/* Current Subscription Status */}
      {billingInfo && billingInfo.plan !== 'FREE' && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              اشتراكك الحالي
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-lg font-semibold">
                  {SUBSCRIPTION_PLANS[billingInfo.plan as keyof typeof SUBSCRIPTION_PLANS]?.nameAr || billingInfo.plan}
                </p>
                <p className="text-sm text-muted-foreground">
                  الحالة: {billingInfo.status === 'active' ? 'نشط' : billingInfo.status}
                </p>
                {billingInfo.current_period_end && (
                  <p className="text-sm text-muted-foreground">
                    ينتهي في: {new Date(billingInfo.current_period_end).toLocaleDateString('ar-SA')}
                  </p>
                )}
              </div>
              <Button onClick={handleManageBilling} variant="outline" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                إدارة الاشتراك
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Object.entries(SUBSCRIPTION_PLANS).map(([planId, plan]) => {
          const isCurrentPlan = billingInfo?.plan === planId;
          const isFreePlan = planId === 'FREE';

          return (
            <Card
              key={planId}
              className={`relative ${getPlanColor(planId)} ${
                isCurrentPlan ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              {planId === 'PRO' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white">الأكثر شعبية</Badge>
                </div>
              )}

              <CardHeader className="text-center">
                <div className="flex justify-center mb-2">
                  {getPlanIcon(planId)}
                </div>
                <CardTitle className="text-xl">{plan.nameAr || plan.name}</CardTitle>
                {plan.descriptionAr && (
                  <p className="text-sm text-muted-foreground mb-2">{plan.descriptionAr}</p>
                )}
                <CardDescription>
                  {plan.customPricing ? (
                    <div className="text-center">
                      <span className="text-lg font-bold">حسب الطلب</span>
                      <p className="text-sm text-muted-foreground mt-1">{plan.contactText}</p>
                    </div>
                  ) : (
                    <>
                      <span className="text-3xl font-bold">
                        {isFreePlan ? 'مجاني' : formatPrice(plan.priceMonthly || 0)}
                      </span>
                      {!isFreePlan && <span className="text-sm">/شهر</span>}
                    </>
                  )}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-2 text-sm">
                  {Object.entries(plan.features).map(([featureKey, enabled], index) => {
                    if (!enabled) return null;

                    const featureLabels: Record<string, string> = {
                      // Free Plan Features
                      basicScheduling: 'جدولة أساسية',
                      basicAnalytics: 'تحليلات أساسية',
                      arabicSupport: 'دعم اللغة العربية',
                      rtlSupport: 'دعم RTL',
                      imageUpload: 'رفع الصور',
                      basicNotifications: 'إشعارات أساسية',

                      // Pro Plan Features
                      socialAccounts5: 'إدارة 5 حسابات اجتماعية',
                      unlimitedPosts: 'عدد غير محدود من المنشورات',
                      users2: 'مستخدمان فقط',
                      postScheduling: 'إنشاء المنشورات وجدولتها',
                      basicAiContent: 'توليد منشورات AI (أساسي)',
                      calendarView: 'عرض تقويم بسيط وجدولة ذكية',
                      imageVideoUpload: 'تحميل الصور ومقاطع الفيديو',
                      commentReplies: 'الرد على تعليقات ومتابعة النشاط',

                      // Business Plan Features
                      allProFeatures: 'كل ميزات خطة المحترف، بالإضافة إلى:',
                      socialAccounts10: 'إدارة 10 حسابات اجتماعية',
                      users5: 'حتى 5 مستخدمين',
                      dragDropScheduling: 'جدولة متقدمة بالسحب والإفلات',
                      advancedAnalytics: 'تحليل أداء متقدم (المنشورات، المتابعين، المقارنة بين المنصات)',
                      contentLibrary: 'مكتبة محتوى وقوالب جاهزة للنشر',
                      advancedAiContent: 'دعم توليد محتوى AI متقدم',
                      realtimeNotifications: 'إشعارات فورية وتحليل النشاط في الوقت الحقيقي',
                      dataExport: 'تصدير البيانات بصيغ CSV / PDF / Excel',
                      prioritySupport: 'دعم مخصص بأولوية عالية',
                      automatedReports: 'تقارير أسبوعية تلقائية',

                      // Enterprise Plan Features
                      unlimitedAccounts: 'عدد غير محدود من الحسابات',
                      unlimitedUsers: 'عدد غير محدود من المستخدمين',
                      customAnalytics: 'تحليلات مخصصة وتقارير شاملة',
                      integrations: 'دعم تكاملات إضافية (CRM، Zapier، WhatsApp API)',
                      adminDashboard: 'لوحة تحكم إدارية موسعة',
                      directTraining: 'تدريب ودعم مباشر',
                      uiCustomization: 'تخصيص كامل للواجهة والتقارير',
                      advancedSecurity: 'طبقة أمان متقدمة (SSO، إدارة أذونات دقيقة)',
                    };

                    return (
                      <li key={featureKey} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span>{featureLabels[featureKey] || featureKey}</span>
                      </li>
                    );
                  })}
                </ul>

                {isCurrentPlan ? (
                  <Button disabled className="w-full">
                    الخطة الحالية
                  </Button>
                ) : isFreePlan ? (
                  <Button variant="outline" disabled className="w-full">
                    خطة مجانية
                  </Button>
                ) : plan.customPricing ? (
                  <Button
                    onClick={() => window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank')}
                    variant="outline"
                    className="w-full"
                  >
                    تواصل معنا
                  </Button>
                ) : (
                  <Button
                    onClick={() => handleUpgrade(planId)}
                    disabled={upgrading === planId}
                    className="w-full"
                  >
                    {upgrading === planId ? 'جاري المعالجة...' : 'ترقية الآن'}
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

// Loading component for Suspense fallback
function BillingLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">جاري تحميل معلومات الفوترة...</p>
      </div>
    </div>
  );
}

// Main component wrapped in Suspense
export default function BillingPage() {
  return (
    <Suspense fallback={<BillingLoading />}>
      <BillingContent />
    </Suspense>
  );
}
