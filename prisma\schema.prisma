// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// This is used for the seed script
generator ts_node {
  provider = "prisma-client-js"
  binaryTargets = ["native"]
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id            String    @id @default(uuid()) @db.Uuid
  name          String?
  email         String    @unique
  emailVerified DateTime? @map("email_verified")
  image         String?
  password      String?
  role          Role      @default(USER)
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  accounts      Account[]
  sessions      Session[]
  posts         Post[]
  socialAccounts SocialAccount[]
  activities    Activity[]

  @@map("users")
}

// NextAuth models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Social media models
model SocialAccount {
  id          String    @id @default(uuid()) @db.Uuid
  userId      String    @map("user_id") @db.Uuid
  platform    Platform
  accountId   String    @map("account_id")
  accountName String    @map("account_name")
  accessToken String    @map("access_token") @db.Text
  refreshToken String?  @map("refresh_token") @db.Text
  expiresAt   DateTime? @map("expires_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  posts       Post[]

  @@unique([userId, platform, accountId])
  @@map("social_accounts")
}

model Post {
  id          String    @id @default(uuid()) @db.Uuid
  userId      String    @map("user_id") @db.Uuid
  content     String    @db.Text
  mediaUrl    String?   @map("media_url")
  status      PostStatus @default(DRAFT)
  scheduledAt DateTime? @map("scheduled_at")
  publishedAt DateTime? @map("published_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  socialAccounts SocialAccount[]
  activities   Activity[]

  @@map("posts")
}

model Activity {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  postId    String?  @map("post_id") @db.Uuid
  action    Action
  details   String?
  metadata  Json?
  createdAt DateTime @default(now()) @map("created_at")

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post      Post?    @relation(fields: [postId], references: [id], onDelete: SetNull)

  @@index([userId, createdAt])
  @@index([action])
  @@map("activities")
}

model ErrorLog {
  id             String      @id @default(cuid())
  errorId        String      @unique
  message        String      @db.Text
  stack          String?     @db.Text
  componentStack String?     @db.Text
  url            String?
  userAgent      String?
  clientIp       String?
  userId         String?
  additionalInfo Json?
  severity       ErrorSeverity @default(MEDIUM)
  environment    String?
  resolved       Boolean     @default(false)
  resolvedAt     DateTime?
  resolvedBy     String?
  timestamp      DateTime    @default(now())
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@index([severity, resolved])
  @@index([timestamp])
  @@index([userId])
}

// Enums
enum Role {
  USER
  ADMIN
}

enum Platform {
  TWITTER
  FACEBOOK
  INSTAGRAM
  LINKEDIN
  TIKTOK
}

enum PostStatus {
  DRAFT
  SCHEDULED
  PUBLISHED
  FAILED
}

enum Action {
  CONNECT_SOCIAL
  POST_SCHEDULED
  POST_PUBLISHED
  POST_FAILED
  REGISTER
  LOGIN
  LOGOUT
}

enum ErrorSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
