import { test, expect } from '@playwright/test';

// End-to-end tests for team collaboration features
test.describe('Team Collaboration E2E Tests', () => {
  let organizationId: string;
  let workspaceId: string;

  test.beforeEach(async ({ page }) => {
    // Navigate to login page and authenticate
    await page.goto('/login');
    
    // Fill login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'testpassword123');
    await page.click('[data-testid="login-button"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('/dashboard');
  });

  test.describe('Organization Management', () => {
    test('should create new organization', async ({ page }) => {
      // Navigate to teams page
      await page.goto('/teams');
      
      // Check if no organizations exist
      const noOrgsText = page.locator('text=لا توجد مؤسسات');
      if (await noOrgsText.isVisible()) {
        // Click create organization button
        await page.click('text=إنشاء مؤسستي الأولى');
        
        // Wait for prompt and enter organization name
        page.on('dialog', async dialog => {
          expect(dialog.message()).toContain('اسم المؤسسة');
          await dialog.accept('Test Organization E2E');
        });
        
        // Wait for organization to be created and page to reload
        await page.waitForURL('/teams');
        await page.waitForSelector('text=Test Organization E2E');
        
        // Verify organization is displayed
        await expect(page.locator('text=Test Organization E2E')).toBeVisible();
        await expect(page.locator('text=owner')).toBeVisible();
      }
    });

    test('should display organization details', async ({ page }) => {
      await page.goto('/teams');
      
      // Wait for organization to load
      await page.waitForSelector('[data-testid="organization-card"]', { timeout: 10000 });
      
      // Verify organization information
      await expect(page.locator('[data-testid="organization-name"]')).toBeVisible();
      await expect(page.locator('[data-testid="member-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="workspace-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="subscription-plan"]')).toBeVisible();
    });

    test('should switch between organizations', async ({ page }) => {
      await page.goto('/teams');
      
      // Open organization selector
      await page.click('[data-testid="organization-selector"]');
      
      // Verify dropdown is open
      await expect(page.locator('[role="listbox"]')).toBeVisible();
      
      // Select first organization (if multiple exist)
      const firstOrg = page.locator('[role="option"]').first();
      if (await firstOrg.isVisible()) {
        await firstOrg.click();
        
        // Verify organization switched
        await page.waitForSelector('[data-testid="organization-card"]');
      }
    });
  });

  test.describe('Workspace Management', () => {
    test('should display default workspace', async ({ page }) => {
      await page.goto('/teams');
      
      // Wait for workspaces to load
      await page.waitForSelector('[data-testid="workspace-card"]', { timeout: 10000 });
      
      // Verify default workspace exists
      await expect(page.locator('text=General')).toBeVisible();
      await expect(page.locator('[data-testid="workspace-members"]')).toBeVisible();
      await expect(page.locator('[data-testid="workspace-posts"]')).toBeVisible();
    });

    test('should create new workspace', async ({ page }) => {
      await page.goto('/teams');
      
      // Wait for page to load
      await page.waitForSelector('[data-testid="create-workspace-button"]', { timeout: 10000 });
      
      // Click create workspace button
      await page.click('[data-testid="create-workspace-button"]');
      
      // Fill workspace form
      await page.fill('[data-testid="workspace-name"]', 'Test Workspace E2E');
      await page.fill('[data-testid="workspace-slug"]', 'test-workspace-e2e');
      await page.fill('[data-testid="workspace-description"]', 'E2E test workspace');
      
      // Submit form
      await page.click('[data-testid="create-workspace-submit"]');
      
      // Wait for workspace to be created
      await page.waitForSelector('text=Test Workspace E2E');
      
      // Verify workspace is displayed
      await expect(page.locator('text=Test Workspace E2E')).toBeVisible();
    });

    test('should switch between workspaces', async ({ page }) => {
      await page.goto('/teams');
      
      // Wait for workspaces to load
      await page.waitForSelector('[data-testid="workspace-card"]', { timeout: 10000 });
      
      // Click on a workspace card
      const workspaceCard = page.locator('[data-testid="workspace-card"]').first();
      await workspaceCard.click();
      
      // Verify workspace is selected (highlighted)
      await expect(workspaceCard).toHaveClass(/ring-2/);
      await expect(workspaceCard).toHaveClass(/ring-blue-500/);
    });
  });

  test.describe('Team Member Management', () => {
    test('should display current user as member', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to members tab
      await page.click('text=الأعضاء');
      
      // Wait for members to load
      await page.waitForSelector('[data-testid="member-card"]', { timeout: 10000 });
      
      // Verify current user is listed
      await expect(page.locator('text=<EMAIL>')).toBeVisible();
      await expect(page.locator('text=admin')).toBeVisible();
      await expect(page.locator('text=نشط')).toBeVisible();
    });

    test('should show invite member button', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to members tab
      await page.click('text=الأعضاء');
      
      // Verify invite button is visible
      await expect(page.locator('text=دعوة عضو')).toBeVisible();
    });

    test('should open invite member dialog', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to members tab
      await page.click('text=الأعضاء');
      
      // Click invite member button
      await page.click('text=دعوة عضو');
      
      // Verify dialog opens
      await expect(page.locator('[role="dialog"]')).toBeVisible();
      await expect(page.locator('text=دعوة عضو جديد')).toBeVisible();
    });
  });

  test.describe('Content Approval Workflow', () => {
    test('should display pending approvals', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to approvals tab
      await page.click('text=الموافقات');
      
      // Wait for approvals to load
      await page.waitForSelector('[data-testid="approvals-container"]', { timeout: 10000 });
      
      // Check if there are any pending approvals
      const noApprovals = page.locator('text=لا توجد طلبات موافقة');
      const approvalCard = page.locator('[data-testid="approval-card"]');
      
      if (await approvalCard.count() > 0) {
        // Verify approval information is displayed
        await expect(approvalCard.first()).toBeVisible();
        await expect(page.locator('[data-testid="approval-status"]')).toBeVisible();
        await expect(page.locator('[data-testid="approval-step"]')).toBeVisible();
      } else {
        // Verify empty state
        await expect(noApprovals).toBeVisible();
      }
    });

    test('should show approval actions for authorized users', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to approvals tab
      await page.click('text=الموافقات');
      
      // Check if there are pending approvals
      const approvalCard = page.locator('[data-testid="approval-card"]');
      
      if (await approvalCard.count() > 0) {
        // Verify approval action buttons
        await expect(page.locator('text=موافقة')).toBeVisible();
        await expect(page.locator('text=طلب تعديل')).toBeVisible();
        await expect(page.locator('text=رفض')).toBeVisible();
      }
    });

    test('should filter approvals by status', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to approvals tab
      await page.click('text=الموافقات');
      
      // Open status filter
      await page.click('[data-testid="approval-status-filter"]');
      
      // Select different status
      await page.click('text=موافق عليها');
      
      // Verify filter is applied
      await expect(page.locator('[data-testid="approval-status-filter"]')).toContainText('موافق عليها');
    });
  });

  test.describe('Dashboard Overview', () => {
    test('should display team statistics', async ({ page }) => {
      await page.goto('/teams');
      
      // Verify overview tab is active by default
      await expect(page.locator('[data-testid="overview-tab"]')).toHaveAttribute('data-state', 'active');
      
      // Verify statistics cards
      await expect(page.locator('[data-testid="team-members-stat"]')).toBeVisible();
      await expect(page.locator('[data-testid="pending-approvals-stat"]')).toBeVisible();
      await expect(page.locator('[data-testid="monthly-posts-stat"]')).toBeVisible();
    });

    test('should display recent activity', async ({ page }) => {
      await page.goto('/teams');
      
      // Verify recent activity section
      await expect(page.locator('text=النشاط الأخير')).toBeVisible();
      await expect(page.locator('[data-testid="activity-feed"]')).toBeVisible();
    });

    test('should show workspace quick stats', async ({ page }) => {
      await page.goto('/teams');
      
      // Verify workspace stats in cards
      const workspaceCard = page.locator('[data-testid="workspace-card"]').first();
      
      await expect(workspaceCard.locator('[data-testid="member-count"]')).toBeVisible();
      await expect(workspaceCard.locator('[data-testid="post-count"]')).toBeVisible();
    });
  });

  test.describe('Settings Management', () => {
    test('should display workspace settings', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to settings tab
      await page.click('text=الإعدادات');
      
      // Verify settings form
      await expect(page.locator('text=إعدادات مساحة العمل')).toBeVisible();
      await expect(page.locator('[data-testid="workspace-name-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="workspace-color-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="workspace-description-input"]')).toBeVisible();
    });

    test('should show save changes button', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to settings tab
      await page.click('text=الإعدادات');
      
      // Verify save button
      await expect(page.locator('text=حفظ التغييرات')).toBeVisible();
    });

    test('should update workspace settings', async ({ page }) => {
      await page.goto('/teams');
      
      // Navigate to settings tab
      await page.click('text=الإعدادات');
      
      // Update workspace name
      await page.fill('[data-testid="workspace-name-input"]', 'Updated Workspace Name');
      
      // Update description
      await page.fill('[data-testid="workspace-description-input"]', 'Updated workspace description');
      
      // Save changes
      await page.click('text=حفظ التغييرات');
      
      // Verify success message
      await expect(page.locator('text=تم حفظ التغييرات بنجاح')).toBeVisible();
    });
  });

  test.describe('Navigation and UI', () => {
    test('should have proper RTL layout', async ({ page }) => {
      await page.goto('/teams');
      
      // Verify RTL direction
      const dashboard = page.locator('[data-testid="team-dashboard"]');
      await expect(dashboard).toHaveAttribute('dir', 'rtl');
    });

    test('should be responsive on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto('/teams');
      
      // Verify mobile layout
      await expect(page.locator('[data-testid="team-dashboard"]')).toBeVisible();
      
      // Verify tabs are still accessible
      await expect(page.locator('text=نظرة عامة')).toBeVisible();
      await expect(page.locator('text=الأعضاء')).toBeVisible();
    });

    test('should navigate between tabs smoothly', async ({ page }) => {
      await page.goto('/teams');
      
      // Test tab navigation
      const tabs = ['نظرة عامة', 'الأعضاء', 'الموافقات', 'الإعدادات'];
      
      for (const tab of tabs) {
        await page.click(`text=${tab}`);
        await page.waitForTimeout(500); // Allow for smooth transition
        
        // Verify tab is active
        const tabElement = page.locator(`text=${tab}`);
        await expect(tabElement).toHaveAttribute('data-state', 'active');
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Intercept API calls and simulate network error
      await page.route('/api/teams/**', route => {
        route.abort('failed');
      });
      
      await page.goto('/teams');
      
      // Verify error handling
      await expect(page.locator('text=خطأ في تحميل البيانات')).toBeVisible();
    });

    test('should handle unauthorized access', async ({ page }) => {
      // Intercept API calls and simulate 401 error
      await page.route('/api/teams/**', route => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Unauthorized' }),
        });
      });
      
      await page.goto('/teams');
      
      // Verify redirect to login or error message
      await expect(page.locator('text=تسجيل الدخول مطلوب')).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load team dashboard within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/teams');
      await page.waitForSelector('[data-testid="team-dashboard"]');
      
      const loadTime = Date.now() - startTime;
      
      // Verify page loads within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    test('should handle large datasets efficiently', async ({ page }) => {
      // Mock large dataset response
      await page.route('/api/teams/members**', route => {
        const largeDataset = Array.from({ length: 100 }, (_, i) => ({
          id: `member-${i}`,
          user_id: `user-${i}`,
          email: `user${i}@ewasl.com`,
          name: `User ${i}`,
          role: 'member',
          status: 'active',
        }));
        
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            members: largeDataset,
          }),
        });
      });
      
      await page.goto('/teams');
      await page.click('text=الأعضاء');
      
      // Verify page remains responsive
      await expect(page.locator('[data-testid="member-card"]')).toHaveCount(100);
    });
  });
});
