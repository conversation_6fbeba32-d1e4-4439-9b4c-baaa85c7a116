"use client";

import React, { useState } from "react";
import { PlusCircle, Calendar, List, BarChart3, Users, Settings, LogOut, Bell, User } from "lucide-react";
import Link from "next/link";

export default function SchedulePage() {
  const [activeTab, setActiveTab] = useState("calendar");

  // Mock data for demonstration
  const mockPosts = [
    {
      id: "1",
      title: "منشور مجدول 1",
      content: "هذا منشور مجدول للنشر على تويتر وفيسبوك",
      start: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2),
      status: "SCHEDULED" as const,
      platforms: ["TWITTER", "FACEBOOK"],
    },
    {
      id: "2",
      title: "منشور مجدول 2",
      content: "منشور آخر مجدول للنشر على انستغرام",
      start: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5),
      status: "SCHEDULED" as const,
      platforms: ["INSTAGRAM"],
    },
    {
      id: "3",
      title: "منشور منشور",
      content: "هذا منشور تم نشره بالفعل",
      start: new Date(Date.now() - 1000 * 60 * 60 * 24 * 1),
      status: "PUBLISHED" as const,
      platforms: ["TWITTER", "LINKEDIN"],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "#6b7280";
      case "SCHEDULED":
        return "#3b82f6";
      case "PUBLISHED":
        return "#10b981";
      case "FAILED":
        return "#ef4444";
      default:
        return "#6b7280";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "مسودة";
      case "SCHEDULED":
        return "مجدول";
      case "PUBLISHED":
        return "منشور";
      case "FAILED":
        return "فشل";
      default:
        return "غير معروف";
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "TWITTER":
        return "𝕏";
      case "FACEBOOK":
        return "f";
      case "INSTAGRAM":
        return "📷";
      case "LINKEDIN":
        return "in";
      default:
        return "?";
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('ar-SA');
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif'
    }}>
      {/* Sidebar */}
      <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        width: '18rem',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(24px)',
        borderLeft: '1px solid rgba(229, 231, 235, 0.5)',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        zIndex: 50,
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Logo Section */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid rgba(229, 231, 235, 0.5)'
        }}>
          <Link href="/dashboard" style={{ textDecoration: 'none' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                borderRadius: '0.75rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1.125rem',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
              }}>
                eW
              </div>
              <div>
                <h2 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(to right, #2563eb, #9333ea)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: 0
                }}>
                  eWasl
                </h2>
                <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>منصة إدارة المحتوى</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <div style={{ flex: 1, padding: '1.5rem 1rem' }}>
          <p style={{
            fontSize: '0.75rem',
            fontWeight: '600',
            color: '#6b7280',
            textTransform: 'uppercase',
            letterSpacing: '0.05em',
            marginBottom: '0.75rem',
            padding: '0 0.75rem'
          }}>
            القائمة الرئيسية
          </p>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
            {[
              { icon: BarChart3, label: 'لوحة التحكم', href: '/dashboard' },
              { icon: PlusCircle, label: 'المنشورات', href: '/posts' },
              { icon: Calendar, label: 'الجدولة', href: '/schedule', active: true },
              { icon: Users, label: 'الحسابات', href: '/social' },
              { icon: BarChart3, label: 'التحليلات', href: '/analytics' },
              { icon: Settings, label: 'الإعدادات', href: '/settings' }
            ].map((item, index) => (
              <Link
                key={index}
                href={item.href}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  height: '2.75rem',
                  padding: '0 0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: item.active ? 'white' : '#374151',
                  textDecoration: 'none',
                  borderRadius: '0.5rem',
                  background: item.active ? 'linear-gradient(to right, #2563eb, #9333ea)' : 'transparent',
                  boxShadow: item.active ? '0 10px 15px -3px rgba(0, 0, 0, 0.1)' : 'none',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.currentTarget.style.background = '#eff6ff';
                    e.currentTarget.style.color = '#2563eb';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.currentTarget.style.background = 'transparent';
                    e.currentTarget.style.color = '#374151';
                  }
                }}
              >
                <item.icon style={{ width: '1.25rem', height: '1.25rem', marginLeft: '0.75rem' }} />
                {item.label}
              </Link>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div style={{ padding: '1rem', borderTop: '1px solid rgba(229, 231, 235, 0.5)' }}>
          <button style={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            height: '2.75rem',
            padding: '0 0.75rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#dc2626',
            background: 'transparent',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}>
            <LogOut style={{ width: '1.25rem', height: '1.25rem', marginLeft: '0.75rem' }} />
            تسجيل الخروج
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ marginRight: '18rem' }}>
        {/* Header */}
        <header style={{
          position: 'sticky',
          top: 0,
          zIndex: 30,
          height: '4rem',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(24px)',
          borderBottom: '1px solid rgba(229, 231, 235, 0.5)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          alignItems: 'center',
          padding: '0 1.5rem',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <div style={{
              width: '2.5rem',
              height: '2.5rem',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '0.875rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              eW
            </div>
            <div>
              <h1 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #9333ea)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                margin: 0
              }}>
                جدولة المنشورات
              </h1>
              <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>إدارة وتنظيم المنشورات المجدولة</p>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <button style={{
              position: 'relative',
              padding: '0.5rem',
              background: 'transparent',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              color: '#6b7280'
            }}>
              <Bell style={{ width: '1.25rem', height: '1.25rem' }} />
              <span style={{
                position: 'absolute',
                top: '0.25rem',
                right: '0.25rem',
                width: '0.5rem',
                height: '0.5rem',
                background: '#ef4444',
                borderRadius: '50%'
              }}></span>
            </button>
            <button style={{
              width: '2rem',
              height: '2rem',
              background: '#f3f4f6',
              border: 'none',
              borderRadius: '50%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6b7280'
            }}>
              <User style={{ width: '1rem', height: '1rem' }} />
            </button>
          </div>
        </header>

        {/* Main Content */}
        <main style={{
          minHeight: 'calc(100vh - 4rem)',
          background: 'linear-gradient(135deg, rgba(249, 250, 251, 0.3) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(239, 246, 255, 0.2) 100%)',
          padding: '1.5rem'
        }}>
          <div style={{ maxWidth: '80rem', margin: '0 auto' }}>
            {/* Welcome Section */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(4px)',
              borderRadius: '1rem',
              padding: '2rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.5)',
              marginBottom: '2rem'
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <div style={{
                    width: '3rem',
                    height: '3rem',
                    background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
                    borderRadius: '0.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                  }}>
                    <Calendar style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
                  </div>
                  <div>
                    <h1 style={{
                      fontSize: '1.875rem',
                      fontWeight: 'bold',
                      background: 'linear-gradient(to right, #2563eb, #9333ea)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      margin: 0
                    }}>
                      جدولة المنشورات 📅
                    </h1>
                    <p style={{ color: '#4b5563', fontSize: '1.125rem', margin: '0.5rem 0 0 0' }}>
                      إدارة وتنظيم المنشورات المجدولة على وسائل التواصل الاجتماعي
                    </p>
                  </div>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                    عرض وإدارة جدولة المحتوى بطريقة تفاعلية ومرئية
                  </p>
                  <Link href="/posts/new" style={{ textDecoration: 'none' }}>
                    <button style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.75rem 1.5rem',
                      background: 'linear-gradient(to right, #2563eb, #9333ea)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '0.5rem',
                      fontWeight: '600',
                      fontSize: '0.875rem',
                      cursor: 'pointer',
                      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                      transition: 'all 0.2s ease'
                    }}>
                      <PlusCircle style={{ width: '1.25rem', height: '1.25rem' }} />
                      إنشاء منشور جديد
                    </button>
                  </Link>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1.5rem',
              marginBottom: '2rem'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #eff6ff, #dbeafe)',
                border: '1px solid #bfdbfe',
                borderRadius: '1rem',
                padding: '1.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#1d4ed8', marginBottom: '0.75rem' }}>
                  منشورات مجدولة
                </h3>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1e3a8a', marginBottom: '0.25rem' }}>
                  {mockPosts.filter(p => p.status === "SCHEDULED").length}
                </div>
                <p style={{ fontSize: '0.75rem', color: '#2563eb', margin: 0 }}>في انتظار النشر</p>
              </div>

              <div style={{
                background: 'linear-gradient(135deg, #f0fdf4, #dcfce7)',
                border: '1px solid #bbf7d0',
                borderRadius: '1rem',
                padding: '1.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#15803d', marginBottom: '0.75rem' }}>
                  منشورات منشورة
                </h3>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#14532d', marginBottom: '0.25rem' }}>
                  {mockPosts.filter(p => p.status === "PUBLISHED").length}
                </div>
                <p style={{ fontSize: '0.75rem', color: '#16a34a', margin: 0 }}>تم نشرها بنجاح</p>
              </div>

              <div style={{
                background: 'linear-gradient(135deg, #faf5ff, #f3e8ff)',
                border: '1px solid #d8b4fe',
                borderRadius: '1rem',
                padding: '1.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#7c3aed', marginBottom: '0.75rem' }}>
                  هذا الأسبوع
                </h3>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#581c87', marginBottom: '0.25rem' }}>
                  12
                </div>
                <p style={{ fontSize: '0.75rem', color: '#8b5cf6', margin: 0 }}>منشور مجدول</p>
              </div>

              <div style={{
                background: 'linear-gradient(135deg, #fff7ed, #fed7aa)',
                border: '1px solid #fdba74',
                borderRadius: '1rem',
                padding: '1.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#ea580c', marginBottom: '0.75rem' }}>
                  الشهر القادم
                </h3>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#9a3412', marginBottom: '0.25rem' }}>
                  45
                </div>
                <p style={{ fontSize: '0.75rem', color: '#f97316', margin: 0 }}>منشور مخطط</p>
              </div>
            </div>

            {/* Posts List */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(4px)',
              borderRadius: '1rem',
              padding: '1.5rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.5)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1.5rem' }}>
                <div style={{
                  width: '0.75rem',
                  height: '0.75rem',
                  background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                  borderRadius: '50%'
                }}></div>
                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                  المنشورات المجدولة
                </h2>
              </div>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1.5rem' }}>
                عرض وإدارة المنشورات المجدولة للنشر على وسائل التواصل الاجتماعي
              </p>

              {/* Tab Buttons */}
              <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '1.5rem' }}>
                <button
                  onClick={() => setActiveTab("calendar")}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.5rem 1rem',
                    background: activeTab === "calendar" ? 'linear-gradient(to right, #2563eb, #9333ea)' : '#f3f4f6',
                    color: activeTab === "calendar" ? 'white' : '#374151',
                    border: 'none',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <Calendar style={{ width: '1rem', height: '1rem' }} />
                  التقويم
                </button>
                <button
                  onClick={() => setActiveTab("list")}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.5rem 1rem',
                    background: activeTab === "list" ? 'linear-gradient(to right, #2563eb, #9333ea)' : '#f3f4f6',
                    color: activeTab === "list" ? 'white' : '#374151',
                    border: 'none',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <List style={{ width: '1rem', height: '1rem' }} />
                  القائمة
                </button>
              </div>

              {/* Tab Content */}
              {activeTab === "calendar" && (
                <div style={{
                  background: '#f9fafb',
                  borderRadius: '0.75rem',
                  padding: '2rem',
                  textAlign: 'center',
                  border: '1px solid #e5e7eb'
                }}>
                  <Calendar style={{ width: '3rem', height: '3rem', color: '#9ca3af', margin: '0 auto 1rem' }} />
                  <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#111827', margin: '0 0 0.5rem 0' }}>
                    التقويم التفاعلي
                  </h3>
                  <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                    عرض المنشورات المجدولة في تقويم تفاعلي
                  </p>
                </div>
              )}

              {activeTab === "list" && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {mockPosts.length === 0 ? (
                    <div style={{
                      background: '#f9fafb',
                      borderRadius: '0.75rem',
                      padding: '2rem',
                      textAlign: 'center',
                      border: '1px solid #e5e7eb'
                    }}>
                      <Calendar style={{ width: '3rem', height: '3rem', color: '#9ca3af', margin: '0 auto 1rem' }} />
                      <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#111827', margin: '0 0 0.5rem 0' }}>
                        لا توجد منشورات مجدولة
                      </h3>
                      <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                        ابدأ بإنشاء منشور جديد وجدولته
                      </p>
                    </div>
                  ) : (
                    mockPosts.map((post) => (
                      <div key={post.id} style={{
                        background: 'white',
                        borderRadius: '0.75rem',
                        padding: '1.5rem',
                        border: '1px solid #e5e7eb',
                        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
                      }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '1rem' }}>
                          <div style={{ flex: 1 }}>
                            <Link href={`/posts/${post.id}`} style={{ textDecoration: 'none' }}>
                              <h3 style={{
                                fontSize: '1rem',
                                fontWeight: '600',
                                color: '#2563eb',
                                margin: '0 0 0.5rem 0',
                                cursor: 'pointer'
                              }}>
                                {post.content.length > 60 ? post.content.substring(0, 60) + "..." : post.content}
                              </h3>
                            </Link>
                            <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>
                              ID: {post.id}
                            </p>
                          </div>
                          <span style={{
                            padding: '0.25rem 0.75rem',
                            background: getStatusColor(post.status),
                            color: 'white',
                            borderRadius: '0.375rem',
                            fontSize: '0.75rem',
                            fontWeight: '500'
                          }}>
                            {getStatusLabel(post.status)}
                          </span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div style={{ display: 'flex', gap: '0.5rem' }}>
                            {post.platforms.map((platform) => (
                              <div
                                key={platform}
                                style={{
                                  width: '2rem',
                                  height: '2rem',
                                  borderRadius: '0.5rem',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'white',
                                  fontSize: '0.875rem',
                                  fontWeight: 'bold',
                                  background: platform === "TWITTER" ? "#000000" :
                                            platform === "FACEBOOK" ? "#1877f2" :
                                            platform === "INSTAGRAM" ? "#e4405f" :
                                            platform === "LINKEDIN" ? "#0077b5" : "#6b7280"
                                }}
                              >
                                {getPlatformIcon(platform)}
                              </div>
                            ))}
                          </div>
                          <div style={{ textAlign: 'left' }}>
                            <div style={{ fontSize: '0.875rem', fontWeight: '500', color: '#111827' }}>
                              {formatDate(post.start)}
                            </div>
                            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                              {formatTime(post.start)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
