{"timestamp": "2025-05-29T22:30:18.834Z", "baseUrl": "https://app.ewasl.com", "summary": {"passed": 1, "failed": 6, "total": 7, "successRate": 14}, "tests": [{"name": "Health Check", "status": "FAILED", "error": "Health check failed with status 404"}, {"name": "Stripe Configuration", "status": "FAILED", "error": "Cannot access health endpoint"}, {"name": "Subscription Plans", "status": "FAILED", "error": "Expected 401 for unauthenticated request"}, {"name": "Usage Tracking", "status": "FAILED", "error": "Expected 401 for unauthenticated request"}, {"name": "Webhook Endpoint", "status": "PASSED", "details": {"message": "Webhook endpoint properly validates signatures"}}, {"name": "Environment Variables", "status": "FAILED", "error": "Cannot access health endpoint"}, {"name": "Database Schema", "status": "FAILED", "error": "Cannot access health endpoint"}]}