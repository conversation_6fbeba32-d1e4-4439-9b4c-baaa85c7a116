import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import JobQueueManager from '@/lib/queue/job-queue';
import RedisClient from '@/lib/queue/redis-client';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/scheduler/status - Get scheduler system status
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching scheduler status...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get scheduler health metrics
    const { data: healthData, error: healthError } = await supabase
      .rpc('get_scheduler_health');

    if (healthError) {
      console.error('Error fetching scheduler health:', healthError);
      return NextResponse.json(
        { error: 'Failed to fetch scheduler health' },
        { status: 500 }
      );
    }

    const health = healthData?.[0] || {
      total_jobs: 0,
      pending_jobs: 0,
      processing_jobs: 0,
      failed_jobs: 0,
      active_schedulers: 0,
      last_activity: null,
    };

    // Get recent scheduler instances
    const { data: schedulerInstances, error: instancesError } = await supabase
      .from('scheduler_status')
      .select('*')
      .order('last_heartbeat', { ascending: false })
      .limit(10);

    if (instancesError) {
      console.error('Error fetching scheduler instances:', instancesError);
    }

    // Get recent job queue status
    const { data: recentJobs, error: jobsError } = await supabase
      .from('job_queue')
      .select('id, job_type, status, priority, attempts, created_at, started_at, completed_at')
      .order('created_at', { ascending: false })
      .limit(20);

    if (jobsError) {
      console.error('Error fetching recent jobs:', jobsError);
    }

    // Get user's recent publish history
    const { data: publishHistory, error: historyError } = await supabase
      .from('post_publish_history')
      .select(`
        id,
        post_id,
        platform,
        status,
        attempt_number,
        published_url,
        error_message,
        started_at,
        completed_at,
        posts (
          id,
          content,
          scheduled_at
        )
      `)
      .eq('posts.user_id', user.id)
      .order('started_at', { ascending: false })
      .limit(10);

    if (historyError) {
      console.error('Error fetching publish history:', historyError);
    }

    // Calculate system status
    const systemStatus = health.active_schedulers > 0 ? 'running' : 'stopped';
    const lastActivity = health.last_activity ? new Date(health.last_activity) : null;
    const isHealthy = health.active_schedulers > 0 &&
                     lastActivity &&
                     (new Date().getTime() - lastActivity.getTime()) < 300000; // 5 minutes

    // Get platform-specific metrics
    const platformMetrics = await getPlatformMetrics(supabase, user.id);

    // Get job queue status
    const queueManager = JobQueueManager.getInstance();
    let queueStats = null;
    let redisInfo = null;

    try {
      if (!queueManager.isHealthy()) {
        await queueManager.initialize();
      }
      queueStats = await queueManager.getQueueStats();
      redisInfo = await RedisClient.getRedisInfo();
    } catch (error) {
      console.warn('Failed to get queue stats:', error);
    }

    return NextResponse.json({
      success: true,
      data: {
        system: {
          status: systemStatus,
          isHealthy,
          lastActivity,
          activeSchedulers: health.active_schedulers,
        },
        jobs: {
          total: health.total_jobs,
          pending: health.pending_jobs,
          processing: health.processing_jobs,
          failed: health.failed_jobs,
          completed: health.total_jobs - health.pending_jobs - health.processing_jobs - health.failed_jobs,
        },
        queue: queueStats || {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0,
          total: 0,
        },
        redis: redisInfo,
        schedulerInstances: schedulerInstances || [],
        recentJobs: recentJobs || [],
        publishHistory: publishHistory || [],
        platformMetrics,
      },
    });

  } catch (error: any) {
    console.error('Error fetching scheduler status:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch scheduler status',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper function to get platform-specific metrics
async function getPlatformMetrics(supabase: any, userId: string) {
  try {
    // Get user's social accounts
    const { data: socialAccounts } = await supabase
      .from('social_accounts')
      .select('id, platform, is_active')
      .eq('user_id', userId);

    if (!socialAccounts) return {};

    const platformMetrics: Record<string, any> = {};

    for (const account of socialAccounts) {
      if (!platformMetrics[account.platform]) {
        platformMetrics[account.platform] = {
          accounts: 0,
          activeAccounts: 0,
          recentPosts: 0,
          successRate: 0,
        };
      }

      platformMetrics[account.platform].accounts++;
      if (account.is_active) {
        platformMetrics[account.platform].activeAccounts++;
      }

      // Get recent posts for this platform
      const { data: recentPosts } = await supabase
        .from('post_publish_history')
        .select('status')
        .eq('social_account_id', account.id)
        .gte('started_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
        .limit(100);

      if (recentPosts) {
        platformMetrics[account.platform].recentPosts += recentPosts.length;

        const successful = recentPosts.filter((p: any) => p.status === 'published').length;
        if (recentPosts.length > 0) {
          platformMetrics[account.platform].successRate =
            Math.round((successful / recentPosts.length) * 100);
        }
      }
    }

    return platformMetrics;

  } catch (error) {
    console.error('Error getting platform metrics:', error);
    return {};
  }
}

// POST /api/scheduler/status - Update scheduler status (admin only)
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Updating scheduler status...');

    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action, instanceId } = body;

    if (!action || !instanceId) {
      return NextResponse.json(
        { error: 'Action and instance ID are required' },
        { status: 400 }
      );
    }

    // Validate action
    const validActions = ['start', 'stop', 'restart', 'heartbeat'];
    if (!validActions.includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be one of: ' + validActions.join(', ') },
        { status: 400 }
      );
    }

    // Update scheduler status
    let updateData: any = {
      last_heartbeat: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    switch (action) {
      case 'start':
        updateData.status = 'running';
        updateData.started_at = new Date().toISOString();
        updateData.stopped_at = null;
        break;
      case 'stop':
        updateData.status = 'stopped';
        updateData.stopped_at = new Date().toISOString();
        break;
      case 'restart':
        updateData.status = 'running';
        updateData.started_at = new Date().toISOString();
        updateData.stopped_at = null;
        break;
      case 'heartbeat':
        // Just update heartbeat timestamp
        break;
    }

    // Upsert scheduler status
    const { data, error } = await supabase
      .from('scheduler_status')
      .upsert({
        instance_id: instanceId,
        ...updateData,
      }, {
        onConflict: 'instance_id',
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating scheduler status:', error);
      return NextResponse.json(
        { error: 'Failed to update scheduler status' },
        { status: 500 }
      );
    }

    console.log(`Scheduler ${instanceId} status updated: ${action}`);

    return NextResponse.json({
      success: true,
      data: {
        instanceId,
        action,
        status: data.status,
        lastHeartbeat: data.last_heartbeat,
      },
      message: `Scheduler ${action} completed successfully`,
    });

  } catch (error: any) {
    console.error('Error updating scheduler status:', error);
    return NextResponse.json(
      {
        error: 'Failed to update scheduler status',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
