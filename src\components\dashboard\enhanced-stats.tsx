"use client";

import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";

interface StatCard {
  title: string;
  value: string | number;
  change: string;
  changeType: 'increase' | 'decrease';
  description: string;
  icon: React.ComponentType<any>;
  gradient: string;
}

interface EnhancedStatsProps {
  stats?: {
    totalPosts: number;
    scheduledPosts: number;
    expectedEngagement: number;
    totalEngagement: number;
  };
}

export function EnhancedStats({ stats }: EnhancedStatsProps) {
  const defaultStats = {
    totalPosts: 156,
    scheduledPosts: 24,
    expectedEngagement: 8,
    totalEngagement: 12543
  };

  const currentStats = stats || defaultStats;

  const statCards: StatCard[] = [
    {
      title: "إجمالي المنشورات",
      value: currentStats.totalPosts,
      change: "+8.2%",
      changeType: 'increase',
      description: "إجمالي المنشورات المنشورة والمجدولة",
      icon: ({ className }: { className?: string }) => <span className={className}>📝</span>,
      gradient: "from-blue-500 to-blue-600"
    },
    {
      title: "منشورات مجدولة",
      value: currentStats.scheduledPosts,
      change: "هذا الأسبوع",
      changeType: 'increase',
      description: "المنشورات المجدولة للنشر لاحقاً",
      icon: ({ className }: { className?: string }) => <span className={className}>📅</span>,
      gradient: "from-purple-500 to-purple-600"
    },
    {
      title: "تفاعلات متوقعة",
      value: currentStats.expectedEngagement,
      change: "24 جديد",
      changeType: 'increase',
      description: "التفاعلات المتوقعة للمنشورات المجدولة",
      icon: ({ className }: { className?: string }) => <span className={className}>👥</span>,
      gradient: "from-green-500 to-green-600"
    },
    {
      title: "إجمالي التفاعلات",
      value: currentStats.totalEngagement.toLocaleString(),
      change: "+15.3%",
      changeType: 'increase',
      description: "إجمالي التفاعلات عبر جميع المنصات",
      icon: ({ className }: { className?: string }) => <span className={className}>❤️</span>,
      gradient: "from-orange-500 to-red-500"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statCards.map((stat, index) => (
        <Card key={index} className="group hover:shadow-lg transition-all duration-200 border-0 bg-gradient-to-br from-white to-gray-50">
          <CardContent className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.gradient} shadow-lg`}>
                <stat.icon className="text-white text-xl" />
              </div>
              {stat.changeType === 'increase' && stat.change.includes('%') && (
                <div className="flex items-center text-green-600 text-sm">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  {stat.change}
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
              <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
              <p className="text-xs text-gray-500">{stat.change}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
