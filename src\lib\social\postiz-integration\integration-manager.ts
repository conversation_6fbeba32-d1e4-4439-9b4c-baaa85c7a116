/**
 * Integration Manager
 * Manages all social media providers and their interactions
 */

import { SocialProvider, PostDetails, PostResponse, AuthTokenDetails } from './interfaces';
import { LinkedInEnhancedProvider } from './providers/linkedin-enhanced';
import { FacebookEnhancedProvider } from './providers/facebook-enhanced';
import { InstagramEnhancedProvider } from './providers/instagram-enhanced';
import { TwitterEnhancedProvider } from './providers/twitter-enhanced';
import { supabaseServiceRole } from '@/lib/supabase/service-role';

export class IntegrationManager {
  private providers: Map<string, SocialProvider> = new Map();
  private supabase = supabaseServiceRole;

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    // Register all providers
    const linkedinProvider = new LinkedInEnhancedProvider();
    this.providers.set(linkedinProvider.identifier, linkedinProvider);

    const facebookProvider = new FacebookEnhancedProvider();
    this.providers.set(facebookProvider.identifier, facebookProvider);

    const instagramProvider = new InstagramEnhancedProvider();
    this.providers.set(instagramProvider.identifier, instagramProvider);

    const twitterProvider = new TwitterEnhancedProvider();
    this.providers.set(twitterProvider.identifier, twitterProvider);

    console.log('[IntegrationManager] Initialized providers:', Array.from(this.providers.keys()));
  }

  getProvider(platform: string): SocialProvider | null {
    return this.providers.get(platform) || null;
  }

  getAllProviders(): SocialProvider[] {
    return Array.from(this.providers.values());
  }

  async generateAuthUrl(platform: string): Promise<{
    url: string;
    codeVerifier: string;
    state: string;
  } | null> {
    const provider = this.getProvider(platform);
    if (!provider) {
      throw new Error(`Provider not found for platform: ${platform}`);
    }

    try {
      return await provider.generateAuthUrl();
    } catch (error) {
      console.error(`[IntegrationManager] Auth URL generation failed for ${platform}:`, error);
      throw error;
    }
  }

  async authenticate(
    platform: string,
    params: {
      code: string;
      codeVerifier: string;
      refresh?: string;
    }
  ): Promise<AuthTokenDetails> {
    const provider = this.getProvider(platform);
    if (!provider) {
      throw new Error(`Provider not found for platform: ${platform}`);
    }

    try {
      return await provider.authenticate(params);
    } catch (error) {
      console.error(`[IntegrationManager] Authentication failed for ${platform}:`, error);
      throw error;
    }
  }

  async refreshToken(platform: string, refreshToken: string): Promise<AuthTokenDetails> {
    const provider = this.getProvider(platform);
    if (!provider) {
      throw new Error(`Provider not found for platform: ${platform}`);
    }

    try {
      return await provider.refreshToken(refreshToken);
    } catch (error) {
      console.error(`[IntegrationManager] Token refresh failed for ${platform}:`, error);
      throw error;
    }
  }

  async post(
    integrationId: string,
    postDetails: PostDetails[]
  ): Promise<PostResponse[]> {
    try {
      // Get integration details from database
      const { data: integration, error } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('id', integrationId)
        .single();

      if (error || !integration) {
        throw new Error(`Integration not found: ${integrationId}`);
      }

      const provider = this.getProvider(integration.platform);
      if (!provider) {
        throw new Error(`Provider not found for platform: ${integration.platform}`);
      }

      // Check if token needs refresh
      if (integration.expires_at && new Date(integration.expires_at) <= new Date()) {
        if (integration.refresh_token) {
          console.log(`[IntegrationManager] Refreshing token for ${integration.platform}`);
          const refreshedAuth = await provider.refreshToken(integration.refresh_token);
          
          // Update tokens in database
          await this.supabase
            .from('social_accounts')
            .update({
              access_token: refreshedAuth.accessToken,
              refresh_token: refreshedAuth.refreshToken,
              expires_at: refreshedAuth.expiresIn 
                ? new Date(Date.now() + refreshedAuth.expiresIn * 1000).toISOString()
                : null,
              updated_at: new Date().toISOString(),
            })
            .eq('id', integrationId);

          integration.access_token = refreshedAuth.accessToken;
        } else {
          throw new Error(`Token expired and no refresh token available for ${integration.platform}`);
        }
      }

      // Perform the post
      const results = await provider.post(
        integration.account_id,
        integration.access_token,
        postDetails,
        integration
      );

      // Log the activity
      await this.logPostActivity(integration.user_id, integration.platform, postDetails, results);

      return results;
    } catch (error) {
      console.error(`[IntegrationManager] Post failed for integration ${integrationId}:`, error);
      throw error;
    }
  }

  async testConnection(integrationId: string): Promise<{
    success: boolean;
    error?: string;
    accountInfo?: any;
  }> {
    try {
      const { data: integration, error } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('id', integrationId)
        .single();

      if (error || !integration) {
        return {
          success: false,
          error: `Integration not found: ${integrationId}`,
        };
      }

      const provider = this.getProvider(integration.platform);
      if (!provider) {
        return {
          success: false,
          error: `Provider not found for platform: ${integration.platform}`,
        };
      }

      // Test the connection by making a simple API call
      // This is platform-specific and should be implemented in each provider
      return {
        success: true,
        accountInfo: {
          platform: integration.platform,
          accountName: integration.account_name,
          accountUsername: integration.account_username,
          lastUsed: integration.updated_at,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getBusinessAccounts(platform: string, accessToken: string): Promise<Array<{
    id: string;
    name: string;
    username?: string;
    picture?: string;
    type: 'personal' | 'business' | 'page';
  }>> {
    const provider = this.getProvider(platform);
    if (!provider) {
      throw new Error(`Provider not found for platform: ${platform}`);
    }

    try {
      switch (platform) {
        case 'linkedin':
          const linkedinProvider = provider as any;
          if (linkedinProvider.getCompanyPages) {
            const pages = await linkedinProvider.getCompanyPages(accessToken);
            return pages.map((page: any) => ({
              id: page.id,
              name: page.name,
              username: page.vanityName,
              picture: page.logoUrl,
              type: 'business' as const,
            }));
          }
          break;

        case 'facebook':
          const facebookProvider = provider as any;
          if (facebookProvider.getBusinessPages) {
            return await facebookProvider.getBusinessPages(accessToken);
          }
          break;

        case 'instagram':
          const instagramProvider = provider as any;
          if (instagramProvider.getInstagramBusinessAccounts) {
            return await instagramProvider.getInstagramBusinessAccounts(accessToken);
          }
          break;
      }

      console.log(`[IntegrationManager] Business accounts retrieval not implemented for ${platform}`);
      return [];
    } catch (error) {
      console.error(`[IntegrationManager] Failed to get business accounts for ${platform}:`, error);
      throw error;
    }
  }

  private async logPostActivity(
    userId: string,
    platform: string,
    postDetails: PostDetails[],
    results: PostResponse[]
  ) {
    try {
      await this.supabase
        .from('activity_logs')
        .insert({
          user_id: userId,
          platform,
          action: 'post_published',
          details: {
            post_count: postDetails.length,
            success_count: results.filter(r => r.status === 'posted').length,
            failed_count: results.filter(r => r.status === 'failed').length,
            post_ids: results.map(r => r.postId),
          },
          created_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error('[IntegrationManager] Failed to log post activity:', error);
    }
  }

  async getIntegrationStats(userId: string): Promise<{
    totalIntegrations: number;
    activeIntegrations: number;
    platformBreakdown: Record<string, number>;
    recentActivity: any[];
  }> {
    try {
      const { data: integrations, error: integrationsError } = await this.supabase
        .from('social_accounts')
        .select('platform, is_active, updated_at')
        .eq('user_id', userId);

      if (integrationsError) {
        throw integrationsError;
      }

      const { data: activities, error: activitiesError } = await this.supabase
        .from('activity_logs')
        .select('platform, action, created_at, details')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (activitiesError) {
        throw activitiesError;
      }

      const platformBreakdown = integrations?.reduce((acc, integration) => {
        acc[integration.platform] = (acc[integration.platform] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      return {
        totalIntegrations: integrations?.length || 0,
        activeIntegrations: integrations?.filter(i => i.is_active).length || 0,
        platformBreakdown,
        recentActivity: activities || [],
      };
    } catch (error) {
      console.error('[IntegrationManager] Failed to get integration stats:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const integrationManager = new IntegrationManager();
