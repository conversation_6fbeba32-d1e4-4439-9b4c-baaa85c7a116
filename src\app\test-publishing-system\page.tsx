'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { 
  Send, 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Facebook,
  Linkedin,
  Instagram,
  Twitter,
  BarChart3,
  Calendar,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  data?: any;
  duration?: number;
}

interface PublishingTest {
  platform: string;
  content: string;
  result?: any;
  status: 'pending' | 'running' | 'success' | 'error';
  error?: string;
}

export default function TestPublishingSystemPage() {
  const [activeTab, setActiveTab] = useState('api-tests');
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Publishing API Endpoint', status: 'pending' },
    { name: 'Publishing Statistics API', status: 'pending' },
    { name: 'Social Accounts Integration', status: 'pending' },
    { name: 'Business Accounts Validation', status: 'pending' },
    { name: 'Content Validation', status: 'pending' },
    { name: 'Scheduling Validation', status: 'pending' }
  ]);

  const [publishingTests, setPublishingTests] = useState<PublishingTest[]>([
    { platform: 'facebook', content: 'Test post for Facebook Page from eWasl Publishing System 🚀', status: 'pending' },
    { platform: 'linkedin', content: 'Test post for LinkedIn Company Page from eWasl Publishing System 💼', status: 'pending' }
  ]);

  const [testContent, setTestContent] = useState('This is a test post from eWasl Publishing System! 🚀\n\nTesting cross-platform publishing capabilities.\n\n#eWasl #SocialMedia #Publishing #Test');
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, ...updates } : test
    ));
  };

  const runTest = async (index: number, testFn: () => Promise<any>) => {
    const startTime = Date.now();
    updateTest(index, { status: 'running' });
    
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      updateTest(index, { 
        status: 'success', 
        message: result.message || 'Test passed',
        data: result.data,
        duration 
      });
      return true;
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateTest(index, { 
        status: 'error', 
        message: error.message || 'Test failed',
        duration 
      });
      return false;
    }
  };

  const testPublishingAPI = async () => {
    const response = await fetch('/api/publishing/publish', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content: 'Test content',
        platforms: [
          { platform: 'facebook', accountId: 'test-account' }
        ]
      })
    });

    if (response.status === 401) {
      return {
        message: 'Publishing API requires authentication (expected)',
        data: { status: 401, requiresAuth: true }
      };
    }

    if (response.status === 400) {
      const data = await response.json();
      return {
        message: 'Publishing API validation working',
        data: { status: 400, validation: data.error }
      };
    }

    const data = await response.json();
    return {
      message: 'Publishing API accessible',
      data: { status: response.status, response: data }
    };
  };

  const testPublishingStats = async () => {
    const response = await fetch('/api/publishing/stats');
    
    if (response.status === 401) {
      return {
        message: 'Publishing stats API requires authentication (expected)',
        data: { status: 401, requiresAuth: true }
      };
    }

    if (response.ok) {
      const data = await response.json();
      return {
        message: 'Publishing stats API working',
        data: { status: 200, stats: data.stats }
      };
    }

    throw new Error(`Publishing stats API failed: ${response.status}`);
  };

  const testSocialAccountsIntegration = async () => {
    const response = await fetch('/api/social/accounts');
    
    if (response.status === 401) {
      return {
        message: 'Social accounts API requires authentication (expected)',
        data: { status: 401, requiresAuth: true }
      };
    }

    if (response.ok) {
      const data = await response.json();
      return {
        message: 'Social accounts integration working',
        data: { status: 200, accountsCount: data.accounts?.length || 0 }
      };
    }

    throw new Error(`Social accounts API failed: ${response.status}`);
  };

  const testBusinessAccountsValidation = async () => {
    const response = await fetch('/api/social/business-accounts?platform=facebook&userId=test');
    
    if (response.status === 401) {
      return {
        message: 'Business accounts API requires authentication (expected)',
        data: { status: 401, requiresAuth: true }
      };
    }

    if (response.status === 405) {
      throw new Error('Business accounts API method not allowed');
    }

    return {
      message: 'Business accounts validation working',
      data: { status: response.status }
    };
  };

  const testContentValidation = async () => {
    // Test with invalid content
    const response = await fetch('/api/publishing/publish', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content: '', // Empty content should fail
        platforms: []
      })
    });

    if (response.status === 400) {
      const data = await response.json();
      return {
        message: 'Content validation working correctly',
        data: { status: 400, validation: data.error }
      };
    }

    if (response.status === 401) {
      return {
        message: 'Content validation requires authentication (expected)',
        data: { status: 401, requiresAuth: true }
      };
    }

    throw new Error('Content validation not working properly');
  };

  const testSchedulingValidation = async () => {
    // Test with past date
    const pastDate = new Date();
    pastDate.setHours(pastDate.getHours() - 1);

    const response = await fetch('/api/publishing/publish', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content: 'Test content',
        platforms: [{ platform: 'facebook', accountId: 'test' }],
        scheduledAt: pastDate.toISOString()
      })
    });

    if (response.status === 400) {
      const data = await response.json();
      if (data.error?.includes('future')) {
        return {
          message: 'Scheduling validation working correctly',
          data: { status: 400, validation: 'Past date rejected' }
        };
      }
    }

    if (response.status === 401) {
      return {
        message: 'Scheduling validation requires authentication (expected)',
        data: { status: 401, requiresAuth: true }
      };
    }

    return {
      message: 'Scheduling validation accessible',
      data: { status: response.status }
    };
  };

  const runAllAPITests = async () => {
    setIsRunning(true);
    
    const testFunctions = [
      testPublishingAPI,
      testPublishingStats,
      testSocialAccountsIntegration,
      testBusinessAccountsValidation,
      testContentValidation,
      testSchedulingValidation
    ];

    let passedTests = 0;

    for (let i = 0; i < testFunctions.length; i++) {
      const passed = await runTest(i, testFunctions[i]);
      if (passed) passedTests++;
      
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    setIsRunning(false);
    
    if (passedTests === testFunctions.length) {
      toast.success('All publishing system tests passed!');
    } else if (passedTests > testFunctions.length / 2) {
      toast.success(`Most tests passed (${passedTests}/${testFunctions.length})`);
    } else {
      toast.error(`Many tests failed (${passedTests}/${testFunctions.length})`);
    }
  };

  const testRealPublishing = async (platform: string) => {
    setPublishingTests(prev => prev.map(test => 
      test.platform === platform 
        ? { ...test, status: 'running' }
        : test
    ));

    try {
      const response = await fetch('/api/publishing/publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: testContent,
          platforms: [
            { platform, accountId: `test-${platform}-account` }
          ]
        })
      });

      const data = await response.json();

      if (response.ok) {
        setPublishingTests(prev => prev.map(test => 
          test.platform === platform 
            ? { 
                ...test, 
                status: 'success', 
                result: data.data,
                error: undefined
              }
            : test
        ));
        toast.success(`${platform} publishing test completed successfully`);
      } else {
        throw new Error(data.error || 'Publishing failed');
      }

    } catch (error: any) {
      setPublishingTests(prev => prev.map(test => 
        test.platform === platform 
          ? { 
              ...test, 
              status: 'error', 
              error: error.message,
              result: undefined
            }
          : test
      ));
      toast.error(`${platform} publishing test failed: ${error.message}`);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'running':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <div className="w-5 h-5 rounded-full bg-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-500">Passed</Badge>;
      case 'error':
        return <Badge variant="destructive">Failed</Badge>;
      case 'running':
        return <Badge className="bg-blue-500">Running</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return <Facebook className="w-4 h-4 text-blue-600" />;
      case 'linkedin':
        return <Linkedin className="w-4 h-4 text-blue-700" />;
      case 'instagram':
        return <Instagram className="w-4 h-4 text-pink-600" />;
      case 'twitter':
        return <Twitter className="w-4 h-4 text-blue-400" />;
      default:
        return <div className="w-4 h-4 bg-gray-400 rounded" />;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <TestTube className="w-8 h-8 text-purple-600" />
          <h1 className="text-3xl font-bold">Publishing System Testing</h1>
        </div>
        <p className="text-muted-foreground">
          Comprehensive testing of the real publishing system implementation
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api-tests" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            API Tests
          </TabsTrigger>
          <TabsTrigger value="publishing-tests" className="flex items-center gap-2">
            <Send className="w-4 h-4" />
            Publishing Tests
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="scheduling" className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Scheduling
          </TabsTrigger>
        </TabsList>

        <TabsContent value="api-tests" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>API Endpoint Testing</CardTitle>
                <Button 
                  onClick={runAllAPITests} 
                  disabled={isRunning}
                  className="flex items-center gap-2"
                >
                  {isRunning ? (
                    <>
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      Running Tests...
                    </>
                  ) : (
                    <>
                      <TestTube className="w-4 h-4" />
                      Run All Tests
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {tests.map((test, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(test.status)}
                      <div>
                        <h3 className="font-medium">{test.name}</h3>
                        {test.message && (
                          <p className="text-sm text-muted-foreground">{test.message}</p>
                        )}
                        {test.duration && (
                          <p className="text-xs text-muted-foreground">
                            {test.duration}ms
                          </p>
                        )}
                      </div>
                    </div>
                    {getStatusBadge(test.status)}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="publishing-tests" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Real Publishing Tests</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <label className="text-sm font-medium">Test Content</label>
                <Textarea
                  value={testContent}
                  onChange={(e) => setTestContent(e.target.value)}
                  rows={4}
                  placeholder="Enter test content for publishing..."
                />
              </div>

              <div className="space-y-4">
                {publishingTests.map((test, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getPlatformIcon(test.platform)}
                        <div>
                          <h3 className="font-medium capitalize">{test.platform} Publishing Test</h3>
                          <p className="text-sm text-muted-foreground">{test.content}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(test.status)}
                        <Button
                          onClick={() => testRealPublishing(test.platform)}
                          disabled={test.status === 'running'}
                          size="sm"
                        >
                          {test.status === 'running' ? (
                            <RefreshCw className="w-4 h-4 animate-spin" />
                          ) : (
                            <Send className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </div>

                    {test.result && (
                      <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded">
                        <p className="text-sm text-green-700">
                          ✅ Publishing ID: {test.result.publishingId}
                        </p>
                        <p className="text-sm text-green-700">
                          Success: {test.result.successfulPlatforms}/{test.result.totalPlatforms}
                        </p>
                      </div>
                    )}

                    {test.error && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                        <p className="text-sm text-red-700">❌ {test.error}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Publishing Analytics Testing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground mb-4">
                  Analytics testing requires authentication and published content
                </p>
                <Button variant="outline" onClick={() => window.open('/publishing', '_blank')}>
                  Open Publishing Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduling" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Scheduling System Testing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground mb-4">
                  Scheduling testing requires authentication and valid social accounts
                </p>
                <Button variant="outline" onClick={() => window.open('/publishing', '_blank')}>
                  Test Scheduling
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
