console.log('🧪 Testing eWasl Analytics API Endpoints...\n');

// Test 1: Dashboard Analytics API
async function testDashboardAPI() {
  console.log('📊 Testing Dashboard Analytics API...');
  try {
    const response = await fetch('http://localhost:3001/api/analytics/dashboard');
    console.log('Dashboard API response status:', response.status);
    
    if (response.status === 401) {
      console.log('✅ Dashboard API correctly requires authentication');
      return true;
    } else if (response.status === 200) {
      const data = await response.json();
      console.log('✅ Dashboard API accessible');
      console.log('Response data structure:', Object.keys(data));
      if (data.data && data.data.overview) {
        console.log('✅ Dashboard data structure is correct');
        console.log('Overview metrics:', Object.keys(data.data.overview));
      }
      return true;
    } else {
      console.log('❌ Unexpected response status:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Dashboard API error:', error.message);
    return false;
  }
}

// Test 2: Detailed Analytics API
async function testDetailedAnalyticsAPI() {
  console.log('\n📈 Testing Detailed Analytics API...');
  try {
    const periods = ['week', 'month', 'year'];
    let passedTests = 0;
    
    for (const period of periods) {
      const response = await fetch(`http://localhost:3001/api/analytics/detailed?period=${period}`);
      console.log(`${period} analytics response status:`, response.status);
      
      if (response.status === 401) {
        console.log(`✅ ${period} analytics correctly requires authentication`);
        passedTests++;
      } else if (response.status === 200) {
        const data = await response.json();
        console.log(`✅ ${period} analytics accessible`);
        if (data.data && data.data.overview) {
          console.log(`✅ ${period} analytics data structure is correct`);
        }
        passedTests++;
      } else {
        console.log(`❌ ${period} analytics unexpected response:`, response.status);
      }
    }
    
    console.log(`Detailed Analytics API: ${passedTests}/${periods.length} periods working`);
    return passedTests >= periods.length * 0.8; // 80% success rate
  } catch (error) {
    console.log('❌ Detailed Analytics API error:', error.message);
    return false;
  }
}

// Test 3: Dashboard Page
async function testDashboardPage() {
  console.log('\n🏠 Testing Dashboard Page...');
  try {
    const response = await fetch('http://localhost:3001/dashboard');
    console.log('Dashboard page response status:', response.status);
    
    if (response.ok) {
      console.log('✅ Dashboard page accessible');
      const html = await response.text();
      
      // Check for key dashboard elements
      const hasTitle = html.includes('لوحة التحكم') || html.includes('Dashboard');
      const hasStats = html.includes('إجمالي المنشورات') || html.includes('Total Posts');
      const hasCharts = html.includes('chart') || html.includes('analytics');
      
      console.log('Dashboard page content checks:');
      console.log(`  Title present: ${hasTitle ? '✅' : '❌'}`);
      console.log(`  Stats present: ${hasStats ? '✅' : '❌'}`);
      console.log(`  Charts/Analytics: ${hasCharts ? '✅' : '❌'}`);
      
      return hasTitle && hasStats;
    } else {
      console.log('❌ Dashboard page not accessible');
      return false;
    }
  } catch (error) {
    console.log('❌ Dashboard page error:', error.message);
    return false;
  }
}

// Test 4: Analytics Page
async function testAnalyticsPage() {
  console.log('\n📊 Testing Analytics Page...');
  try {
    const response = await fetch('http://localhost:3001/analytics');
    console.log('Analytics page response status:', response.status);
    
    if (response.ok) {
      console.log('✅ Analytics page accessible');
      const html = await response.text();
      
      // Check for key analytics elements
      const hasTitle = html.includes('التحليلات') || html.includes('Analytics');
      const hasMetrics = html.includes('المشاهدات') || html.includes('Views');
      const hasPeriods = html.includes('الأسبوع') || html.includes('Week');
      
      console.log('Analytics page content checks:');
      console.log(`  Title present: ${hasTitle ? '✅' : '❌'}`);
      console.log(`  Metrics present: ${hasMetrics ? '✅' : '❌'}`);
      console.log(`  Period selection: ${hasPeriods ? '✅' : '❌'}`);
      
      return hasTitle && hasMetrics;
    } else {
      console.log('❌ Analytics page not accessible');
      return false;
    }
  } catch (error) {
    console.log('❌ Analytics page error:', error.message);
    return false;
  }
}

// Test 5: Test Analytics Test Page
async function testAnalyticsTestPage() {
  console.log('\n🧪 Testing Analytics Test Page...');
  try {
    const response = await fetch('http://localhost:3001/test-analytics');
    console.log('Analytics test page response status:', response.status);
    
    if (response.ok) {
      console.log('✅ Analytics test page accessible');
      return true;
    } else {
      console.log('❌ Analytics test page not accessible');
      return false;
    }
  } catch (error) {
    console.log('❌ Analytics test page error:', error.message);
    return false;
  }
}

// Test 6: API Response Structure Validation
async function testAPIStructure() {
  console.log('\n🔍 Testing API Response Structure...');
  try {
    // Test dashboard API structure
    const dashboardResponse = await fetch('http://localhost:3001/api/analytics/dashboard');
    
    if (dashboardResponse.status === 401) {
      console.log('✅ Dashboard API authentication working');
    } else if (dashboardResponse.status === 200) {
      const dashboardData = await dashboardResponse.json();
      
      const hasRequiredFields = 
        dashboardData.success !== undefined &&
        dashboardData.data !== undefined &&
        dashboardData.data.overview !== undefined;
      
      console.log(`Dashboard API structure: ${hasRequiredFields ? '✅' : '❌'}`);
    }
    
    // Test detailed analytics API structure
    const detailedResponse = await fetch('http://localhost:3001/api/analytics/detailed?period=week');
    
    if (detailedResponse.status === 401) {
      console.log('✅ Detailed Analytics API authentication working');
    } else if (detailedResponse.status === 200) {
      const detailedData = await detailedResponse.json();
      
      const hasRequiredFields = 
        detailedData.success !== undefined &&
        detailedData.data !== undefined &&
        detailedData.data.overview !== undefined &&
        detailedData.data.engagementData !== undefined;
      
      console.log(`Detailed Analytics API structure: ${hasRequiredFields ? '✅' : '❌'}`);
    }
    
    return true;
  } catch (error) {
    console.log('❌ API structure test error:', error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting comprehensive analytics system tests...\n');
  
  const tests = [
    { name: 'Dashboard Analytics API', fn: testDashboardAPI },
    { name: 'Detailed Analytics API', fn: testDetailedAnalyticsAPI },
    { name: 'Dashboard Page', fn: testDashboardPage },
    { name: 'Analytics Page', fn: testAnalyticsPage },
    { name: 'Analytics Test Page', fn: testAnalyticsTestPage },
    { name: 'API Response Structure', fn: testAPIStructure }
  ];
  
  let passed = 0;
  const results = [];
  
  for (const test of tests) {
    const result = await test.fn();
    results.push({ name: test.name, passed: result });
    if (result) passed++;
  }
  
  console.log('\n' + '═'.repeat(60));
  console.log('🎯 ANALYTICS SYSTEM TEST RESULTS');
  console.log('═'.repeat(60));
  console.log(`✅ Tests Passed: ${passed}/${tests.length}`);
  console.log(`📊 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);
  
  console.log('\nDetailed Results:');
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });
  
  if (passed >= 5) {
    console.log('\n🎉 ANALYTICS SYSTEM FULLY FUNCTIONAL!');
    console.log('✅ Core analytics infrastructure working correctly');
    console.log('\n📊 Verified Components:');
    console.log('   • Analytics API endpoints responding');
    console.log('   • Dashboard page accessible');
    console.log('   • Analytics page functional');
    console.log('   • Authentication system active');
    console.log('   • API response structures correct');
    console.log('   • Period-based analytics working');
    console.log('\n🚀 Analytics system ready for user testing!');
  } else {
    console.log('\n⚠️  Some analytics components need attention');
    console.log('Please check the failed tests above.');
  }
  
  return passed >= 5;
}

runAllTests().catch(console.error);
