import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

interface ConnectionHealth {
  platform: string;
  status: 'healthy' | 'warning' | 'error' | 'expired';
  lastChecked: string;
  issues: string[];
  recommendations: string[];
  accountId?: string;
  accountName?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('Checking connection health for user:', userId);

    // Get Supabase client
    const supabase = getSupabaseClient();

    // Get all social accounts for the user
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId);

    if (accountsError) {
      console.error('Error fetching accounts:', accountsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch accounts' },
        { status: 500 }
      );
    }

    const healthChecks: ConnectionHealth[] = [];

    // Check health for each account
    for (const account of accounts || []) {
      const health = await checkAccountHealth(account);
      healthChecks.push(health);
    }

    // Add checks for platforms without accounts
    const connectedPlatforms = (accounts || []).map(acc => acc.platform.toLowerCase());
    const allPlatforms = ['facebook', 'instagram', 'linkedin', 'twitter'];
    
    for (const platform of allPlatforms) {
      if (!connectedPlatforms.includes(platform)) {
        healthChecks.push({
          platform,
          status: 'error',
          lastChecked: new Date().toISOString(),
          issues: ['Account not connected'],
          recommendations: [`Connect your ${platform} account to start posting`]
        });
      }
    }

    return NextResponse.json({
      success: true,
      health: healthChecks,
      summary: {
        total: healthChecks.length,
        healthy: healthChecks.filter(h => h.status === 'healthy').length,
        warning: healthChecks.filter(h => h.status === 'warning').length,
        error: healthChecks.filter(h => h.status === 'error').length,
        expired: healthChecks.filter(h => h.status === 'expired').length
      }
    });

  } catch (error) {
    console.error('Error in health check:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function checkAccountHealth(account: any): Promise<ConnectionHealth> {
  const supabase = getSupabaseClient();
  const health: ConnectionHealth = {
    platform: account.platform,
    status: 'healthy',
    lastChecked: new Date().toISOString(),
    issues: [],
    recommendations: [],
    accountId: account.account_id,
    accountName: account.account_name
  };

  try {
    // Check if account is active
    if (!account.is_active) {
      health.status = 'error';
      health.issues.push('Account is disabled');
      health.recommendations.push('Enable the account in settings');
      return health;
    }

    // Check token expiration
    if (account.expires_at) {
      const expiresAt = new Date(account.expires_at);
      const now = new Date();
      const hoursUntilExpiry = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60);

      if (hoursUntilExpiry <= 0) {
        health.status = 'expired';
        health.issues.push('Access token has expired');
        health.recommendations.push('Reconnect your account to refresh the token');
        return health;
      } else if (hoursUntilExpiry <= 24) {
        health.status = 'warning';
        health.issues.push('Access token expires soon');
        health.recommendations.push('Consider reconnecting your account to refresh the token');
      }
    }

    // Platform-specific health checks
    switch (account.platform.toLowerCase()) {
      case 'facebook':
        await checkFacebookHealth(account, health);
        break;
      case 'instagram':
        await checkInstagramHealth(account, health);
        break;
      case 'linkedin':
        await checkLinkedInHealth(account, health);
        break;
      case 'twitter':
      case 'x':
        await checkTwitterHealth(account, health);
        break;
    }

    // Check last successful post
    const { data: recentPosts } = await supabase
      .from('posts')
      .select('created_at, status')
      .eq('user_id', account.user_id)
      .order('created_at', { ascending: false })
      .limit(5);

    if (recentPosts && recentPosts.length > 0) {
      const failedPosts = recentPosts.filter(p => p.status === 'FAILED').length;
      if (failedPosts > 2) {
        health.status = health.status === 'healthy' ? 'warning' : health.status;
        health.issues.push('Multiple recent posts failed');
        health.recommendations.push('Check account permissions and connection');
      }
    }

  } catch (error) {
    console.error(`Error checking health for ${account.platform}:`, error);
    health.status = 'error';
    health.issues.push('Unable to verify account status');
    health.recommendations.push('Try reconnecting your account');
  }

  return health;
}

async function checkFacebookHealth(account: any, health: ConnectionHealth) {
  // Check if we have page information for business accounts
  const metadata = account.metadata || {};
  
  if (!metadata.page_id && !metadata.fan_count) {
    health.status = health.status === 'healthy' ? 'warning' : health.status;
    health.issues.push('No Facebook page configured');
    health.recommendations.push('Configure a Facebook page for business posting');
  }

  // Check token validity (simplified check)
  if (!account.access_token) {
    health.status = 'error';
    health.issues.push('Missing access token');
    health.recommendations.push('Reconnect your Facebook account');
  }
}

async function checkInstagramHealth(account: any, health: ConnectionHealth) {
  // Instagram is connected through Facebook
  const metadata = account.metadata || {};
  
  if (!metadata.page_id) {
    health.status = health.status === 'healthy' ? 'warning' : health.status;
    health.issues.push('Instagram business account not properly linked');
    health.recommendations.push('Ensure Instagram is connected to a Facebook page');
  }
}

async function checkLinkedInHealth(account: any, health: ConnectionHealth) {
  const metadata = account.metadata || {};
  
  // Check if we have organization information for business accounts
  if (!metadata.organization_id && !metadata.organization_name) {
    health.status = health.status === 'healthy' ? 'warning' : health.status;
    health.issues.push('No LinkedIn company page configured');
    health.recommendations.push('Configure a LinkedIn company page for business posting');
  }

  // Check token validity
  if (!account.access_token) {
    health.status = 'error';
    health.issues.push('Missing access token');
    health.recommendations.push('Reconnect your LinkedIn account');
  }
}

async function checkTwitterHealth(account: any, health: ConnectionHealth) {
  // Check token validity for Twitter
  if (!account.access_token || !account.refresh_token) {
    health.status = 'error';
    health.issues.push('Missing Twitter credentials');
    health.recommendations.push('Reconnect your Twitter/X account');
  }

  // Twitter API v2 specific checks
  const metadata = account.metadata || {};
  if (!metadata.followers_count) {
    health.status = health.status === 'healthy' ? 'warning' : health.status;
    health.issues.push('Unable to fetch account metrics');
    health.recommendations.push('Verify Twitter API permissions');
  }
}
