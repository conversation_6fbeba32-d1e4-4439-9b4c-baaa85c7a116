'use client';

import React, { useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Send, 
  Calendar, 
  BarChart3, 
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  TrendingUp,
  Users,
  Eye,
  Heart,
  MessageCircle,
  Share
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { PublishingDashboard } from '@/components/publishing/publishing-dashboard';
import { createClient } from '@/lib/supabase/client';

interface PublishingStats {
  totalPosts: number;
  scheduledPosts: number;
  successfulPosts: number;
  failedPosts: number;
  totalEngagement: number;
  topPlatform: string;
}

interface RecentPost {
  id: string;
  content: string;
  platforms: string[];
  status: 'published' | 'scheduled' | 'failed';
  publishedAt: string;
  engagement?: {
    likes: number;
    shares: number;
    comments: number;
    views: number;
  };
}

export default function PublishingPage() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<PublishingStats>({
    totalPosts: 0,
    scheduledPosts: 0,
    successfulPosts: 0,
    failedPosts: 0,
    totalEngagement: 0,
    topPlatform: ''
  });
  const [recentPosts, setRecentPosts] = useState<RecentPost[]>([]);
  const [activeTab, setActiveTab] = useState('publish');
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkAuthAndLoadData();
  }, []);

  const checkAuthAndLoadData = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        router.push('/auth/signin');
        return;
      }

      setUser(user);
      await loadPublishingData();
      
    } catch (error) {
      console.error('Error checking auth:', error);
      router.push('/auth/signin');
    } finally {
      setIsLoading(false);
    }
  };

  const loadPublishingData = async () => {
    try {
      // Load publishing statistics
      const statsResponse = await fetch('/api/publishing/stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.stats || stats);
      }

      // Load recent posts
      const postsResponse = await fetch('/api/publishing/publish?limit=5');
      if (postsResponse.ok) {
        const postsData = await postsResponse.json();
        setRecentPosts(postsData.data || []);
      }

    } catch (error) {
      console.error('Error loading publishing data:', error);
    }
  };

  const refreshData = async () => {
    await loadPublishingData();
    toast.success('تم تحديث البيانات');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-500">منشور</Badge>;
      case 'scheduled':
        return <Badge className="bg-blue-500">مجدول</Badge>;
      case 'failed':
        return <Badge variant="destructive">فشل</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 max-w-6xl">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
          <span className="mr-3 text-lg">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold">نظام النشر المتقدم</h1>
            <p className="text-muted-foreground">
              انشر محتواك على جميع منصات التواصل الاجتماعي من مكان واحد
            </p>
          </div>
          <Button onClick={refreshData} variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            تحديث
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">إجمالي المنشورات</p>
                  <p className="text-2xl font-bold">{stats.totalPosts}</p>
                </div>
                <Send className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">المنشورات المجدولة</p>
                  <p className="text-2xl font-bold">{stats.scheduledPosts}</p>
                </div>
                <Calendar className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">نجح النشر</p>
                  <p className="text-2xl font-bold">{stats.successfulPosts}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">إجمالي التفاعل</p>
                  <p className="text-2xl font-bold">{stats.totalEngagement}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="publish" className="flex items-center gap-2">
            <Send className="w-4 h-4" />
            نشر جديد
          </TabsTrigger>
          <TabsTrigger value="scheduled" className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            المجدولة
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            التحليلات
          </TabsTrigger>
        </TabsList>

        <TabsContent value="publish" className="space-y-6">
          <PublishingDashboard />
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                المنشورات المجدولة
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recentPosts.filter(post => post.status === 'scheduled').length > 0 ? (
                <div className="space-y-4">
                  {recentPosts
                    .filter(post => post.status === 'scheduled')
                    .map((post) => (
                      <div key={post.id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <p className="text-sm text-muted-foreground mb-2">
                              {formatDate(post.publishedAt)}
                            </p>
                            <p className="line-clamp-2">{post.content}</p>
                          </div>
                          {getStatusBadge(post.status)}
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">المنصات:</span>
                          {post.platforms.map((platform, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {platform}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">لا توجد منشورات مجدولة</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Posts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  المنشورات الأخيرة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentPosts.length > 0 ? (
                  <div className="space-y-4">
                    {recentPosts.slice(0, 5).map((post) => (
                      <div key={post.id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <p className="text-sm text-muted-foreground mb-2">
                              {formatDate(post.publishedAt)}
                            </p>
                            <p className="line-clamp-2">{post.content}</p>
                          </div>
                          {getStatusBadge(post.status)}
                        </div>
                        
                        {post.engagement && (
                          <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Heart className="w-4 h-4" />
                              {post.engagement.likes}
                            </div>
                            <div className="flex items-center gap-1">
                              <MessageCircle className="w-4 h-4" />
                              {post.engagement.comments}
                            </div>
                            <div className="flex items-center gap-1">
                              <Share className="w-4 h-4" />
                              {post.engagement.shares}
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="w-4 h-4" />
                              {post.engagement.views}
                            </div>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-2 mt-3">
                          <span className="text-sm text-muted-foreground">المنصات:</span>
                          {post.platforms.map((platform, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {platform}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">لا توجد منشورات بعد</p>
                    <Button 
                      onClick={() => setActiveTab('publish')} 
                      className="mt-4"
                      variant="outline"
                    >
                      إنشاء منشور جديد
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  ملخص الأداء
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded">
                    <span>معدل نجاح النشر</span>
                    <span className="font-bold text-green-600">
                      {stats.totalPosts > 0 
                        ? Math.round((stats.successfulPosts / stats.totalPosts) * 100)
                        : 0}%
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 border rounded">
                    <span>المنصة الأكثر استخداماً</span>
                    <span className="font-bold">{stats.topPlatform || 'غير محدد'}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 border rounded">
                    <span>متوسط التفاعل</span>
                    <span className="font-bold">
                      {stats.totalPosts > 0 
                        ? Math.round(stats.totalEngagement / stats.totalPosts)
                        : 0}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 border rounded">
                    <span>المنشورات في الانتظار</span>
                    <span className="font-bold text-blue-600">{stats.scheduledPosts}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
