"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>, Bar<PERSON>hart, Line, LineChart, ResponsiveContainer, <PERSON>ltip, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";

interface EngagementData {
  date: string;
  likes: number;
  comments: number;
  shares: number;
  views: number;
}

interface EngagementChartProps {
  data: EngagementData[];
  title: string;
  description: string;
}

export function EngagementChart({ data, title, description }: EngagementChartProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="line">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="line">خط</TabsTrigger>
              <TabsTrigger value="bar">أعمدة</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="line" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={data}
                margin={{
                  top: 5,
                  right: 10,
                  left: 10,
                  bottom: 5,
                }}
              >
                <XAxis
                  dataKey="date"
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value}`}
                />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="likes"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                  name="إعجابات"
                />
                <Line
                  type="monotone"
                  dataKey="comments"
                  stroke="#82ca9d"
                  name="تعليقات"
                />
                <Line
                  type="monotone"
                  dataKey="shares"
                  stroke="#ffc658"
                  name="مشاركات"
                />
                <Line
                  type="monotone"
                  dataKey="views"
                  stroke="#ff8042"
                  name="مشاهدات"
                />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="bar" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={data}
                margin={{
                  top: 5,
                  right: 10,
                  left: 10,
                  bottom: 5,
                }}
              >
                <XAxis
                  dataKey="date"
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#888888"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value}`}
                />
                <Tooltip />
                <Bar
                  dataKey="likes"
                  fill="#8884d8"
                  radius={[4, 4, 0, 0]}
                  name="إعجابات"
                />
                <Bar
                  dataKey="comments"
                  fill="#82ca9d"
                  radius={[4, 4, 0, 0]}
                  name="تعليقات"
                />
                <Bar
                  dataKey="shares"
                  fill="#ffc658"
                  radius={[4, 4, 0, 0]}
                  name="مشاركات"
                />
                <Bar
                  dataKey="views"
                  fill="#ff8042"
                  radius={[4, 4, 0, 0]}
                  name="مشاهدات"
                />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
