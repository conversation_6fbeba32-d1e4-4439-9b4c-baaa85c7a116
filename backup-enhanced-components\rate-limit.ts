import { NextRequest, NextResponse } from 'next/server';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RequestInfo {
  count: number;
  resetTime: number;
}

// In-memory store for rate limiting (use Redis in production)
const requestStore = new Map<string, RequestInfo>();

/**
 * Rate limiting middleware for API routes
 */
export function createRateLimit(config: RateLimitConfig) {
  const {
    windowMs,
    maxRequests,
    message = 'Too many requests, please try again later.',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
  } = config;

  return async function rateLimit(
    request: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> {
    // Get client identifier (IP address)
    const clientId = getClientId(request);
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up expired entries
    cleanupExpiredEntries(windowStart);

    // Get or create request info for this client
    let requestInfo = requestStore.get(clientId);
    
    if (!requestInfo || requestInfo.resetTime <= now) {
      // Create new window
      requestInfo = {
        count: 0,
        resetTime: now + windowMs,
      };
    }

    // Check if limit exceeded
    if (requestInfo.count >= maxRequests) {
      return NextResponse.json(
        {
          error: message,
          retryAfter: Math.ceil((requestInfo.resetTime - now) / 1000),
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': requestInfo.resetTime.toString(),
            'Retry-After': Math.ceil((requestInfo.resetTime - now) / 1000).toString(),
          }
        }
      );
    }

    // Execute the handler
    const response = await handler(request);

    // Update count based on response status
    const shouldCount = 
      (!skipSuccessfulRequests || response.status >= 400) &&
      (!skipFailedRequests || response.status < 400);

    if (shouldCount) {
      requestInfo.count++;
      requestStore.set(clientId, requestInfo);
    }

    // Add rate limit headers
    response.headers.set('X-RateLimit-Limit', maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', Math.max(0, maxRequests - requestInfo.count).toString());
    response.headers.set('X-RateLimit-Reset', requestInfo.resetTime.toString());

    return response;
  };
}

/**
 * Get client identifier from request
 */
function getClientId(request: NextRequest): string {
  // Try to get real IP from headers (for proxies/load balancers)
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  // Use the first available IP
  const ip = forwardedFor?.split(',')[0]?.trim() || 
             realIp || 
             cfConnectingIp || 
             request.ip || 
             'unknown';
  
  return ip;
}

/**
 * Clean up expired entries from the store
 */
function cleanupExpiredEntries(windowStart: number): void {
  for (const [key, info] of requestStore.entries()) {
    if (info.resetTime <= windowStart) {
      requestStore.delete(key);
    }
  }
}

/**
 * Predefined rate limit configurations
 */
export const rateLimitConfigs = {
  // Strict rate limiting for authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    message: 'Too many authentication attempts. Please try again in 15 minutes.',
  },
  
  // Moderate rate limiting for API endpoints
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
    message: 'API rate limit exceeded. Please slow down your requests.',
  },
  
  // Lenient rate limiting for general endpoints
  general: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  },
  
  // Very strict for sensitive operations
  sensitive: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10, // 10 requests per hour
    message: 'Rate limit exceeded for sensitive operation. Please try again later.',
  },
};

/**
 * Utility function to apply rate limiting to API routes
 */
export function withRateLimit(
  config: RateLimitConfig,
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  const rateLimit = createRateLimit(config);
  
  return async function(request: NextRequest) {
    return rateLimit(request, handler);
  };
}
