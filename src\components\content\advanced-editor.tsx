'use client';

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';

import {
  Wand2,
  <PERSON>h,
  <PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FileText,
  Zap,
  Target,
  Globe,
  Palette,
  Clock,
  <PERSON><PERSON><PERSON>3,
  <PERSON>,
  MessageSquare,
} from 'lucide-react';
import { toast } from 'sonner';
import { TemplatesBrowser } from '@/components/templates/templates-browser';

interface ContentGenerationRequest {
  prompt: string;
  platform: string;
  tone: string;
  language: string;
  includeHashtags: boolean;
  includeEmojis: boolean;
  keywords?: string[];
  targetAudience?: string;
}

interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  platform: string[];
  content: string;
  variables: any[];
  hashtags: string[];
}

interface AdvancedEditorProps {
  initialContent?: string;
  platform?: string;
  onContentChange?: (content: string) => void;
  onSave?: (content: string, metadata: any) => void;
}

export function AdvancedEditor({
  initialContent = '',
  platform = 'facebook',
  onContentChange,
  onSave,
}: AdvancedEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [isGenerating, setIsGenerating] = useState(false);
  const [templates, setTemplates] = useState<ContentTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ContentTemplate | null>(null);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});
  const [generationSettings, setGenerationSettings] = useState(() => ({
    platform: platform,
    tone: 'friendly',
    language: 'ar',
    includeHashtags: true,
    includeEmojis: true,
    targetAudience: '',
  }));
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [contentStats, setContentStats] = useState({
    characterCount: 0,
    wordCount: 0,
    hashtagCount: 0,
    emojiCount: 0,
  });

  const onContentChangeRef = useRef(onContentChange);
  onContentChangeRef.current = onContentChange;

  useEffect(() => {
    const characterCount = content.length;
    const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
    const hashtagCount = (content.match(/#[\w\u0600-\u06FF]+/g) || []).length;
    const emojiCount = (content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || []).length;

    setContentStats({
      characterCount,
      wordCount,
      hashtagCount,
      emojiCount,
    });

    onContentChangeRef.current?.(content);
  }, [content]);

  useEffect(() => {
    fetchTemplates();
  }, []);



  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/content/templates?action=popular&limit=10');
      const result = await response.json();

      if (result.success) {
        setTemplates(result.templates);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const generateContent = async (prompt: string) => {
    if (!prompt.trim()) {
      toast.error('يرجى إدخال وصف للمحتوى المطلوب');
      return;
    }

    setIsGenerating(true);

    try {
      const response = await fetch('/api/content/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          prompt,
          ...generationSettings,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setContent(result.result.content);
        if (result.result.hashtags) {
          setHashtags(result.result.hashtags);
        }
        toast.success('تم إنشاء المحتوى بنجاح');
      } else {
        toast.error(result.error || 'فشل في إنشاء المحتوى');
      }
    } catch (error) {
      console.error('Content generation error:', error);
      toast.error('خطأ في إنشاء المحتوى');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateHashtags = async () => {
    if (!content.trim()) {
      toast.error('يرجى إدخال محتوى أولاً');
      return;
    }

    try {
      const response = await fetch('/api/content/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'hashtags',
          content,
          platform: generationSettings.platform,
          language: generationSettings.language,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setHashtags(result.hashtags.map((h: any) => h.tag || h));
        toast.success('تم إنشاء الهاشتاقات بنجاح');
      } else {
        toast.error(result.error || 'فشل في إنشاء الهاشتاقات');
      }
    } catch (error) {
      console.error('Hashtag generation error:', error);
      toast.error('خطأ في إنشاء الهاشتاقات');
    }
  };

  const optimizeContent = async (toPlatform: string) => {
    if (!content.trim()) {
      toast.error('يرجى إدخال محتوى أولاً');
      return;
    }

    try {
      const response = await fetch('/api/content/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'optimize',
          content,
          fromPlatform: generationSettings.platform,
          toPlatform,
          language: generationSettings.language,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setContent(result.optimizedContent);
        setGenerationSettings(prev => ({ ...prev, platform: toPlatform }));
        toast.success(`تم تحسين المحتوى لـ ${toPlatform}`);
      } else {
        toast.error(result.error || 'فشل في تحسين المحتوى');
      }
    } catch (error) {
      console.error('Content optimization error:', error);
      toast.error('خطأ في تحسين المحتوى');
    }
  };

  const processTemplate = async (template: ContentTemplate) => {
    try {
      const response = await fetch('/api/content/templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'process',
          templateId: template.id,
          variables: templateVariables,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setContent(result.content);
        if (result.hashtags) {
          setHashtags(result.hashtags);
        }
        toast.success('تم تطبيق القالب بنجاح');
      } else {
        toast.error(result.error || 'فشل في تطبيق القالب');
      }
    } catch (error) {
      console.error('Template processing error:', error);
      toast.error('خطأ في تطبيق القالب');
    }
  };

  const addHashtagToContent = (hashtag: string) => {
    const newContent = content + (content.endsWith(' ') ? '' : ' ') + `#${hashtag}`;
    setContent(newContent);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('تم النسخ إلى الحافظة');
  };

  const getPlatformLimit = (platform: string) => {
    const limits = {
      twitter: 280,
      facebook: 2000,
      instagram: 2200,
      linkedin: 3000,
      snapchat: 250,
    };
    return limits[platform as keyof typeof limits] || 2000;
  };

  const isOverLimit = () => {
    const limit = getPlatformLimit(generationSettings.platform);
    return contentStats.characterCount > limit;
  };

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">محرر المحتوى المتقدم</h2>
          <p className="text-gray-600">إنشاء وتحرير المحتوى بمساعدة الذكاء الاصطناعي</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => copyToClipboard(content)}>
            <Copy className="h-4 w-4 mr-2" />
            نسخ
          </Button>
          <Button onClick={() => onSave?.(content, { hashtags, stats: contentStats })}>
            حفظ المحتوى
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Editor */}
        <div className="lg:col-span-2 space-y-4">
          {/* Content Editor */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                محرر النص
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="اكتب محتواك هنا أو استخدم الذكاء الاصطناعي لإنشاء محتوى..."
                className="min-h-48 text-lg"
                dir="rtl"
              />

              {/* Content Stats */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex gap-4">
                  <span>الأحرف: {contentStats.characterCount}</span>
                  <span>الكلمات: {contentStats.wordCount}</span>
                  <span>الهاشتاقات: {contentStats.hashtagCount}</span>
                  <span>الإيموجي: {contentStats.emojiCount}</span>
                </div>
                <div className={`font-medium ${isOverLimit() ? 'text-red-600' : 'text-green-600'}`}>
                  {getPlatformLimit(generationSettings.platform) - contentStats.characterCount} حرف متبقي
                </div>
              </div>

              {isOverLimit() && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-800 text-sm">
                    المحتوى يتجاوز الحد المسموح لمنصة {generationSettings.platform}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI Generation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-5 w-5" />
                إنشاء بالذكاء الاصطناعي
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>المنصة</Label>
                  <select
                    className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    value={generationSettings.platform}
                    onChange={(e) => setGenerationSettings(prev => ({ ...prev, platform: e.target.value }))}
                  >
                    <option value="facebook">فيسبوك</option>
                    <option value="instagram">إنستغرام</option>
                    <option value="twitter">تويتر</option>
                    <option value="linkedin">لينكد إن</option>
                    <option value="snapchat">سناب شات</option>
                  </select>
                </div>

                <div>
                  <Label>نبرة المحتوى</Label>
                  <select
                    className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    value={generationSettings.tone}
                    onChange={(e) => setGenerationSettings(prev => ({ ...prev, tone: e.target.value }))}
                  >
                    <option value="professional">مهني</option>
                    <option value="casual">عادي</option>
                    <option value="friendly">ودود</option>
                    <option value="formal">رسمي</option>
                    <option value="humorous">فكاهي</option>
                    <option value="inspiring">ملهم</option>
                  </select>
                </div>
              </div>

              <div>
                <Label>الجمهور المستهدف</Label>
                <Input
                  value={generationSettings.targetAudience}
                  onChange={(e) => setGenerationSettings(prev => ({ ...prev, targetAudience: e.target.value }))}
                  placeholder="مثال: الشباب المهتمين بالتكنولوجيا"
                />
              </div>

              <div className="flex gap-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="flex-1">
                      <Sparkles className="h-4 w-4 mr-2" />
                      إنشاء محتوى
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>إنشاء محتوى بالذكاء الاصطناعي</DialogTitle>
                      <DialogDescription>
                        اكتب وصفاً للمحتوى الذي تريد إنشاءه
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <Textarea
                        placeholder="مثال: منشور عن فوائد التمارين الرياضية للصحة النفسية"
                        className="min-h-24"
                        id="ai-prompt"
                      />
                      <Button
                        onClick={() => {
                          const prompt = (document.getElementById('ai-prompt') as HTMLTextAreaElement)?.value;
                          generateContent(prompt);
                        }}
                        disabled={isGenerating}
                        className="w-full"
                      >
                        {isGenerating ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            جاري الإنشاء...
                          </>
                        ) : (
                          <>
                            <Wand2 className="h-4 w-4 mr-2" />
                            إنشاء المحتوى
                          </>
                        )}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button variant="outline" onClick={generateHashtags}>
                  <Hash className="h-4 w-4 mr-2" />
                  هاشتاقات
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Platform Optimization */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                تحسين للمنصات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                {['facebook', 'instagram', 'twitter', 'linkedin'].map((platform) => (
                  <Button
                    key={platform}
                    variant="outline"
                    size="sm"
                    onClick={() => optimizeContent(platform)}
                    className="text-xs"
                  >
                    {platform === 'facebook' && 'فيسبوك'}
                    {platform === 'instagram' && 'إنستغرام'}
                    {platform === 'twitter' && 'تويتر'}
                    {platform === 'linkedin' && 'لينكد إن'}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Hashtags */}
          {hashtags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Hash className="h-5 w-5" />
                  الهاشتاقات المقترحة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {hashtags.map((hashtag, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="cursor-pointer hover:bg-blue-50"
                      onClick={() => addHashtagToContent(hashtag)}
                    >
                      #{hashtag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Templates Browser */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                القوالب الجاهزة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TemplatesBrowser
                onTemplateSelect={(template, processedContent) => {
                  setContent(processedContent);
                  toast.success('تم تطبيق القالب بنجاح');
                }}
                selectedPlatform={generationSettings.platform}
                language={generationSettings.language}
              />
            </CardContent>
          </Card>
        </div>
      </div>


    </div>
  );
}
