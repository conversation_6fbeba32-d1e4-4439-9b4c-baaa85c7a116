# 🧪 LIVE OAUTH TESTING GUIDE

## 📋 **COMPREHENSIVE OAUTH FLOW TESTING**

This guide provides step-by-step instructions for testing the OAuth flows with real social media accounts on the live eWasl application.

### 🎯 **TESTING OBJECTIVES**
- Validate OAuth callback URL consistency
- Test enhanced provider implementations
- Verify token storage and management
- Confirm business account selection flows
- Test posting capabilities with real accounts

---

## 🔐 **PHASE 1: AUTHENTICATION & ACCESS**

### **Step 1.1: Access the Application**
1. Navigate to: `https://app.ewasl.com`
2. Sign in with demo credentials or create account
3. Navigate to Social Media section

### **Step 1.2: Verify Current Connections**
1. Go to `/social` page
2. Check existing connections:
   - ✅ LinkedIn: burak ozan (already connected)
   - ✅ Facebook: <PERSON> (already connected)
3. Note: These prove <PERSON><PERSON><PERSON> is working in production

---

## 📱 **PHASE 2: TWITTER/X OAUTH TESTING**

### **Step 2.1: Initiate Twitter Connection**
1. Click "Connect Twitter/X" button
2. **Expected**: Redirect to Twitter OAuth page
3. **Verify URL**: Should contain `/api/social/callback/twitter`

### **Step 2.2: Twitter OAuth Flow**
1. **OAuth URL Pattern**: 
   ```
   https://twitter.com/i/oauth2/authorize?
   response_type=code&
   client_id=[CLIENT_ID]&
   redirect_uri=https://app.ewasl.com/api/social/callback/twitter&
   scope=tweet.read+tweet.write+users.read+offline.access&
   state=[STATE]&
   code_challenge=[CHALLENGE]&
   code_challenge_method=S256
   ```

2. **Authorize Application**:
   - Grant permissions for reading profile
   - Grant permissions for posting tweets
   - Grant permissions for offline access

3. **Callback Verification**:
   - Should redirect to: `https://app.ewasl.com/api/social/callback/twitter`
   - Should include `code` and `state` parameters
   - Should complete authentication successfully

### **Step 2.3: Twitter Connection Verification**
1. **Check Account List**: Should show new Twitter account
2. **Account Details**: Should display username and profile info
3. **Token Storage**: Account should persist after page refresh

---

## 📘 **PHASE 3: FACEBOOK OAUTH TESTING**

### **Step 3.1: Test Additional Facebook Account**
1. Click "Connect Facebook" button (to add second account)
2. **Expected**: Redirect to Facebook OAuth dialog

### **Step 3.2: Facebook OAuth Flow**
1. **OAuth URL Pattern**:
   ```
   https://www.facebook.com/v19.0/dialog/oauth?
   client_id=[APP_ID]&
   redirect_uri=https://app.ewasl.com/api/social/callback/facebook&
   scope=pages_manage_posts,pages_read_engagement,business_management&
   response_type=code&
   state=[STATE]
   ```

2. **Business Account Selection**:
   - Should show Facebook Pages you manage
   - Select appropriate business page
   - Grant posting permissions

3. **Callback Verification**:
   - Should redirect to: `https://app.ewasl.com/api/social/callback/facebook`
   - Should complete authentication successfully

---

## 📸 **PHASE 4: INSTAGRAM OAUTH TESTING**

### **Step 4.1: Instagram Business Account Connection**
1. Click "Connect Instagram" button
2. **Expected**: Redirect to Facebook OAuth (Instagram uses Facebook Graph API)

### **Step 4.2: Instagram OAuth Flow**
1. **OAuth URL Pattern**:
   ```
   https://www.facebook.com/v19.0/dialog/oauth?
   client_id=[APP_ID]&
   redirect_uri=https://app.ewasl.com/api/social/callback/instagram&
   scope=instagram_graph_user_profile,instagram_graph_user_media,instagram_content_publish&
   response_type=code&
   state=[STATE]_instagram
   ```

2. **Instagram Business Account Selection**:
   - Should show Instagram business accounts linked to Facebook pages
   - Select appropriate Instagram business account
   - Grant content publishing permissions

---

## 💼 **PHASE 5: LINKEDIN OAUTH TESTING**

### **Step 5.1: Test Additional LinkedIn Account**
1. Click "Connect LinkedIn" button (to test second account)
2. **Expected**: Redirect to LinkedIn OAuth page

### **Step 5.2: LinkedIn OAuth Flow**
1. **OAuth URL Pattern**:
   ```
   https://www.linkedin.com/oauth/v2/authorization?
   response_type=code&
   client_id=[CLIENT_ID]&
   redirect_uri=https://app.ewasl.com/api/social/callback/linkedin&
   scope=openid+profile+w_member_social+email&
   state=[STATE]
   ```

2. **Company Page Selection**:
   - Should show LinkedIn company pages you manage
   - Select appropriate company page for business posting
   - Grant posting permissions

---

## 🧪 **PHASE 6: POSTING TESTS**

### **Step 6.1: Test Post Creation**
1. Navigate to `/posts/new`
2. Create a test post:
   ```
   🧪 Testing eWasl social media integration!
   
   This is a test post to verify our enhanced OAuth flows and posting capabilities.
   
   #eWasl #SocialMediaManagement #Testing
   ```

### **Step 6.2: Platform Selection**
1. Select connected platforms for posting
2. Verify all connected accounts appear in selection
3. Choose 2-3 platforms for testing

### **Step 6.3: Publishing Test**
1. Click "Publish Now" or "Schedule Post"
2. **Expected Results**:
   - ✅ Post should appear on selected platforms
   - ✅ Success confirmation in eWasl dashboard
   - ✅ Post links should be generated and stored

---

## 🔍 **PHASE 7: VERIFICATION & VALIDATION**

### **Step 7.1: Account Status Verification**
1. Go to `/social` page
2. Verify all connected accounts show:
   - ✅ Platform name
   - ✅ Account name/username
   - ✅ Connection status (active)
   - ✅ Last activity timestamp

### **Step 7.2: Token Health Check**
1. Test account connectivity:
   - Click "Test Connection" for each account
   - Verify API responses are successful
   - Check for any token expiration issues

### **Step 7.3: Post History Verification**
1. Go to `/posts` page
2. Verify test posts appear in history
3. Check post status and platform delivery
4. Verify post links and engagement data

---

## 📊 **TESTING CHECKLIST**

### **OAuth Callback URLs** ✅
- [ ] Twitter: `/api/social/callback/twitter`
- [ ] Facebook: `/api/social/callback/facebook`
- [ ] Instagram: `/api/social/callback/instagram`
- [ ] LinkedIn: `/api/social/callback/linkedin`

### **Enhanced Provider Features** ✅
- [ ] OAuth 2.0 with PKCE (Twitter)
- [ ] Business account selection (Facebook/Instagram)
- [ ] Company page selection (LinkedIn)
- [ ] Token refresh handling
- [ ] Error handling and user feedback

### **Account Management** ✅
- [ ] Multiple accounts per platform
- [ ] Account disconnection
- [ ] Token expiration handling
- [ ] Account status monitoring

### **Posting Capabilities** ✅
- [ ] Text posts to all platforms
- [ ] Media uploads (images)
- [ ] Scheduled posting
- [ ] Post status tracking
- [ ] Error handling for failed posts

---

## 🚨 **TROUBLESHOOTING GUIDE**

### **Common OAuth Issues**
1. **404 on Callback**: Check callback URL configuration in platform developer console
2. **Invalid State**: Clear browser cache and retry OAuth flow
3. **Permission Denied**: Verify app permissions in platform developer settings
4. **Token Expired**: Use refresh token flow or re-authenticate

### **Platform-Specific Issues**

#### **Twitter/X**
- Ensure app has OAuth 2.0 enabled (not OAuth 1.0a)
- Verify callback URL in Twitter Developer Portal
- Check API v2 permissions

#### **Facebook/Instagram**
- Verify app is in Live mode (not Development)
- Check Facebook App Review status
- Ensure Instagram Business account is linked to Facebook Page

#### **LinkedIn**
- Verify company page admin permissions
- Check LinkedIn app permissions and products
- Ensure Marketing Developer Platform access

---

## 📈 **SUCCESS CRITERIA**

### **Minimum Requirements** ✅
- [ ] At least 3 platforms connected successfully
- [ ] OAuth flows complete without errors
- [ ] Test posts publish successfully
- [ ] Account management functions work
- [ ] Token storage persists across sessions

### **Optimal Results** 🎯
- [ ] All 4 platforms connected (Twitter, Facebook, Instagram, LinkedIn)
- [ ] Business/company accounts selected appropriately
- [ ] Multiple accounts per platform supported
- [ ] Real-time posting works flawlessly
- [ ] Comprehensive error handling demonstrated

---

## 🎉 **COMPLETION VERIFICATION**

After completing all tests, verify:

1. **OAuth Integration**: All callback URLs working consistently
2. **Enhanced Providers**: Postiz integration architecture functional
3. **Account Management**: Multiple accounts manageable
4. **Publishing System**: Real posts delivered to platforms
5. **Error Handling**: Graceful handling of edge cases

**🎯 SUCCESS INDICATOR**: Ability to connect new accounts and publish real content to multiple social media platforms simultaneously through the eWasl interface.
