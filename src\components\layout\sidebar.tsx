"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  LayoutDashboard,
  Calendar,
  Settings,
  Users,
  LogOut,
  MessageSquare,
  Share2,
  BarChart3,
  GitBranch,
} from "lucide-react";
import { useSupabase } from "@/components/auth/supabase-provider";

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { user, signOut } = useSupabase();
  const isAdmin = user?.user_metadata?.role === "ADMIN";

  const routes = [
    {
      label: "لوحة التحكم",
      icon: LayoutDashboard,
      href: "/dashboard",
      active: pathname === "/dashboard",
    },
    {
      label: "المنشورات",
      icon: MessageSquare,
      href: "/posts",
      active: pathname.startsWith("/posts"),
    },
    {
      label: "تقويم المحتوى",
      icon: Calendar,
      href: "/calendar",
      active: pathname.startsWith("/calendar"),
    },
    {
      label: "الحسابات",
      icon: Share2,
      href: "/social",
      active: pathname.startsWith("/social"),
    },
    {
      label: "التحليلات",
      icon: BarChart3,
      href: "/analytics",
      active: pathname.startsWith("/analytics"),
    },
    {
      label: "إدارة الفريق",
      icon: Users,
      href: "/teams",
      active: pathname.startsWith("/teams"),
    },
    {
      label: "سير العمل",
      icon: GitBranch,
      href: "/workflows",
      active: pathname.startsWith("/workflows"),
    },
    {
      label: "الإعدادات",
      icon: Settings,
      href: "/settings",
      active: pathname.startsWith("/settings"),
    },
  ];

  const adminRoutes = [
    {
      label: "المستخدمين",
      icon: Users,
      href: "/admin/users",
      active: pathname.startsWith("/admin/users"),
    },
    {
      label: "إعدادات API",
      icon: GitBranch,
      href: "/admin/api-config",
      active: pathname.startsWith("/admin/api-config"),
    },
  ];

  return (
    <div className={cn("h-full flex flex-col bg-white/50 backdrop-blur-sm", className)}>
      <div className="flex flex-col h-full">
        {/* Logo Section */}
        <div className="p-6 border-b border-gray-200/50">
          <Link href="/dashboard" className="block">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                eW
              </div>
              <div>
                <h2 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  eWasl
                </h2>
                <p className="text-xs text-gray-500">منصة إدارة المحتوى</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <div className="flex-1 px-4 py-6 space-y-2" dir="rtl">
          <div className="mb-6">
            <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">
              القائمة الرئيسية
            </p>
            <div className="space-y-1">
              {routes.map((route) => (
                <Button
                  key={route.href}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start h-11 px-3 text-sm font-medium transition-all duration-200",
                    route.active
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg hover:shadow-xl"
                      : "text-gray-700 hover:bg-blue-50 hover:text-blue-700"
                  )}
                  asChild
                >
                  <Link href={route.href}>
                    <route.icon className="ml-3 h-5 w-5" />
                    {route.label}
                  </Link>
                </Button>
              ))}
            </div>
          </div>

          {isAdmin && (
            <div className="mt-8">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">
                إدارة النظام
              </p>
              <div className="space-y-1">
                {adminRoutes.map((route) => (
                  <Button
                    key={route.href}
                    variant="ghost"
                    className={cn(
                      "w-full justify-start h-11 px-3 text-sm font-medium transition-all duration-200",
                      route.active
                        ? "bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg hover:shadow-xl"
                        : "text-gray-700 hover:bg-orange-50 hover:text-orange-700"
                    )}
                    asChild
                  >
                    <Link href={route.href}>
                      <route.icon className="ml-3 h-5 w-5" />
                      {route.label}
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200/50">
          <Button
            variant="ghost"
            className="w-full justify-start h-11 px-3 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200"
            onClick={() => signOut()}
          >
            <LogOut className="ml-3 h-5 w-5" />
            تسجيل الخروج
          </Button>
        </div>
      </div>
    </div>
  );
}
