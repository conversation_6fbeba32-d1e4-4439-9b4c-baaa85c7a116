'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  TrendingUp,
  Users,
  Heart,
  Share2,
  MessageCircle,
  Eye,
  MousePointer,
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { AdvancedAnalyticsDashboard } from '@/components/analytics/advanced-dashboard';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface AnalyticsData {
  overview: {
    total_posts: number;
    published_posts: number;
    scheduled_posts: number;
    draft_posts: number;
    total_likes: number;
    total_shares: number;
    total_comments: number;
    total_reach: number;
    total_impressions: number;
    total_clicks: number;
    avg_engagement_rate: number;
    avg_reach_per_post: number;
    avg_engagement_per_post: number;
  };
  platform_metrics: Array<{
    platform: string;
    posts: number;
    likes: number;
    shares: number;
    comments: number;
    reach: number;
    impressions: number;
    clicks: number;
    avg_engagement_rate: number;
    avg_reach: number;
    avg_engagement: number;
  }>;
  daily_metrics: Array<{
    date: string;
    posts: number;
    likes: number;
    shares: number;
    comments: number;
    reach: number;
    impressions: number;
    engagement_rate: number;
  }>;
  top_posts: Array<{
    id: string;
    content: string;
    published_at: string;
    total_engagement: number;
    analytics: any[];
  }>;
}

export default function AnalyticsPage() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [period, setPeriod] = useState('30d');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  useEffect(() => {
    checkAuth();
  }, []);

  useEffect(() => {
    if (user) {
      loadAnalytics();
    }
  }, [user, period]);

  const checkAuth = async () => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      toast.error('يرجى تسجيل الدخول أولاً');
      window.location.href = '/auth/signin';
      return;
    }

    setUser(user);
    setIsLoading(false);
  };

  const loadAnalytics = async () => {
    try {
      setIsRefreshing(true);

      const response = await fetch(`/api/analytics/overview?period=${period}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load analytics');
      }

      setAnalyticsData(data);
    } catch (error: any) {
      console.error('Error loading analytics:', error);
      toast.error('فشل في تحميل البيانات التحليلية');
    } finally {
      setIsRefreshing(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'twitter': return '𝕏';
      case 'facebook': return '📘';
      case 'instagram': return '📷';
      case 'linkedin': return '💼';
      case 'tiktok': return '🎵';
      case 'snapchat': return '👻';
      default: return '📱';
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'twitter': return 'bg-black text-white';
      case 'facebook': return 'bg-blue-600 text-white';
      case 'instagram': return 'bg-pink-500 text-white';
      case 'linkedin': return 'bg-blue-700 text-white';
      case 'tiktok': return 'bg-gray-900 text-white';
      case 'snapchat': return 'bg-yellow-400 text-black';
      default: return 'bg-gray-500 text-white';
    }
  };
  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p style={{ color: '#6b7280' }}>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #f3e8ff 100%)',
      direction: 'rtl',
      fontFamily: 'Inter, sans-serif'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              margin: '0 0 0.5rem 0'
            }}>
              📊 التحليلات والإحصائيات
            </h1>
            <p style={{
              color: '#6b7280',
              fontSize: '1rem',
              margin: 0
            }}>
              تتبع أداء منشوراتك وتحليل النتائج
            </p>
          </div>

          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger style={{ width: '150px' }}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">آخر 7 أيام</SelectItem>
                <SelectItem value="30d">آخر 30 يوم</SelectItem>
                <SelectItem value="90d">آخر 90 يوم</SelectItem>
                <SelectItem value="1y">آخر سنة</SelectItem>
              </SelectContent>
            </Select>

            <Button
              onClick={loadAnalytics}
              disabled={isRefreshing}
              variant="outline"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <RefreshCw style={{
                width: '1rem',
                height: '1rem',
                animation: isRefreshing ? 'spin 1s linear infinite' : 'none'
              }} />
              تحديث
            </Button>

            <Button
              variant="outline"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <Download style={{ width: '1rem', height: '1rem' }} />
              تصدير
            </Button>
          </div>
        </div>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
            <TabsTrigger value="advanced">التحليلات المتقدمة</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {analyticsData ? (
              <>
                {/* Overview Cards */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem',
              marginBottom: '2rem'
            }}>
              <Card>
                <CardHeader style={{ paddingBottom: '0.5rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <CardTitle style={{ fontSize: '0.875rem', color: '#6b7280' }}>إجمالي المنشورات</CardTitle>
                    <BarChart3 style={{ width: '1.25rem', height: '1.25rem', color: '#3b82f6' }} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827' }}>
                    {analyticsData.overview.total_posts}
                  </div>
                  <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
                    {analyticsData.overview.published_posts} منشور • {analyticsData.overview.scheduled_posts} مجدول
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader style={{ paddingBottom: '0.5rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <CardTitle style={{ fontSize: '0.875rem', color: '#6b7280' }}>إجمالي الوصول</CardTitle>
                    <Users style={{ width: '1.25rem', height: '1.25rem', color: '#10b981' }} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827' }}>
                    {formatNumber(analyticsData.overview.total_reach)}
                  </div>
                  <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
                    {Math.round(analyticsData.overview.avg_reach_per_post)} متوسط لكل منشور
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader style={{ paddingBottom: '0.5rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <CardTitle style={{ fontSize: '0.875rem', color: '#6b7280' }}>إجمالي التفاعل</CardTitle>
                    <Heart style={{ width: '1.25rem', height: '1.25rem', color: '#ef4444' }} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827' }}>
                    {formatNumber(analyticsData.overview.total_likes + analyticsData.overview.total_shares + analyticsData.overview.total_comments)}
                  </div>
                  <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
                    {Math.round(analyticsData.overview.avg_engagement_per_post)} متوسط لكل منشور
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader style={{ paddingBottom: '0.5rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <CardTitle style={{ fontSize: '0.875rem', color: '#6b7280' }}>معدل التفاعل</CardTitle>
                    <TrendingUp style={{ width: '1.25rem', height: '1.25rem', color: '#8b5cf6' }} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827' }}>
                    {analyticsData.overview.avg_engagement_rate.toFixed(1)}%
                  </div>
                  <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
                    متوسط عبر جميع المنصات
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Platform Performance */}
            <Card style={{ marginBottom: '2rem' }}>
              <CardHeader>
                <CardTitle>أداء المنصات</CardTitle>
                <CardDescription>مقارنة الأداء عبر منصات التواصل الاجتماعي</CardDescription>
              </CardHeader>
              <CardContent>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                  gap: '1rem'
                }}>
                  {analyticsData.platform_metrics.map((platform) => (
                    <div
                      key={platform.platform}
                      style={{
                        border: '1px solid #e5e7eb',
                        borderRadius: '0.5rem',
                        padding: '1rem',
                        background: 'white'
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                        <div style={{
                          width: '2.5rem',
                          height: '2.5rem',
                          borderRadius: '0.5rem',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '1.25rem'
                        }} className={getPlatformColor(platform.platform)}>
                          {getPlatformIcon(platform.platform)}
                        </div>
                        <div>
                          <h3 style={{ fontSize: '1rem', fontWeight: '600', margin: 0 }}>
                            {platform.platform}
                          </h3>
                          <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>
                            {platform.posts} منشور
                          </p>
                        </div>
                      </div>

                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(2, 1fr)',
                        gap: '0.75rem',
                        fontSize: '0.875rem'
                      }}>
                        <div>
                          <div style={{ color: '#6b7280' }}>الوصول</div>
                          <div style={{ fontWeight: '600' }}>{formatNumber(platform.reach)}</div>
                        </div>
                        <div>
                          <div style={{ color: '#6b7280' }}>التفاعل</div>
                          <div style={{ fontWeight: '600' }}>
                            {formatNumber(platform.likes + platform.shares + platform.comments)}
                          </div>
                        </div>
                        <div>
                          <div style={{ color: '#6b7280' }}>معدل التفاعل</div>
                          <div style={{ fontWeight: '600' }}>{platform.avg_engagement_rate.toFixed(1)}%</div>
                        </div>
                        <div>
                          <div style={{ color: '#6b7280' }}>النقرات</div>
                          <div style={{ fontWeight: '600' }}>{formatNumber(platform.clicks)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Posts */}
            <Card>
              <CardHeader>
                <CardTitle>أفضل المنشورات أداءً</CardTitle>
                <CardDescription>المنشورات التي حققت أعلى تفاعل</CardDescription>
              </CardHeader>
              <CardContent>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {analyticsData.top_posts.length > 0 ? (
                    analyticsData.top_posts.map((post, index) => (
                      <div
                        key={post.id}
                        style={{
                          border: '1px solid #e5e7eb',
                          borderRadius: '0.5rem',
                          padding: '1rem',
                          background: 'white'
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '0.75rem' }}>
                          <div style={{ flex: 1 }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                              <Badge variant="secondary">#{index + 1}</Badge>
                              <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                                {new Date(post.published_at).toLocaleDateString('ar-SA')}
                              </span>
                            </div>
                            <p style={{ fontSize: '0.875rem', color: '#374151', margin: 0 }}>
                              {post.content}
                            </p>
                          </div>
                          <div style={{ textAlign: 'left', minWidth: '100px' }}>
                            <div style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#111827' }}>
                              {formatNumber(post.total_engagement)}
                            </div>
                            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                              إجمالي التفاعل
                            </div>
                          </div>
                        </div>

                        <div style={{
                          display: 'flex',
                          gap: '1rem',
                          fontSize: '0.75rem',
                          color: '#6b7280'
                        }}>
                          {post.analytics.map((analytics: any) => (
                            <div key={analytics.platform} style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                              <span>{getPlatformIcon(analytics.platform)}</span>
                              <span>{formatNumber((analytics.likes || 0) + (analytics.shares || 0) + (analytics.comments || 0))}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div style={{
                      textAlign: 'center',
                      padding: '2rem',
                      color: '#6b7280'
                    }}>
                      <BarChart3 style={{ width: '3rem', height: '3rem', margin: '0 auto 1rem', opacity: 0.5 }} />
                      <p>لا توجد بيانات تحليلية متاحة حتى الآن</p>
                      <p style={{ fontSize: '0.875rem' }}>ابدأ بنشر المحتوى لرؤية التحليلات</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '4rem 2rem',
            color: '#6b7280'
          }}>
            <BarChart3 style={{ width: '4rem', height: '4rem', margin: '0 auto 1rem', opacity: 0.5 }} />
            <h3 style={{ fontSize: '1.25rem', fontWeight: '600', margin: '0 0 0.5rem 0' }}>
              لا توجد بيانات تحليلية
            </h3>
            <p style={{ margin: '0 0 1.5rem 0' }}>
              ابدأ بنشر المحتوى لرؤية التحليلات والإحصائيات
            </p>
            <Button onClick={() => window.location.href = '/posts/new'}>
              إنشاء منشور جديد
            </Button>
          </div>
        )}
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            {user && <AdvancedAnalyticsDashboard userId={user.id} />}
          </TabsContent>
        </Tabs>

        {/* Task Status */}
        <div style={{
          marginTop: '2rem',
          padding: '1rem',
          background: '#eff6ff',
          borderRadius: '0.5rem',
          border: '1px solid #bfdbfe'
        }}>
          <p style={{ color: '#1d4ed8', fontWeight: '500', margin: 0 }}>
            📊 Task 1.9 Status: Analytics & Performance Dashboard
          </p>
          <p style={{ color: '#1d4ed8', fontSize: '0.875rem', marginTop: '0.5rem', margin: 0 }}>
            ✅ Analytics data collection API<br/>
            ✅ Performance metrics overview<br/>
            ✅ Platform-specific analytics<br/>
            ✅ Top performing posts analysis<br/>
            ✅ Comprehensive analytics dashboard<br/>
            🔄 Building advanced analytics features...
          </p>
        </div>
      </div>
    </div>
  );
}