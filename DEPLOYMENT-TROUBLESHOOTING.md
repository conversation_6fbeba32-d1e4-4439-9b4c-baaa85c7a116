# 🛠️ DigitalOcean Deployment Troubleshooting Guide

## 🔍 COMMON DEPLOYMENT ERRORS & SOLUTIONS

### **1. BUILD TIMEOUT ERROR**
```
Error: Build process timed out
```

**Solution:**
- Increase build timeout in app spec
- Optimize build process
- Use smaller dependencies

**Fix:**
```yaml
# In app-spec.yaml
build_command: npm run build
environment_slug: node-js
instance_count: 1
instance_size_slug: basic-xxs
```

### **2. ENVIRONMENT VARIABLES MISSING**
```
Error: Required environment variable not found
```

**Required Variables:**
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://app.ewasl.com
```

### **3. NODE.JS VERSION MISMATCH**
```
Error: Node.js version not supported
```

**Solution:**
Add `.nvmrc` file or specify in package.json:
```json
{
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  }
}
```

### **4. MEMORY LIMIT EXCEEDED**
```
Error: JavaScript heap out of memory
```

**Solution:**
```json
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' next build"
  }
}
```

### **5. MISSING DEPENDENCIES**
```
Error: Module not found
```

**Solution:**
Ensure all dependencies are in package.json:
```bash
npm install --save-dev @types/node typescript
```

## 🔧 IMMEDIATE FIXES

### **Fix 1: Update Package.json for Production**
```json
{
  "engines": {
    "node": "18.x",
    "npm": "8.x"
  },
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' next build",
    "start": "next start -p $PORT"
  }
}
```

### **Fix 2: Create .nvmrc File**
```
18.17.0
```

### **Fix 3: Optimize Build Process**
```yaml
# app-spec.yaml
name: ewasl-app
services:
- name: web
  source_dir: /
  github:
    repo: TahaOsa/eWasl.com
    branch: main
  run_command: npm start
  build_command: npm run build
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  routes:
  - path: /
  envs:
  - key: NODE_ENV
    value: production
  - key: NEXT_PUBLIC_APP_URL
    value: https://app.ewasl.com
```

## 🚀 DEPLOYMENT CHECKLIST

### **Before Deployment:**
- [ ] All environment variables configured
- [ ] Node.js version specified
- [ ] Build command optimized
- [ ] Dependencies up to date
- [ ] Local build successful

### **During Deployment:**
- [ ] Monitor build logs
- [ ] Check resource usage
- [ ] Verify environment variables
- [ ] Watch for timeout errors

### **After Deployment:**
- [ ] Test application functionality
- [ ] Check API endpoints
- [ ] Verify database connections
- [ ] Monitor performance

## 📊 RESOURCE OPTIMIZATION

### **Build Optimization:**
```json
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' next build",
    "build:analyze": "ANALYZE=true npm run build"
  }
}
```

### **Next.js Configuration:**
```javascript
// next.config.js
module.exports = {
  experimental: {
    optimizeCss: true,
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  output: 'standalone',
}
```

## 🔍 DEBUGGING STEPS

### **1. Check Build Logs**
- Look for specific error messages
- Identify failing dependencies
- Check memory usage

### **2. Verify Environment**
- Ensure all required variables are set
- Check variable names and values
- Verify secrets are properly configured

### **3. Test Locally**
- Run `npm run build` locally
- Test with production environment variables
- Verify all features work

### **4. Monitor Resources**
- Check memory usage during build
- Monitor CPU usage
- Verify disk space

## 🆘 EMERGENCY FIXES

### **Quick Fix 1: Reduce Bundle Size**
```javascript
// next.config.js
module.exports = {
  experimental: {
    optimizeCss: true,
  },
  webpack: (config) => {
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    };
    return config;
  },
}
```

### **Quick Fix 2: Increase Timeout**
```yaml
# In DigitalOcean App Platform settings
build_command: timeout 1800 npm run build
```

### **Quick Fix 3: Use Smaller Instance**
```yaml
instance_size_slug: basic-xxs  # Smallest available
```

## 📞 SUPPORT RESOURCES

- **DigitalOcean Docs**: https://docs.digitalocean.com/products/app-platform/
- **Next.js Deployment**: https://nextjs.org/docs/deployment
- **Node.js Memory**: https://nodejs.org/api/cli.html#--max-old-space-sizesize

---

**🎯 Most deployment issues are resolved by:**
1. Setting correct environment variables
2. Specifying Node.js version
3. Optimizing build process
4. Increasing memory limits
