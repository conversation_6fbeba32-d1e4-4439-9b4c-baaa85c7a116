'use server';

import { redirect } from 'next/navigation';
import { createCheckoutSession, createCustomerPortalSession } from './config';
import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';

export async function checkoutAction(formData: FormData) {
  const priceId = formData.get('priceId') as string;
  
  if (!priceId) {
    throw new Error('Price ID is required');
  }

  const supabase = await createClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/auth/signin?redirect=/dashboard/billing');
  }

  try {
    // Get or create customer ID from user metadata
    let customerId = user.user_metadata?.stripe_customer_id;

    const session = await createCheckoutSession({
      userId: user.id,
      priceId,
      customerId,
    });

    if (session.url) {
      redirect(session.url);
    } else {
      throw new Error('Failed to create checkout session');
    }
  } catch (error) {
    console.error('Checkout error:', error);
    throw new Error('Failed to create checkout session');
  }
}

export async function customerPortalAction() {
  const supabase = await createClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/auth/signin?redirect=/dashboard/billing');
  }

  const customerId = user.user_metadata?.stripe_customer_id;

  if (!customerId) {
    // Redirect to billing page if no customer ID
    redirect('/dashboard/billing?error=no_subscription');
  }

  try {
    const portalSession = await createCustomerPortalSession(customerId);
    redirect(portalSession.url);
  } catch (error) {
    console.error('Customer portal error:', error);
    throw new Error('Failed to create customer portal session');
  }
}

export async function cancelSubscriptionAction() {
  const supabase = await createClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/auth/signin?redirect=/dashboard/billing');
  }

  // This will redirect to customer portal where they can cancel
  return customerPortalAction();
}

export async function updateSubscriptionAction(formData: FormData) {
  const newPriceId = formData.get('newPriceId') as string;
  
  if (!newPriceId) {
    throw new Error('New price ID is required');
  }

  // This will redirect to customer portal where they can change plans
  return customerPortalAction();
}

export async function reactivateSubscriptionAction() {
  // This will redirect to customer portal where they can reactivate
  return customerPortalAction();
}

// Helper function to refresh billing data
export async function refreshBillingData() {
  revalidatePath('/dashboard/billing');
}
