import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Allow all API testing routes to pass through without authentication
  const isAPITestingRoute =
    request.nextUrl.pathname.startsWith("/api-testing") ||
    request.nextUrl.pathname.startsWith("/test-api") ||
    request.nextUrl.pathname.startsWith("/api-test-simple") ||
    request.nextUrl.pathname.startsWith("/routing-test");

  if (isAPITestingRoute) {
    return NextResponse.next();
  }

  // Allow auth callback and bypass routes
  if (request.nextUrl.pathname.startsWith('/auth/callback') ||
      request.nextUrl.pathname === '/bypass') {
    return response;
  }

  // Check if user is authenticated
  const { data: { user }, error } = await supabase.auth.getUser()

  // Define protected routes
  const protectedRoutes = ['/dashboard', '/posts', '/social', '/analytics', '/settings', '/admin', '/schedule', '/media', '/billing', '/team', '/teams', '/templates', '/workflows', '/calendar', '/publishing']
  const authRoutes = ['/auth/signin', '/auth/signup', '/auth/forgot-password', '/auth/reset-password']

  const isProtectedRoute = protectedRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )
  const isAuthRoute = authRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  // Handle root URL
  const isRootUrl = request.nextUrl.pathname === '/'

  // Bypass route for testing (temporary)
  if (request.nextUrl.pathname === '/bypass') {
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/dashboard'
    return NextResponse.redirect(redirectUrl)
  }

  // Handle root URL - redirect based on authentication status
  if (isRootUrl) {
    if (!user) {
      // User not authenticated, redirect to login
      const redirectUrl = request.nextUrl.clone()
      redirectUrl.pathname = '/auth/signin'
      return NextResponse.redirect(redirectUrl)
    } else {
      // User authenticated, redirect to dashboard
      const redirectUrl = request.nextUrl.clone()
      redirectUrl.pathname = '/dashboard'
      return NextResponse.redirect(redirectUrl)
    }
  }

  // If user is not authenticated and trying to access protected route
  if (!user && isProtectedRoute) {
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/auth/signin'
    redirectUrl.searchParams.set('callbackUrl', request.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If user is authenticated and trying to access auth routes
  if (user && isAuthRoute) {
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/dashboard'
    return NextResponse.redirect(redirectUrl)
  }

  return response;
}

export const config = {
  matcher: [
    "/",
    "/dashboard/:path*",
    "/posts/:path*",
    "/social/:path*",
    "/settings/:path*",
    "/admin/:path*",
    "/schedule/:path*",
    "/analytics/:path*",
    "/media/:path*",
    "/billing/:path*",
    "/team/:path*",
    "/teams/:path*",
    "/templates/:path*",
    "/workflows/:path*",
    "/calendar/:path*",
    "/publishing/:path*",
    "/api-testing/:path*",
    "/test-api/:path*",
    "/api-test-simple/:path*",
    "/routing-test/:path*",
    "/auth/signin",
    "/auth/signup",
    "/auth/forgot-password",
    "/auth/reset-password",
    "/auth/callback",
    "/bypass",
  ],
};
