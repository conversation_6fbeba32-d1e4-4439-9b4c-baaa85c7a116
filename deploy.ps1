# Set variables
$APP_ID = "92d1f7b6-85f2-47e2-8a69-d823b1586159"
$TOKEN = "***********************************************************************"

# Update the app with the app-spec.yaml
Write-Host "Updating app with app-spec.yaml..." -ForegroundColor Cyan
doctl apps update $APP_ID --spec app-spec.yaml --access-token $TOKEN

# Wait for the update to complete
Write-Host "Waiting for update to complete..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

# Get the app info
Write-Host "Getting app info..." -ForegroundColor Cyan
doctl apps get $APP_ID --access-token $TOKEN

Write-Host "Deployment initiated. Check the DigitalOcean App Platform dashboard for progress." -ForegroundColor Green