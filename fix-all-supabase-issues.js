// Comprehensive script to fix all Supabase module-level initialization issues
const fs = require('fs');
const path = require('path');

console.log('🔧 COMPREHENSIVE SUPABASE BUILD ISSUE FIX');
console.log('==========================================\n');

// Find all API route files
function findApiRoutes(dir, routes = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findApiRoutes(fullPath, routes);
    } else if (file === 'route.ts') {
      routes.push(fullPath);
    }
  });
  
  return routes;
}

// Check if file has module-level Supabase client
function hasModuleLevelSupabase(content) {
  const patterns = [
    /const supabase = createClient\(/,
    /export const supabase = createClient\(/,
    /let supabase = createClient\(/
  ];
  
  return patterns.some(pattern => pattern.test(content));
}

// Fix module-level Supabase client
function fixSupabaseClient(content, filePath) {
  console.log(`  🔧 Fixing: ${filePath}`);
  
  // Replace module-level client with function
  content = content.replace(
    /(const|let|export const) supabase = createClient\(\s*process\.env\.NEXT_PUBLIC_SUPABASE_URL!,\s*process\.env\.SUPABASE_SERVICE_ROLE_KEY!,\s*\{[^}]*\}\s*\);/g,
    `function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}`
  );
  
  // Add client creation at the start of exported functions
  content = content.replace(
    /(export async function (GET|POST|PUT|DELETE|PATCH)\([^)]*\)\s*\{)/g,
    '$1\n  const supabase = getSupabaseClient();'
  );
  
  return content;
}

// Main execution
const apiDir = 'src/app/api';
const routes = findApiRoutes(apiDir);

console.log(`📊 Found ${routes.length} API route files\n`);

let totalFixed = 0;
let problematicFiles = [];

routes.forEach(routePath => {
  try {
    const content = fs.readFileSync(routePath, 'utf8');
    
    if (hasModuleLevelSupabase(content)) {
      problematicFiles.push(routePath);
      const fixedContent = fixSupabaseClient(content, routePath);
      fs.writeFileSync(routePath, fixedContent, 'utf8');
      totalFixed++;
      console.log(`  ✅ Fixed: ${routePath}`);
    }
  } catch (error) {
    console.log(`  ❌ Error processing ${routePath}: ${error.message}`);
  }
});

console.log(`\n📈 RESULTS:`);
console.log(`Total API routes: ${routes.length}`);
console.log(`Problematic files found: ${problematicFiles.length}`);
console.log(`Files fixed: ${totalFixed}`);

if (totalFixed > 0) {
  console.log('\n🎉 ALL SUPABASE BUILD ISSUES FIXED!');
  console.log('✅ Module-level clients converted to runtime functions');
  console.log('✅ Environment variables now loaded at runtime');
  console.log('✅ Build should complete successfully');
  
  console.log('\n📋 Fixed files:');
  problematicFiles.forEach(file => {
    console.log(`  - ${file}`);
  });
} else {
  console.log('\n✅ No module-level Supabase clients found');
}

console.log('\n🚀 Ready for successful DigitalOcean deployment!');
