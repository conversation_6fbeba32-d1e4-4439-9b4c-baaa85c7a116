'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  Settings,
  Facebook,
  Linkedin,
  Instagram,
  Twitter,
  Building,
  Users,
  Globe,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import { BusinessAccount, BusinessAccountConfig } from '@/lib/social/business-accounts/business-account-types';
import { BusinessAccountCard } from './business-account-card';

interface UnifiedBusinessAccountSelectorProps {
  userId: string;
  onAccountSelected?: (platform: string, accountId: string) => void;
  onConfigurationChange?: (platform: string, config: BusinessAccountConfig) => void;
  className?: string;
}

interface PlatformStatus {
  platform: string;
  isConnected: boolean;
  isConfigured: boolean;
  hasBusinessAccounts: boolean;
  selectedAccount?: BusinessAccount;
  businessAccounts: BusinessAccount[];
  requiresReconnection: boolean;
  missingPermissions: string[];
  isLoading: boolean;
  error?: string;
}

export function UnifiedBusinessAccountSelector({ 
  userId, 
  onAccountSelected,
  onConfigurationChange,
  className 
}: UnifiedBusinessAccountSelectorProps) {
  const [activeTab, setActiveTab] = useState('facebook');
  const [platforms, setPlatforms] = useState<Record<string, PlatformStatus>>({});
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const platformConfig = {
    facebook: {
      name: 'فيسبوك',
      icon: Facebook,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      description: 'إدارة صفحات فيسبوك التجارية'
    },
    linkedin: {
      name: 'لينكد إن',
      icon: Linkedin,
      color: 'text-blue-700',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-300',
      description: 'إدارة شركات لينكد إن'
    },
    instagram: {
      name: 'إنستغرام',
      icon: Instagram,
      color: 'text-pink-600',
      bgColor: 'bg-pink-50',
      borderColor: 'border-pink-200',
      description: 'إدارة حسابات إنستغرام التجارية'
    },
    twitter: {
      name: 'تويتر',
      icon: Twitter,
      color: 'text-blue-400',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-100',
      description: 'إدارة حسابات تويتر'
    }
  };

  useEffect(() => {
    loadAllPlatforms();
  }, [userId]);

  const loadAllPlatforms = async () => {
    setIsInitialLoading(true);
    
    const platformNames = Object.keys(platformConfig);
    const loadPromises = platformNames.map(platform => loadPlatformConfig(platform, false));
    
    await Promise.all(loadPromises);
    setIsInitialLoading(false);
  };

  const loadPlatformConfig = async (platform: string, showToast = true) => {
    setPlatforms(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        platform,
        isLoading: true,
        error: undefined
      }
    }));

    try {
      const response = await fetch(`/api/social/business-accounts?platform=${platform}&userId=${userId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to load ${platform} configuration`);
      }

      const platformStatus: PlatformStatus = {
        platform,
        isConnected: !data.requiresReconnection,
        isConfigured: data.isConfigured,
        hasBusinessAccounts: data.hasBusinessAccounts,
        selectedAccount: data.selectedAccount,
        businessAccounts: data.businessAccounts || [],
        requiresReconnection: data.requiresReconnection,
        missingPermissions: data.missingPermissions || [],
        isLoading: false
      };

      setPlatforms(prev => ({
        ...prev,
        [platform]: platformStatus
      }));

      if (onConfigurationChange) {
        onConfigurationChange(platform, data);
      }

      if (showToast && data.isConfigured) {
        const config = platformConfig[platform as keyof typeof platformConfig];
        toast.success(`تم تحميل إعدادات ${config?.name || platform} بنجاح`);
      }

    } catch (error: any) {
      console.error(`Error loading ${platform} config:`, error);
      
      setPlatforms(prev => ({
        ...prev,
        [platform]: {
          ...prev[platform],
          platform,
          isConnected: false,
          isConfigured: false,
          hasBusinessAccounts: false,
          businessAccounts: [],
          requiresReconnection: true,
          missingPermissions: [],
          isLoading: false,
          error: error.message
        }
      }));

      if (showToast) {
        const config = platformConfig[platform as keyof typeof platformConfig];
        toast.error(`فشل في تحميل إعدادات ${config?.name || platform}`);
      }
    }
  };

  const refreshPlatform = async (platform: string) => {
    try {
      setIsRefreshing(true);
      
      const response = await fetch('/api/social/business-accounts/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform,
          userId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to refresh ${platform}`);
      }

      // Reload the platform configuration
      await loadPlatformConfig(platform, false);
      
      const config = platformConfig[platform as keyof typeof platformConfig];
      toast.success(`تم تحديث ${config?.name || platform} بنجاح`);

    } catch (error: any) {
      console.error(`Error refreshing ${platform}:`, error);
      const config = platformConfig[platform as keyof typeof platformConfig];
      toast.error(`فشل في تحديث ${config?.name || platform}`);
    } finally {
      setIsRefreshing(false);
    }
  };

  const selectAccount = async (platform: string, accountId: string) => {
    try {
      const response = await fetch('/api/social/business-accounts/select', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform,
          userId,
          businessAccountId: accountId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to select ${platform} account`);
      }

      // Update the platform status
      setPlatforms(prev => ({
        ...prev,
        [platform]: {
          ...prev[platform],
          selectedAccount: prev[platform].businessAccounts.find(acc => acc.id === accountId),
          businessAccounts: prev[platform].businessAccounts.map(acc => ({
            ...acc,
            isSelected: acc.id === accountId
          }))
        }
      }));

      onAccountSelected?.(platform, accountId);
      const config = platformConfig[platform as keyof typeof platformConfig];
      toast.success(`تم اختيار حساب ${config?.name || platform} بنجاح`);

    } catch (error: any) {
      console.error(`Error selecting ${platform} account:`, error);
      const config = platformConfig[platform as keyof typeof platformConfig];
      toast.error(`فشل في اختيار حساب ${config?.name || platform}`);
    }
  };

  const connectPlatform = async (platform: string) => {
    try {
      const response = await fetch('/api/social/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ platform }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to connect ${platform}`);
      }

      if (data.authUrl) {
        window.location.href = data.authUrl;
      }

    } catch (error: any) {
      console.error(`Error connecting ${platform}:`, error);
      const config = platformConfig[platform as keyof typeof platformConfig];
      toast.error(`فشل في ربط ${config?.name || platform}`);
    }
  };

  const getPlatformStatusBadge = (platform: string) => {
    const status = platforms[platform];
    if (!status) return <Badge variant="secondary">غير محمل</Badge>;

    if (status.isLoading) {
      return <Badge variant="secondary">جاري التحميل...</Badge>;
    }

    if (status.error) {
      return <Badge variant="destructive">خطأ</Badge>;
    }

    if (!status.isConnected) {
      return <Badge variant="destructive">غير متصل</Badge>;
    }

    if (!status.hasBusinessAccounts) {
      return <Badge variant="secondary">لا توجد حسابات</Badge>;
    }

    if (!status.selectedAccount) {
      return <Badge className="bg-yellow-500">لم يتم الاختيار</Badge>;
    }

    return <Badge className="bg-green-500">مكون</Badge>;
  };

  const getPlatformStatusIcon = (platform: string) => {
    const status = platforms[platform];
    if (!status) return <AlertCircle className="w-4 h-4 text-gray-400" />;

    if (status.isLoading) {
      return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
    }

    if (status.error || !status.isConnected) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }

    if (status.selectedAccount) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }

    return <AlertCircle className="w-4 h-4 text-yellow-500" />;
  };

  const renderPlatformContent = (platform: string) => {
    const status = platforms[platform];
    const config = platformConfig[platform as keyof typeof platformConfig];

    if (!status) {
      return (
        <div className="text-center py-8">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">جاري تحميل إعدادات {config.name}...</p>
        </div>
      );
    }

    if (status.error) {
      return (
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">خطأ في تحميل {config.name}</h3>
          <p className="text-muted-foreground mb-4">{status.error}</p>
          <Button onClick={() => loadPlatformConfig(platform)} variant="outline">
            إعادة المحاولة
          </Button>
        </div>
      );
    }

    if (!status.isConnected) {
      return (
        <div className="text-center py-8">
          <config.icon className={`w-12 h-12 ${config.color} mx-auto mb-4`} />
          <h3 className="text-lg font-semibold mb-2">ربط حساب {config.name}</h3>
          <p className="text-muted-foreground mb-4">{config.description}</p>
          <Button onClick={() => connectPlatform(platform)} className="flex items-center gap-2">
            <ExternalLink className="w-4 h-4" />
            ربط {config.name}
          </Button>
        </div>
      );
    }

    if (!status.hasBusinessAccounts) {
      return (
        <div className="text-center py-8">
          <Building className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">لا توجد حسابات تجارية</h3>
          <p className="text-muted-foreground mb-4">
            لم يتم العثور على حسابات تجارية قابلة للإدارة في {config.name}
          </p>
          <div className="flex gap-2 justify-center">
            <Button onClick={() => refreshPlatform(platform)} variant="outline" disabled={isRefreshing}>
              {isRefreshing ? (
                <>
                  <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
                  جاري التحديث...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 ml-2" />
                  تحديث
                </>
              )}
            </Button>
            <Button onClick={() => connectPlatform(platform)} variant="outline">
              <ExternalLink className="w-4 h-4 ml-2" />
              إعادة الربط
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">{config.name} ({status.businessAccounts.length})</h3>
            <p className="text-sm text-muted-foreground">{config.description}</p>
          </div>
          <Button 
            onClick={() => refreshPlatform(platform)} 
            variant="outline" 
            size="sm"
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
          </Button>
        </div>

        <div className="space-y-3">
          {status.businessAccounts.map((account) => (
            <BusinessAccountCard
              key={account.id}
              account={account}
              isSelected={account.isSelected || status.selectedAccount?.id === account.id}
              onSelect={(accountId) => selectAccount(platform, accountId)}
            />
          ))}
        </div>

        {status.selectedAccount && (
          <div className="mt-4 p-3 bg-green-50 dark:bg-green-950 border border-green-200 rounded-lg">
            <p className="text-sm text-green-700 dark:text-green-300">
              ✅ سيتم النشر على: {status.selectedAccount.name}
            </p>
          </div>
        )}
      </div>
    );
  };

  if (isInitialLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-6 h-6" />
            إعداد الحسابات التجارية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
            <span className="mr-2 text-muted-foreground">جاري تحميل الإعدادات...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-6 h-6" />
          إعداد الحسابات التجارية
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            {Object.entries(platformConfig).map(([platform, config]) => (
              <TabsTrigger 
                key={platform} 
                value={platform}
                className="flex items-center gap-2"
              >
                {getPlatformStatusIcon(platform)}
                <config.icon className={`w-4 h-4 ${config.color}`} />
                <span className="hidden sm:inline">{config.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.keys(platformConfig).map((platform) => (
            <TabsContent key={platform} value={platform} className="space-y-4">
              {renderPlatformContent(platform)}
            </TabsContent>
          ))}
        </Tabs>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {Object.values(platforms).filter(p => p.selectedAccount).length} من{' '}
              {Object.keys(platformConfig).length} منصات مكونة
            </div>
            <div className="flex gap-2">
              {Object.entries(platformConfig).map(([platform, config]) => (
                <div key={platform} className="flex items-center gap-1">
                  <config.icon className={`w-4 h-4 ${config.color}`} />
                  {getPlatformStatusBadge(platform)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
