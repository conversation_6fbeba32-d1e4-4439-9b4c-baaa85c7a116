import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

function getSupabaseClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Validation schema for analytics data
const analyticsDataSchema = z.object({
  post_id: z.string(),
  platform: z.enum(['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN', 'TIKTOK', 'SNAPCHAT']),
  likes: z.number().min(0).default(0),
  shares: z.number().min(0).default(0),
  comments: z.number().min(0).default(0),
  reach: z.number().min(0).default(0),
  impressions: z.number().min(0).default(0),
  clicks: z.number().min(0).default(0),
  engagement_rate: z.number().min(0).max(100).default(0),
  platform_post_id: z.string().optional(),
  collected_at: z.string().optional(),
});

// POST - Collect analytics data for a post
export async function POST(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Collecting analytics data...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = analyticsDataSchema.parse(body);

    console.log('Analytics data:', validatedData);

    // Verify the post belongs to the authenticated user
    const { data: post, error: postError } = await supabase
      .from('posts')
      .select('id, user_id, status')
      .eq('id', validatedData.post_id)
      .eq('user_id', user.id)
      .single();

    if (postError || !post) {
      console.error('Post not found or unauthorized:', postError);
      return NextResponse.json(
        { error: 'Post not found or unauthorized' },
        { status: 404 }
      );
    }

    // Check if analytics record already exists for this post and platform
    const { data: existingAnalytics, error: checkError } = await supabase
      .from('analytics')
      .select('id')
      .eq('post_id', validatedData.post_id)
      .eq('platform', validatedData.platform)
      .single();

    const analyticsData = {
      post_id: validatedData.post_id,
      platform: validatedData.platform,
      likes: validatedData.likes,
      shares: validatedData.shares,
      comments: validatedData.comments,
      reach: validatedData.reach,
      impressions: validatedData.impressions,
      clicks: validatedData.clicks,
      engagement_rate: validatedData.engagement_rate,
      platform_post_id: validatedData.platform_post_id,
      collected_at: validatedData.collected_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    let result;

    if (existingAnalytics && !checkError) {
      // Update existing analytics record
      console.log('Updating existing analytics record:', existingAnalytics.id);
      
      const { data, error } = await supabase
        .from('analytics')
        .update(analyticsData)
        .eq('id', existingAnalytics.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating analytics:', error);
        return NextResponse.json(
          { error: 'Failed to update analytics data' },
          { status: 500 }
        );
      }

      result = data;
    } else {
      // Create new analytics record
      console.log('Creating new analytics record');
      
      const { data, error } = await supabase
        .from('analytics')
        .insert({
          ...analyticsData,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating analytics:', error);
        return NextResponse.json(
          { error: 'Failed to create analytics data' },
          { status: 500 }
        );
      }

      result = data;
    }

    // Log activity
    await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        action: 'ANALYTICS_COLLECTED',
        details: `Analytics data collected for post ${validatedData.post_id} on ${validatedData.platform}`,
        created_at: new Date().toISOString(),
      });

    console.log('Analytics data saved successfully:', result.id);

    return NextResponse.json({
      success: true,
      analytics: result,
      message: 'Analytics data collected successfully'
    }, { status: 200 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        { error: 'Invalid analytics data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Analytics collection error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - Get analytics data for a specific post
export async function GET(request: NextRequest) {
  const supabase = getSupabaseClient();
  try {
    console.log('Fetching post analytics...');
    
    // Get user from Supabase Auth
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('User not authenticated');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('post_id');
    const platform = searchParams.get('platform');

    if (!postId) {
      return NextResponse.json(
        { error: 'Post ID is required' },
        { status: 400 }
      );
    }

    // Verify the post belongs to the authenticated user
    const { data: post, error: postError } = await supabase
      .from('posts')
      .select('id, user_id')
      .eq('id', postId)
      .eq('user_id', user.id)
      .single();

    if (postError || !post) {
      console.error('Post not found or unauthorized:', postError);
      return NextResponse.json(
        { error: 'Post not found or unauthorized' },
        { status: 404 }
      );
    }

    // Build analytics query
    let analyticsQuery = supabase
      .from('analytics')
      .select('*')
      .eq('post_id', postId)
      .order('updated_at', { ascending: false });

    // Filter by platform if specified
    if (platform) {
      analyticsQuery = analyticsQuery.eq('platform', platform);
    }

    const { data: analytics, error: analyticsError } = await analyticsQuery;

    if (analyticsError) {
      console.error('Error fetching analytics:', analyticsError);
      return NextResponse.json(
        { error: 'Failed to fetch analytics data' },
        { status: 500 }
      );
    }

    // Calculate totals across all platforms
    const totals = analytics?.reduce((acc, curr) => ({
      likes: acc.likes + (curr.likes || 0),
      shares: acc.shares + (curr.shares || 0),
      comments: acc.comments + (curr.comments || 0),
      reach: acc.reach + (curr.reach || 0),
      impressions: acc.impressions + (curr.impressions || 0),
      clicks: acc.clicks + (curr.clicks || 0),
      engagement_rate: acc.engagement_rate + (curr.engagement_rate || 0),
    }), {
      likes: 0,
      shares: 0,
      comments: 0,
      reach: 0,
      impressions: 0,
      clicks: 0,
      engagement_rate: 0,
    }) || {
      likes: 0,
      shares: 0,
      comments: 0,
      reach: 0,
      impressions: 0,
      clicks: 0,
      engagement_rate: 0,
    };

    // Calculate average engagement rate
    if (analytics && analytics.length > 0) {
      totals.engagement_rate = totals.engagement_rate / analytics.length;
    }

    return NextResponse.json({
      success: true,
      post_id: postId,
      analytics: analytics || [],
      totals,
      platforms_count: analytics?.length || 0
    }, { status: 200 });

  } catch (error) {
    console.error('Analytics fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
